(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3938],{2511:(e,a,t)=>{"use strict";t.d(a,{b:()=>r});let r={information_media:"Information / Media",econommic_social_development:"Economic & Social Development",security_police_peacekeeping:"Security / Police / Peacekeeping",disarmament_and_demobilization:"Disarmament & Demobilization",environment:"Environment",private_sector:"Private Sector",humanitarian_coordination_information_management:"Humanitarian - Coordination & Info Management",humanitarian_multiple_clusters:"Humanitarian - Multiple Clusters",humanitarian_camp_management_and_coordination:"Humanitarian - Camp Management & Coordination",humanitarian_early_recovery:"Humanitarian - Early Recovery",humanitarian_education:"Humanitarian - Education",humanitarian_emergency_shelter:"Humanitarian - Emergency Shelter",humanitarian_emergency_telecoms:"Humanitarian - Emergency Telecoms",humanitarian_food_security:"Humanitarian - Food Security",humanitarian_health:"Humanitarian - Health",humanitarian_logistics:"Humanitarian - Logistics",humanitarian_nutrition:"Humanitarian - Nutrition",humanitarian_protection:"Humanitarian - Protection",humanitarian_sanitation_water_and_hygiene:"Humanitarian - Sanitation / Water / Hygiene",other:"Other"}},13163:(e,a,t)=>{"use strict";t.d(a,{A:()=>l});var r=t(95155),n=t(60760),i=t(44518),o=t(95233),s=t(54416);t(12115);let l=e=>{let{children:a,className:t,isOpen:l,onClose:c,preventOutsideClick:u=!1}=e;return(0,r.jsx)(n.N,{children:l&&(0,r.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40 p-4 overflow-y-auto",onClick:e=>{u||c()},children:(0,r.jsxs)(i.P.div,{initial:{scale:.6,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.6,opacity:0},transition:{duration:.3,ease:o.am},className:"relative section flex flex-col max-w-4xl max-h-[90vh] overflow-y-auto ".concat(t),onClick:e=>e.stopPropagation(),children:[(0,r.jsx)(s.A,{onClick:c,className:"absolute self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300"}),a]})})})}},25784:(e,a,t)=>{"use strict";t.d(a,{A:()=>n});let r=t(23464).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let n=r},26303:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>_});var r=t(95155),n=t(12115),i=t(62177),o=t(25784),s=t(35695),l=t(88570),c=t(26715),u=t(19373),d=t(5041),m=t(77361),p=t(34540),h=t(71402),y=t(50408),v=t(57434),g=t(34869),b=t(17576),x=t(57799),f=t(2511),j=t(74567),N=t(64368),S=t(29350),C=t(10786),w=t(63642);let k=async e=>{let{projectId:a,dataToSend:t}=e,{data:r}=await o.A.patch("/projects/".concat(a),t);return r},_=()=>{let[e,a]=(0,n.useState)(!1);(0,n.useEffect)(()=>{a(!0)},[]);let{register:t,formState:{isSubmitting:o,errors:_,isSubmitted:E},handleSubmit:P,setValue:A,reset:D}=(0,i.mN)(),z=(0,s.useRouter)(),[B,L]=(0,n.useState)(!1),[T,M]=(0,n.useState)(null),[F,I]=(0,n.useState)(null),[H,R]=(0,n.useState)(!1),[K,q]=(0,n.useState)(!1),[G,U]=(0,n.useState)(null);(0,n.useEffect)(()=>{t("country",{required:"Please select a country"}),t("sector",{required:"Please select a sector"})},[t]),(0,n.useEffect)(()=>{A("country",T,{shouldValidate:E}),A("sector",F,{shouldValidate:E})},[A,T,F]);let{hashedId:Q}=(0,s.useParams)(),V=(0,l.D)(Q),{user:O}=(0,S.A)(),J=(0,c.jE)();(0,n.useEffect)(()=>()=>{V&&(null==O?void 0:O.id)&&J.cancelQueries({queryKey:["projects",O.id,V]})},[V,null==O?void 0:O.id,J]);let{data:Y,isLoading:W,isError:Z}=(0,u.I)({queryKey:["projects",null==O?void 0:O.id,V],queryFn:()=>(0,m.kf)({projectId:V}),enabled:!!V&&!!(null==O?void 0:O.id)&&!B});(0,n.useEffect)(()=>{Y&&(D({projectName:Y.name||"",description:Y.description||"",country:Y.country||"",sector:Y.sector||""}),M(Y.country||null),I(Y.sector||null))},[Y,D]);let X=(0,p.wA)(),$=(0,d.n)({mutationFn:k,onSuccess:()=>{J.invalidateQueries({queryKey:["projects",null==O?void 0:O.id],exact:!1}),X((0,h.Ds)({message:"Project details have been updated",type:"success"}))},onError:e=>{X((0,h.Ds)({message:"Failed to update project details. Please try again."+e.message,type:"error"}))}}),ee=(0,d.n)({mutationFn:e=>(0,m.pf)(V,(null==e?void 0:e.isUnarchive)||!1),onSuccess:(e,a)=>{let t=(null==a?void 0:a.isUnarchive)||!1;J.invalidateQueries({queryKey:["projects",null==O?void 0:O.id]}),X((0,h.Ds)({message:t?"Project unarchived successfully":"Project deployed successfully",type:"success"})),q(!1)},onError:e=>{X((0,h.Ds)({message:"Failed to deploy project. Please try again",type:"error"})),q(!1)}}),ea=(0,d.n)({mutationFn:()=>(0,m.Im)(V),onSuccess:()=>{J.invalidateQueries({queryKey:["projects",null==O?void 0:O.id,V]}),X((0,h.Ds)({message:"Project has been archived successfully",type:"success"})),q(!1),z.push("/dashboard"),J.invalidateQueries({queryKey:["projects",null==O?void 0:O.id]})},onError:e=>{console.error("Project archive error:",e),X((0,h.Ds)({message:"Failed to archive project. Please try again.",type:"error"})),q(!1)}}),et=(0,d.n)({mutationFn:()=>(0,m.xx)(V),onSuccess:()=>{L(!0),q(!1),J.cancelQueries({queryKey:["projects",null==O?void 0:O.id,V]}),J.removeQueries({queryKey:["projects",null==O?void 0:O.id,V]}),J.invalidateQueries({queryKey:["projects",null==O?void 0:O.id]}),X((0,h.Ds)({message:"Project has been deleted successfully",type:"success"})),setTimeout(()=>{z.push("/dashboard")},1e3)},onError:e=>{q(!1),console.error("Project deletion error:",e),X((0,h.Ds)({message:"Failed to delete project. Please try again.",type:"error"}))}}),er=()=>{let e=(null==Y?void 0:Y.status)==="archived",a="Are you sure you want to deploy this project?";(null==Y?void 0:Y.status)==="deployed"?a="Are you sure you want to redeploy this project?":(null==Y?void 0:Y.status)==="archived"&&(a="Are you sure you want to Deploy this project?"),U({title:e?"Confirm Deploy":"Confirm Unarchive",description:a,confirmButtonText:e?"Deploy":"Unarchive",confirmButtonClass:"btn-primary",onConfirm:()=>{ee.mutate({isUnarchive:e})}}),q(!0)},en=async e=>{$.mutate({projectId:V,dataToSend:{name:e.projectName,description:e.description,country:e.country,sector:e.sector}})};return e?B||W?(0,r.jsx)(x.A,{}):Q&&null!==V?Z&&!B?(0,r.jsx)("p",{className:"text-red-500",children:"Failed to fetch project. Please try again."}):(0,r.jsxs)("form",{className:"flex flex-col gap-8",onSubmit:P(en),children:[(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"project-name",className:"label-text",children:[(0,r.jsx)(v.A,{size:16})," Project Name"]}),(0,r.jsx)("input",{...t("projectName",{required:"Project name is required."}),id:"project-name",type:"text",className:"input-field",placeholder:"Enter a project name"}),_.projectName&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(_.projectName.message)})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"description",className:"label-text",children:"Description"}),(0,r.jsx)("textarea",{id:"description",...t("description"),className:"input-field resize-none",cols:4,placeholder:"Enter the project description"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,r.jsx)(g.A,{size:16}),"Country"]}),(0,r.jsx)(y.l,{id:"country",options:j,value:T,onChange:M}),_.country&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(_.country.message)})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,r.jsx)(b.A,{size:16})," Sector"]}),(0,r.jsx)(y.l,{id:"sector",options:Object.values(f.b),value:F&&f.b[F]?f.b[F]:"Select an option",onChange:e=>{I((0,N.H)(e,f.b))}}),_.sector&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(_.sector.message)})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(null==Y?void 0:Y.status)==="deployed"&&(0,r.jsx)("button",{onClick:()=>{U({title:"Confirm Archive",description:(0,r.jsx)(r.Fragment,{children:"Are you sure you want to archive this project? Your form will not accept submissions while it is archived."}),confirmButtonText:"Archive",confirmButtonClass:"btn-primary",onConfirm:()=>{ea.mutate()}}),q(!0)},type:"button",className:"btn-outline",children:"Archive"}),(null==Y?void 0:Y.status)==="deployed"&&(0,r.jsx)("button",{onClick:er,type:"button",className:"btn-outline",children:"ReDeploy"}),(null==Y?void 0:Y.status)==="archived"&&(0,r.jsx)("button",{onClick:er,type:"button",className:"btn-outline",children:"Deploy"}),(null==Y?void 0:Y.status)==="draft"&&(0,r.jsx)("button",{onClick:er,type:"button",className:"btn-outline",children:"Deploy"}),(0,r.jsx)("button",{type:"button",className:"btn-outline",onClick:()=>{R(!0)},children:"Share"}),(0,r.jsx)("button",{onClick:()=>{U({title:"Confirm Delete",description:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:"Are you sure you want to delete this project? This action cannot be undone."}),(0,r.jsxs)("ul",{className:"list-disc list-inside flex flex-col gap-2 mt-2 text-neutral-700",children:[(0,r.jsx)("li",{children:"All data gathered for this project will be deleted."}),(0,r.jsx)("li",{children:"Forms associated with this project will be deleted."}),(0,r.jsx)("li",{children:"You will not be able to recover this project after deletion."})]})]}),confirmButtonText:"Delete",confirmButtonClass:"btn-danger",onConfirm:()=>{et.mutate()}}),q(!0)},type:"button",className:"btn-danger",children:"Delete"})]}),(0,r.jsx)("button",{type:"submit",className:"btn-primary self-end",children:o?(0,r.jsxs)("span",{className:"flex items-center gap-2",children:["Saving"," ",(0,r.jsx)("div",{className:"size-4 animate-spin border-x border-neutral-100 rounded-full"})]}):"Save Changes"})]})]}),(0,r.jsx)(C.m,{showModal:H,onClose:()=>R(!1),onShare:()=>{R(!1)}}),G&&(0,r.jsx)(w.R,{showModal:K,onClose:()=>q(!1),title:G.title,description:G.description,confirmButtonText:G.confirmButtonText,confirmButtonClass:G.confirmButtonClass,onConfirm:G.onConfirm})]}):(0,r.jsxs)("div",{className:"error-message",children:[(0,r.jsx)("h1",{className:"text-red-500",children:"Error: Invalid Project ID (hashedId)."}),(0,r.jsx)("p",{className:"text-neutral-700",children:"Please make sure the URL contains a valid project identifier."})]}):null}},50408:(e,a,t)=>{"use strict";t.d(a,{l:()=>o});var r=t(95155),n=t(66474),i=t(12115);let o=e=>{let{id:a,options:t,value:o,onChange:s}=e,[l,c]=(0,i.useState)(!1),u=(0,i.useRef)(null),d=(0,i.useRef)([]),m=(0,i.useRef)(null);(0,i.useEffect)(()=>{let e=e=>{m.current&&!m.current.contains(e.target)&&c(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let p=e=>{if(!l)return;let a=e.key.toLowerCase();if(a.match(/[a-z]/)){let e=t.findIndex(e=>e.toLowerCase().startsWith(a));if(-1!==e&&d.current[e]){var r;null==(r=d.current[e])||r.scrollIntoView({behavior:"auto",block:"nearest"})}}};return(0,i.useEffect)(()=>(document.addEventListener("keydown",p),()=>{document.removeEventListener("keydown",p)}),[l,t]),(0,r.jsxs)("div",{className:"relative",ref:m,children:[(0,r.jsxs)("button",{id:a,type:"button",className:"px-4 py-2 flex items-center justify-between rounded-md border border-neutral-400 focus:border-primary-500 duration-300 w-full text-left cursor-pointer",onClick:()=>{c(!l)},children:[(0,r.jsx)("span",{children:o||"Select an option"}),(0,r.jsx)(n.A,{})]}),l&&(0,r.jsx)("ul",{className:"absolute z-10 max-h-[180px] overflow-auto border border-neutral-400 rounded-md bg-neutral-100 w-full mt-1 flex flex-col",ref:u,children:t.map((e,a)=>(0,r.jsx)("li",{ref:e=>{d.current[a]=e},className:"cursor-pointer bg-neutral-100 hover:bg-neutral-200 px-4 py-2",onClick:()=>{s(e),c(!1)},children:e},a))})]})}},53999:(e,a,t)=>{"use strict";t.d(a,{Y:()=>o,cn:()=>i});var r=t(52596),n=t(39688);function i(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,n.QP)((0,r.$)(a))}function o(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"short";if(!e)return"";try{let t="string"==typeof e?new Date(e):e;if(isNaN(t.getTime()))return"";switch(a){case"short":return t.toLocaleDateString(void 0,{year:"numeric",month:"short",day:"numeric"});case"long":return t.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});case"full":return t.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",weekday:"long",hour:"2-digit",minute:"2-digit",second:"2-digit"});default:return t.toLocaleDateString()}}catch(a){return console.error("Error formatting date:",a),String(e)}}},64368:(e,a,t)=>{"use strict";t.d(a,{H:()=>r});let r=(e,a)=>{let t=Object.entries(a).find(a=>{let[t,r]=a;return r===e});return t?t[0]:null}},71106:(e,a,t)=>{Promise.resolve().then(t.bind(t,26303))},74567:e=>{"use strict";e.exports=JSON.parse('["Afghanistan","Albania","Algeria","Andorra","Angola","Argentina","Armenia","Australia","Austria","Azerbaijan","Bahamas","Bahrain","Bangladesh","Barbados","Belarus","Belgium","Belize","Benin","Bhutan","Bolivia","Bosnia and Herzegovina","Botswana","Brazil","Brunei","Bulgaria","Burkina Faso","Burundi","Cabo Verde","Cambodia","Cameroon","Canada","Central African Republic","Chad","Chile","China","Colombia","Comoros","Congo (Congo-Brazzaville)","Costa Rica","Croatia","Cuba","Cyprus","Czech Republic","Denmark","Djibouti","Dominica","Dominican Republic","Ecuador","Egypt","El Salvador","Equatorial Guinea","Eritrea","Estonia","Eswatini","Ethiopia","Fiji","Finland","France","Gabon","Gambia","Georgia","Germany","Ghana","Greece","Grenada","Guatemala","Guinea","Guinea-Bissau","Guyana","Haiti","Honduras","Hungary","Iceland","India","Indonesia","Iran","Iraq","Ireland","Israel","Italy","Jamaica","Japan","Jordan","Kazakhstan","Kenya","Kiribati","Kuwait","Kyrgyzstan","Laos","Latvia","Lebanon","Lesotho","Liberia","Libya","Liechtenstein","Lithuania","Luxembourg","Madagascar","Malawi","Malaysia","Maldives","Mali","Malta","Marshall Islands","Mauritania","Mauritius","Mexico","Micronesia","Moldova","Monaco","Mongolia","Montenegro","Morocco","Mozambique","Myanmar","Namibia","Nauru","Nepal","Netherlands","New Zealand","Nicaragua","Niger","Nigeria","North Korea","North Macedonia","Norway","Oman","Pakistan","Palau","Palestine","Panama","Papua New Guinea","Paraguay","Peru","Philippines","Poland","Portugal","Qatar","Romania","Russia","Rwanda","Saint Kitts and Nevis","Saint Lucia","Saint Vincent and the Grenadines","Samoa","San Marino","Sao Tome and Principe","Saudi Arabia","Senegal","Serbia","Seychelles","Sierra Leone","Singapore","Slovakia","Slovenia","Solomon Islands","Somalia","South Africa","South Korea","South Sudan","Spain","Sri Lanka","Sudan","Suriname","Sweden","Switzerland","Syria","Taiwan","Tajikistan","Tanzania","Thailand","Timor-Leste","Togo","Tonga","Trinidad and Tobago","Tunisia","Turkey","Turkmenistan","Tuvalu","Uganda","Ukraine","United Arab Emirates","United Kingdom","United States","Uruguay","Uzbekistan","Vanuatu","Vatican City","Venezuela","Vietnam","Yemen","Zambia","Zimbabwe"]')},88570:(e,a,t)=>{"use strict";t.d(a,{D:()=>s,l:()=>o});var r=t(41050);let n=t(49509).env.SALT||"rushan-salt",i=new r.A(n,12),o=e=>i.encode(e),s=e=>{let a=i.decode(e)[0];return"bigint"==typeof a?a<Number.MAX_SAFE_INTEGER?Number(a):null:"number"==typeof a?a:null}},97168:(e,a,t)=>{"use strict";t.d(a,{$:()=>l});var r=t(95155);t(12115);var n=t(99708),i=t(74466),o=t(53999);let s=(0,i.F)("inline-flex items-center justify-center gap-2 neutral-100space-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-neutral-100 shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:a,variant:t,size:i,asChild:l=!1,...c}=e,u=l?n.DX:"button";return(0,r.jsx)(u,{"data-slot":"button",className:(0,o.cn)(s({variant:t,size:i,className:a})),...c})}}},e=>{var a=a=>e(e.s=a);e.O(0,[2150,635,1445,6967,6903,4601,2177,4277,740,3465,8441,1684,7358],()=>a(71106)),_N_E=e.O()}]);