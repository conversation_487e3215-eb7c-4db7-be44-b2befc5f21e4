"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7252],{5040:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},9343:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("folder-plus",[["path",{d:"M12 10v6",key:"1bos4e"}],["path",{d:"M9 13h6",key:"1uhe8q"}],["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]])},13052:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},13717:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},14186:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},18084:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("copy-plus",[["line",{x1:"15",x2:"15",y1:"12",y2:"18",key:"1p7wdc"}],["line",{x1:"12",x2:"18",y1:"15",y2:"15",key:"1nscbv"}],["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},42355:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},47924:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},48021:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("grip-vertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]])},49103:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("circle-plus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},50402:(e,t,n)=>{n.d(t,{_G:()=>c,be:()=>a,gB:()=>f,gl:()=>w});var r=n(12115),l=n(75143),i=n(78266);function a(e,t,n){let r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function o(e){return null!==e&&e>=0}let u=e=>{let{rects:t,activeIndex:n,overIndex:r,index:l}=e,i=a(t,r,n),o=t[l],u=i[l];return u&&o?{x:u.left-o.left,y:u.top-o.top,scaleX:u.width/o.width,scaleY:u.height/o.height}:null},s={scaleX:1,scaleY:1},c=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:l,rects:i,overIndex:a}=e,o=null!=(t=i[n])?t:r;if(!o)return null;if(l===n){let e=i[a];return e?{x:0,y:n<a?e.top+e.height-(o.top+o.height):e.top-o.top,...s}:null}let u=function(e,t,n){let r=e[t],l=e[t-1],i=e[t+1];return r?n<t?l?r.top-(l.top+l.height):i?i.top-(r.top+r.height):0:i?i.top-(r.top+r.height):l?r.top-(l.top+l.height):0:0}(i,l,n);return l>n&&l<=a?{x:0,y:-o.height-u,...s}:l<n&&l>=a?{x:0,y:o.height+u,...s}:{x:0,y:0,...s}},d="Sortable",h=r.createContext({activeIndex:-1,containerId:d,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:u,disabled:{draggable:!1,droppable:!1}});function f(e){let{children:t,id:n,items:a,strategy:o=u,disabled:s=!1}=e,{active:c,dragOverlay:f,droppableRects:g,over:p,measureDroppableContainers:v}=(0,l.fF)(),y=(0,i.YG)(d,n),b=null!==f.rect,m=(0,r.useMemo)(()=>a.map(e=>"object"==typeof e&&"id"in e?e.id:e),[a]),w=null!=c,x=c?m.indexOf(c.id):-1,k=p?m.indexOf(p.id):-1,E=(0,r.useRef)(m),D=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(m,E.current),M=-1!==k&&-1===x||D,C="boolean"==typeof s?{draggable:s,droppable:s}:s;(0,i.Es)(()=>{D&&w&&v(m)},[D,m,w,v]),(0,r.useEffect)(()=>{E.current=m},[m]);let S=(0,r.useMemo)(()=>({activeIndex:x,containerId:y,disabled:C,disableTransforms:M,items:m,overIndex:k,useDragOverlay:b,sortedRects:m.reduce((e,t,n)=>{let r=g.get(t);return r&&(e[n]=r),e},Array(m.length)),strategy:o}),[x,y,C.draggable,C.droppable,M,m,k,g,b,o]);return r.createElement(h.Provider,{value:S},t)}let g=e=>{let{id:t,items:n,activeIndex:r,overIndex:l}=e;return a(n,r,l).indexOf(t)},p=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:l,items:i,newIndex:a,previousItems:o,previousContainerId:u,transition:s}=e;return!!s&&!!r&&(o===i||l!==a)&&(!!n||a!==l&&t===u)},v={duration:200,easing:"ease"},y="transform",b=i.Ks.Transition.toString({property:y,duration:0,easing:"linear"}),m={roleDescription:"sortable"};function w(e){var t,n,a,u;let{animateLayoutChanges:s=p,attributes:c,disabled:d,data:f,getNewIndex:w=g,id:x,strategy:k,resizeObserverConfig:E,transition:D=v}=e,{items:M,containerId:C,activeIndex:S,disabled:R,disableTransforms:A,sortedRects:T,overIndex:L,useDragOverlay:O,strategy:z}=(0,r.useContext)(h),N=(t=d,n=R,"boolean"==typeof t?{draggable:t,droppable:!1}:{draggable:null!=(a=null==t?void 0:t.draggable)?a:n.draggable,droppable:null!=(u=null==t?void 0:t.droppable)?u:n.droppable}),I=M.indexOf(x),P=(0,r.useMemo)(()=>({sortable:{containerId:C,index:I,items:M},...f}),[C,f,I,M]),j=(0,r.useMemo)(()=>M.slice(M.indexOf(x)),[M,x]),{rect:W,node:Y,isOver:B,setNodeRef:K}=(0,l.zM)({id:x,data:P,disabled:N.droppable,resizeObserverConfig:{updateMeasurementsFor:j,...E}}),{active:F,activatorEvent:U,activeNodeRect:_,attributes:X,setNodeRef:G,listeners:q,isDragging:V,over:H,setActivatorNodeRef:Q,transform:Z}=(0,l.PM)({id:x,data:P,attributes:{...m,...c},disabled:N.draggable}),J=(0,i.jn)(K,G),$=!!F,ee=$&&!A&&o(S)&&o(L),et=!O&&V,en=et&&ee?Z:null,er=ee?null!=en?en:(null!=k?k:z)({rects:T,activeNodeRect:_,activeIndex:S,overIndex:L,index:I}):null,el=o(S)&&o(L)?w({id:x,items:M,activeIndex:S,overIndex:L}):I,ei=null==F?void 0:F.id,ea=(0,r.useRef)({activeId:ei,items:M,newIndex:el,containerId:C}),eo=M!==ea.current.items,eu=s({active:F,containerId:C,isDragging:V,isSorting:$,id:x,index:I,items:M,newIndex:ea.current.newIndex,previousItems:ea.current.items,previousContainerId:ea.current.containerId,transition:D,wasDragging:null!=ea.current.activeId}),es=function(e){let{disabled:t,index:n,node:a,rect:o}=e,[u,s]=(0,r.useState)(null),c=(0,r.useRef)(n);return(0,i.Es)(()=>{if(!t&&n!==c.current&&a.current){let e=o.current;if(e){let t=(0,l.Sj)(a.current,{ignoreTransform:!0}),n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(n.x||n.y)&&s(n)}}n!==c.current&&(c.current=n)},[t,n,a,o]),(0,r.useEffect)(()=>{u&&s(null)},[u]),u}({disabled:!eu,index:I,node:Y,rect:W});return(0,r.useEffect)(()=>{$&&ea.current.newIndex!==el&&(ea.current.newIndex=el),C!==ea.current.containerId&&(ea.current.containerId=C),M!==ea.current.items&&(ea.current.items=M)},[$,el,C,M]),(0,r.useEffect)(()=>{if(ei===ea.current.activeId)return;if(ei&&!ea.current.activeId){ea.current.activeId=ei;return}let e=setTimeout(()=>{ea.current.activeId=ei},50);return()=>clearTimeout(e)},[ei]),{active:F,activeIndex:S,attributes:X,data:P,rect:W,index:I,newIndex:el,items:M,isOver:B,isSorting:$,isDragging:V,listeners:q,node:Y,overIndex:L,over:H,setNodeRef:J,setActivatorNodeRef:Q,setDroppableNodeRef:K,setDraggableNodeRef:G,transform:null!=es?es:er,transition:es||eo&&ea.current.newIndex===I?b:(!et||(0,i.kx)(U))&&D&&($||eu)?i.Ks.Transition.toString({...D,property:y}):void 0}}l.vL.Down,l.vL.Right,l.vL.Up,l.vL.Left},69074:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},74126:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])},75143:(e,t,n)=>{let r;n.d(t,{Mp:()=>eN,vL:()=>o,uN:()=>en,AN:()=>eo,fp:()=>L,Sj:()=>P,fF:()=>eW,PM:()=>ej,zM:()=>eB,MS:()=>M,FR:()=>C});var l,i,a,o,u,s,c,d,h,f,g=n(12115),p=n(47650),v=n(78266);let y={display:"none"};function b(e){let{id:t,value:n}=e;return g.createElement("div",{id:t,style:y},n)}function m(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;return g.createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":r,"aria-atomic":!0},n)}let w=(0,g.createContext)(null),x={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},k={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function E(e){let{announcements:t=k,container:n,hiddenTextDescribedById:r,screenReaderInstructions:l=x}=e,{announce:i,announcement:a}=function(){let[e,t]=(0,g.useState)("");return{announce:(0,g.useCallback)(e=>{null!=e&&t(e)},[]),announcement:e}}(),o=(0,v.YG)("DndLiveRegion"),[u,s]=(0,g.useState)(!1);(0,g.useEffect)(()=>{s(!0)},[]);var c=(0,g.useMemo)(()=>({onDragStart(e){let{active:n}=e;i(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:r}=e;t.onDragMove&&i(t.onDragMove({active:n,over:r}))},onDragOver(e){let{active:n,over:r}=e;i(t.onDragOver({active:n,over:r}))},onDragEnd(e){let{active:n,over:r}=e;i(t.onDragEnd({active:n,over:r}))},onDragCancel(e){let{active:n,over:r}=e;i(t.onDragCancel({active:n,over:r}))}}),[i,t]);let d=(0,g.useContext)(w);if((0,g.useEffect)(()=>{if(!d)throw Error("useDndMonitor must be used within a children of <DndContext>");return d(c)},[c,d]),!u)return null;let h=g.createElement(g.Fragment,null,g.createElement(b,{id:r,value:l.draggable}),g.createElement(m,{id:o,announcement:a}));return n?(0,p.createPortal)(h,n):h}function D(){}function M(e,t){return(0,g.useMemo)(()=>({sensor:e,options:null!=t?t:{}}),[e,t])}function C(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,g.useMemo)(()=>[...t].filter(e=>null!=e),[...t])}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(l||(l={}));let S=Object.freeze({x:0,y:0});function R(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return n-r}function A(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function T(e,t,n){return void 0===t&&(t=e.left),void 0===n&&(n=e.top),{x:t+.5*e.width,y:n+.5*e.height}}let L=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=T(t,t.left,t.top),i=[];for(let e of r){let{id:t}=e,r=n.get(t);if(r){let n=function(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}(T(r),l);i.push({id:t,data:{droppableContainer:e,value:n}})}}return i.sort(R)},O=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=[];for(let e of r){let{id:r}=e,i=n.get(r);if(i){let n=function(e,t){let n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),l=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height);if(r<l&&n<i){let a=t.width*t.height,o=e.width*e.height,u=(l-r)*(i-n);return Number((u/(a+o-u)).toFixed(4))}return 0}(i,t);n>0&&l.push({id:r,data:{droppableContainer:e,value:n}})}}return l.sort(A)};function z(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:S}let N=function(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];return r.reduce((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x}),{...t})}}(1),I={ignoreTransform:!1};function P(e,t){void 0===t&&(t=I);let n=e.getBoundingClientRect();if(t.ignoreTransform){let{transform:t,transformOrigin:r}=(0,v.zk)(e).getComputedStyle(e);t&&(n=function(e,t,n){let r=function(e){if(e.startsWith("matrix3d(")){let t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){let t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}(t);if(!r)return e;let{scaleX:l,scaleY:i,x:a,y:o}=r,u=e.left-a-(1-l)*parseFloat(n),s=e.top-o-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),c=l?e.width/l:e.width,d=i?e.height/i:e.height;return{width:c,height:d,top:s,right:u+c,bottom:s+d,left:u}}(n,t,r))}let{top:r,left:l,width:i,height:a,bottom:o,right:u}=n;return{top:r,left:l,width:i,height:a,bottom:o,right:u}}function j(e){return P(e,{ignoreTransform:!0})}function W(e,t){let n=[];return e?function r(l){var i;if(null!=t&&n.length>=t||!l)return n;if((0,v.wz)(l)&&null!=l.scrollingElement&&!n.includes(l.scrollingElement))return n.push(l.scrollingElement),n;if(!(0,v.sb)(l)||(0,v.xZ)(l)||n.includes(l))return n;let a=(0,v.zk)(e).getComputedStyle(l);return(l!==e&&function(e,t){void 0===t&&(t=(0,v.zk)(e).getComputedStyle(e));let n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(e=>{let r=t[e];return"string"==typeof r&&n.test(r)})}(l,a)&&n.push(l),void 0===(i=a)&&(i=(0,v.zk)(l).getComputedStyle(l)),"fixed"===i.position)?n:r(l.parentNode)}(e):n}function Y(e){let[t]=W(e,1);return null!=t?t:null}function B(e){return v.Sw&&e?(0,v.l6)(e)?e:(0,v.Ll)(e)?(0,v.wz)(e)||e===(0,v.TW)(e).scrollingElement?window:(0,v.sb)(e)?e:null:null:null}function K(e){return(0,v.l6)(e)?e.scrollX:e.scrollLeft}function F(e){return(0,v.l6)(e)?e.scrollY:e.scrollTop}function U(e){return{x:K(e),y:F(e)}}function _(e){return!!v.Sw&&!!e&&e===document.scrollingElement}function X(e){let t={x:0,y:0},n=_(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height},l=e.scrollTop<=t.y,i=e.scrollLeft<=t.x;return{isTop:l,isLeft:i,isBottom:e.scrollTop>=r.y,isRight:e.scrollLeft>=r.x,maxScroll:r,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(i||(i={}));let G={x:.2,y:.2};function q(e){return e.reduce((e,t)=>(0,v.WQ)(e,U(t)),S)}let V=[["x",["left","right"],function(e){return e.reduce((e,t)=>e+K(t),0)}],["y",["top","bottom"],function(e){return e.reduce((e,t)=>e+F(t),0)}]];class H{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;let n=W(t),r=q(n);for(let[t,l,i]of(this.rect={...e},this.width=e.width,this.height=e.height,V))for(let e of l)Object.defineProperty(this,e,{get:()=>{let l=i(n),a=r[t]-l;return this.rect[e]+a},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class Q{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)})},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function Z(e,t){let n=Math.abs(e.x),r=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}function J(e){e.preventDefault()}function $(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(a||(a={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"}(o||(o={}));let ee={start:[o.Space,o.Enter],cancel:[o.Esc],end:[o.Space,o.Enter,o.Tab]},et=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case o.Right:return{...n,x:n.x+25};case o.Left:return{...n,x:n.x-25};case o.Down:return{...n,y:n.y+25};case o.Up:return{...n,y:n.y-25}}};class en{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;let{event:{target:t}}=e;this.props=e,this.listeners=new Q((0,v.TW)(t)),this.windowListeners=new Q((0,v.zk)(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(a.Resize,this.handleCancel),this.windowListeners.add(a.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(a.Keydown,this.handleKeyDown))}handleStart(){let{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&function(e,t){if(void 0===t&&(t=P),!e)return;let{top:n,left:r,bottom:l,right:i}=t(e);Y(e)&&(l<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}(n),t(S)}handleKeyDown(e){if((0,v.kx)(e)){let{active:t,context:n,options:r}=this.props,{keyboardCodes:l=ee,coordinateGetter:i=et,scrollBehavior:a="smooth"}=r,{code:u}=e;if(l.end.includes(u))return void this.handleEnd(e);if(l.cancel.includes(u))return void this.handleCancel(e);let{collisionRect:s}=n.current,c=s?{x:s.left,y:s.top}:S;this.referenceCoordinates||(this.referenceCoordinates=c);let d=i(e,{active:t,context:n.current,currentCoordinates:c});if(d){let t=(0,v.Re)(d,c),r={x:0,y:0},{scrollableAncestors:l}=n.current;for(let n of l){let l=e.code,{isTop:i,isRight:u,isLeft:s,isBottom:c,maxScroll:h,minScroll:f}=X(n),g=function(e){if(e===document.scrollingElement){let{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}let{top:t,left:n,right:r,bottom:l}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:l,width:e.clientWidth,height:e.clientHeight}}(n),p={x:Math.min(l===o.Right?g.right-g.width/2:g.right,Math.max(l===o.Right?g.left:g.left+g.width/2,d.x)),y:Math.min(l===o.Down?g.bottom-g.height/2:g.bottom,Math.max(l===o.Down?g.top:g.top+g.height/2,d.y))},v=l===o.Right&&!u||l===o.Left&&!s,y=l===o.Down&&!c||l===o.Up&&!i;if(v&&p.x!==d.x){let e=n.scrollLeft+t.x,i=l===o.Right&&e<=h.x||l===o.Left&&e>=f.x;if(i&&!t.y)return void n.scrollTo({left:e,behavior:a});i?r.x=n.scrollLeft-e:r.x=l===o.Right?n.scrollLeft-h.x:n.scrollLeft-f.x,r.x&&n.scrollBy({left:-r.x,behavior:a});break}if(y&&p.y!==d.y){let e=n.scrollTop+t.y,i=l===o.Down&&e<=h.y||l===o.Up&&e>=f.y;if(i&&!t.x)return void n.scrollTo({top:e,behavior:a});i?r.y=n.scrollTop-e:r.y=l===o.Down?n.scrollTop-h.y:n.scrollTop-f.y,r.y&&n.scrollBy({top:-r.y,behavior:a});break}}this.handleMove(e,(0,v.WQ)((0,v.Re)(d,this.referenceCoordinates),r))}}}handleMove(e,t){let{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){let{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){let{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function er(e){return!!(e&&"distance"in e)}function el(e){return!!(e&&"delay"in e)}en.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=ee,onActivation:l}=t,{active:i}=n,{code:a}=e.nativeEvent;if(r.start.includes(a)){let t=i.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==l||l({event:e.nativeEvent}),!0)}return!1}}];class ei{constructor(e,t,n){var r;void 0===n&&(n=function(e){let{EventTarget:t}=(0,v.zk)(e);return e instanceof t?e:(0,v.TW)(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;let{event:l}=e,{target:i}=l;this.props=e,this.events=t,this.document=(0,v.TW)(i),this.documentListeners=new Q(this.document),this.listeners=new Q(n),this.windowListeners=new Q((0,v.zk)(i)),this.initialCoordinates=null!=(r=(0,v.e_)(l))?r:S,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){let{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(a.Resize,this.handleCancel),this.windowListeners.add(a.DragStart,J),this.windowListeners.add(a.VisibilityChange,this.handleCancel),this.windowListeners.add(a.ContextMenu,J),this.documentListeners.add(a.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(el(t)){this.timeoutId=setTimeout(this.handleStart,t.delay),this.handlePending(t);return}if(er(t))return void this.handlePending(t)}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){let{active:n,onPending:r}=this.props;r(n,e,this.initialCoordinates,t)}handleStart(){let{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(a.Click,$,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(a.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;let{activated:n,initialCoordinates:r,props:l}=this,{onMove:i,options:{activationConstraint:a}}=l;if(!r)return;let o=null!=(t=(0,v.e_)(e))?t:S,u=(0,v.Re)(r,o);if(!n&&a){if(er(a)){if(null!=a.tolerance&&Z(u,a.tolerance))return this.handleCancel();if(Z(u,a.distance))return this.handleStart()}return el(a)&&Z(u,a.tolerance)?this.handleCancel():void this.handlePending(a,u)}e.cancelable&&e.preventDefault(),i(o)}handleEnd(){let{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){let{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===o.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}let ea={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class eo extends ei{constructor(e){let{event:t}=e;super(e,ea,(0,v.TW)(t.target))}}eo.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!!n.isPrimary&&0===n.button&&(null==r||r({event:n}),!0)}}];let eu={move:{name:"mousemove"},end:{name:"mouseup"}};!function(e){e[e.RightClick=2]="RightClick"}(u||(u={}));class es extends ei{constructor(e){super(e,eu,(0,v.TW)(e.event.target))}}es.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==u.RightClick&&(null==r||r({event:n}),!0)}}];let ec={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class ed extends ei{constructor(e){super(e,ec)}static setup(){return window.addEventListener(ec.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(ec.move.name,e)};function e(){}}}ed.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t,{touches:l}=n;return!(l.length>1)&&(null==r||r({event:n}),!0)}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(s||(s={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(c||(c={}));let eh={x:{[i.Backward]:!1,[i.Forward]:!1},y:{[i.Backward]:!1,[i.Forward]:!1}};!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(d||(d={})),(h||(h={})).Optimized="optimized";let ef=new Map;function eg(e,t){return(0,v.KG)(n=>e?n||("function"==typeof t?t(e):e):null,[t,e])}function ep(e){let{callback:t,disabled:n}=e,r=(0,v._q)(t),l=(0,g.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.ResizeObserver)return;let{ResizeObserver:e}=window;return new e(r)},[n]);return(0,g.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}function ev(e){return new H(P(e),e)}function ey(e,t,n){void 0===t&&(t=ev);let[r,l]=(0,g.useState)(null);function i(){l(r=>{if(!e)return null;if(!1===e.isConnected){var l;return null!=(l=null!=r?r:n)?l:null}let i=t(e);return JSON.stringify(r)===JSON.stringify(i)?r:i})}let a=function(e){let{callback:t,disabled:n}=e,r=(0,v._q)(t),l=(0,g.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.MutationObserver)return;let{MutationObserver:e}=window;return new e(r)},[r,n]);return(0,g.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}({callback(t){if(e)for(let n of t){let{type:t,target:r}=n;if("childList"===t&&r instanceof HTMLElement&&r.contains(e)){i();break}}}}),o=ep({callback:i});return(0,v.Es)(()=>{i(),e?(null==o||o.observe(e),null==a||a.observe(document.body,{childList:!0,subtree:!0})):(null==o||o.disconnect(),null==a||a.disconnect())},[e]),r}let eb=[];function em(e,t){void 0===t&&(t=[]);let n=(0,g.useRef)(null);return(0,g.useEffect)(()=>{n.current=null},t),(0,g.useEffect)(()=>{let t=e!==S;t&&!n.current&&(n.current=e),!t&&n.current&&(n.current=null)},[e]),n.current?(0,v.Re)(e,n.current):S}function ew(e){return(0,g.useMemo)(()=>e?function(e){let t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(e):null,[e])}let ex=[],ek=[{sensor:eo,options:{}},{sensor:en,options:{}}],eE={current:{}},eD={draggable:{measure:j},droppable:{measure:j,strategy:d.WhileDragging,frequency:h.Optimized},dragOverlay:{measure:P}};class eM extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(e=>{let{disabled:t}=e;return!t})}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}let eC={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new eM,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:D},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:eD,measureDroppableContainers:D,windowRect:null,measuringScheduled:!1},eS={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:D,draggableNodes:new Map,over:null,measureDroppableContainers:D},eR=(0,g.createContext)(eS),eA=(0,g.createContext)(eC);function eT(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new eM}}}function eL(e,t){switch(t.type){case l.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case l.DragMove:if(null==e.draggable.active)return e;return{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case l.DragEnd:case l.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case l.RegisterDroppable:{let{element:n}=t,{id:r}=n,l=new eM(e.droppable.containers);return l.set(r,n),{...e,droppable:{...e.droppable,containers:l}}}case l.SetDroppableDisabled:{let{id:n,key:r,disabled:l}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;let a=new eM(e.droppable.containers);return a.set(n,{...i,disabled:l}),{...e,droppable:{...e.droppable,containers:a}}}case l.UnregisterDroppable:{let{id:n,key:r}=t,l=e.droppable.containers.get(n);if(!l||r!==l.key)return e;let i=new eM(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function eO(e){let{disabled:t}=e,{active:n,activatorEvent:r,draggableNodes:l}=(0,g.useContext)(eR),i=(0,v.ZC)(r),a=(0,v.ZC)(null==n?void 0:n.id);return(0,g.useEffect)(()=>{if(!t&&!r&&i&&null!=a){if(!(0,v.kx)(i)||document.activeElement===i.target)return;let e=l.get(a);if(!e)return;let{activatorNode:t,node:n}=e;(t.current||n.current)&&requestAnimationFrame(()=>{for(let e of[t.current,n.current]){if(!e)continue;let t=(0,v.ag)(e);if(t){t.focus();break}}})}},[r,t,l,a,i]),null}let ez=(0,g.createContext)({...S,scaleX:1,scaleY:1});!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(f||(f={}));let eN=(0,g.memo)(function(e){var t,n,r,a,o,u;let{id:h,accessibility:y,autoScroll:b=!0,children:m,sensors:x=ek,collisionDetection:k=O,measuring:D,modifiers:M,...C}=e,[R,A]=(0,g.useReducer)(eL,void 0,eT),[T,L]=function(){let[e]=(0,g.useState)(()=>new Set),t=(0,g.useCallback)(t=>(e.add(t),()=>e.delete(t)),[e]);return[(0,g.useCallback)(t=>{let{type:n,event:r}=t;e.forEach(e=>{var t;return null==(t=e[n])?void 0:t.call(e,r)})},[e]),t]}(),[I,j]=(0,g.useState)(f.Uninitialized),K=I===f.Initialized,{draggable:{active:F,nodes:V,translate:Q},droppable:{containers:Z}}=R,J=null!=F?V.get(F):null,$=(0,g.useRef)({initial:null,translated:null}),ee=(0,g.useMemo)(()=>{var e;return null!=F?{id:F,data:null!=(e=null==J?void 0:J.data)?e:eE,rect:$}:null},[F,J]),et=(0,g.useRef)(null),[en,er]=(0,g.useState)(null),[el,ei]=(0,g.useState)(null),ea=(0,v.YN)(C,Object.values(C)),eo=(0,v.YG)("DndDescribedBy",h),eu=(0,g.useMemo)(()=>Z.getEnabled(),[Z]),es=(0,g.useMemo)(()=>({draggable:{...eD.draggable,...null==D?void 0:D.draggable},droppable:{...eD.droppable,...null==D?void 0:D.droppable},dragOverlay:{...eD.dragOverlay,...null==D?void 0:D.dragOverlay}}),[null==D?void 0:D.draggable,null==D?void 0:D.droppable,null==D?void 0:D.dragOverlay]),{droppableRects:ec,measureDroppableContainers:ed,measuringScheduled:ev}=function(e,t){let{dragging:n,dependencies:r,config:l}=t,[i,a]=(0,g.useState)(null),{frequency:o,measure:u,strategy:s}=l,c=(0,g.useRef)(e),h=function(){switch(s){case d.Always:return!1;case d.BeforeDragging:return n;default:return!n}}(),f=(0,v.YN)(h),p=(0,g.useCallback)(function(e){void 0===e&&(e=[]),f.current||a(t=>null===t?e:t.concat(e.filter(e=>!t.includes(e))))},[f]),y=(0,g.useRef)(null),b=(0,v.KG)(t=>{if(h&&!n)return ef;if(!t||t===ef||c.current!==e||null!=i){let t=new Map;for(let n of e){if(!n)continue;if(i&&i.length>0&&!i.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}let e=n.node.current,r=e?new H(u(e),e):null;n.rect.current=r,r&&t.set(n.id,r)}return t}return t},[e,i,n,h,u]);return(0,g.useEffect)(()=>{c.current=e},[e]),(0,g.useEffect)(()=>{h||p()},[n,h]),(0,g.useEffect)(()=>{i&&i.length>0&&a(null)},[JSON.stringify(i)]),(0,g.useEffect)(()=>{h||"number"!=typeof o||null!==y.current||(y.current=setTimeout(()=>{p(),y.current=null},o))},[o,h,p,...r]),{droppableRects:b,measureDroppableContainers:p,measuringScheduled:null!=i}}(eu,{dragging:K,dependencies:[Q.x,Q.y],config:es.droppable}),eM=function(e,t){let n=null!=t?e.get(t):void 0,r=n?n.node.current:null;return(0,v.KG)(e=>{var n;return null==t?null:null!=(n=null!=r?r:e)?n:null},[r,t])}(V,F),eC=(0,g.useMemo)(()=>el?(0,v.e_)(el):null,[el]),eS=function(){let e=(null==en?void 0:en.autoScrollEnabled)===!1,t="object"==typeof b?!1===b.enabled:!1===b,n=K&&!e&&!t;return"object"==typeof b?{...b,enabled:n}:{enabled:n}}(),eN=eg(eM,es.draggable.measure);!function(e){let{activeNode:t,measure:n,initialRect:r,config:l=!0}=e,i=(0,g.useRef)(!1),{x:a,y:o}="boolean"==typeof l?{x:l,y:l}:l;(0,v.Es)(()=>{if(!a&&!o||!t){i.current=!1;return}if(i.current||!r)return;let e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;let l=z(n(e),r);if(a||(l.x=0),o||(l.y=0),i.current=!0,Math.abs(l.x)>0||Math.abs(l.y)>0){let t=Y(e);t&&t.scrollBy({top:l.y,left:l.x})}},[t,a,o,r,n])}({activeNode:null!=F?V.get(F):null,config:eS.layoutShiftCompensation,initialRect:eN,measure:es.draggable.measure});let eI=ey(eM,es.draggable.measure,eN),eP=ey(eM?eM.parentElement:null),ej=(0,g.useRef)({activatorEvent:null,active:null,activeNode:eM,collisionRect:null,collisions:null,droppableRects:ec,draggableNodes:V,draggingNode:null,draggingNodeRect:null,droppableContainers:Z,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),eW=Z.getNodeFor(null==(t=ej.current.over)?void 0:t.id),eY=function(e){let{measure:t}=e,[n,r]=(0,g.useState)(null),l=ep({callback:(0,g.useCallback)(e=>{for(let{target:n}of e)if((0,v.sb)(n)){r(e=>{let r=t(n);return e?{...e,width:r.width,height:r.height}:r});break}},[t])}),i=(0,g.useCallback)(e=>{let n=function(e){if(!e)return null;if(e.children.length>1)return e;let t=e.children[0];return(0,v.sb)(t)?t:e}(e);null==l||l.disconnect(),n&&(null==l||l.observe(n)),r(n?t(n):null)},[t,l]),[a,o]=(0,v.lk)(i);return(0,g.useMemo)(()=>({nodeRef:a,rect:n,setRef:o}),[n,a,o])}({measure:es.dragOverlay.measure}),eB=null!=(n=eY.nodeRef.current)?n:eM,eK=K?null!=(r=eY.rect)?r:eI:null,eF=!!(eY.nodeRef.current&&eY.rect),eU=function(e){let t=eg(e);return z(e,t)}(eF?null:eI),e_=ew(eB?(0,v.zk)(eB):null),eX=function(e){let t=(0,g.useRef)(e),n=(0,v.KG)(n=>e?n&&n!==eb&&e&&t.current&&e.parentNode===t.current.parentNode?n:W(e):eb,[e]);return(0,g.useEffect)(()=>{t.current=e},[e]),n}(K?null!=eW?eW:eM:null),eG=function(e,t){void 0===t&&(t=P);let[n]=e,r=ew(n?(0,v.zk)(n):null),[l,i]=(0,g.useState)(ex);function a(){i(()=>e.length?e.map(e=>_(e)?r:new H(t(e),e)):ex)}let o=ep({callback:a});return(0,v.Es)(()=>{null==o||o.disconnect(),a(),e.forEach(e=>null==o?void 0:o.observe(e))},[e]),l}(eX),eq=function(e,t){let{transform:n,...r}=t;return null!=e&&e.length?e.reduce((e,t)=>t({transform:e,...r}),n):n}(M,{transform:{x:Q.x-eU.x,y:Q.y-eU.y,scaleX:1,scaleY:1},activatorEvent:el,active:ee,activeNodeRect:eI,containerNodeRect:eP,draggingNodeRect:eK,over:ej.current.over,overlayNodeRect:eY.rect,scrollableAncestors:eX,scrollableAncestorRects:eG,windowRect:e_}),eV=eC?(0,v.WQ)(eC,Q):null,eH=function(e){let[t,n]=(0,g.useState)(null),r=(0,g.useRef)(e),l=(0,g.useCallback)(e=>{let t=B(e.target);t&&n(e=>e?(e.set(t,U(t)),new Map(e)):null)},[]);return(0,g.useEffect)(()=>{let t=r.current;if(e!==t){i(t);let a=e.map(e=>{let t=B(e);return t?(t.addEventListener("scroll",l,{passive:!0}),[t,U(t)]):null}).filter(e=>null!=e);n(a.length?new Map(a):null),r.current=e}return()=>{i(e),i(t)};function i(e){e.forEach(e=>{let t=B(e);null==t||t.removeEventListener("scroll",l)})}},[l,e]),(0,g.useMemo)(()=>e.length?t?Array.from(t.values()).reduce((e,t)=>(0,v.WQ)(e,t),S):q(e):S,[e,t])}(eX),eQ=em(eH),eZ=em(eH,[eI]),eJ=(0,v.WQ)(eq,eQ),e$=eK?N(eK,eq):null,e0=ee&&e$?k({active:ee,collisionRect:e$,droppableRects:ec,droppableContainers:eu,pointerCoordinates:eV}):null,e1=function(e,t){if(!e||0===e.length)return null;let[n]=e;return n.id}(e0,"id"),[e2,e4]=(0,g.useState)(null),e6=(o=eF?eq:(0,v.WQ)(eq,eZ),u=null!=(a=null==e2?void 0:e2.rect)?a:null,{...o,scaleX:u&&eI?u.width/eI.width:1,scaleY:u&&eI?u.height/eI.height:1}),e9=(0,g.useRef)(null),e3=(0,g.useCallback)((e,t)=>{let{sensor:n,options:r}=t;if(null==et.current)return;let i=V.get(et.current);if(!i)return;let a=e.nativeEvent,o=new n({active:et.current,activeNode:i,event:a,options:r,context:ej,onAbort(e){if(!V.get(e))return;let{onDragAbort:t}=ea.current,n={id:e};null==t||t(n),T({type:"onDragAbort",event:n})},onPending(e,t,n,r){if(!V.get(e))return;let{onDragPending:l}=ea.current,i={id:e,constraint:t,initialCoordinates:n,offset:r};null==l||l(i),T({type:"onDragPending",event:i})},onStart(e){let t=et.current;if(null==t)return;let n=V.get(t);if(!n)return;let{onDragStart:r}=ea.current,i={activatorEvent:a,active:{id:t,data:n.data,rect:$}};(0,p.unstable_batchedUpdates)(()=>{null==r||r(i),j(f.Initializing),A({type:l.DragStart,initialCoordinates:e,active:t}),T({type:"onDragStart",event:i}),er(e9.current),ei(a)})},onMove(e){A({type:l.DragMove,coordinates:e})},onEnd:u(l.DragEnd),onCancel:u(l.DragCancel)});function u(e){return async function(){let{active:t,collisions:n,over:r,scrollAdjustedTranslate:i}=ej.current,o=null;if(t&&i){let{cancelDrop:u}=ea.current;o={activatorEvent:a,active:t,collisions:n,delta:i,over:r},e===l.DragEnd&&"function"==typeof u&&await Promise.resolve(u(o))&&(e=l.DragCancel)}et.current=null,(0,p.unstable_batchedUpdates)(()=>{A({type:e}),j(f.Uninitialized),e4(null),er(null),ei(null),e9.current=null;let t=e===l.DragEnd?"onDragEnd":"onDragCancel";if(o){let e=ea.current[t];null==e||e(o),T({type:t,event:o})}})}}e9.current=o},[V]),e5=(0,g.useCallback)((e,t)=>(n,r)=>{let l=n.nativeEvent,i=V.get(r);null!==et.current||!i||l.dndKit||l.defaultPrevented||!0===e(n,t.options,{active:i})&&(l.dndKit={capturedBy:t.sensor},et.current=r,e3(n,t))},[V,e3]),e8=(0,g.useMemo)(()=>x.reduce((e,t)=>{let{sensor:n}=t;return[...e,...n.activators.map(e=>({eventName:e.eventName,handler:e5(e.handler,t)}))]},[]),[x,e5]);(0,g.useEffect)(()=>{if(!v.Sw)return;let e=x.map(e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()});return()=>{for(let t of e)null==t||t()}},x.map(e=>{let{sensor:t}=e;return t})),(0,v.Es)(()=>{eI&&I===f.Initializing&&j(f.Initialized)},[eI,I]),(0,g.useEffect)(()=>{let{onDragMove:e}=ea.current,{active:t,activatorEvent:n,collisions:r,over:l}=ej.current;if(!t||!n)return;let i={active:t,activatorEvent:n,collisions:r,delta:{x:eJ.x,y:eJ.y},over:l};(0,p.unstable_batchedUpdates)(()=>{null==e||e(i),T({type:"onDragMove",event:i})})},[eJ.x,eJ.y]),(0,g.useEffect)(()=>{let{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:l}=ej.current;if(!e||null==et.current||!t||!l)return;let{onDragOver:i}=ea.current,a=r.get(e1),o=a&&a.rect.current?{id:a.id,rect:a.rect.current,data:a.data,disabled:a.disabled}:null,u={active:e,activatorEvent:t,collisions:n,delta:{x:l.x,y:l.y},over:o};(0,p.unstable_batchedUpdates)(()=>{e4(o),null==i||i(u),T({type:"onDragOver",event:u})})},[e1]),(0,v.Es)(()=>{ej.current={activatorEvent:el,active:ee,activeNode:eM,collisionRect:e$,collisions:e0,droppableRects:ec,draggableNodes:V,draggingNode:eB,draggingNodeRect:eK,droppableContainers:Z,over:e2,scrollableAncestors:eX,scrollAdjustedTranslate:eJ},$.current={initial:eK,translated:e$}},[ee,eM,e0,e$,V,eB,eK,ec,Z,e2,eX,eJ]),function(e){let{acceleration:t,activator:n=s.Pointer,canScroll:r,draggingRect:l,enabled:a,interval:o=5,order:u=c.TreeOrder,pointerCoordinates:d,scrollableAncestors:h,scrollableAncestorRects:f,delta:p,threshold:y}=e,b=function(e){let{delta:t,disabled:n}=e,r=(0,v.ZC)(t);return(0,v.KG)(e=>{if(n||!r||!e)return eh;let l={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[i.Backward]:e.x[i.Backward]||-1===l.x,[i.Forward]:e.x[i.Forward]||1===l.x},y:{[i.Backward]:e.y[i.Backward]||-1===l.y,[i.Forward]:e.y[i.Forward]||1===l.y}}},[n,t,r])}({delta:p,disabled:!a}),[m,w]=(0,v.$$)(),x=(0,g.useRef)({x:0,y:0}),k=(0,g.useRef)({x:0,y:0}),E=(0,g.useMemo)(()=>{switch(n){case s.Pointer:return d?{top:d.y,bottom:d.y,left:d.x,right:d.x}:null;case s.DraggableRect:return l}},[n,l,d]),D=(0,g.useRef)(null),M=(0,g.useCallback)(()=>{let e=D.current;if(!e)return;let t=x.current.x*k.current.x,n=x.current.y*k.current.y;e.scrollBy(t,n)},[]),C=(0,g.useMemo)(()=>u===c.TreeOrder?[...h].reverse():h,[u,h]);(0,g.useEffect)(()=>{if(!a||!h.length||!E)return void w();for(let e of C){if((null==r?void 0:r(e))===!1)continue;let n=f[h.indexOf(e)];if(!n)continue;let{direction:l,speed:a}=function(e,t,n,r,l){let{top:a,left:o,right:u,bottom:s}=n;void 0===r&&(r=10),void 0===l&&(l=G);let{isTop:c,isBottom:d,isLeft:h,isRight:f}=X(e),g={x:0,y:0},p={x:0,y:0},v={height:t.height*l.y,width:t.width*l.x};return!c&&a<=t.top+v.height?(g.y=i.Backward,p.y=r*Math.abs((t.top+v.height-a)/v.height)):!d&&s>=t.bottom-v.height&&(g.y=i.Forward,p.y=r*Math.abs((t.bottom-v.height-s)/v.height)),!f&&u>=t.right-v.width?(g.x=i.Forward,p.x=r*Math.abs((t.right-v.width-u)/v.width)):!h&&o<=t.left+v.width&&(g.x=i.Backward,p.x=r*Math.abs((t.left+v.width-o)/v.width)),{direction:g,speed:p}}(e,n,E,t,y);for(let e of["x","y"])b[e][l[e]]||(a[e]=0,l[e]=0);if(a.x>0||a.y>0){w(),D.current=e,m(M,o),x.current=a,k.current=l;return}}x.current={x:0,y:0},k.current={x:0,y:0},w()},[t,M,r,w,a,o,JSON.stringify(E),JSON.stringify(b),m,h,C,f,JSON.stringify(y)])}({...eS,delta:Q,draggingRect:e$,pointerCoordinates:eV,scrollableAncestors:eX,scrollableAncestorRects:eG});let e7=(0,g.useMemo)(()=>({active:ee,activeNode:eM,activeNodeRect:eI,activatorEvent:el,collisions:e0,containerNodeRect:eP,dragOverlay:eY,draggableNodes:V,droppableContainers:Z,droppableRects:ec,over:e2,measureDroppableContainers:ed,scrollableAncestors:eX,scrollableAncestorRects:eG,measuringConfiguration:es,measuringScheduled:ev,windowRect:e_}),[ee,eM,eI,el,e0,eP,eY,V,Z,ec,e2,ed,eX,eG,es,ev,e_]),te=(0,g.useMemo)(()=>({activatorEvent:el,activators:e8,active:ee,activeNodeRect:eI,ariaDescribedById:{draggable:eo},dispatch:A,draggableNodes:V,over:e2,measureDroppableContainers:ed}),[el,e8,ee,eI,A,eo,V,e2,ed]);return g.createElement(w.Provider,{value:L},g.createElement(eR.Provider,{value:te},g.createElement(eA.Provider,{value:e7},g.createElement(ez.Provider,{value:e6},m)),g.createElement(eO,{disabled:(null==y?void 0:y.restoreFocus)===!1})),g.createElement(E,{...y,hiddenTextDescribedById:eo}))}),eI=(0,g.createContext)(null),eP="button";function ej(e){let{id:t,data:n,disabled:r=!1,attributes:l}=e,i=(0,v.YG)("Draggable"),{activators:a,activatorEvent:o,active:u,activeNodeRect:s,ariaDescribedById:c,draggableNodes:d,over:h}=(0,g.useContext)(eR),{role:f=eP,roleDescription:p="draggable",tabIndex:y=0}=null!=l?l:{},b=(null==u?void 0:u.id)===t,m=(0,g.useContext)(b?ez:eI),[w,x]=(0,v.lk)(),[k,E]=(0,v.lk)(),D=(0,g.useMemo)(()=>a.reduce((e,n)=>{let{eventName:r,handler:l}=n;return e[r]=e=>{l(e,t)},e},{}),[a,t]),M=(0,v.YN)(n);return(0,v.Es)(()=>(d.set(t,{id:t,key:i,node:w,activatorNode:k,data:M}),()=>{let e=d.get(t);e&&e.key===i&&d.delete(t)}),[d,t]),{active:u,activatorEvent:o,activeNodeRect:s,attributes:(0,g.useMemo)(()=>({role:f,tabIndex:y,"aria-disabled":r,"aria-pressed":!!b&&f===eP||void 0,"aria-roledescription":p,"aria-describedby":c.draggable}),[r,f,y,b,p,c.draggable]),isDragging:b,listeners:r?void 0:D,node:w,over:h,setNodeRef:x,setActivatorNodeRef:E,transform:m}}function eW(){return(0,g.useContext)(eA)}let eY={timeout:25};function eB(e){let{data:t,disabled:n=!1,id:r,resizeObserverConfig:i}=e,a=(0,v.YG)("Droppable"),{active:o,dispatch:u,over:s,measureDroppableContainers:c}=(0,g.useContext)(eR),d=(0,g.useRef)({disabled:n}),h=(0,g.useRef)(!1),f=(0,g.useRef)(null),p=(0,g.useRef)(null),{disabled:y,updateMeasurementsFor:b,timeout:m}={...eY,...i},w=(0,v.YN)(null!=b?b:r),x=ep({callback:(0,g.useCallback)(()=>{if(!h.current){h.current=!0;return}null!=p.current&&clearTimeout(p.current),p.current=setTimeout(()=>{c(Array.isArray(w.current)?w.current:[w.current]),p.current=null},m)},[m]),disabled:y||!o}),k=(0,g.useCallback)((e,t)=>{x&&(t&&(x.unobserve(t),h.current=!1),e&&x.observe(e))},[x]),[E,D]=(0,v.lk)(k),M=(0,v.YN)(t);return(0,g.useEffect)(()=>{x&&E.current&&(x.disconnect(),h.current=!1,x.observe(E.current))},[E,x]),(0,g.useEffect)(()=>(u({type:l.RegisterDroppable,element:{id:r,key:a,disabled:n,node:E,rect:f,data:M}}),()=>u({type:l.UnregisterDroppable,key:a,id:r})),[r]),(0,g.useEffect)(()=>{n!==d.current.disabled&&(u({type:l.SetDroppableDisabled,id:r,key:a,disabled:n}),d.current.disabled=n)},[r,a,n,u]),{active:o,rect:f,isOver:(null==s?void 0:s.id)===r,node:E,over:s,setNodeRef:D}}r={styles:{active:{opacity:"0"}}},e=>{let{active:t,dragOverlay:n}=e,l={},{styles:i,className:a}=r;if(null!=i&&i.active)for(let[e,n]of Object.entries(i.active))void 0!==n&&(l[e]=t.node.style.getPropertyValue(e),t.node.style.setProperty(e,n));if(null!=i&&i.dragOverlay)for(let[e,t]of Object.entries(i.dragOverlay))void 0!==t&&n.node.style.setProperty(e,t);return null!=a&&a.active&&t.node.classList.add(a.active),null!=a&&a.dragOverlay&&n.node.classList.add(a.dragOverlay),function(){for(let[e,n]of Object.entries(l))t.node.style.setProperty(e,n);null!=a&&a.active&&t.node.classList.remove(a.active)}}},78266:(e,t,n)=>{n.d(t,{$$:()=>p,Es:()=>f,KG:()=>y,Ks:()=>S,Ll:()=>o,Re:()=>D,Sw:()=>i,TW:()=>h,WQ:()=>E,YG:()=>x,YN:()=>v,ZC:()=>m,_q:()=>g,ag:()=>A,e_:()=>C,jn:()=>l,kx:()=>M,l6:()=>a,lk:()=>b,sb:()=>c,wz:()=>s,xZ:()=>d,zk:()=>u});var r=n(12115);function l(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)(()=>e=>{t.forEach(t=>t(e))},t)}let i="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function a(e){let t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function o(e){return"nodeType"in e}function u(e){var t,n;return e?a(e)?e:o(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function s(e){let{Document:t}=u(e);return e instanceof t}function c(e){return!a(e)&&e instanceof u(e).HTMLElement}function d(e){return e instanceof u(e).SVGElement}function h(e){return e?a(e)?e.document:o(e)?s(e)?e:c(e)||d(e)?e.ownerDocument:document:document:document}let f=i?r.useLayoutEffect:r.useEffect;function g(e){let t=(0,r.useRef)(e);return f(()=>{t.current=e}),(0,r.useCallback)(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}function p(){let e=(0,r.useRef)(null);return[(0,r.useCallback)((t,n)=>{e.current=setInterval(t,n)},[]),(0,r.useCallback)(()=>{null!==e.current&&(clearInterval(e.current),e.current=null)},[])]}function v(e,t){void 0===t&&(t=[e]);let n=(0,r.useRef)(e);return f(()=>{n.current!==e&&(n.current=e)},t),n}function y(e,t){let n=(0,r.useRef)();return(0,r.useMemo)(()=>{let t=e(n.current);return n.current=t,t},[...t])}function b(e){let t=g(e),n=(0,r.useRef)(null),l=(0,r.useCallback)(e=>{e!==n.current&&(null==t||t(e,n.current)),n.current=e},[]);return[n,l]}function m(e){let t=(0,r.useRef)();return(0,r.useEffect)(()=>{t.current=e},[e]),t.current}let w={};function x(e,t){return(0,r.useMemo)(()=>{if(t)return t;let n=null==w[e]?0:w[e]+1;return w[e]=n,e+"-"+n},[e,t])}function k(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];return r.reduce((t,n)=>{for(let[r,l]of Object.entries(n)){let n=t[r];null!=n&&(t[r]=n+e*l)}return t},{...t})}}let E=k(1),D=k(-1);function M(e){if(!e)return!1;let{KeyboardEvent:t}=u(e.target);return t&&e instanceof t}function C(e){if(function(e){if(!e)return!1;let{TouchEvent:t}=u(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){let{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}else if(e.changedTouches&&e.changedTouches.length){let{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return"clientX"in e&&"clientY"in e?{x:e.clientX,y:e.clientY}:null}let S=Object.freeze({Translate:{toString(e){if(!e)return;let{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;let{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[S.Translate.toString(e),S.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),R="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function A(e){return e.matches(R)?e:e.querySelector(R)}},84616:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},89917:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])}}]);