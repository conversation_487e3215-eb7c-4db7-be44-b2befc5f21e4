(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5066],{5041:(e,t,s)=>{"use strict";s.d(t,{n:()=>d});var a=s(12115),r=s(34560),i=s(7165),n=s(25910),o=s(52020),l=class extends n.Q{#e;#t=void 0;#s;#a;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#r()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,o.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#s,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,o.EN)(t.mutationKey)!==(0,o.EN)(this.options.mutationKey)?this.reset():this.#s?.state.status==="pending"&&this.#s.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#s?.removeObserver(this)}onMutationUpdate(e){this.#r(),this.#i(e)}getCurrentResult(){return this.#t}reset(){this.#s?.removeObserver(this),this.#s=void 0,this.#r(),this.#i()}mutate(e,t){return this.#a=t,this.#s?.removeObserver(this),this.#s=this.#e.getMutationCache().build(this.#e,this.options),this.#s.addObserver(this),this.#s.execute(e)}#r(){let e=this.#s?.state??(0,r.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#i(e){i.jG.batch(()=>{if(this.#a&&this.hasListeners()){let t=this.#t.variables,s=this.#t.context;e?.type==="success"?(this.#a.onSuccess?.(e.data,t,s),this.#a.onSettled?.(e.data,null,t,s)):e?.type==="error"&&(this.#a.onError?.(e.error,t,s),this.#a.onSettled?.(void 0,e.error,t,s))}this.listeners.forEach(e=>{e(this.#t)})})}},c=s(26715),u=s(63768);function d(e,t){let s=(0,c.jE)(t),[r]=a.useState(()=>new l(s,e));a.useEffect(()=>{r.setOptions(e)},[r,e]);let n=a.useSyncExternalStore(a.useCallback(e=>r.subscribe(i.jG.batchCalls(e)),[r]),()=>r.getCurrentResult(),()=>r.getCurrentResult()),o=a.useCallback((e,t)=>{r.mutate(e,t).catch(u.l)},[r]);if(n.error&&(0,u.G)(r.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:o,mutateAsync:n.mutate}}},13163:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var a=s(95155),r=s(60760),i=s(44518),n=s(95233),o=s(54416);s(12115);let l=e=>{let{children:t,className:s,isOpen:l,onClose:c,preventOutsideClick:u=!1}=e;return(0,a.jsx)(r.N,{children:l&&(0,a.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40 p-4 overflow-y-auto",onClick:e=>{u||c()},children:(0,a.jsxs)(i.P.div,{initial:{scale:.6,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.6,opacity:0},transition:{duration:.3,ease:n.am},className:"relative section flex flex-col max-w-4xl max-h-[90vh] overflow-y-auto ".concat(s),onClick:e=>e.stopPropagation(),children:[(0,a.jsx)(o.A,{onClick:c,className:"absolute self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300"}),t]})})})}},25784:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let a=s(23464).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});a.interceptors.request.use(e=>e,e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let r=a},28883:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},29350:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var a=s(97381),r=s(59362),i=s(25784),n=s(35695),o=s(12115),l=s(34540);let c=e=>{let t=(0,l.wA)(),s=(0,n.useRouter)(),c=(0,n.usePathname)(),{status:u,user:d,error:h}=(0,l.d4)(e=>e.auth),m=async()=>{try{t((0,a.Le)());let e=(await i.A.get("/users/me")).data;t((0,a.tQ)(e))}catch(i){if(t((0,a.x9)()),(0,r.F0)(i)){var e,n,o,l,u;if(console.error("Auth error:",null==(e=i.response)?void 0:e.status,null==(n=i.response)?void 0:n.data),(null==(o=i.response)?void 0:o.status)===401){if(c.startsWith("/form-submission"))return;s.push("/")}else t((0,a.jB)((null==(u=i.response)||null==(l=u.data)?void 0:l.message)||i.message))}else t((0,a.jB)(i instanceof Error?i.message:"An unknown error occurred."))}};return(0,o.useEffect)(()=>{(null==e?void 0:e.skipFetchUser)||m()},[null==e?void 0:e.skipFetchUser]),(0,o.useEffect)(()=>{let e=e=>{if("logout"===e.key&&"true"===e.newValue)if(t((0,a.x9)()),c.startsWith("/form-submission")){let e=c.split("/")[2];e?s.push("/form-submission/".concat(e,"/sign-in")):s.push("/")}else s.push("/")};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[t,s,c]),{status:u,user:d,error:h,isAuthenticated:"authenticated"===u,isLoading:"loading"===u,refreshAuthState:()=>{m()},signin:async(e,t,s)=>{try{await i.A.post("/users/login",e),await m(),null==t||t()}catch(e){if(e instanceof r.pe){var a,n;let t=null==(n=e.response)||null==(a=n.data)?void 0:a.errorType;null==s||s(t)}else null==s||s()}},logout:async()=>{try{await i.A.post("/users/logout"),localStorage.setItem("logout","true"),setTimeout(()=>localStorage.removeItem("logout"),100)}finally{if(t((0,a.x9)()),c.startsWith("/form-submission")){let e=c.split("/")[2];e?s.push("/form-submission/".concat(e,"/sign-in")):s.push("/")}else s.push("/")}}}}},34560:(e,t,s)=>{"use strict";s.d(t,{$:()=>o,s:()=>n});var a=s(7165),r=s(57948),i=s(6784),n=class extends r.k{#n;#o;#l;constructor(e){super(),this.mutationId=e.mutationId,this.#o=e.mutationCache,this.#n=[],this.state=e.state||o(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#n.includes(e)||(this.#n.push(e),this.clearGcTimeout(),this.#o.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#n=this.#n.filter(t=>t!==e),this.scheduleGc(),this.#o.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#n.length||("pending"===this.state.status?this.scheduleGc():this.#o.remove(this))}continue(){return this.#l?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#c({type:"continue"})};this.#l=(0,i.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#c({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#c({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#o.canRun(this)});let s="pending"===this.state.status,a=!this.#l.canStart();try{if(s)t();else{this.#c({type:"pending",variables:e,isPaused:a}),await this.#o.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#c({type:"pending",context:t,variables:e,isPaused:a})}let r=await this.#l.start();return await this.#o.config.onSuccess?.(r,e,this.state.context,this),await this.options.onSuccess?.(r,e,this.state.context),await this.#o.config.onSettled?.(r,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(r,null,e,this.state.context),this.#c({type:"success",data:r}),r}catch(t){try{throw await this.#o.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#o.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#c({type:"error",error:t})}}finally{this.#o.runNext(this)}}#c(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),a.jG.batch(()=>{this.#n.forEach(t=>{t.onMutationUpdate(e)}),this.#o.notify({mutation:this,type:"updated",action:e})})}};function o(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},53999:(e,t,s)=>{"use strict";s.d(t,{Y:()=>n,cn:()=>i});var a=s(52596),r=s(39688);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"short";if(!e)return"";try{let s="string"==typeof e?new Date(e):e;if(isNaN(s.getTime()))return"";switch(t){case"short":return s.toLocaleDateString(void 0,{year:"numeric",month:"short",day:"numeric"});case"long":return s.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});case"full":return s.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",weekday:"long",hour:"2-digit",minute:"2-digit",second:"2-digit"});default:return s.toLocaleDateString()}}catch(t){return console.error("Error formatting date:",t),String(e)}}},54416:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},57799:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(95155);s(12115);let r=()=>(0,a.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,a.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},59362:(e,t,s)=>{"use strict";s.d(t,{F0:()=>d,pe:()=>r});let{Axios:a,AxiosError:r,CanceledError:i,isCancel:n,CancelToken:o,VERSION:l,all:c,Cancel:u,isAxiosError:d,spread:h,toFormData:m,AxiosHeaders:p,HttpStatusCode:x,formToJSON:f,getAdapter:y,mergeConfig:g}=s(23464).A},62334:(e,t,s)=>{"use strict";s.d(t,{BZ:()=>i,eg:()=>n,ep:()=>o,kH:()=>l,l2:()=>r});var a=s(25784);let r=async()=>{let{data:e}=await a.A.get("/users/profile");return e.profile},i=async e=>{let{email:t}=e,{data:s}=await a.A.patch("/users/change-email",{email:t});return s},n=async e=>{let{dataToSend:t}=e,{data:s}=await a.A.patch("/users/update",t);return s},o=async()=>{let{data:e}=await a.A.get("/users/sessions");return e.sessions},l=async e=>{let{data:t}=await a.A.post("/users/sendverificationemail",{email:e});return t}},63642:(e,t,s)=>{"use strict";s.d(t,{R:()=>i});var a=s(95155);s(12115);var r=s(13163);let i=e=>{let{showModal:t,onClose:s,onConfirm:i,title:n,description:o,confirmButtonText:l,cancelButtonText:c,confirmButtonClass:u,children:d}=e;return(0,a.jsxs)(r.A,{isOpen:t,onClose:s,className:"p-6 rounded-md max-w-xl",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:n}),(0,a.jsx)("div",{className:"text-neutral-700 mt-2",children:o}),d&&(0,a.jsx)("div",{className:"mt-6 space-y-4",children:d}),(0,a.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,a.jsx)("button",{className:"btn-outline",onClick:s,type:"button",children:c||"Cancel"}),(0,a.jsx)("button",{className:"font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ".concat(u),onClick:i,type:"button",children:l})]})]})}},71402:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>n,Ds:()=>r,_b:()=>i});let a=(0,s(51990).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,t)=>{e.message=t.payload.message,e.type=t.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:r,hideNotification:i}=a.actions,n=a.reducer},76051:(e,t,s)=>{Promise.resolve().then(s.bind(s,81853))},78749:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},81853:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>E});var a=s(95155),r=s(12115),i=s(19946);let n=(0,i.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),o=(0,i.A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var l=s(28883),c=s(71402),u=s(25784),d=s(78749),h=s(92657),m=s(62177),p=s(34540);let x=()=>{let[e,t]=(0,r.useState)(!1),[s,i]=(0,r.useState)(!1),[n,o]=(0,r.useState)(!1),[l,x]=(0,r.useState)(!1),f=(0,p.wA)(),{register:y,handleSubmit:g,formState:{errors:v},setValue:w,watch:b}=(0,m.mN)({defaultValues:{currentPassword:"",newPassword:"",confirmPassword:"",email:""}}),j=b("newPassword");b("confirmPassword");let N=async e=>{let{currentPassword:s,newPassword:a,confirmPassword:r}=e;if(a!==r)return void f((0,c.Ds)({message:"New password and confirm password do not match.",type:"error"}));try{let e=await u.A.post("/users/changepassword",{currentPassword:s,newPassword:a,confirmPassword:r});200===e.status&&(f((0,c.Ds)({message:"Password changed successfully",type:"success"})),t(!1),w("currentPassword",""),w("newPassword",""),w("confirmPassword",""))}catch(e){var i,n;f((0,c.Ds)({message:(null==(n=e.response)||null==(i=n.data)?void 0:i.message)||"Server error",type:"error"}))}};return(0,a.jsx)("div",{children:e?(0,a.jsxs)("form",{onSubmit:g(N),className:"flex-col flex gap-4",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsxs)("div",{className:"label-input-group group",children:[(0,a.jsx)("label",{htmlFor:"current-password",className:"label-text",children:"Current Password"}),(0,a.jsxs)("div",{className:"relative laptop:w-1/3",children:[(0,a.jsx)("input",{id:"current-password",type:s?"text":"password",placeholder:"Enter current password",className:"input-field w-full pr-10",...y("currentPassword",{required:"Current password is required"})}),(0,a.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>i(!s),children:[s?(0,a.jsx)(d.A,{className:"h-4 w-4"}):(0,a.jsx)(h.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{className:"sr-only",children:[s?"Hide":"Show"," password"]})]})]}),v.currentPassword&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:v.currentPassword.message})]}),(0,a.jsxs)("div",{className:"label-input-group group",children:[(0,a.jsx)("label",{htmlFor:"new-password",className:"label-text",children:"New Password"}),(0,a.jsxs)("div",{className:"relative laptop:w-1/3",children:[(0,a.jsx)("input",{id:"new-password",type:n?"text":"password",placeholder:"Enter new password",className:"input-field w-full pr-10",...y("newPassword",{required:"New password is required",minLength:{value:6,message:"Password must be at least 6 characters"},validate:e=>e!==b("currentPassword")||"New password must be different from current password"})}),(0,a.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>o(!n),children:[n?(0,a.jsx)(d.A,{className:"h-4 w-4"}):(0,a.jsx)(h.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{className:"sr-only",children:[n?"Hide":"Show"," password"]})]})]}),v.newPassword&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:v.newPassword.message})]}),(0,a.jsxs)("div",{className:"label-input-group group",children:[(0,a.jsx)("label",{htmlFor:"confirm-password",className:"label-text",children:"Confirm Password"}),(0,a.jsxs)("div",{className:"relative laptop:w-1/3",children:[(0,a.jsx)("input",{id:"confirm-password",type:l?"text":"password",placeholder:"Enter confirm password",className:"input-field w-full pr-10",...y("confirmPassword",{required:"Please confirm your password",validate:e=>e===j||"Passwords do not match"})}),(0,a.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>x(!l),children:[l?(0,a.jsx)(d.A,{className:"h-4 w-4"}):(0,a.jsx)(h.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{className:"sr-only",children:[l?"Hide":"Show"," password"]})]})]}),v.confirmPassword&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:v.confirmPassword.message})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{type:"submit",className:"btn-primary",children:"Update Password"}),(0,a.jsx)("button",{className:"btn-outline",onClick:()=>t(!1),children:"Cancel"})]})]}):(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsx)("button",{className:"btn-primary",onClick:()=>t(!0),children:"Change password"})})})};var f=s(36268),y=s(11032),g=s(88524);function v(e){var t;let{columns:s,data:i}=e,[n,o]=r.useState({pageIndex:0,pageSize:8}),l=(0,f.N4)({data:i,columns:s,onPaginationChange:o,getPaginationRowModel:(0,y.kW)(),getCoreRowModel:(0,y.HT)(),state:{pagination:n}});return(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"rounded-md border border-neutral-400 overflow-hidden",children:(0,a.jsxs)(g.XI,{children:[(0,a.jsx)(g.A0,{children:l.getHeaderGroups().map(e=>(0,a.jsx)(g.Hj,{className:"text-sm border-neutral-400",children:e.headers.map(e=>(0,a.jsx)(g.nd,{className:"py-1 px-6 text-left bg-primary-500 text-neutral-100 font-semibold",children:e.isPlaceholder?null:(0,f.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,a.jsx)(g.BF,{children:(null==(t=l.getRowModel().rows)?void 0:t.length)?l.getRowModel().rows.map(e=>(0,a.jsx)(g.Hj,{className:" text-sm border-neutral-400","data-state":e.getIsSelected()&&"selected",children:e.getVisibleCells().map(e=>(0,a.jsx)(g.nA,{className:"py-4 px-6",children:(0,f.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,a.jsx)(g.Hj,{children:(0,a.jsx)(g.nA,{colSpan:s.length,className:"h-24 text-center",children:"No results."})})})]})}),i.length>n.pageSize&&(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2 py-4",children:[(0,a.jsx)("button",{className:"btn-primary",onClick:()=>l.previousPage(),disabled:!l.getCanPreviousPage(),children:"Previous"}),(0,a.jsx)("button",{className:"btn-primary",onClick:()=>l.nextPage(),disabled:!l.getCanNextPage(),children:"Next"})]})]})}let w=[{accessorKey:"deviceInfo",header:"Device"},{accessorKey:"browserInfo",header:"Browser"},{accessorKey:"updatedAt",header:"Last Activity"},{accessorKey:"ipAddress",header:"IP Address"},{accessorKey:"isActive",header:"Status",cell:e=>{let{getValue:t}=e;return t()?"Active":"Inactive"}}];var b=s(29350),j=s(19373),N=s(26715),C=s(5041),A=s(62334),P=s(57799),k=s(63642),S=s(59362);let E=()=>{var e;let[t,s]=(0,r.useState)(!1),{user:i,logout:u}=(0,b.A)(),{register:d,formState:{errors:h},handleSubmit:f,getValues:y,reset:g,setError:E}=(0,m.mN)(),{data:M,isLoading:R,isError:O}=(0,j.I)({queryKey:["sessions",null==i?void 0:i.id],queryFn:A.ep,enabled:!!(null==i?void 0:i.id)}),D=(0,p.wA)(),I=(0,N.jE)(),[F,L]=(0,r.useState)(!1),K=(0,C.n)({mutationFn:A.BZ,onSuccess:async()=>{try{await I.invalidateQueries({queryKey:["profile",null==i?void 0:i.id]}),await (0,A.kH)(y("email")),s(!1),D((0,c.Ds)({message:"Email changed successfully. A verification email has been sent to your new address.",type:"success"})),u()}catch(e){D((0,c.Ds)({message:"Email changed, but we couldn't send the verification email. Please try again manually.",type:"warning"}))}},onError:e=>{if(e instanceof S.pe){var t,s;E(null==(t=e.response)?void 0:t.data.errorField,{message:null==(s=e.response)?void 0:s.data.message})}else D((0,c.Ds)({message:"Failed to change email. Please try again",type:"error"}))}});(0,r.useEffect)(()=>{t||g()},[t,g]);let[H,T]=(0,r.useState)(!1);(0,r.useEffect)(()=>{T(!0)},[]);let U=async e=>{K.mutate({email:e.email})};return H?R?(0,a.jsx)(P.A,{}):O||!M?(0,a.jsx)("p",{className:"text-sm text-red-500",children:"Error loading data"}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(k.R,{showModal:F,onClose:()=>L(!1),onConfirm:()=>{f(U)(),L(!1)},title:"Confirm email change?",description:"Changing email will log you out and you will need to verify your new email before logging in. The verification email will be sent to your new email address.",confirmButtonText:"Change",confirmButtonClass:"btn-primary"}),(0,a.jsxs)("div",{className:"flex flex-col gap-10",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(n,{className:"h-8 w-8"}),(0,a.jsx)("h2",{className:"heading-text",children:"Security Settings"})]}),(0,a.jsx)("p",{className:"sub-text",children:"Manage your account security settings and preferences"})]}),(0,a.jsxs)("div",{className:"flex-col gap-10 flex",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-5 shadow-sm border-muted p-4",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o,{className:"h-5 w-5"}),(0,a.jsx)("h2",{className:"sub-heading-text",children:"Password"})]}),(0,a.jsx)("p",{className:"sub-text",children:"Update your password to keep your account secure"})]}),(0,a.jsx)("div",{children:(0,a.jsx)(x,{})})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-5 shadow-sm border-muted p-4",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-2 ",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(l.A,{className:"h-5 w-5"}),(0,a.jsx)("h2",{className:"sub-heading-text",children:"Email Address"})]}),(0,a.jsx)("p",{className:"sub-text",children:"Your email address is used for notifications and account recovery"})]}),(0,a.jsx)("div",{children:t?(0,a.jsxs)("form",{className:"space-y-4",noValidate:!0,onSubmit:e=>e.preventDefault(),children:[(0,a.jsx)("input",{...d("email",{required:"Please enter your new email."}),type:"email",placeholder:"eg: <EMAIL>",className:"input-field"}),h.email&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:"".concat(null==(e=h.email)?void 0:e.message)}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{type:"button",className:"btn-primary",onClick:()=>L(!0),children:"save"}),(0,a.jsx)("button",{type:"button",className:"btn-outline",onClick:()=>s(!1),children:"Cancel"})]})]}):(0,a.jsxs)("div",{className:"flex justify-between items-center ",children:[(0,a.jsx)("span",{children:null==i?void 0:i.email}),(0,a.jsx)("button",{type:"button",className:"btn-primary",onClick:()=>s(!0),children:"Change"})]})})]})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsx)("h2",{className:"heading-text",children:"Recent Account Activity"}),(0,a.jsx)(v,{columns:w,data:M})]})]})]}):null}},88524:(e,t,s)=>{"use strict";s.d(t,{A0:()=>n,BF:()=>o,Hj:()=>l,XI:()=>i,nA:()=>u,nd:()=>c});var a=s(95155);s(12115);var r=s(53999);function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",t),...s})})}function n(e){let{className:t,...s}=e;return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",t),...s})}function o(e){let{className:t,...s}=e;return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...s})}function u(e){let{className:t,...s}=e;return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...s})}},92657:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},97381:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>l,Le:()=>n,jB:()=>o,tQ:()=>r,x9:()=>i});let a=(0,s(51990).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,t)=>{e.status="authenticated",e.user=t.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,t)=>{e.status="unauthenticated",e.error=t.payload,e.user=null}}}),{setAuthenticatedUser:r,setUnauthenticated:i,setAuthLoading:n,setAuthError:o}=a.actions,l=a.reducer}},e=>{var t=t=>e(e.s=t);e.O(0,[635,1445,6967,6903,4601,2177,4277,6268,8441,1684,7358],()=>t(76051)),_N_E=e.O()}]);