(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7023],{24545:(e,t,r)=>{Promise.resolve().then(r.bind(r,85372))},35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},39286:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>s,Gl:()=>n,th:()=>o});let a=(0,r(51990).Z0)({name:"createProject",initialState:{visible:!1},reducers:{showCreateProjectModal:e=>{e.visible=!0},hideCreateProjectModal:e=>{e.visible=!1}}}),{showCreateProjectModal:n,hideCreateProjectModal:o}=a.actions,s=a.reducer},74436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>u});var a=r(12115),n={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},o=a.createContext&&a.createContext(n),s=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(this,arguments)}function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var a,n,o;a=e,n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in a?Object.defineProperty(a,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):a[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(e){return t=>a.createElement(d,i({attr:l({},e.attr)},t),function e(t){return t&&t.map((t,r)=>a.createElement(t.tag,l({key:r},t.attr),e(t.child)))}(e.child))}function d(e){var t=t=>{var r,{attr:n,size:o,title:c}=e,u=function(e,t){if(null==e)return{};var r,a,n=function(e,t){if(null==e)return{};var r={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(t.indexOf(a)>=0)continue;r[a]=e[a]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(a=0;a<o.length;a++)r=o[a],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(e,s),d=o||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),a.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,n,u,{className:r,style:l(l({color:e.color||t.color},t.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),c&&a.createElement("title",null,c),e.children)};return void 0!==o?a.createElement(o.Consumer,null,e=>t(e)):t(n)}},85372:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var a=r(95155);r(12115);var n=r(35695),o=r(15305),s=r(34540),i=r(39286);let c=()=>{let{status:e}=(0,n.useParams)(),t=(0,s.wA)();return(0,a.jsx)("div",{className:"flex flex-col items-center justify-center py-16 px-4 min-h-[70vh]",children:(0,a.jsxs)("div",{className:"bg-neutral-100 rounded-lg shadow-sm p-8 max-w-md w-full text-center",children:[(0,a.jsx)("div",{className:"flex justify-center mb-6",children:(0,a.jsx)("div",{className:"bg-neutral-200 p-5 rounded-full",children:(0,a.jsx)(o._PQ,{size:50,className:"text-primary-500"})})}),(0,a.jsxs)("h2",{className:"text-2xl font-semibold text-neutral-800 mb-2",children:["No ",e&&"string"==typeof e?e.charAt(0).toUpperCase()+e.slice(1):"Projects"," Projects"]}),(0,a.jsxs)("p",{className:"text-neutral-600 mb-8",children:["draft"===e&&"You don't have any draft projects yet. Create a new project to get started.","deployed"===e&&"You don't have any deployed projects yet. Deploy a project to see it here.","archived"===e&&"You don't have any archived projects yet. Archived projects will appear here.",!e&&"No projects available in this category."]}),"draft"===e&&(0,a.jsx)("button",{onClick:()=>{t((0,i.Gl)())},className:"btn-primary w-full",children:"Create New Project"}),"deployed"===e&&(0,a.jsx)("div",{className:"text-sm text-neutral-500",children:"Create a project and deploy it to see it in this section."}),"archived"===e&&(0,a.jsx)("div",{className:"text-sm text-neutral-500",children:"Archive projects you no longer need but want to keep for reference."})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8087,635,8441,1684,7358],()=>t(24545)),_N_E=e.O()}]);