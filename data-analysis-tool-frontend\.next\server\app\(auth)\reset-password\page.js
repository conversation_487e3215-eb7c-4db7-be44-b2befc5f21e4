(()=>{var e={};e.id=3180,e.ids=[3180],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10125:(e,t,r)=>{"use strict";r.d(t,{Notification:()=>p});var s=r(60687),i=r(43210),o=r(54864),a=r(88920),n=r(57101),l=r(19150),d=r(14719),c=r(43649),u=r(93613);let p=()=>{let e=(0,o.wA)(),{message:t,type:r,visible:p}=(0,o.d4)(e=>e.notification);(0,i.useEffect)(()=>{if(p){let t=setTimeout(()=>{e((0,l._b)())},5e3);return()=>clearTimeout(t)}},[p,e]);let m="success"===r?(0,s.jsx)(d.A,{}):"warning"===r?(0,s.jsx)(c.A,{}):(0,s.jsx)(u.A,{});return(0,s.jsx)(a.N,{children:p&&(0,s.jsxs)(n.P.div,{className:`z-50 fixed top-0 right-0 m-4 px-4 py-2 rounded font-semibold w-auto max-w-xs flex items-center gap-2 cursor-pointer ${"success"===r?"bg-green-500 hover:bg-green-600":"warning"===r?"bg-yellow-500 hover:bg-yellow-600":"bg-red-500 hover:bg-red-600"} transition-colors duration-300`,onClick:()=>e((0,l._b)()),initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3,ease:"easeIn"},children:[(0,s.jsx)("span",{className:"text-2xl",children:m}),(0,s.jsx)("span",{className:"break-words neutral-100space-normal",children:t})]})})}},10271:(e,t,r)=>{"use strict";r.d(t,{ReactQueryProvider:()=>l});var s=r(60687),i=r(43210),o=r(39091),a=r(8693),n=r(9124);let l=({children:e})=>{let[t]=(0,i.useState)(()=>new o.E({defaultOptions:{queries:{staleTime:3e5,refetchOnWindowFocus:!1}}}));return(0,s.jsxs)(a.Ht,{client:t,children:[e,(0,s.jsx)(n.E,{initialIsOpen:!1})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12412:e=>{"use strict";e.exports=require("assert")},12810:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let s=r(51060).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});s.interceptors.request.use(e=>e,e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let i=s},16319:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},17004:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),i=r(48088),o=r(88170),a=r.n(o),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d={children:["",{children:["(auth)",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,66351)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(auth)\\reset-password\\page.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(auth)\\reset-password\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(auth)/reset-password/page",pathname:"/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19150:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>a,Ds:()=>i,_b:()=>o});let s=(0,r(9317).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,t)=>{e.message=t.payload.message,e.type=t.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:i,hideNotification:o}=s.actions,a=s.reducer},19169:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},21335:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(60687),i=r(43210),o=r(38587),a=r(38038),n=r(19169),l=r(28559),d=r(85814),c=r.n(d);let u=({email:e,showModal:t,setShowModal:r})=>(0,s.jsxs)(o.A,{isOpen:t,onClose:()=>r(!1),className:"flex flex-col gap-8",children:[(0,s.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,s.jsx)(a.A,{size:36}),(0,s.jsx)("h1",{className:"text-2xl tablet:text-3xl font-semibold text-center",children:"Check your email"}),(0,s.jsxs)("p",{className:"text-neutral-700 text-center",children:["We've sent a password reset link to ",e]})]}),(0,s.jsxs)("div",{className:"rounded-md p-4 bg-neutral-200 text-neutral-700 flex flex-col gap-2",children:[(0,s.jsxs)("span",{className:"flex items-center gap-2 text-lg font-medium",children:[(0,s.jsx)(n.A,{size:18})," What to do next:"]}),(0,s.jsx)("ol",{children:["Check your email inbox.","Look for an email from our company","Click on the reset link in the email","Follow the instructions to create a new password"].map((e,t)=>(0,s.jsx)("li",{children:`${t+1}. ${e}`},t))})]}),(0,s.jsxs)(c(),{href:"/",className:"text-neutral-700 self-center flex items-center gap-2",children:[(0,s.jsx)(l.A,{size:16})," Back to signin page"]})]});var p=r(27605),m=r(12810);let h=()=>{let{register:e,formState:{errors:t,isSubmitting:r},handleSubmit:o,getValues:n}=(0,p.mN)(),[d,h]=(0,i.useState)(!1),x=async e=>{try{await m.A.post("/users/forgetpassword",{email:e.email}),h(!0)}catch(e){console.error(e instanceof Error?e.message:e)}};return(0,s.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[(0,s.jsx)(u,{email:n("email"),showModal:d,setShowModal:h}),(0,s.jsxs)("div",{className:"section flex flex-col gap-8 w-11/12 mobile:w-4/5 tablet:w-lg",children:[(0,s.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,s.jsx)(a.A,{size:36}),(0,s.jsx)("h1",{className:"text-2xl tablet:text-3xl font-semibold text-center",children:"Reset your password"}),(0,s.jsx)("p",{className:"text-neutral-700 text-center",children:"Enter your email address and we'll send you a link to reset your password"})]}),(0,s.jsxs)("form",{className:"flex flex-col gap-4 ",onSubmit:o(x),noValidate:!0,children:[(0,s.jsxs)("div",{className:"label-input-group group",children:[(0,s.jsx)("label",{htmlFor:"email",className:"label-text",children:"Email"}),(0,s.jsx)("input",{...e("email",{required:"Please enter your email"}),id:"email",type:"email",className:"input-field",placeholder:"eg: <EMAIL>"}),t.email&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:`${t.email.message}`})]}),(0,s.jsx)("button",{type:"submit",className:"btn-primary",children:r?(0,s.jsxs)("span",{className:"flex items-center gap-2",children:["Sending"," ",(0,s.jsx)("div",{className:"animate-spin border-x-2 border-neutral-100 rounded-full size-4"})]}):(0,s.jsx)("span",{className:"flex items-center gap-2",children:"Send reset link"})})]}),(0,s.jsxs)(c(),{href:"/",className:"text-neutral-700 self-center flex items-center gap-2",children:[(0,s.jsx)(l.A,{size:16})," Back to signin page"]})]})]})}},21820:e=>{"use strict";e.exports=require("os")},26946:(e,t,r)=>{Promise.resolve().then(r.bind(r,10125)),Promise.resolve().then(r.bind(r,10271)),Promise.resolve().then(r.bind(r,49271))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34720:(e,t,r)=>{Promise.resolve().then(r.bind(r,21335))},35790:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>a,l:()=>o,yg:()=>i});let s=(0,r(9317).Z0)({name:"createLibraryItem",initialState:{visible:!1},reducers:{showCreateLibraryModal:e=>{e.visible=!0},hideCreateLibraryModal:e=>{e.visible=!1}}}),{showCreateLibraryModal:i,hideCreateLibraryModal:o}=s.actions,a=s.reducer},38038:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("shield-check",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},38587:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(60687),i=r(88920),o=r(57101),a=r(74699),n=r(11860);r(43210);let l=({children:e,className:t,isOpen:r,onClose:l,preventOutsideClick:d=!1})=>(0,s.jsx)(i.N,{children:r&&(0,s.jsx)(o.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40 p-4 overflow-y-auto",onClick:e=>{d||l()},children:(0,s.jsxs)(o.P.div,{initial:{scale:.6,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.6,opacity:0},transition:{duration:.3,ease:a.am},className:`relative section flex flex-col max-w-4xl max-h-[90vh] overflow-y-auto ${t}`,onClick:e=>e.stopPropagation(),children:[(0,s.jsx)(n.A,{onClick:l,className:"absolute self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300"}),e]})})})},42895:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>l,Le:()=>a,jB:()=>n,tQ:()=>i,x9:()=>o});let s=(0,r(9317).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,t)=>{e.status="authenticated",e.user=t.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,t)=>{e.status="unauthenticated",e.error=t.payload,e.user=null}}}),{setAuthenticatedUser:i,setUnauthenticated:o,setAuthLoading:a,setAuthError:n}=s.actions,l=s.reducer},44395:(e,t,r)=>{"use strict";r.d(t,{Notification:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Notification() from the server but Notification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\components\\general\\Notification.tsx","Notification")},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},47872:(e,t,r)=>{Promise.resolve().then(r.bind(r,66351))},49271:(e,t,r)=>{"use strict";r.d(t,{ReduxProvider:()=>p});var s=r(60687),i=r(9317),o=r(19150),a=r(58432),n=r(42895),l=r(35790),d=r(89011);let c=(0,i.U1)({reducer:{notification:o.Ay,createProject:a.Ay,auth:n.Ay,createLibrary:l.Ay,createLibraryItem:d.Ay}});r(43210);var u=r(54864);let p=({children:e})=>(0,s.jsx)(u.Kq,{store:c,children:e})},50823:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p,metadata:()=>u});var s=r(37413);r(82704);var i=r(7990),o=r.n(i),a=r(60866),n=r.n(a),l=r(77832),d=r(44395),c=r(60265);let u={title:"Data analysis tool",description:"A tool for data collection and analysis."};function p({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${o().className} ${n().className} antialiased`,children:(0,s.jsx)(l.ReduxProvider,{children:(0,s.jsxs)(c.ReactQueryProvider,{children:[(0,s.jsx)(d.Notification,{}),(0,s.jsx)("main",{className:"bg-neutral-200",children:e})]})})})})}},58432:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>a,Gl:()=>i,th:()=>o});let s=(0,r(9317).Z0)({name:"createProject",initialState:{visible:!1},reducers:{showCreateProjectModal:e=>{e.visible=!0},hideCreateProjectModal:e=>{e.visible=!1}}}),{showCreateProjectModal:i,hideCreateProjectModal:o}=s.actions,a=s.reducer},60265:(e,t,r)=>{"use strict";r.d(t,{ReactQueryProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ReactQueryProvider() from the server but ReactQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\providers\\ReactQueryProvider.tsx","ReactQueryProvider")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66351:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\(auth)\\\\reset-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(auth)\\reset-password\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},77832:(e,t,r)=>{"use strict";r.d(t,{ReduxProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ReduxProvider() from the server but ReduxProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\providers\\ReduxProvider.tsx","ReduxProvider")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82704:()=>{},83997:e=>{"use strict";e.exports=require("tty")},86778:(e,t,r)=>{Promise.resolve().then(r.bind(r,44395)),Promise.resolve().then(r.bind(r,60265)),Promise.resolve().then(r.bind(r,77832))},89011:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>a,dQ:()=>i,g7:()=>o});let s=(0,r(9317).Z0)({initialState:{visible:!1,option:""},name:"createLibraryItem",reducers:{showCreateLibraryItemModal:(e,t)=>{e.visible=!0,e.option=t.payload},hideCreateLibraryItemModal:e=>{e.visible=!1,e.option=""}}}),{showCreateLibraryItemModal:i,hideCreateLibraryItemModal:o}=s.actions,a=s.reducer},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7404,517,7605,5814],()=>r(17004));module.exports=s})();