"use strict";exports.id=8581,exports.ids=[8581],exports.modules={1359:(e,t,n)=>{n.d(t,{Oh:()=>l});var r=n(43210),o=0;function l(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??i()),document.body.insertAdjacentElement("beforeend",e[1]??i()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function i(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},1510:(e,t,n)=>{n.d(t,{F0:()=>c,pe:()=>o});let{Axios:r,AxiosError:o,CanceledError:l,isCancel:i,CancelToken:a,VERSION:u,all:s,Cancel:d,isAxiosError:c,spread:g,toFormData:f,AxiosHeaders:p,HttpStatusCode:h,formToJSON:m,getAdapter:v,mergeConfig:w}=n(51060).A},3589:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},4503:(e,t,n)=>{n.d(t,{BN:()=>p,ER:()=>h,Ej:()=>v,UE:()=>b,UU:()=>m,cY:()=>f,jD:()=>w,we:()=>c});var r=n(25605),o=n(43210),l=n(51215),i="undefined"!=typeof document?o.useLayoutEffect:o.useEffect;function a(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!a(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!a(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function u(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function s(e,t){let n=u(e);return Math.round(t*n)/n}function d(e){let t=o.useRef(e);return i(()=>{t.current=e}),t}function c(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:c=[],platform:g,elements:{reference:f,floating:p}={},transform:h=!0,whileElementsMounted:m,open:v}=e,[w,b]=o.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[y,C]=o.useState(c);a(y,c)||C(c);let[x,S]=o.useState(null),[R,M]=o.useState(null),E=o.useCallback(e=>{e!==I.current&&(I.current=e,S(e))},[]),P=o.useCallback(e=>{e!==k.current&&(k.current=e,M(e))},[]),F=f||x,L=p||R,I=o.useRef(null),k=o.useRef(null),V=o.useRef(w),A=null!=m,D=d(m),_=d(g),j=d(v),O=o.useCallback(()=>{if(!I.current||!k.current)return;let e={placement:t,strategy:n,middleware:y};_.current&&(e.platform=_.current),(0,r.rD)(I.current,k.current,e).then(e=>{let t={...e,isPositioned:!1!==j.current};T.current&&!a(V.current,t)&&(V.current=t,l.flushSync(()=>{b(t)}))})},[y,t,n,_,j]);i(()=>{!1===v&&V.current.isPositioned&&(V.current.isPositioned=!1,b(e=>({...e,isPositioned:!1})))},[v]);let T=o.useRef(!1);i(()=>(T.current=!0,()=>{T.current=!1}),[]),i(()=>{if(F&&(I.current=F),L&&(k.current=L),F&&L){if(D.current)return D.current(F,L,O);O()}},[F,L,O,D,A]);let H=o.useMemo(()=>({reference:I,floating:k,setReference:E,setFloating:P}),[E,P]),N=o.useMemo(()=>({reference:F,floating:L}),[F,L]),z=o.useMemo(()=>{let e={position:n,left:0,top:0};if(!N.floating)return e;let t=s(N.floating,w.x),r=s(N.floating,w.y);return h?{...e,transform:"translate("+t+"px, "+r+"px)",...u(N.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,h,N.floating,w.x,w.y]);return o.useMemo(()=>({...w,update:O,refs:H,elements:N,floatingStyles:z}),[w,O,H,N,z])}let g=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:o}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?(0,r.UE)({element:n.current,padding:o}).fn(t):{}:n?(0,r.UE)({element:n,padding:o}).fn(t):{}}}),f=(e,t)=>({...(0,r.cY)(e),options:[e,t]}),p=(e,t)=>({...(0,r.BN)(e),options:[e,t]}),h=(e,t)=>({...(0,r.ER)(e),options:[e,t]}),m=(e,t)=>({...(0,r.UU)(e),options:[e,t]}),v=(e,t)=>({...(0,r.Ej)(e),options:[e,t]}),w=(e,t)=>({...(0,r.jD)(e),options:[e,t]}),b=(e,t)=>({...g(e),options:[e,t]})},10022:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},11437:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},11860:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},16309:(e,t,n)=>{n.d(t,{U:()=>l});var r=n(43210),o=n(13495);function l(e,t=globalThis?.document){let n=(0,o.c)(e);r.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}},17019:(e,t,n)=>{n.d(t,{B88:()=>o});var r=n(90296);function o(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"},child:[]},{tag:"polyline",attr:{points:"17 8 12 3 7 8"},child:[]},{tag:"line",attr:{x1:"12",y1:"3",x2:"12",y2:"15"},child:[]}]})(e)}},17257:(e,t,n)=>{n.d(t,{_PQ:()=>o,nko:()=>l});var r=n(90296);function o(e){return(0,r.k5)({tag:"svg",attr:{fill:"currentColor",viewBox:"0 0 16 16"},child:[{tag:"path",attr:{d:"m.5 3 .04.87a2 2 0 0 0-.342 1.311l.637 7A2 2 0 0 0 2.826 14H9v-1H2.826a1 1 0 0 1-.995-.91l-.637-7A1 1 0 0 1 2.19 4h11.62a1 1 0 0 1 .996 1.09L14.54 8h1.005l.256-2.819A2 2 0 0 0 13.81 3H9.828a2 2 0 0 1-1.414-.586l-.828-.828A2 2 0 0 0 6.172 1H2.5a2 2 0 0 0-2 2m5.672-1a1 1 0 0 1 .707.293L7.586 3H2.19q-.362.002-.683.12L1.5 2.98a1 1 0 0 1 1-.98z"},child:[]},{tag:"path",attr:{d:"M13.5 9a.5.5 0 0 1 .5.5V11h1.5a.5.5 0 1 1 0 1H14v1.5a.5.5 0 1 1-1 0V12h-1.5a.5.5 0 0 1 0-1H13V9.5a.5.5 0 0 1 .5-.5"},child:[]}]})(e)}function l(e){return(0,r.k5)({tag:"svg",attr:{fill:"currentColor",viewBox:"0 0 16 16"},child:[{tag:"path",attr:{d:"M12.17 9.53c2.307-2.592 3.278-4.684 3.641-6.218.21-.887.214-1.58.16-2.065a3.6 3.6 0 0 0-.108-.563 2 2 0 0 0-.078-.23V.453c-.073-.164-.168-.234-.352-.295a2 2 0 0 0-.16-.045 4 4 0 0 0-.57-.093c-.49-.044-1.19-.03-2.08.188-1.536.374-3.618 1.343-6.161 3.604l-2.4.238h-.006a2.55 2.55 0 0 0-1.524.734L.15 7.17a.512.512 0 0 0 .433.868l1.896-.271c.28-.04.592.013.955.132.232.076.437.16.655.248l.203.083c.196.816.66 1.58 1.275 2.195.613.614 1.376 1.08 2.191 1.277l.082.202c.089.218.173.424.249.657.118.363.172.676.132.956l-.271 1.9a.512.512 0 0 0 .867.433l2.382-2.386c.41-.41.668-.949.732-1.526zm.11-3.699c-.797.8-1.93.961-2.528.362-.598-.6-.436-1.733.361-2.532.798-.799 1.93-.96 2.528-.361s.437 1.732-.36 2.531Z"},child:[]},{tag:"path",attr:{d:"M5.205 10.787a7.6 7.6 0 0 0 1.804 1.352c-1.118 1.007-4.929 2.028-5.054 1.903-.126-.127.737-4.189 1.839-5.18.346.69.837 1.35 1.411 1.925"},child:[]}]})(e)}},20255:(e,t,n)=>{n.d(t,{Blu:()=>l,Yvo:()=>i,fzI:()=>o});var r=n(90296);function o(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{fill:"none",d:"M0 0h24v24H0z"},child:[]},{tag:"path",attr:{d:"M14 19.88V22h2.12l5.17-5.17-2.12-2.12zM20 8l-6-6H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H12v-2.95l8-8V8zm-7 1V3.5L18.5 9H13zM22.71 14l-.71-.71a.996.996 0 0 0-1.41 0l-.71.71L22 16.12l.71-.71a.996.996 0 0 0 0-1.41z"},child:[]}]})(e)}function l(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{fill:"none",d:"M0 0h24v24H0z"},child:[]},{tag:"path",attr:{d:"M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm-1 4 6 6v10c0 1.1-.9 2-2 2H7.99C6.89 23 6 22.1 6 21l.01-14c0-1.1.89-2 1.99-2h7zm-1 7h5.5L14 6.5V12z"},child:[]}]})(e)}function i(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{fill:"none",d:"M0 0h24v24H0z"},child:[]},{tag:"path",attr:{d:"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04a.996.996 0 0 0 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"},child:[]}]})(e)}},24224:(e,t,n)=>{n.d(t,{F:()=>i});var r=n(49384);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=r.$,i=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return l(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:i,defaultVariants:a}=t,u=Object.keys(i).map(e=>{let t=null==n?void 0:n[e],r=null==a?void 0:a[e];if(null===t)return null;let l=o(t)||o(r);return i[e][l]}),s=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return l(e,u,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...o}=t;return Object.entries(o).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...a,...s}[t]):({...a,...s})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},25028:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(43210),o=n(51215),l=n(14163),i=n(66156),a=n(60687),u=r.forwardRef((e,t)=>{let{container:n,...u}=e,[s,d]=r.useState(!1);(0,i.N)(()=>d(!0),[]);let c=n||s&&globalThis?.document?.body;return c?o.createPortal((0,a.jsx)(l.sG.div,{...u,ref:t}),c):null});u.displayName="Portal"},25605:(e,t,n)=>{n.d(t,{UE:()=>eg,ll:()=>ei,rD:()=>ep,UU:()=>es,jD:()=>ec,ER:()=>ef,cY:()=>ea,BN:()=>eu,Ej:()=>ed});let r=["top","right","bottom","left"],o=Math.min,l=Math.max,i=Math.round,a=Math.floor,u=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function c(e,t){return"function"==typeof e?e(t):e}function g(e){return e.split("-")[0]}function f(e){return e.split("-")[1]}function p(e){return"x"===e?"y":"x"}function h(e){return"y"===e?"height":"width"}function m(e){return["top","bottom"].includes(g(e))?"y":"x"}function v(e){return e.replace(/start|end/g,e=>d[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function b(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function y(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function C(e,t,n){let r,{reference:o,floating:l}=e,i=m(t),a=p(m(t)),u=h(a),s=g(t),d="y"===i,c=o.x+o.width/2-l.width/2,v=o.y+o.height/2-l.height/2,w=o[u]/2-l[u]/2;switch(s){case"top":r={x:c,y:o.y-l.height};break;case"bottom":r={x:c,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:v};break;case"left":r={x:o.x-l.width,y:v};break;default:r={x:o.x,y:o.y}}switch(f(t)){case"start":r[a]-=w*(n&&d?-1:1);break;case"end":r[a]+=w*(n&&d?-1:1)}return r}let x=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:l=[],platform:i}=n,a=l.filter(Boolean),u=await (null==i.isRTL?void 0:i.isRTL(t)),s=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:c}=C(s,r,u),g=r,f={},p=0;for(let n=0;n<a.length;n++){let{name:l,fn:h}=a[n],{x:m,y:v,data:w,reset:b}=await h({x:d,y:c,initialPlacement:r,placement:g,strategy:o,middlewareData:f,rects:s,platform:i,elements:{reference:e,floating:t}});d=null!=m?m:d,c=null!=v?v:c,f={...f,[l]:{...f[l],...w}},b&&p<=50&&(p++,"object"==typeof b&&(b.placement&&(g=b.placement),b.rects&&(s=!0===b.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):b.rects),{x:d,y:c}=C(s,g,u)),n=-1)}return{x:d,y:c,placement:g,strategy:o,middlewareData:f}};async function S(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:l,rects:i,elements:a,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:d="viewport",elementContext:g="floating",altBoundary:f=!1,padding:p=0}=c(t,e),h=b(p),m=a[f?"floating"===g?"reference":"floating":g],v=y(await l.getClippingRect({element:null==(n=await (null==l.isElement?void 0:l.isElement(m)))||n?m:m.contextElement||await (null==l.getDocumentElement?void 0:l.getDocumentElement(a.floating)),boundary:s,rootBoundary:d,strategy:u})),w="floating"===g?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,C=await (null==l.getOffsetParent?void 0:l.getOffsetParent(a.floating)),x=await (null==l.isElement?void 0:l.isElement(C))&&await (null==l.getScale?void 0:l.getScale(C))||{x:1,y:1},S=y(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:C,strategy:u}):w);return{top:(v.top-S.top+h.top)/x.y,bottom:(S.bottom-v.bottom+h.bottom)/x.y,left:(v.left-S.left+h.left)/x.x,right:(S.right-v.right+h.right)/x.x}}function R(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function M(e){return r.some(t=>e[t]>=0)}async function E(e,t){let{placement:n,platform:r,elements:o}=e,l=await (null==r.isRTL?void 0:r.isRTL(o.floating)),i=g(n),a=f(n),u="y"===m(n),s=["left","top"].includes(i)?-1:1,d=l&&u?-1:1,p=c(t,e),{mainAxis:h,crossAxis:v,alignmentAxis:w}="number"==typeof p?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return a&&"number"==typeof w&&(v="end"===a?-1*w:w),u?{x:v*d,y:h*s}:{x:h*s,y:v*d}}function P(){return"undefined"!=typeof window}function F(e){return k(e)?(e.nodeName||"").toLowerCase():"#document"}function L(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function I(e){var t;return null==(t=(k(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function k(e){return!!P()&&(e instanceof Node||e instanceof L(e).Node)}function V(e){return!!P()&&(e instanceof Element||e instanceof L(e).Element)}function A(e){return!!P()&&(e instanceof HTMLElement||e instanceof L(e).HTMLElement)}function D(e){return!!P()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof L(e).ShadowRoot)}function _(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=N(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function j(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function O(e){let t=T(),n=V(e)?N(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function T(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function H(e){return["html","body","#document"].includes(F(e))}function N(e){return L(e).getComputedStyle(e)}function z(e){return V(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function G(e){if("html"===F(e))return e;let t=e.assignedSlot||e.parentNode||D(e)&&e.host||I(e);return D(t)?t.host:t}function B(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=G(t);return H(n)?t.ownerDocument?t.ownerDocument.body:t.body:A(n)&&_(n)?n:e(n)}(e),l=o===(null==(r=e.ownerDocument)?void 0:r.body),i=L(o);if(l){let e=W(i);return t.concat(i,i.visualViewport||[],_(o)?o:[],e&&n?B(e):[])}return t.concat(o,B(o,[],n))}function W(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function U(e){let t=N(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=A(e),l=o?e.offsetWidth:n,a=o?e.offsetHeight:r,u=i(n)!==l||i(r)!==a;return u&&(n=l,r=a),{width:n,height:r,$:u}}function q(e){return V(e)?e:e.contextElement}function K(e){let t=q(e);if(!A(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:l}=U(t),a=(l?i(n.width):n.width)/r,s=(l?i(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),s&&Number.isFinite(s)||(s=1),{x:a,y:s}}let Z=u(0);function $(e){let t=L(e);return T()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:Z}function X(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let l=e.getBoundingClientRect(),i=q(e),a=u(1);t&&(r?V(r)&&(a=K(r)):a=K(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===L(i))&&o)?$(i):u(0),d=(l.left+s.x)/a.x,c=(l.top+s.y)/a.y,g=l.width/a.x,f=l.height/a.y;if(i){let e=L(i),t=r&&V(r)?L(r):r,n=e,o=W(n);for(;o&&r&&t!==n;){let e=K(o),t=o.getBoundingClientRect(),r=N(o),l=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;d*=e.x,c*=e.y,g*=e.x,f*=e.y,d+=l,c+=i,o=W(n=L(o))}}return y({width:g,height:f,x:d,y:c})}function Y(e,t){let n=z(e).scrollLeft;return t?t.left+n:X(I(e)).left+n}function J(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:Y(e,r)),y:r.top+t.scrollTop}}function Q(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=L(e),r=I(e),o=n.visualViewport,l=r.clientWidth,i=r.clientHeight,a=0,u=0;if(o){l=o.width,i=o.height;let e=T();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:l,height:i,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=I(e),n=z(e),r=e.ownerDocument.body,o=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+Y(e),u=-n.scrollTop;return"rtl"===N(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:u}}(I(e));else if(V(t))r=function(e,t){let n=X(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,l=A(e)?K(e):u(1),i=e.clientWidth*l.x,a=e.clientHeight*l.y;return{width:i,height:a,x:o*l.x,y:r*l.y}}(t,n);else{let n=$(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return y(r)}function ee(e){return"static"===N(e).position}function et(e,t){if(!A(e)||"fixed"===N(e).position)return null;if(t)return t(e);let n=e.offsetParent;return I(e)===n&&(n=n.ownerDocument.body),n}function en(e,t){let n=L(e);if(j(e))return n;if(!A(e)){let t=G(e);for(;t&&!H(t);){if(V(t)&&!ee(t))return t;t=G(t)}return n}let r=et(e,t);for(;r&&["table","td","th"].includes(F(r))&&ee(r);)r=et(r,t);return r&&H(r)&&ee(r)&&!O(r)?n:r||function(e){let t=G(e);for(;A(t)&&!H(t);){if(O(t))return t;if(j(t))break;t=G(t)}return null}(e)||n}let er=async function(e){let t=this.getOffsetParent||en,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=A(t),o=I(t),l="fixed"===n,i=X(e,!0,l,t),a={scrollLeft:0,scrollTop:0},s=u(0);if(r||!r&&!l)if(("body"!==F(t)||_(o))&&(a=z(t)),r){let e=X(t,!0,l,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=Y(o));let d=!o||r||l?u(0):J(o,a);return{x:i.left+a.scrollLeft-s.x-d.x,y:i.top+a.scrollTop-s.y-d.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eo={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,l="fixed"===o,i=I(r),a=!!t&&j(t.floating);if(r===i||a&&l)return n;let s={scrollLeft:0,scrollTop:0},d=u(1),c=u(0),g=A(r);if((g||!g&&!l)&&(("body"!==F(r)||_(i))&&(s=z(r)),A(r))){let e=X(r);d=K(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let f=!i||g||l?u(0):J(i,s,!0);return{width:n.width*d.x,height:n.height*d.y,x:n.x*d.x-s.scrollLeft*d.x+c.x+f.x,y:n.y*d.y-s.scrollTop*d.y+c.y+f.y}},getDocumentElement:I,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,a=[..."clippingAncestors"===n?j(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=B(e,[],!1).filter(e=>V(e)&&"body"!==F(e)),o=null,l="fixed"===N(e).position,i=l?G(e):e;for(;V(i)&&!H(i);){let t=N(i),n=O(i);n||"fixed"!==t.position||(o=null),(l?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||_(i)&&!n&&function e(t,n){let r=G(t);return!(r===n||!V(r)||H(r))&&("fixed"===N(r).position||e(r,n))}(e,i))?r=r.filter(e=>e!==i):o=t,i=G(i)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=a[0],s=a.reduce((e,n)=>{let r=Q(t,n,i);return e.top=l(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=l(r.left,e.left),e},Q(t,u,i));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:en,getElementRects:er,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=U(e);return{width:t,height:n}},getScale:K,isElement:V,isRTL:function(e){return"rtl"===N(e).direction}};function el(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function ei(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:u=!0,ancestorResize:s=!0,elementResize:d="function"==typeof ResizeObserver,layoutShift:c="function"==typeof IntersectionObserver,animationFrame:g=!1}=r,f=q(e),p=u||s?[...f?B(f):[],...B(t)]:[];p.forEach(e=>{u&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)});let h=f&&c?function(e,t){let n,r=null,i=I(e);function u(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function s(d,c){void 0===d&&(d=!1),void 0===c&&(c=1),u();let g=e.getBoundingClientRect(),{left:f,top:p,width:h,height:m}=g;if(d||t(),!h||!m)return;let v=a(p),w=a(i.clientWidth-(f+h)),b={rootMargin:-v+"px "+-w+"px "+-a(i.clientHeight-(p+m))+"px "+-a(f)+"px",threshold:l(0,o(1,c))||1},y=!0;function C(t){let r=t[0].intersectionRatio;if(r!==c){if(!y)return s();r?s(!1,r):n=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==r||el(g,e.getBoundingClientRect())||s(),y=!1}try{r=new IntersectionObserver(C,{...b,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(C,b)}r.observe(e)}(!0),u}(f,n):null,m=-1,v=null;d&&(v=new ResizeObserver(e=>{let[r]=e;r&&r.target===f&&v&&(v.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),n()}),f&&!g&&v.observe(f),v.observe(t));let w=g?X(e):null;return g&&function t(){let r=X(e);w&&!el(w,r)&&n(),w=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;p.forEach(e=>{u&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)}),null==h||h(),null==(e=v)||e.disconnect(),v=null,g&&cancelAnimationFrame(i)}}let ea=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:l,placement:i,middlewareData:a}=t,u=await E(t,e);return i===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:l+u.y,data:{...u,placement:i}}}}},eu=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:i}=t,{mainAxis:a=!0,crossAxis:u=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...d}=c(e,t),f={x:n,y:r},h=await S(t,d),v=m(g(i)),w=p(v),b=f[w],y=f[v];if(a){let e="y"===w?"top":"left",t="y"===w?"bottom":"right",n=b+h[e],r=b-h[t];b=l(n,o(b,r))}if(u){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=y+h[e],r=y-h[t];y=l(n,o(y,r))}let C=s.fn({...t,[w]:b,[v]:y});return{...C,data:{x:C.x-n,y:C.y-r,enabled:{[w]:a,[v]:u}}}}}},es=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,l,i;let{placement:a,middlewareData:u,rects:s,initialPlacement:d,platform:b,elements:y}=t,{mainAxis:C=!0,crossAxis:x=!0,fallbackPlacements:R,fallbackStrategy:M="bestFit",fallbackAxisSideDirection:E="none",flipAlignment:P=!0,...F}=c(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let L=g(a),I=m(d),k=g(d)===d,V=await (null==b.isRTL?void 0:b.isRTL(y.floating)),A=R||(k||!P?[w(d)]:function(e){let t=w(e);return[v(e),t,v(t)]}(d)),D="none"!==E;!R&&D&&A.push(...function(e,t,n,r){let o=f(e),l=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(g(e),"start"===n,r);return o&&(l=l.map(e=>e+"-"+o),t&&(l=l.concat(l.map(v)))),l}(d,P,E,V));let _=[d,...A],j=await S(t,F),O=[],T=(null==(r=u.flip)?void 0:r.overflows)||[];if(C&&O.push(j[L]),x){let e=function(e,t,n){void 0===n&&(n=!1);let r=f(e),o=p(m(e)),l=h(o),i="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[l]>t.floating[l]&&(i=w(i)),[i,w(i)]}(a,s,V);O.push(j[e[0]],j[e[1]])}if(T=[...T,{placement:a,overflows:O}],!O.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=_[e];if(t)return{data:{index:e,overflows:T},reset:{placement:t}};let n=null==(l=T.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:l.placement;if(!n)switch(M){case"bestFit":{let e=null==(i=T.filter(e=>{if(D){let t=m(e.placement);return t===I||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:i[0];e&&(n=e);break}case"initialPlacement":n=d}if(a!==n)return{reset:{placement:n}}}return{}}}},ed=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let i,a,{placement:u,rects:s,platform:d,elements:p}=t,{apply:h=()=>{},...v}=c(e,t),w=await S(t,v),b=g(u),y=f(u),C="y"===m(u),{width:x,height:R}=s.floating;"top"===b||"bottom"===b?(i=b,a=y===(await (null==d.isRTL?void 0:d.isRTL(p.floating))?"start":"end")?"left":"right"):(a=b,i="end"===y?"top":"bottom");let M=R-w.top-w.bottom,E=x-w.left-w.right,P=o(R-w[i],M),F=o(x-w[a],E),L=!t.middlewareData.shift,I=P,k=F;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(k=E),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(I=M),L&&!y){let e=l(w.left,0),t=l(w.right,0),n=l(w.top,0),r=l(w.bottom,0);C?k=x-2*(0!==e||0!==t?e+t:l(w.left,w.right)):I=R-2*(0!==n||0!==r?n+r:l(w.top,w.bottom))}await h({...t,availableWidth:k,availableHeight:I});let V=await d.getDimensions(p.floating);return x!==V.width||R!==V.height?{reset:{rects:!0}}:{}}}},ec=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=c(e,t);switch(r){case"referenceHidden":{let e=R(await S(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:M(e)}}}case"escaped":{let e=R(await S(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:M(e)}}}default:return{}}}}},eg=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:a,platform:u,elements:s,middlewareData:d}=t,{element:g,padding:v=0}=c(e,t)||{};if(null==g)return{};let w=b(v),y={x:n,y:r},C=p(m(i)),x=h(C),S=await u.getDimensions(g),R="y"===C,M=R?"clientHeight":"clientWidth",E=a.reference[x]+a.reference[C]-y[C]-a.floating[x],P=y[C]-a.reference[C],F=await (null==u.getOffsetParent?void 0:u.getOffsetParent(g)),L=F?F[M]:0;L&&await (null==u.isElement?void 0:u.isElement(F))||(L=s.floating[M]||a.floating[x]);let I=L/2-S[x]/2-1,k=o(w[R?"top":"left"],I),V=o(w[R?"bottom":"right"],I),A=L-S[x]-V,D=L/2-S[x]/2+(E/2-P/2),_=l(k,o(D,A)),j=!d.arrow&&null!=f(i)&&D!==_&&a.reference[x]/2-(D<k?k:V)-S[x]/2<0,O=j?D<k?D-k:D-A:0;return{[C]:y[C]+O,data:{[C]:_,centerOffset:D-_-O,...j&&{alignmentOffset:O}},reset:j}}}),ef=function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:l,middlewareData:i}=t,{offset:a=0,mainAxis:u=!0,crossAxis:s=!0}=c(e,t),d={x:n,y:r},f=m(o),h=p(f),v=d[h],w=d[f],b=c(a,t),y="number"==typeof b?{mainAxis:b,crossAxis:0}:{mainAxis:0,crossAxis:0,...b};if(u){let e="y"===h?"height":"width",t=l.reference[h]-l.floating[e]+y.mainAxis,n=l.reference[h]+l.reference[e]-y.mainAxis;v<t?v=t:v>n&&(v=n)}if(s){var C,x;let e="y"===h?"width":"height",t=["top","left"].includes(g(o)),n=l.reference[f]-l.floating[e]+(t&&(null==(C=i.offset)?void 0:C[f])||0)+(t?0:y.crossAxis),r=l.reference[f]+l.reference[e]+(t?0:(null==(x=i.offset)?void 0:x[f])||0)-(t?y.crossAxis:0);w<n?w=n:w>r&&(w=r)}return{[h]:v,[f]:w}}}},ep=(e,t,n)=>{let r=new Map,o={platform:eo,...n},l={...o.platform,_c:r};return x(e,t,{...o,platform:l})}},26273:(e,t,n)=>{n.d(t,{CBv:()=>o,D4o:()=>i,S1H:()=>a,_rf:()=>s,rjU:()=>u,t2D:()=>l});var r=n(90296);function o(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"m21 16-4 4-4-4"},child:[]},{tag:"path",attr:{d:"M17 20V4"},child:[]},{tag:"path",attr:{d:"m3 8 4-4 4 4"},child:[]},{tag:"path",attr:{d:"M7 4v16"},child:[]}]})(e)}function l(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M20 7h-3a2 2 0 0 1-2-2V2"},child:[]},{tag:"path",attr:{d:"M9 18a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h7l4 4v10a2 2 0 0 1-2 2Z"},child:[]},{tag:"path",attr:{d:"M3 7.6v12.8A1.6 1.6 0 0 0 4.6 22h9.8"},child:[]}]})(e)}function i(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M3 7V5a2 2 0 0 1 2-2h2"},child:[]},{tag:"path",attr:{d:"M17 3h2a2 2 0 0 1 2 2v2"},child:[]},{tag:"path",attr:{d:"M21 17v2a2 2 0 0 1-2 2h-2"},child:[]},{tag:"path",attr:{d:"M7 21H5a2 2 0 0 1-2-2v-2"},child:[]},{tag:"rect",attr:{width:"10",height:"8",x:"7",y:"8",rx:"1"},child:[]}]})(e)}function a(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"rect",attr:{width:"18",height:"7",x:"3",y:"3",rx:"1"},child:[]},{tag:"rect",attr:{width:"9",height:"7",x:"3",y:"14",rx:"1"},child:[]},{tag:"rect",attr:{width:"5",height:"7",x:"16",y:"14",rx:"1"},child:[]}]})(e)}function u(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"m16 6 4 14"},child:[]},{tag:"path",attr:{d:"M12 6v14"},child:[]},{tag:"path",attr:{d:"M8 8v12"},child:[]},{tag:"path",attr:{d:"M4 4v16"},child:[]}]})(e)}function s(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M5 12h14"},child:[]},{tag:"path",attr:{d:"M12 5v14"},child:[]}]})(e)}},26312:(e,t,n)=>{n.d(t,{H_:()=>eX,UC:()=>eZ,ty:()=>eT,q7:()=>e$,VF:()=>eY,ZL:()=>eK,bL:()=>eU,l9:()=>eq});var r=n(43210),o=n(70569),l=n(98599),i=n(11273),a=n(65551),u=n(14163),s=n(9510),d=n(43),c=n(92010),g=n(1359),f=n(32547),p=n(96963),h=n(57678),m=n(25028),v=n(46059),w=n(72942),b=n(8730),y=n(13495),C=n(63376),x=n(42247),S=n(60687),R=["Enter"," "],M=["ArrowUp","PageDown","End"],E=["ArrowDown","PageUp","Home",...M],P={ltr:[...R,"ArrowRight"],rtl:[...R,"ArrowLeft"]},F={ltr:["ArrowLeft"],rtl:["ArrowRight"]},L="Menu",[I,k,V]=(0,s.N)(L),[A,D]=(0,i.A)(L,[V,h.Bk,w.RG]),_=(0,h.Bk)(),j=(0,w.RG)(),[O,T]=A(L),[H,N]=A(L),z=e=>{let{__scopeMenu:t,open:n=!1,children:o,dir:l,onOpenChange:i,modal:a=!0}=e,u=_(t),[s,c]=r.useState(null),g=r.useRef(!1),f=(0,y.c)(i),p=(0,d.jH)(l);return r.useEffect(()=>{let e=()=>{g.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>g.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,S.jsx)(h.bL,{...u,children:(0,S.jsx)(O,{scope:t,open:n,onOpenChange:f,content:s,onContentChange:c,children:(0,S.jsx)(H,{scope:t,onClose:r.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:g,dir:p,modal:a,children:o})})})};z.displayName=L;var G=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=_(n);return(0,S.jsx)(h.Mz,{...o,...r,ref:t})});G.displayName="MenuAnchor";var B="MenuPortal",[W,U]=A(B,{forceMount:void 0}),q=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,l=T(B,t);return(0,S.jsx)(W,{scope:t,forceMount:n,children:(0,S.jsx)(v.C,{present:n||l.open,children:(0,S.jsx)(m.Z,{asChild:!0,container:o,children:r})})})};q.displayName=B;var K="MenuContent",[Z,$]=A(K),X=r.forwardRef((e,t)=>{let n=U(K,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,l=T(K,e.__scopeMenu),i=N(K,e.__scopeMenu);return(0,S.jsx)(I.Provider,{scope:e.__scopeMenu,children:(0,S.jsx)(v.C,{present:r||l.open,children:(0,S.jsx)(I.Slot,{scope:e.__scopeMenu,children:i.modal?(0,S.jsx)(Y,{...o,ref:t}):(0,S.jsx)(J,{...o,ref:t})})})})}),Y=r.forwardRef((e,t)=>{let n=T(K,e.__scopeMenu),i=r.useRef(null),a=(0,l.s)(t,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,C.Eq)(e)},[]),(0,S.jsx)(ee,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),J=r.forwardRef((e,t)=>{let n=T(K,e.__scopeMenu);return(0,S.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Q=(0,b.TL)("MenuContent.ScrollLock"),ee=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:i=!1,trapFocus:a,onOpenAutoFocus:u,onCloseAutoFocus:s,disableOutsidePointerEvents:d,onEntryFocus:p,onEscapeKeyDown:m,onPointerDownOutside:v,onFocusOutside:b,onInteractOutside:y,onDismiss:C,disableOutsideScroll:R,...P}=e,F=T(K,n),L=N(K,n),I=_(n),V=j(n),A=k(n),[D,O]=r.useState(null),H=r.useRef(null),z=(0,l.s)(t,H,F.onContentChange),G=r.useRef(0),B=r.useRef(""),W=r.useRef(0),U=r.useRef(null),q=r.useRef("right"),$=r.useRef(0),X=R?x.A:r.Fragment,Y=e=>{let t=B.current+e,n=A().filter(e=>!e.disabled),r=document.activeElement,o=n.find(e=>e.ref.current===r)?.textValue,l=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,i=(r=Math.max(l,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let a=i.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return a!==n?a:void 0}(n.map(e=>e.textValue),t,o),i=n.find(e=>e.textValue===l)?.ref.current;!function e(t){B.current=t,window.clearTimeout(G.current),""!==t&&(G.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};r.useEffect(()=>()=>window.clearTimeout(G.current),[]),(0,g.Oh)();let J=r.useCallback(e=>q.current===U.current?.side&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,l=t.length-1;e<t.length;l=e++){let i=t[e],a=t[l],u=i.x,s=i.y,d=a.x,c=a.y;s>r!=c>r&&n<(d-u)*(r-s)/(c-s)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,U.current?.area),[]);return(0,S.jsx)(Z,{scope:n,searchRef:B,onItemEnter:r.useCallback(e=>{J(e)&&e.preventDefault()},[J]),onItemLeave:r.useCallback(e=>{J(e)||(H.current?.focus(),O(null))},[J]),onTriggerLeave:r.useCallback(e=>{J(e)&&e.preventDefault()},[J]),pointerGraceTimerRef:W,onPointerGraceIntentChange:r.useCallback(e=>{U.current=e},[]),children:(0,S.jsx)(X,{...R?{as:Q,allowPinchZoom:!0}:void 0,children:(0,S.jsx)(f.n,{asChild:!0,trapped:a,onMountAutoFocus:(0,o.m)(u,e=>{e.preventDefault(),H.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,S.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:m,onPointerDownOutside:v,onFocusOutside:b,onInteractOutside:y,onDismiss:C,children:(0,S.jsx)(w.bL,{asChild:!0,...V,dir:L.dir,orientation:"vertical",loop:i,currentTabStopId:D,onCurrentTabStopIdChange:O,onEntryFocus:(0,o.m)(p,e=>{L.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,S.jsx)(h.UC,{role:"menu","aria-orientation":"vertical","data-state":eE(F.open),"data-radix-menu-content":"",dir:L.dir,...I,...P,ref:z,style:{outline:"none",...P.style},onKeyDown:(0,o.m)(P.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&Y(e.key));let o=H.current;if(e.target!==o||!E.includes(e.key))return;e.preventDefault();let l=A().filter(e=>!e.disabled).map(e=>e.ref.current);M.includes(e.key)&&l.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(l)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(G.current),B.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eL(e=>{let t=e.target,n=$.current!==e.clientX;e.currentTarget.contains(t)&&n&&(q.current=e.clientX>$.current?"right":"left",$.current=e.clientX)}))})})})})})})});X.displayName=K;var et=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,S.jsx)(u.sG.div,{role:"group",...r,ref:t})});et.displayName="MenuGroup";var en=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,S.jsx)(u.sG.div,{...r,ref:t})});en.displayName="MenuLabel";var er="MenuItem",eo="menu.itemSelect",el=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:i,...a}=e,s=r.useRef(null),d=N(er,e.__scopeMenu),c=$(er,e.__scopeMenu),g=(0,l.s)(t,s),f=r.useRef(!1);return(0,S.jsx)(ei,{...a,ref:g,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=s.current;if(!n&&e){let t=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>i?.(e),{once:!0}),(0,u.hO)(e,t),t.defaultPrevented?f.current=!1:d.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),f.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{f.current||e.currentTarget?.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==c.searchRef.current;n||t&&" "===e.key||R.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});el.displayName=er;var ei=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:i=!1,textValue:a,...s}=e,d=$(er,n),c=j(n),g=r.useRef(null),f=(0,l.s)(t,g),[p,h]=r.useState(!1),[m,v]=r.useState("");return r.useEffect(()=>{let e=g.current;e&&v((e.textContent??"").trim())},[s.children]),(0,S.jsx)(I.ItemSlot,{scope:n,disabled:i,textValue:a??m,children:(0,S.jsx)(w.q7,{asChild:!0,...c,focusable:!i,children:(0,S.jsx)(u.sG.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":i||void 0,"data-disabled":i?"":void 0,...s,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,eL(e=>{i?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eL(e=>d.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>h(!0)),onBlur:(0,o.m)(e.onBlur,()=>h(!1))})})})}),ea=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...l}=e;return(0,S.jsx)(eh,{scope:e.__scopeMenu,checked:n,children:(0,S.jsx)(el,{role:"menuitemcheckbox","aria-checked":eP(n)?"mixed":n,...l,ref:t,"data-state":eF(n),onSelect:(0,o.m)(l.onSelect,()=>r?.(!!eP(n)||!n),{checkForDefaultPrevented:!1})})})});ea.displayName="MenuCheckboxItem";var eu="MenuRadioGroup",[es,ed]=A(eu,{value:void 0,onValueChange:()=>{}}),ec=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,l=(0,y.c)(r);return(0,S.jsx)(es,{scope:e.__scopeMenu,value:n,onValueChange:l,children:(0,S.jsx)(et,{...o,ref:t})})});ec.displayName=eu;var eg="MenuRadioItem",ef=r.forwardRef((e,t)=>{let{value:n,...r}=e,l=ed(eg,e.__scopeMenu),i=n===l.value;return(0,S.jsx)(eh,{scope:e.__scopeMenu,checked:i,children:(0,S.jsx)(el,{role:"menuitemradio","aria-checked":i,...r,ref:t,"data-state":eF(i),onSelect:(0,o.m)(r.onSelect,()=>l.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});ef.displayName=eg;var ep="MenuItemIndicator",[eh,em]=A(ep,{checked:!1}),ev=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,l=em(ep,n);return(0,S.jsx)(v.C,{present:r||eP(l.checked)||!0===l.checked,children:(0,S.jsx)(u.sG.span,{...o,ref:t,"data-state":eF(l.checked)})})});ev.displayName=ep;var ew=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,S.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});ew.displayName="MenuSeparator";var eb=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=_(n);return(0,S.jsx)(h.i3,{...o,...r,ref:t})});eb.displayName="MenuArrow";var[ey,eC]=A("MenuSub"),ex="MenuSubTrigger",eS=r.forwardRef((e,t)=>{let n=T(ex,e.__scopeMenu),i=N(ex,e.__scopeMenu),a=eC(ex,e.__scopeMenu),u=$(ex,e.__scopeMenu),s=r.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:c}=u,g={__scopeMenu:e.__scopeMenu},f=r.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return r.useEffect(()=>f,[f]),r.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),c(null)}},[d,c]),(0,S.jsx)(G,{asChild:!0,...g,children:(0,S.jsx)(ei,{id:a.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":a.contentId,"data-state":eE(n.open),...e,ref:(0,l.t)(t,a.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eL(t=>{u.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||s.current||(u.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eL(e=>{f();let t=n.content?.getBoundingClientRect();if(t){let r=n.content?.dataset.side,o="right"===r,l=t[o?"left":"right"],i=t[o?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:l,y:t.top},{x:i,y:t.top},{x:i,y:t.bottom},{x:l,y:t.bottom}],side:r}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let r=""!==u.searchRef.current;e.disabled||r&&" "===t.key||P[i.dir].includes(t.key)&&(n.onOpenChange(!0),n.content?.focus(),t.preventDefault())})})})});eS.displayName=ex;var eR="MenuSubContent",eM=r.forwardRef((e,t)=>{let n=U(K,e.__scopeMenu),{forceMount:i=n.forceMount,...a}=e,u=T(K,e.__scopeMenu),s=N(K,e.__scopeMenu),d=eC(eR,e.__scopeMenu),c=r.useRef(null),g=(0,l.s)(t,c);return(0,S.jsx)(I.Provider,{scope:e.__scopeMenu,children:(0,S.jsx)(v.C,{present:i||u.open,children:(0,S.jsx)(I.Slot,{scope:e.__scopeMenu,children:(0,S.jsx)(ee,{id:d.contentId,"aria-labelledby":d.triggerId,...a,ref:g,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{s.isUsingKeyboardRef.current&&c.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=F[s.dir].includes(e.key);t&&n&&(u.onOpenChange(!1),d.trigger?.focus(),e.preventDefault())})})})})})});function eE(e){return e?"open":"closed"}function eP(e){return"indeterminate"===e}function eF(e){return eP(e)?"indeterminate":e?"checked":"unchecked"}function eL(e){return t=>"mouse"===t.pointerType?e(t):void 0}eM.displayName=eR;var eI="DropdownMenu",[ek,eV]=(0,i.A)(eI,[D]),eA=D(),[eD,e_]=ek(eI),ej=e=>{let{__scopeDropdownMenu:t,children:n,dir:o,open:l,defaultOpen:i,onOpenChange:u,modal:s=!0}=e,d=eA(t),c=r.useRef(null),[g,f]=(0,a.i)({prop:l,defaultProp:i??!1,onChange:u,caller:eI});return(0,S.jsx)(eD,{scope:t,triggerId:(0,p.B)(),triggerRef:c,contentId:(0,p.B)(),open:g,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),modal:s,children:(0,S.jsx)(z,{...d,open:g,onOpenChange:f,dir:o,modal:s,children:n})})};ej.displayName=eI;var eO="DropdownMenuTrigger",eT=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...i}=e,a=e_(eO,n),s=eA(n);return(0,S.jsx)(G,{asChild:!0,...s,children:(0,S.jsx)(u.sG.button,{type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...i,ref:(0,l.t)(t,a.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(a.onOpenToggle(),a.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&a.onOpenToggle(),"ArrowDown"===e.key&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eT.displayName=eO;var eH=e=>{let{__scopeDropdownMenu:t,...n}=e,r=eA(t);return(0,S.jsx)(q,{...r,...n})};eH.displayName="DropdownMenuPortal";var eN="DropdownMenuContent",ez=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...l}=e,i=e_(eN,n),a=eA(n),u=r.useRef(!1);return(0,S.jsx)(X,{id:i.contentId,"aria-labelledby":i.triggerId,...a,...l,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{u.current||i.triggerRef.current?.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!i.modal||r)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});ez.displayName=eN,r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eA(n);return(0,S.jsx)(et,{...o,...r,ref:t})}).displayName="DropdownMenuGroup",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eA(n);return(0,S.jsx)(en,{...o,...r,ref:t})}).displayName="DropdownMenuLabel";var eG=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eA(n);return(0,S.jsx)(el,{...o,...r,ref:t})});eG.displayName="DropdownMenuItem";var eB=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eA(n);return(0,S.jsx)(ea,{...o,...r,ref:t})});eB.displayName="DropdownMenuCheckboxItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eA(n);return(0,S.jsx)(ec,{...o,...r,ref:t})}).displayName="DropdownMenuRadioGroup",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eA(n);return(0,S.jsx)(ef,{...o,...r,ref:t})}).displayName="DropdownMenuRadioItem";var eW=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eA(n);return(0,S.jsx)(ev,{...o,...r,ref:t})});eW.displayName="DropdownMenuItemIndicator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eA(n);return(0,S.jsx)(ew,{...o,...r,ref:t})}).displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eA(n);return(0,S.jsx)(eb,{...o,...r,ref:t})}).displayName="DropdownMenuArrow",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eA(n);return(0,S.jsx)(eS,{...o,...r,ref:t})}).displayName="DropdownMenuSubTrigger",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eA(n);return(0,S.jsx)(eM,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var eU=ej,eq=eT,eK=eH,eZ=ez,e$=eG,eX=eB,eY=eW},32547:(e,t,n)=>{n.d(t,{n:()=>c});var r=n(43210),o=n(98599),l=n(14163),i=n(13495),a=n(60687),u="focusScope.autoFocusOnMount",s="focusScope.autoFocusOnUnmount",d={bubbles:!1,cancelable:!0},c=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:c=!1,onMountAutoFocus:m,onUnmountAutoFocus:v,...w}=e,[b,y]=r.useState(null),C=(0,i.c)(m),x=(0,i.c)(v),S=r.useRef(null),R=(0,o.s)(t,e=>y(e)),M=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(c){let e=function(e){if(M.paused||!b)return;let t=e.target;b.contains(t)?S.current=t:p(S.current,{select:!0})},t=function(e){if(M.paused||!b)return;let t=e.relatedTarget;null!==t&&(b.contains(t)||p(S.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&p(b)});return b&&n.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[c,b,M.paused]),r.useEffect(()=>{if(b){h.add(M);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(u,d);b.addEventListener(u,C),b.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(p(r,{select:t}),document.activeElement!==n)return}(g(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&p(b))}return()=>{b.removeEventListener(u,C),setTimeout(()=>{let t=new CustomEvent(s,d);b.addEventListener(s,x),b.dispatchEvent(t),t.defaultPrevented||p(e??document.body,{select:!0}),b.removeEventListener(s,x),h.remove(M)},0)}}},[b,C,x,M]);let E=r.useCallback(e=>{if(!n&&!c||M.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,l]=function(e){let t=g(e);return[f(t,e),f(t.reverse(),e)]}(t);o&&l?e.shiftKey||r!==l?e.shiftKey&&r===o&&(e.preventDefault(),n&&p(l,{select:!0})):(e.preventDefault(),n&&p(o,{select:!0})):r===t&&e.preventDefault()}},[n,c,M.paused]);return(0,a.jsx)(l.sG.div,{tabIndex:-1,...w,ref:R,onKeyDown:E})});function g(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function f(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function p(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}c.displayName="FocusScope";var h=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=m(e,t)).unshift(t)},remove(t){e=m(e,t),e[0]?.resume()}}}();function m(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},40083:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("log-out",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},42247:(e,t,n)=>{n.d(t,{A:()=>q});var r,o,l=function(){return(l=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,n(43210)),u="right-scroll-bar-position",s="width-before-scroll-bar";function d(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var c="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,g=new WeakMap;function f(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,o,i=(t=null,void 0===n&&(n=f),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var l=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(l)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return i.options=l({async:!0,ssr:!1},e),i}(),h=function(){},m=a.forwardRef(function(e,t){var n,r,o,u,s=a.useRef(null),f=a.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),m=f[0],v=f[1],w=e.forwardProps,b=e.children,y=e.className,C=e.removeScrollBar,x=e.enabled,S=e.shards,R=e.sideCar,M=e.noIsolation,E=e.inert,P=e.allowPinchZoom,F=e.as,L=e.gapMode,I=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),k=(n=[s,t],r=function(e){return n.forEach(function(t){return d(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,c(function(){var e=g.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||d(e,null)}),r.forEach(function(e){t.has(e)||d(e,o)})}g.set(u,n)},[n]),u),V=l(l({},I),m);return a.createElement(a.Fragment,null,x&&a.createElement(R,{sideCar:p,removeScrollBar:C,shards:S,noIsolation:M,inert:E,setCallbacks:v,allowPinchZoom:!!P,lockRef:s,gapMode:L}),w?a.cloneElement(a.Children.only(b),l(l({},V),{ref:k})):a.createElement(void 0===F?"div":F,l({},V,{className:y,ref:k}),b))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:s,zeroRight:u};var v=function(e){var t=e.sideCar,n=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,l({},n))};v.isSideCarExport=!0;var w=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var l,i;(l=t).styleSheet?l.styleSheet.cssText=r:l.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},b=function(){var e=w();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},y=function(){var e=b();return function(t){return e(t.styles,t.dynamic),null}},C={left:0,top:0,right:0,gap:0},x=function(e){return parseInt(e||"",10)||0},S=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[x(n),x(r),x(o)]},R=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return C;var t=S(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},M=y(),E="data-scroll-locked",P=function(e,t,n,r){var o=e.left,l=e.top,i=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(E,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(l,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(s," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(s," .").concat(s," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(E,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},F=function(){var e=parseInt(document.body.getAttribute(E)||"0",10);return isFinite(e)?e:0},L=function(){a.useEffect(function(){return document.body.setAttribute(E,(F()+1).toString()),function(){var e=F()-1;e<=0?document.body.removeAttribute(E):document.body.setAttribute(E,e.toString())}},[])},I=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;L();var l=a.useMemo(function(){return R(o)},[o]);return a.createElement(M,{styles:P(l,!t,o,n?"":"!important")})},k=!1;if("undefined"!=typeof window)try{var V=Object.defineProperty({},"passive",{get:function(){return k=!0,!0}});window.addEventListener("test",V,V),window.removeEventListener("test",V,V)}catch(e){k=!1}var A=!!k&&{passive:!1},D=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},_=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),j(e,r)){var o=O(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},j=function(e,t){return"v"===e?D(t,"overflowY"):D(t,"overflowX")},O=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},T=function(e,t,n,r,o){var l,i=(l=window.getComputedStyle(t).direction,"h"===e&&"rtl"===l?-1:1),a=i*r,u=n.target,s=t.contains(u),d=!1,c=a>0,g=0,f=0;do{var p=O(e,u),h=p[0],m=p[1]-p[2]-i*h;(h||m)&&j(e,u)&&(g+=m,f+=h),u=u instanceof ShadowRoot?u.host:u.parentNode}while(!s&&u!==document.body||s&&(t.contains(u)||t===u));return c&&(o&&1>Math.abs(g)||!o&&a>g)?d=!0:!c&&(o&&1>Math.abs(f)||!o&&-a>f)&&(d=!0),d},H=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},N=function(e){return[e.deltaX,e.deltaY]},z=function(e){return e&&"current"in e?e.current:e},G=0,B=[];let W=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(G++)[0],l=a.useState(y)[0],i=a.useRef(e);a.useEffect(function(){i.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,l=t.length;o<l;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(z),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,l=H(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-l[0],s="deltaY"in e?e.deltaY:a[1]-l[1],d=e.target,c=Math.abs(u)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===c&&"range"===d.type)return!1;var g=_(c,d);if(!g)return!0;if(g?o=c:(o="v"===c?"h":"v",g=_(c,d)),!g)return!1;if(!r.current&&"changedTouches"in e&&(u||s)&&(r.current=o),!o)return!0;var f=r.current||o;return T(f,t,e,"h"===f?u:s,!0)},[]),s=a.useCallback(function(e){if(B.length&&B[B.length-1]===l){var n="deltaY"in e?N(e):H(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(i.current.shards||[]).map(z).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),d=a.useCallback(function(e,n,r,o){var l={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(l),setTimeout(function(){t.current=t.current.filter(function(e){return e!==l})},1)},[]),c=a.useCallback(function(e){n.current=H(e),r.current=void 0},[]),g=a.useCallback(function(t){d(t.type,N(t),t.target,u(t,e.lockRef.current))},[]),f=a.useCallback(function(t){d(t.type,H(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return B.push(l),e.setCallbacks({onScrollCapture:g,onWheelCapture:g,onTouchMoveCapture:f}),document.addEventListener("wheel",s,A),document.addEventListener("touchmove",s,A),document.addEventListener("touchstart",c,A),function(){B=B.filter(function(e){return e!==l}),document.removeEventListener("wheel",s,A),document.removeEventListener("touchmove",s,A),document.removeEventListener("touchstart",c,A)}},[]);var p=e.removeScrollBar,h=e.inert;return a.createElement(a.Fragment,null,h?a.createElement(l,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,p?a.createElement(I,{gapMode:e.gapMode}):null)},p.useMedium(r),v);var U=a.forwardRef(function(e,t){return a.createElement(m,l({},e,{ref:t,sideCar:W}))});U.classNames=m.classNames;let q=U},44255:(e,t,n)=>{n.d(t,{Kt4:()=>i,_zY:()=>o,hJ0:()=>l});var r=n(90296);function o(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M19 22H5C3.34315 22 2 20.6569 2 19V3C2 2.44772 2.44772 2 3 2H17C17.5523 2 18 2.44772 18 3V15H22V19C22 20.6569 20.6569 22 19 22ZM18 17V19C18 19.5523 18.4477 20 19 20C19.5523 20 20 19.5523 20 19V17H18ZM6 7V9H14V7H6ZM6 11V13H14V11H6ZM6 15V17H11V15H6Z"},child:[]}]})(e)}function l(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M17 4H22V6H20V21C20 21.5523 19.5523 22 19 22H5C4.44772 22 4 21.5523 4 21V6H2V4H7V2H17V4ZM9 9V17H11V9H9ZM13 9V17H15V9H13Z"},child:[]}]})(e)}function i(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22ZM12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20ZM11 15H13V17H11V15ZM13 13.3551V14H11V12.5C11 11.9477 11.4477 11.5 12 11.5C12.8284 11.5 13.5 10.8284 13.5 10C13.5 9.17157 12.8284 8.5 12 8.5C11.2723 8.5 10.6656 9.01823 10.5288 9.70577L8.56731 9.31346C8.88637 7.70919 10.302 6.5 12 6.5C13.933 6.5 15.5 8.067 15.5 10C15.5 11.5855 14.4457 12.9248 13 13.3551Z"},child:[]}]})(e)}},56090:(e,t,n)=>{n.d(t,{Kv:()=>l,N4:()=>i});var r=n(43210),o=n(93772);function l(e,t){var n,o,l;return e?"function"==typeof(o=n=e)&&(()=>{let e=Object.getPrototypeOf(o);return e.prototype&&e.prototype.isReactComponent})()||"function"==typeof n||"object"==typeof(l=n)&&"symbol"==typeof l.$$typeof&&["react.memo","react.forward_ref"].includes(l.$$typeof.description)?r.createElement(e,t):e:null}function i(e){let t={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[n]=r.useState(()=>({current:(0,o.ZR)(t)})),[l,i]=r.useState(()=>n.current.initialState);return n.current.setOptions(t=>({...t,...e,state:{...l,...e.state},onStateChange:t=>{i(t),null==e.onStateChange||e.onStateChange(t)}})),n.current}},57678:(e,t,n)=>{n.d(t,{Mz:()=>A,i3:()=>_,UC:()=>D,bL:()=>V,Bk:()=>m});var r=n(43210),o=n(4503),l=n(25605),i=n(14163),a=n(60687),u=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...l}=e;return(0,a.jsx)(i.sG.svg,{...l,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,a.jsx)("polygon",{points:"0,0 30,0 15,10"})})});u.displayName="Arrow";var s=n(98599),d=n(11273),c=n(13495),g=n(66156),f=n(18853),p="Popper",[h,m]=(0,d.A)(p),[v,w]=h(p),b=e=>{let{__scopePopper:t,children:n}=e,[o,l]=r.useState(null);return(0,a.jsx)(v,{scope:t,anchor:o,onAnchorChange:l,children:n})};b.displayName=p;var y="PopperAnchor",C=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...l}=e,u=w(y,n),d=r.useRef(null),c=(0,s.s)(t,d);return r.useEffect(()=>{u.onAnchorChange(o?.current||d.current)}),o?null:(0,a.jsx)(i.sG.div,{...l,ref:c})});C.displayName=y;var x="PopperContent",[S,R]=h(x),M=r.forwardRef((e,t)=>{let{__scopePopper:n,side:u="bottom",sideOffset:d=0,align:p="center",alignOffset:h=0,arrowPadding:m=0,avoidCollisions:v=!0,collisionBoundary:b=[],collisionPadding:y=0,sticky:C="partial",hideWhenDetached:R=!1,updatePositionStrategy:M="optimized",onPlaced:E,...P}=e,F=w(x,n),[V,A]=r.useState(null),D=(0,s.s)(t,e=>A(e)),[_,j]=r.useState(null),O=(0,f.X)(_),T=O?.width??0,H=O?.height??0,N="number"==typeof y?y:{top:0,right:0,bottom:0,left:0,...y},z=Array.isArray(b)?b:[b],G=z.length>0,B={padding:N,boundary:z.filter(L),altBoundary:G},{refs:W,floatingStyles:U,placement:q,isPositioned:K,middlewareData:Z}=(0,o.we)({strategy:"fixed",placement:u+("center"!==p?"-"+p:""),whileElementsMounted:(...e)=>(0,l.ll)(...e,{animationFrame:"always"===M}),elements:{reference:F.anchor},middleware:[(0,o.cY)({mainAxis:d+H,alignmentAxis:h}),v&&(0,o.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===C?(0,o.ER)():void 0,...B}),v&&(0,o.UU)({...B}),(0,o.Ej)({...B,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:l}=t.reference,i=e.floating.style;i.setProperty("--radix-popper-available-width",`${n}px`),i.setProperty("--radix-popper-available-height",`${r}px`),i.setProperty("--radix-popper-anchor-width",`${o}px`),i.setProperty("--radix-popper-anchor-height",`${l}px`)}}),_&&(0,o.UE)({element:_,padding:m}),I({arrowWidth:T,arrowHeight:H}),R&&(0,o.jD)({strategy:"referenceHidden",...B})]}),[$,X]=k(q),Y=(0,c.c)(E);(0,g.N)(()=>{K&&Y?.()},[K,Y]);let J=Z.arrow?.x,Q=Z.arrow?.y,ee=Z.arrow?.centerOffset!==0,[et,en]=r.useState();return(0,g.N)(()=>{V&&en(window.getComputedStyle(V).zIndex)},[V]),(0,a.jsx)("div",{ref:W.setFloating,"data-radix-popper-content-wrapper":"",style:{...U,transform:K?U.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:et,"--radix-popper-transform-origin":[Z.transformOrigin?.x,Z.transformOrigin?.y].join(" "),...Z.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,a.jsx)(S,{scope:n,placedSide:$,onArrowChange:j,arrowX:J,arrowY:Q,shouldHideArrow:ee,children:(0,a.jsx)(i.sG.div,{"data-side":$,"data-align":X,...P,ref:D,style:{...P.style,animation:K?void 0:"none"}})})})});M.displayName=x;var E="PopperArrow",P={top:"bottom",right:"left",bottom:"top",left:"right"},F=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=R(E,n),l=P[o.placedSide];return(0,a.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,a.jsx)(u,{...r,ref:t,style:{...r.style,display:"block"}})})});function L(e){return null!==e}F.displayName=E;var I=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,l=o.arrow?.centerOffset!==0,i=l?0:e.arrowWidth,a=l?0:e.arrowHeight,[u,s]=k(n),d={start:"0%",center:"50%",end:"100%"}[s],c=(o.arrow?.x??0)+i/2,g=(o.arrow?.y??0)+a/2,f="",p="";return"bottom"===u?(f=l?d:`${c}px`,p=`${-a}px`):"top"===u?(f=l?d:`${c}px`,p=`${r.floating.height+a}px`):"right"===u?(f=`${-a}px`,p=l?d:`${g}px`):"left"===u&&(f=`${r.floating.width+a}px`,p=l?d:`${g}px`),{data:{x:f,y:p}}}});function k(e){let[t,n="center"]=e.split("-");return[t,n]}var V=b,A=C,D=M,_=F},57800:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},63376:(e,t,n)=>{n.d(t,{Eq:()=>d});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,l=new WeakMap,i={},a=0,u=function(e){return e&&(e.host||u(e.parentNode))},s=function(e,t,n,r){var s=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[n]||(i[n]=new WeakMap);var d=i[n],c=[],g=new Set,f=new Set(s),p=function(e){!e||g.has(e)||(g.add(e),p(e.parentNode))};s.forEach(p);var h=function(e){!e||f.has(e)||Array.prototype.forEach.call(e.children,function(e){if(g.has(e))h(e);else try{var t=e.getAttribute(r),i=null!==t&&"false"!==t,a=(o.get(e)||0)+1,u=(d.get(e)||0)+1;o.set(e,a),d.set(e,u),c.push(e),1===a&&i&&l.set(e,!0),1===u&&e.setAttribute(n,"true"),i||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),g.clear(),a++,function(){c.forEach(function(e){var t=o.get(e)-1,i=d.get(e)-1;o.set(e,t),d.set(e,i),t||(l.has(e)||e.removeAttribute(r),l.delete(e)),i||e.removeAttribute(n)}),--a||(o=new WeakMap,o=new WeakMap,l=new WeakMap,i={})}},d=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),l=t||r(e);return l?(o.push.apply(o,Array.from(l.querySelectorAll("[aria-live]"))),s(o,l,n,"aria-hidden")):function(){return null}}},69587:(e,t,n)=>{n.d(t,{M1W:()=>a,NPy:()=>u,Ucs:()=>i,Vr3:()=>l,Wlj:()=>o});var r=n(90296);function o(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M32 448c0 17.7 14.3 32 32 32h384c17.7 0 32-14.3 32-32V160H32v288zm160-212c0-6.6 5.4-12 12-12h104c6.6 0 12 5.4 12 12v8c0 6.6-5.4 12-12 12H204c-6.6 0-12-5.4-12-12v-8zM480 32H32C14.3 32 0 46.3 0 64v48c0 8.8 7.2 16 16 16h480c8.8 0 16-7.2 16-16V64c0-17.7-14.3-32-32-32z"},child:[]}]})(e)}function l(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M207.029 381.476L12.686 187.132c-9.373-9.373-9.373-24.569 0-33.941l22.667-22.667c9.357-9.357 24.522-9.375 33.901-.04L224 284.505l154.745-154.021c9.379-9.335 24.544-9.317 33.901.04l22.667 22.667c9.373 9.373 9.373 24.569 0 33.941L240.971 381.476c-9.373 9.372-24.569 9.372-33.942 0z"},child:[]}]})(e)}function i(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M240.971 130.524l194.343 194.343c9.373 9.373 9.373 24.569 0 33.941l-22.667 22.667c-9.357 9.357-24.522 9.375-33.901.04L224 227.495 69.255 381.516c-9.379 9.335-24.544 9.317-33.901-.04l-22.667-22.667c-9.373-9.373-9.373-24.569 0-33.941L207.03 130.525c9.372-9.373 24.568-9.373 33.941-.001z"},child:[]}]})(e)}function a(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M464 128H272l-64-64H48C21.49 64 0 85.49 0 112v288c0 26.51 21.49 48 48 48h416c26.51 0 48-21.49 48-48V176c0-26.51-21.49-48-48-48z"},child:[]}]})(e)}function u(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M624 208h-64v-64c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16v64h-64c-8.8 0-16 7.2-16 16v32c0 8.8 7.2 16 16 16h64v64c0 8.8 7.2 16 16 16h32c8.8 0 16-7.2 16-16v-64h64c8.8 0 16-7.2 16-16v-32c0-8.8-7.2-16-16-16zm-400 48c70.7 0 128-57.3 128-128S294.7 0 224 0 96 57.3 96 128s57.3 128 128 128zm89.6 32h-16.7c-22.2 10.2-46.9 16-72.9 16s-50.6-5.8-72.9-16h-16.7C60.2 288 0 348.2 0 422.4V464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48v-41.6c0-74.2-60.2-134.4-134.4-134.4z"},child:[]}]})(e)}},79962:(e,t,n)=>{n.d(t,{$4x:()=>o,zFA:()=>l});var r=n(90296);function o(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M9 3a2 2 0 0 1 2 2v6a2 2 0 0 1 -2 2h-4a2 2 0 0 1 -2 -2v-6a2 2 0 0 1 2 -2zm0 12a2 2 0 0 1 2 2v2a2 2 0 0 1 -2 2h-4a2 2 0 0 1 -2 -2v-2a2 2 0 0 1 2 -2zm10 -4a2 2 0 0 1 2 2v6a2 2 0 0 1 -2 2h-4a2 2 0 0 1 -2 -2v-6a2 2 0 0 1 2 -2zm0 -8a2 2 0 0 1 2 2v2a2 2 0 0 1 -2 2h-4a2 2 0 0 1 -2 -2v-2a2 2 0 0 1 2 -2z"},child:[]}]})(e)}function l(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M4 4m0 1a1 1 0 0 1 1 -1h14a1 1 0 0 1 1 1v2a1 1 0 0 1 -1 1h-14a1 1 0 0 1 -1 -1z"},child:[]},{tag:"path",attr:{d:"M4 12m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v6a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"},child:[]},{tag:"path",attr:{d:"M14 12l6 0"},child:[]},{tag:"path",attr:{d:"M14 16l6 0"},child:[]},{tag:"path",attr:{d:"M14 20l6 0"},child:[]}]})(e)}},90270:(e,t,n)=>{n.d(t,{bL:()=>x,zi:()=>S});var r=n(43210),o=n(70569),l=n(98599),i=n(11273),a=n(65551),u=n(83721),s=n(18853),d=n(14163),c=n(60687),g="Switch",[f,p]=(0,i.A)(g),[h,m]=f(g),v=r.forwardRef((e,t)=>{let{__scopeSwitch:n,name:i,checked:u,defaultChecked:s,required:f,disabled:p,value:m="on",onCheckedChange:v,form:w,...b}=e,[x,S]=r.useState(null),R=(0,l.s)(t,e=>S(e)),M=r.useRef(!1),E=!x||w||!!x.closest("form"),[P,F]=(0,a.i)({prop:u,defaultProp:s??!1,onChange:v,caller:g});return(0,c.jsxs)(h,{scope:n,checked:P,disabled:p,children:[(0,c.jsx)(d.sG.button,{type:"button",role:"switch","aria-checked":P,"aria-required":f,"data-state":C(P),"data-disabled":p?"":void 0,disabled:p,value:m,...b,ref:R,onClick:(0,o.m)(e.onClick,e=>{F(e=>!e),E&&(M.current=e.isPropagationStopped(),M.current||e.stopPropagation())})}),E&&(0,c.jsx)(y,{control:x,bubbles:!M.current,name:i,value:m,checked:P,required:f,disabled:p,form:w,style:{transform:"translateX(-100%)"}})]})});v.displayName=g;var w="SwitchThumb",b=r.forwardRef((e,t)=>{let{__scopeSwitch:n,...r}=e,o=m(w,n);return(0,c.jsx)(d.sG.span,{"data-state":C(o.checked),"data-disabled":o.disabled?"":void 0,...r,ref:t})});b.displayName=w;var y=r.forwardRef(({__scopeSwitch:e,control:t,checked:n,bubbles:o=!0,...i},a)=>{let d=r.useRef(null),g=(0,l.s)(d,a),f=(0,u.Z)(n),p=(0,s.X)(t);return r.useEffect(()=>{let e=d.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(f!==n&&t){let r=new Event("click",{bubbles:o});t.call(e,n),e.dispatchEvent(r)}},[f,n,o]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...i,tabIndex:-1,ref:g,style:{...i.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function C(e){return e?"checked":"unchecked"}y.displayName="SwitchBubbleInput";var x=v,S=b},90296:(e,t,n)=>{n.d(t,{k5:()=>d});var r=n(43210),o={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},l=r.createContext&&r.createContext(o),i=["attr","size","title"];function a(){return(a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach(function(t){var r,o,l;r=e,o=t,l=n[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(o))in r?Object.defineProperty(r,o,{value:l,enumerable:!0,configurable:!0,writable:!0}):r[o]=l}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function d(e){return t=>r.createElement(c,a({attr:s({},e.attr)},t),function e(t){return t&&t.map((t,n)=>r.createElement(t.tag,s({key:n},t.attr),e(t.child)))}(e.child))}function c(e){var t=t=>{var n,{attr:o,size:l,title:u}=e,d=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(r=0;r<l.length;r++)n=l[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,i),c=l||t.size||"1em";return t.className&&(n=t.className),e.className&&(n=(n?n+" ":"")+e.className),r.createElement("svg",a({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,o,d,{className:n,style:s(s({color:e.color||t.color},t.style),e.style),height:c,width:c,xmlns:"http://www.w3.org/2000/svg"}),u&&r.createElement("title",null,u),e.children)};return void 0!==l?r.createElement(l.Consumer,null,e=>t(e)):t(o)}},90471:(e,t,n)=>{n.d(t,{ZK4:()=>o});var r=n(90296);function o(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"m184 112 144 144-144 144"},child:[]}]})(e)}},92010:(e,t,n)=>{n.d(t,{qW:()=>f});var r,o=n(43210),l=n(70569),i=n(14163),a=n(98599),u=n(13495),s=n(16309),d=n(60687),c="dismissableLayer.update",g=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:f,onPointerDownOutside:m,onFocusOutside:v,onInteractOutside:w,onDismiss:b,...y}=e,C=o.useContext(g),[x,S]=o.useState(null),R=x?.ownerDocument??globalThis?.document,[,M]=o.useState({}),E=(0,a.s)(t,e=>S(e)),P=Array.from(C.layers),[F]=[...C.layersWithOutsidePointerEventsDisabled].slice(-1),L=P.indexOf(F),I=x?P.indexOf(x):-1,k=C.layersWithOutsidePointerEventsDisabled.size>0,V=I>=L,A=function(e,t=globalThis?.document){let n=(0,u.c)(e),r=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){h("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",l.current),l.current=r,t.addEventListener("click",l.current,{once:!0})):r()}else t.removeEventListener("click",l.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",l.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...C.branches].some(e=>e.contains(t));V&&!n&&(m?.(e),w?.(e),e.defaultPrevented||b?.())},R),D=function(e,t=globalThis?.document){let n=(0,u.c)(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&h("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...C.branches].some(e=>e.contains(t))&&(v?.(e),w?.(e),e.defaultPrevented||b?.())},R);return(0,s.U)(e=>{I===C.layers.size-1&&(f?.(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},R),o.useEffect(()=>{if(x)return n&&(0===C.layersWithOutsidePointerEventsDisabled.size&&(r=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),C.layersWithOutsidePointerEventsDisabled.add(x)),C.layers.add(x),p(),()=>{n&&1===C.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=r)}},[x,R,n,C]),o.useEffect(()=>()=>{x&&(C.layers.delete(x),C.layersWithOutsidePointerEventsDisabled.delete(x),p())},[x,C]),o.useEffect(()=>{let e=()=>M({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,d.jsx)(i.sG.div,{...y,ref:E,style:{pointerEvents:k?V?"auto":"none":void 0,...e.style},onFocusCapture:(0,l.m)(e.onFocusCapture,D.onFocusCapture),onBlurCapture:(0,l.m)(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:(0,l.m)(e.onPointerDownCapture,A.onPointerDownCapture)})});function p(){let e=new CustomEvent(c);document.dispatchEvent(e)}function h(e,t,n,{discrete:r}){let o=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,i.hO)(o,l):o.dispatchEvent(l)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(g),r=o.useRef(null),l=(0,a.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,d.jsx)(i.sG.div,{...e,ref:l})}).displayName="DismissableLayerBranch"},93772:(e,t,n)=>{function r(e,t){return"function"==typeof e?e(t):e}function o(e,t){return n=>{t.setState(t=>({...t,[e]:r(n,t[e])}))}}function l(e){return e instanceof Function}n.d(t,{HT:()=>W,ZR:()=>B,h5:()=>K,hM:()=>U,kW:()=>q});function i(e,t,n){let r,o=[];return l=>{let i,a;n.key&&n.debug&&(i=Date.now());let u=e(l);if(!(u.length!==o.length||u.some((e,t)=>o[t]!==e)))return r;if(o=u,n.key&&n.debug&&(a=Date.now()),r=t(...u),null==n||null==n.onChange||n.onChange(r),n.key&&n.debug&&null!=n&&n.debug()){let e=Math.round((Date.now()-i)*100)/100,t=Math.round((Date.now()-a)*100)/100,r=t/16,o=(e,t)=>{for(e=String(e);e.length<t;)e=" "+e;return e};console.info(`%c⏱ ${o(t,5)} /${o(e,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*r,120))}deg 100% 31%);`,null==n?void 0:n.key)}return r}}function a(e,t,n,r){return{debug:()=>{var n;return null!=(n=null==e?void 0:e.debugAll)?n:e[t]},key:!1,onChange:r}}let u="debugHeaders";function s(e,t,n){var r;let o={id:null!=(r=n.id)?r:t.id,column:t,index:n.index,isPlaceholder:!!n.isPlaceholder,placeholderId:n.placeholderId,depth:n.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{let e=[],t=n=>{n.subHeaders&&n.subHeaders.length&&n.subHeaders.map(t),e.push(n)};return t(o),e},getContext:()=>({table:e,header:o,column:t})};return e._features.forEach(t=>{null==t.createHeader||t.createHeader(o,e)}),o}function d(e,t,n,r){var o,l;let i=0,a=function(e,t){void 0===t&&(t=1),i=Math.max(i,t),e.filter(e=>e.getIsVisible()).forEach(e=>{var n;null!=(n=e.columns)&&n.length&&a(e.columns,t+1)},0)};a(e);let u=[],d=(e,t)=>{let o={depth:t,id:[r,`${t}`].filter(Boolean).join("_"),headers:[]},l=[];e.forEach(e=>{let i,a=[...l].reverse()[0],u=e.column.depth===o.depth,d=!1;if(u&&e.column.parent?i=e.column.parent:(i=e.column,d=!0),a&&(null==a?void 0:a.column)===i)a.subHeaders.push(e);else{let o=s(n,i,{id:[r,t,i.id,null==e?void 0:e.id].filter(Boolean).join("_"),isPlaceholder:d,placeholderId:d?`${l.filter(e=>e.column===i).length}`:void 0,depth:t,index:l.length});o.subHeaders.push(e),l.push(o)}o.headers.push(e),e.headerGroup=o}),u.push(o),t>0&&d(l,t-1)};d(t.map((e,t)=>s(n,e,{depth:i,index:t})),i-1),u.reverse();let c=e=>e.filter(e=>e.column.getIsVisible()).map(e=>{let t=0,n=0,r=[0];return e.subHeaders&&e.subHeaders.length?(r=[],c(e.subHeaders).forEach(e=>{let{colSpan:n,rowSpan:o}=e;t+=n,r.push(o)})):t=1,n+=Math.min(...r),e.colSpan=t,e.rowSpan=n,{colSpan:t,rowSpan:n}});return c(null!=(o=null==(l=u[0])?void 0:l.headers)?o:[]),u}let c=(e,t,n,r,o,l,u)=>{let s={id:t,index:r,original:n,depth:o,parentId:u,_valuesCache:{},_uniqueValuesCache:{},getValue:t=>{if(s._valuesCache.hasOwnProperty(t))return s._valuesCache[t];let n=e.getColumn(t);if(null!=n&&n.accessorFn)return s._valuesCache[t]=n.accessorFn(s.original,r),s._valuesCache[t]},getUniqueValues:t=>{if(s._uniqueValuesCache.hasOwnProperty(t))return s._uniqueValuesCache[t];let n=e.getColumn(t);if(null!=n&&n.accessorFn)return n.columnDef.getUniqueValues?s._uniqueValuesCache[t]=n.columnDef.getUniqueValues(s.original,r):s._uniqueValuesCache[t]=[s.getValue(t)],s._uniqueValuesCache[t]},renderValue:t=>{var n;return null!=(n=s.getValue(t))?n:e.options.renderFallbackValue},subRows:null!=l?l:[],getLeafRows:()=>(function(e,t){let n=[],r=e=>{e.forEach(e=>{n.push(e);let o=t(e);null!=o&&o.length&&r(o)})};return r(e),n})(s.subRows,e=>e.subRows),getParentRow:()=>s.parentId?e.getRow(s.parentId,!0):void 0,getParentRows:()=>{let e=[],t=s;for(;;){let n=t.getParentRow();if(!n)break;e.push(n),t=n}return e.reverse()},getAllCells:i(()=>[e.getAllLeafColumns()],t=>t.map(t=>(function(e,t,n,r){let o={id:`${t.id}_${n.id}`,row:t,column:n,getValue:()=>t.getValue(r),renderValue:()=>{var t;return null!=(t=o.getValue())?t:e.options.renderFallbackValue},getContext:i(()=>[e,n,t,o],(e,t,n,r)=>({table:e,column:t,row:n,cell:r,getValue:r.getValue,renderValue:r.renderValue}),a(e.options,"debugCells","cell.getContext"))};return e._features.forEach(r=>{null==r.createCell||r.createCell(o,n,t,e)},{}),o})(e,s,t,t.id)),a(e.options,"debugRows","getAllCells")),_getAllCellsByColumnId:i(()=>[s.getAllCells()],e=>e.reduce((e,t)=>(e[t.column.id]=t,e),{}),a(e.options,"debugRows","getAllCellsByColumnId"))};for(let t=0;t<e._features.length;t++){let n=e._features[t];null==n||null==n.createRow||n.createRow(s,e)}return s},g=(e,t,n)=>{var r,o;let l=null==n||null==(r=n.toString())?void 0:r.toLowerCase();return!!(null==(o=e.getValue(t))||null==(o=o.toString())||null==(o=o.toLowerCase())?void 0:o.includes(l))};g.autoRemove=e=>x(e);let f=(e,t,n)=>{var r;return!!(null==(r=e.getValue(t))||null==(r=r.toString())?void 0:r.includes(n))};f.autoRemove=e=>x(e);let p=(e,t,n)=>{var r;return(null==(r=e.getValue(t))||null==(r=r.toString())?void 0:r.toLowerCase())===(null==n?void 0:n.toLowerCase())};p.autoRemove=e=>x(e);let h=(e,t,n)=>{var r;return null==(r=e.getValue(t))?void 0:r.includes(n)};h.autoRemove=e=>x(e);let m=(e,t,n)=>!n.some(n=>{var r;return!(null!=(r=e.getValue(t))&&r.includes(n))});m.autoRemove=e=>x(e)||!(null!=e&&e.length);let v=(e,t,n)=>n.some(n=>{var r;return null==(r=e.getValue(t))?void 0:r.includes(n)});v.autoRemove=e=>x(e)||!(null!=e&&e.length);let w=(e,t,n)=>e.getValue(t)===n;w.autoRemove=e=>x(e);let b=(e,t,n)=>e.getValue(t)==n;b.autoRemove=e=>x(e);let y=(e,t,n)=>{let[r,o]=n,l=e.getValue(t);return l>=r&&l<=o};y.resolveFilterValue=e=>{let[t,n]=e,r="number"!=typeof t?parseFloat(t):t,o="number"!=typeof n?parseFloat(n):n,l=null===t||Number.isNaN(r)?-1/0:r,i=null===n||Number.isNaN(o)?1/0:o;if(l>i){let e=l;l=i,i=e}return[l,i]},y.autoRemove=e=>x(e)||x(e[0])&&x(e[1]);let C={includesString:g,includesStringSensitive:f,equalsString:p,arrIncludes:h,arrIncludesAll:m,arrIncludesSome:v,equals:w,weakEquals:b,inNumberRange:y};function x(e){return null==e||""===e}function S(e,t,n){return!!e&&!!e.autoRemove&&e.autoRemove(t,n)||void 0===t||"string"==typeof t&&!t}let R={sum:(e,t,n)=>n.reduce((t,n)=>{let r=n.getValue(e);return t+("number"==typeof r?r:0)},0),min:(e,t,n)=>{let r;return n.forEach(t=>{let n=t.getValue(e);null!=n&&(r>n||void 0===r&&n>=n)&&(r=n)}),r},max:(e,t,n)=>{let r;return n.forEach(t=>{let n=t.getValue(e);null!=n&&(r<n||void 0===r&&n>=n)&&(r=n)}),r},extent:(e,t,n)=>{let r,o;return n.forEach(t=>{let n=t.getValue(e);null!=n&&(void 0===r?n>=n&&(r=o=n):(r>n&&(r=n),o<n&&(o=n)))}),[r,o]},mean:(e,t)=>{let n=0,r=0;if(t.forEach(t=>{let o=t.getValue(e);null!=o&&(o*=1)>=o&&(++n,r+=o)}),n)return r/n},median:(e,t)=>{if(!t.length)return;let n=t.map(t=>t.getValue(e));if(!function(e){return Array.isArray(e)&&e.every(e=>"number"==typeof e)}(n))return;if(1===n.length)return n[0];let r=Math.floor(n.length/2),o=n.sort((e,t)=>e-t);return n.length%2!=0?o[r]:(o[r-1]+o[r])/2},unique:(e,t)=>Array.from(new Set(t.map(t=>t.getValue(e))).values()),uniqueCount:(e,t)=>new Set(t.map(t=>t.getValue(e))).size,count:(e,t)=>t.length},M=()=>({left:[],right:[]}),E={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},P=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),F=null;function L(e){return"touchstart"===e.type}function I(e,t){return t?"center"===t?e.getCenterVisibleLeafColumns():"left"===t?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}let k=()=>({pageIndex:0,pageSize:10}),V=()=>({top:[],bottom:[]}),A=(e,t,n,r,o)=>{var l;let i=o.getRow(t,!0);n?(i.getCanMultiSelect()||Object.keys(e).forEach(t=>delete e[t]),i.getCanSelect()&&(e[t]=!0)):delete e[t],r&&null!=(l=i.subRows)&&l.length&&i.getCanSelectSubRows()&&i.subRows.forEach(t=>A(e,t.id,n,r,o))};function D(e,t){let n=e.getState().rowSelection,r=[],o={},l=function(e,t){return e.map(e=>{var t;let i=_(e,n);if(i&&(r.push(e),o[e.id]=e),null!=(t=e.subRows)&&t.length&&(e={...e,subRows:l(e.subRows)}),i)return e}).filter(Boolean)};return{rows:l(t.rows),flatRows:r,rowsById:o}}function _(e,t){var n;return null!=(n=t[e.id])&&n}function j(e,t,n){var r;if(!(null!=(r=e.subRows)&&r.length))return!1;let o=!0,l=!1;return e.subRows.forEach(e=>{if((!l||o)&&(e.getCanSelect()&&(_(e,t)?l=!0:o=!1),e.subRows&&e.subRows.length)){let n=j(e,t);"all"===n?l=!0:("some"===n&&(l=!0),o=!1)}}),o?"all":!!l&&"some"}let O=/([0-9]+)/gm;function T(e,t){return e===t?0:e>t?1:-1}function H(e){return"number"==typeof e?isNaN(e)||e===1/0||e===-1/0?"":String(e):"string"==typeof e?e:""}function N(e,t){let n=e.split(O).filter(Boolean),r=t.split(O).filter(Boolean);for(;n.length&&r.length;){let e=n.shift(),t=r.shift(),o=parseInt(e,10),l=parseInt(t,10),i=[o,l].sort();if(isNaN(i[0])){if(e>t)return 1;if(t>e)return -1;continue}if(isNaN(i[1]))return isNaN(o)?-1:1;if(o>l)return 1;if(l>o)return -1}return n.length-r.length}let z={alphanumeric:(e,t,n)=>N(H(e.getValue(n)).toLowerCase(),H(t.getValue(n)).toLowerCase()),alphanumericCaseSensitive:(e,t,n)=>N(H(e.getValue(n)),H(t.getValue(n))),text:(e,t,n)=>T(H(e.getValue(n)).toLowerCase(),H(t.getValue(n)).toLowerCase()),textCaseSensitive:(e,t,n)=>T(H(e.getValue(n)),H(t.getValue(n))),datetime:(e,t,n)=>{let r=e.getValue(n),o=t.getValue(n);return r>o?1:r<o?-1:0},basic:(e,t,n)=>T(e.getValue(n),t.getValue(n))},G=[{createTable:e=>{e.getHeaderGroups=i(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,r,o)=>{var l,i;let a=null!=(l=null==r?void 0:r.map(e=>n.find(t=>t.id===e)).filter(Boolean))?l:[],u=null!=(i=null==o?void 0:o.map(e=>n.find(t=>t.id===e)).filter(Boolean))?i:[];return d(t,[...a,...n.filter(e=>!(null!=r&&r.includes(e.id))&&!(null!=o&&o.includes(e.id))),...u],e)},a(e.options,u,"getHeaderGroups")),e.getCenterHeaderGroups=i(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,r,o)=>d(t,n=n.filter(e=>!(null!=r&&r.includes(e.id))&&!(null!=o&&o.includes(e.id))),e,"center"),a(e.options,u,"getCenterHeaderGroups")),e.getLeftHeaderGroups=i(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(t,n,r)=>{var o;return d(t,null!=(o=null==r?void 0:r.map(e=>n.find(t=>t.id===e)).filter(Boolean))?o:[],e,"left")},a(e.options,u,"getLeftHeaderGroups")),e.getRightHeaderGroups=i(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(t,n,r)=>{var o;return d(t,null!=(o=null==r?void 0:r.map(e=>n.find(t=>t.id===e)).filter(Boolean))?o:[],e,"right")},a(e.options,u,"getRightHeaderGroups")),e.getFooterGroups=i(()=>[e.getHeaderGroups()],e=>[...e].reverse(),a(e.options,u,"getFooterGroups")),e.getLeftFooterGroups=i(()=>[e.getLeftHeaderGroups()],e=>[...e].reverse(),a(e.options,u,"getLeftFooterGroups")),e.getCenterFooterGroups=i(()=>[e.getCenterHeaderGroups()],e=>[...e].reverse(),a(e.options,u,"getCenterFooterGroups")),e.getRightFooterGroups=i(()=>[e.getRightHeaderGroups()],e=>[...e].reverse(),a(e.options,u,"getRightFooterGroups")),e.getFlatHeaders=i(()=>[e.getHeaderGroups()],e=>e.map(e=>e.headers).flat(),a(e.options,u,"getFlatHeaders")),e.getLeftFlatHeaders=i(()=>[e.getLeftHeaderGroups()],e=>e.map(e=>e.headers).flat(),a(e.options,u,"getLeftFlatHeaders")),e.getCenterFlatHeaders=i(()=>[e.getCenterHeaderGroups()],e=>e.map(e=>e.headers).flat(),a(e.options,u,"getCenterFlatHeaders")),e.getRightFlatHeaders=i(()=>[e.getRightHeaderGroups()],e=>e.map(e=>e.headers).flat(),a(e.options,u,"getRightFlatHeaders")),e.getCenterLeafHeaders=i(()=>[e.getCenterFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),a(e.options,u,"getCenterLeafHeaders")),e.getLeftLeafHeaders=i(()=>[e.getLeftFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),a(e.options,u,"getLeftLeafHeaders")),e.getRightLeafHeaders=i(()=>[e.getRightFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),a(e.options,u,"getRightLeafHeaders")),e.getLeafHeaders=i(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(e,t,n)=>{var r,o,l,i,a,u;return[...null!=(r=null==(o=e[0])?void 0:o.headers)?r:[],...null!=(l=null==(i=t[0])?void 0:i.headers)?l:[],...null!=(a=null==(u=n[0])?void 0:u.headers)?a:[]].map(e=>e.getLeafHeaders()).flat()},a(e.options,u,"getLeafHeaders"))}},{getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:o("columnVisibility",e)}),createColumn:(e,t)=>{e.toggleVisibility=n=>{e.getCanHide()&&t.setColumnVisibility(t=>({...t,[e.id]:null!=n?n:!e.getIsVisible()}))},e.getIsVisible=()=>{var n,r;let o=e.columns;return null==(n=o.length?o.some(e=>e.getIsVisible()):null==(r=t.getState().columnVisibility)?void 0:r[e.id])||n},e.getCanHide=()=>{var n,r;return(null==(n=e.columnDef.enableHiding)||n)&&(null==(r=t.options.enableHiding)||r)},e.getToggleVisibilityHandler=()=>t=>{null==e.toggleVisibility||e.toggleVisibility(t.target.checked)}},createRow:(e,t)=>{e._getAllVisibleCells=i(()=>[e.getAllCells(),t.getState().columnVisibility],e=>e.filter(e=>e.column.getIsVisible()),a(t.options,"debugRows","_getAllVisibleCells")),e.getVisibleCells=i(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(e,t,n)=>[...e,...t,...n],a(t.options,"debugRows","getVisibleCells"))},createTable:e=>{let t=(t,n)=>i(()=>[n(),n().filter(e=>e.getIsVisible()).map(e=>e.id).join("_")],e=>e.filter(e=>null==e.getIsVisible?void 0:e.getIsVisible()),a(e.options,"debugColumns",t));e.getVisibleFlatColumns=t("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=t("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=t("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=t("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=t("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=t=>null==e.options.onColumnVisibilityChange?void 0:e.options.onColumnVisibilityChange(t),e.resetColumnVisibility=t=>{var n;e.setColumnVisibility(t?{}:null!=(n=e.initialState.columnVisibility)?n:{})},e.toggleAllColumnsVisible=t=>{var n;t=null!=(n=t)?n:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((e,n)=>({...e,[n.id]:t||!(null!=n.getCanHide&&n.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(e=>!(null!=e.getIsVisible&&e.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(e=>null==e.getIsVisible?void 0:e.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>t=>{var n;e.toggleAllColumnsVisible(null==(n=t.target)?void 0:n.checked)}}},{getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:o("columnOrder",e)}),createColumn:(e,t)=>{e.getIndex=i(e=>[I(t,e)],t=>t.findIndex(t=>t.id===e.id),a(t.options,"debugColumns","getIndex")),e.getIsFirstColumn=n=>{var r;return(null==(r=I(t,n)[0])?void 0:r.id)===e.id},e.getIsLastColumn=n=>{var r;let o=I(t,n);return(null==(r=o[o.length-1])?void 0:r.id)===e.id}},createTable:e=>{e.setColumnOrder=t=>null==e.options.onColumnOrderChange?void 0:e.options.onColumnOrderChange(t),e.resetColumnOrder=t=>{var n;e.setColumnOrder(t?[]:null!=(n=e.initialState.columnOrder)?n:[])},e._getOrderColumnsFn=i(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(e,t,n)=>r=>{let o=[];if(null!=e&&e.length){let t=[...e],n=[...r];for(;n.length&&t.length;){let e=t.shift(),r=n.findIndex(t=>t.id===e);r>-1&&o.push(n.splice(r,1)[0])}o=[...o,...n]}else o=r;return function(e,t,n){if(!(null!=t&&t.length)||!n)return e;let r=e.filter(e=>!t.includes(e.id));return"remove"===n?r:[...t.map(t=>e.find(e=>e.id===t)).filter(Boolean),...r]}(o,t,n)},a(e.options,"debugTable","_getOrderColumnsFn"))}},{getInitialState:e=>({columnPinning:M(),...e}),getDefaultOptions:e=>({onColumnPinningChange:o("columnPinning",e)}),createColumn:(e,t)=>{e.pin=n=>{let r=e.getLeafColumns().map(e=>e.id).filter(Boolean);t.setColumnPinning(e=>{var t,o,l,i,a,u;return"right"===n?{left:(null!=(l=null==e?void 0:e.left)?l:[]).filter(e=>!(null!=r&&r.includes(e))),right:[...(null!=(i=null==e?void 0:e.right)?i:[]).filter(e=>!(null!=r&&r.includes(e))),...r]}:"left"===n?{left:[...(null!=(a=null==e?void 0:e.left)?a:[]).filter(e=>!(null!=r&&r.includes(e))),...r],right:(null!=(u=null==e?void 0:e.right)?u:[]).filter(e=>!(null!=r&&r.includes(e)))}:{left:(null!=(t=null==e?void 0:e.left)?t:[]).filter(e=>!(null!=r&&r.includes(e))),right:(null!=(o=null==e?void 0:e.right)?o:[]).filter(e=>!(null!=r&&r.includes(e)))}})},e.getCanPin=()=>e.getLeafColumns().some(e=>{var n,r,o;return(null==(n=e.columnDef.enablePinning)||n)&&(null==(r=null!=(o=t.options.enableColumnPinning)?o:t.options.enablePinning)||r)}),e.getIsPinned=()=>{let n=e.getLeafColumns().map(e=>e.id),{left:r,right:o}=t.getState().columnPinning,l=n.some(e=>null==r?void 0:r.includes(e)),i=n.some(e=>null==o?void 0:o.includes(e));return l?"left":!!i&&"right"},e.getPinnedIndex=()=>{var n,r;let o=e.getIsPinned();return o?null!=(n=null==(r=t.getState().columnPinning)||null==(r=r[o])?void 0:r.indexOf(e.id))?n:-1:0}},createRow:(e,t)=>{e.getCenterVisibleCells=i(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,t.getState().columnPinning.right],(e,t,n)=>{let r=[...null!=t?t:[],...null!=n?n:[]];return e.filter(e=>!r.includes(e.column.id))},a(t.options,"debugRows","getCenterVisibleCells")),e.getLeftVisibleCells=i(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.column.id===t)).filter(Boolean).map(e=>({...e,position:"left"})),a(t.options,"debugRows","getLeftVisibleCells")),e.getRightVisibleCells=i(()=>[e._getAllVisibleCells(),t.getState().columnPinning.right],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.column.id===t)).filter(Boolean).map(e=>({...e,position:"right"})),a(t.options,"debugRows","getRightVisibleCells"))},createTable:e=>{e.setColumnPinning=t=>null==e.options.onColumnPinningChange?void 0:e.options.onColumnPinningChange(t),e.resetColumnPinning=t=>{var n,r;return e.setColumnPinning(t?M():null!=(n=null==(r=e.initialState)?void 0:r.columnPinning)?n:M())},e.getIsSomeColumnsPinned=t=>{var n,r,o;let l=e.getState().columnPinning;return t?!!(null==(n=l[t])?void 0:n.length):!!((null==(r=l.left)?void 0:r.length)||(null==(o=l.right)?void 0:o.length))},e.getLeftLeafColumns=i(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.id===t)).filter(Boolean),a(e.options,"debugColumns","getLeftLeafColumns")),e.getRightLeafColumns=i(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.id===t)).filter(Boolean),a(e.options,"debugColumns","getRightLeafColumns")),e.getCenterLeafColumns=i(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(e,t,n)=>{let r=[...null!=t?t:[],...null!=n?n:[]];return e.filter(e=>!r.includes(e.id))},a(e.options,"debugColumns","getCenterLeafColumns"))}},{createColumn:(e,t)=>{e._getFacetedRowModel=t.options.getFacetedRowModel&&t.options.getFacetedRowModel(t,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():t.getPreFilteredRowModel(),e._getFacetedUniqueValues=t.options.getFacetedUniqueValues&&t.options.getFacetedUniqueValues(t,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=t.options.getFacetedMinMaxValues&&t.options.getFacetedMinMaxValues(t,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},{getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:o("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,t)=>{e.getAutoFilterFn=()=>{let n=t.getCoreRowModel().flatRows[0],r=null==n?void 0:n.getValue(e.id);return"string"==typeof r?C.includesString:"number"==typeof r?C.inNumberRange:"boolean"==typeof r||null!==r&&"object"==typeof r?C.equals:Array.isArray(r)?C.arrIncludes:C.weakEquals},e.getFilterFn=()=>{var n,r;return l(e.columnDef.filterFn)?e.columnDef.filterFn:"auto"===e.columnDef.filterFn?e.getAutoFilterFn():null!=(n=null==(r=t.options.filterFns)?void 0:r[e.columnDef.filterFn])?n:C[e.columnDef.filterFn]},e.getCanFilter=()=>{var n,r,o;return(null==(n=e.columnDef.enableColumnFilter)||n)&&(null==(r=t.options.enableColumnFilters)||r)&&(null==(o=t.options.enableFilters)||o)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var n;return null==(n=t.getState().columnFilters)||null==(n=n.find(t=>t.id===e.id))?void 0:n.value},e.getFilterIndex=()=>{var n,r;return null!=(n=null==(r=t.getState().columnFilters)?void 0:r.findIndex(t=>t.id===e.id))?n:-1},e.setFilterValue=n=>{t.setColumnFilters(t=>{var o,l;let i=e.getFilterFn(),a=null==t?void 0:t.find(t=>t.id===e.id),u=r(n,a?a.value:void 0);if(S(i,u,e))return null!=(o=null==t?void 0:t.filter(t=>t.id!==e.id))?o:[];let s={id:e.id,value:u};return a?null!=(l=null==t?void 0:t.map(t=>t.id===e.id?s:t))?l:[]:null!=t&&t.length?[...t,s]:[s]})}},createRow:(e,t)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=t=>{let n=e.getAllLeafColumns();null==e.options.onColumnFiltersChange||e.options.onColumnFiltersChange(e=>{var o;return null==(o=r(t,e))?void 0:o.filter(e=>{let t=n.find(t=>t.id===e.id);return!(t&&S(t.getFilterFn(),e.value,t))&&!0})})},e.resetColumnFilters=t=>{var n,r;e.setColumnFilters(t?[]:null!=(n=null==(r=e.initialState)?void 0:r.columnFilters)?n:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel)?e.getPreFilteredRowModel():e._getFilteredRowModel()}},{createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},{getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:o("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:t=>{var n;let r=null==(n=e.getCoreRowModel().flatRows[0])||null==(n=n._getAllCellsByColumnId()[t.id])?void 0:n.getValue();return"string"==typeof r||"number"==typeof r}}),createColumn:(e,t)=>{e.getCanGlobalFilter=()=>{var n,r,o,l;return(null==(n=e.columnDef.enableGlobalFilter)||n)&&(null==(r=t.options.enableGlobalFilter)||r)&&(null==(o=t.options.enableFilters)||o)&&(null==(l=null==t.options.getColumnCanGlobalFilter?void 0:t.options.getColumnCanGlobalFilter(e))||l)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>C.includesString,e.getGlobalFilterFn=()=>{var t,n;let{globalFilterFn:r}=e.options;return l(r)?r:"auto"===r?e.getGlobalAutoFilterFn():null!=(t=null==(n=e.options.filterFns)?void 0:n[r])?t:C[r]},e.setGlobalFilter=t=>{null==e.options.onGlobalFilterChange||e.options.onGlobalFilterChange(t)},e.resetGlobalFilter=t=>{e.setGlobalFilter(t?void 0:e.initialState.globalFilter)}}},{getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:o("sorting",e),isMultiSortEvent:e=>e.shiftKey}),createColumn:(e,t)=>{e.getAutoSortingFn=()=>{let n=t.getFilteredRowModel().flatRows.slice(10),r=!1;for(let t of n){let n=null==t?void 0:t.getValue(e.id);if("[object Date]"===Object.prototype.toString.call(n))return z.datetime;if("string"==typeof n&&(r=!0,n.split(O).length>1))return z.alphanumeric}return r?z.text:z.basic},e.getAutoSortDir=()=>{let n=t.getFilteredRowModel().flatRows[0];return"string"==typeof(null==n?void 0:n.getValue(e.id))?"asc":"desc"},e.getSortingFn=()=>{var n,r;if(!e)throw Error();return l(e.columnDef.sortingFn)?e.columnDef.sortingFn:"auto"===e.columnDef.sortingFn?e.getAutoSortingFn():null!=(n=null==(r=t.options.sortingFns)?void 0:r[e.columnDef.sortingFn])?n:z[e.columnDef.sortingFn]},e.toggleSorting=(n,r)=>{let o=e.getNextSortingOrder(),l=null!=n;t.setSorting(i=>{let a,u=null==i?void 0:i.find(t=>t.id===e.id),s=null==i?void 0:i.findIndex(t=>t.id===e.id),d=[],c=l?n:"desc"===o;if("toggle"!=(a=null!=i&&i.length&&e.getCanMultiSort()&&r?u?"toggle":"add":null!=i&&i.length&&s!==i.length-1?"replace":u?"toggle":"replace")||l||o||(a="remove"),"add"===a){var g;(d=[...i,{id:e.id,desc:c}]).splice(0,d.length-(null!=(g=t.options.maxMultiSortColCount)?g:Number.MAX_SAFE_INTEGER))}else d="toggle"===a?i.map(t=>t.id===e.id?{...t,desc:c}:t):"remove"===a?i.filter(t=>t.id!==e.id):[{id:e.id,desc:c}];return d})},e.getFirstSortDir=()=>{var n,r;return(null!=(n=null!=(r=e.columnDef.sortDescFirst)?r:t.options.sortDescFirst)?n:"desc"===e.getAutoSortDir())?"desc":"asc"},e.getNextSortingOrder=n=>{var r,o;let l=e.getFirstSortDir(),i=e.getIsSorted();return i?(i===l||null!=(r=t.options.enableSortingRemoval)&&!r||!!n&&null!=(o=t.options.enableMultiRemove)&&!o)&&("desc"===i?"asc":"desc"):l},e.getCanSort=()=>{var n,r;return(null==(n=e.columnDef.enableSorting)||n)&&(null==(r=t.options.enableSorting)||r)&&!!e.accessorFn},e.getCanMultiSort=()=>{var n,r;return null!=(n=null!=(r=e.columnDef.enableMultiSort)?r:t.options.enableMultiSort)?n:!!e.accessorFn},e.getIsSorted=()=>{var n;let r=null==(n=t.getState().sorting)?void 0:n.find(t=>t.id===e.id);return!!r&&(r.desc?"desc":"asc")},e.getSortIndex=()=>{var n,r;return null!=(n=null==(r=t.getState().sorting)?void 0:r.findIndex(t=>t.id===e.id))?n:-1},e.clearSorting=()=>{t.setSorting(t=>null!=t&&t.length?t.filter(t=>t.id!==e.id):[])},e.getToggleSortingHandler=()=>{let n=e.getCanSort();return r=>{n&&(null==r.persist||r.persist(),null==e.toggleSorting||e.toggleSorting(void 0,!!e.getCanMultiSort()&&(null==t.options.isMultiSortEvent?void 0:t.options.isMultiSortEvent(r))))}}},createTable:e=>{e.setSorting=t=>null==e.options.onSortingChange?void 0:e.options.onSortingChange(t),e.resetSorting=t=>{var n,r;e.setSorting(t?[]:null!=(n=null==(r=e.initialState)?void 0:r.sorting)?n:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel)?e.getPreSortedRowModel():e._getSortedRowModel()}},{getDefaultColumnDef:()=>({aggregatedCell:e=>{var t,n;return null!=(t=null==(n=e.getValue())||null==n.toString?void 0:n.toString())?t:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:o("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,t)=>{e.toggleGrouping=()=>{t.setGrouping(t=>null!=t&&t.includes(e.id)?t.filter(t=>t!==e.id):[...null!=t?t:[],e.id])},e.getCanGroup=()=>{var n,r;return(null==(n=e.columnDef.enableGrouping)||n)&&(null==(r=t.options.enableGrouping)||r)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var n;return null==(n=t.getState().grouping)?void 0:n.includes(e.id)},e.getGroupedIndex=()=>{var n;return null==(n=t.getState().grouping)?void 0:n.indexOf(e.id)},e.getToggleGroupingHandler=()=>{let t=e.getCanGroup();return()=>{t&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{let n=t.getCoreRowModel().flatRows[0],r=null==n?void 0:n.getValue(e.id);return"number"==typeof r?R.sum:"[object Date]"===Object.prototype.toString.call(r)?R.extent:void 0},e.getAggregationFn=()=>{var n,r;if(!e)throw Error();return l(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:"auto"===e.columnDef.aggregationFn?e.getAutoAggregationFn():null!=(n=null==(r=t.options.aggregationFns)?void 0:r[e.columnDef.aggregationFn])?n:R[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=t=>null==e.options.onGroupingChange?void 0:e.options.onGroupingChange(t),e.resetGrouping=t=>{var n,r;e.setGrouping(t?[]:null!=(n=null==(r=e.initialState)?void 0:r.grouping)?n:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel)?e.getPreGroupedRowModel():e._getGroupedRowModel()},createRow:(e,t)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=n=>{if(e._groupingValuesCache.hasOwnProperty(n))return e._groupingValuesCache[n];let r=t.getColumn(n);return null!=r&&r.columnDef.getGroupingValue?(e._groupingValuesCache[n]=r.columnDef.getGroupingValue(e.original),e._groupingValuesCache[n]):e.getValue(n)},e._groupingValuesCache={}},createCell:(e,t,n,r)=>{e.getIsGrouped=()=>t.getIsGrouped()&&t.id===n.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&t.getIsGrouped(),e.getIsAggregated=()=>{var t;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!(null!=(t=n.subRows)&&t.length)}}},{getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:o("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let t=!1,n=!1;e._autoResetExpanded=()=>{var r,o;if(!t)return void e._queue(()=>{t=!0});if(null!=(r=null!=(o=e.options.autoResetAll)?o:e.options.autoResetExpanded)?r:!e.options.manualExpanding){if(n)return;n=!0,e._queue(()=>{e.resetExpanded(),n=!1})}},e.setExpanded=t=>null==e.options.onExpandedChange?void 0:e.options.onExpandedChange(t),e.toggleAllRowsExpanded=t=>{(null!=t?t:!e.getIsAllRowsExpanded())?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=t=>{var n,r;e.setExpanded(t?{}:null!=(n=null==(r=e.initialState)?void 0:r.expanded)?n:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(e=>e.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>t=>{null==t.persist||t.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{let t=e.getState().expanded;return!0===t||Object.values(t).some(Boolean)},e.getIsAllRowsExpanded=()=>{let t=e.getState().expanded;return"boolean"==typeof t?!0===t:!(!Object.keys(t).length||e.getRowModel().flatRows.some(e=>!e.getIsExpanded()))},e.getExpandedDepth=()=>{let t=0;return(!0===e.getState().expanded?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(e=>{let n=e.split(".");t=Math.max(t,n.length)}),t},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel)?e.getPreExpandedRowModel():e._getExpandedRowModel()},createRow:(e,t)=>{e.toggleExpanded=n=>{t.setExpanded(r=>{var o;let l=!0===r||!!(null!=r&&r[e.id]),i={};if(!0===r?Object.keys(t.getRowModel().rowsById).forEach(e=>{i[e]=!0}):i=r,n=null!=(o=n)?o:!l,!l&&n)return{...i,[e.id]:!0};if(l&&!n){let{[e.id]:t,...n}=i;return n}return r})},e.getIsExpanded=()=>{var n;let r=t.getState().expanded;return!!(null!=(n=null==t.options.getIsRowExpanded?void 0:t.options.getIsRowExpanded(e))?n:!0===r||(null==r?void 0:r[e.id]))},e.getCanExpand=()=>{var n,r,o;return null!=(n=null==t.options.getRowCanExpand?void 0:t.options.getRowCanExpand(e))?n:(null==(r=t.options.enableExpanding)||r)&&!!(null!=(o=e.subRows)&&o.length)},e.getIsAllParentsExpanded=()=>{let n=!0,r=e;for(;n&&r.parentId;)n=(r=t.getRow(r.parentId,!0)).getIsExpanded();return n},e.getToggleExpandedHandler=()=>{let t=e.getCanExpand();return()=>{t&&e.toggleExpanded()}}}},{getInitialState:e=>({...e,pagination:{...k(),...null==e?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:o("pagination",e)}),createTable:e=>{let t=!1,n=!1;e._autoResetPageIndex=()=>{var r,o;if(!t)return void e._queue(()=>{t=!0});if(null!=(r=null!=(o=e.options.autoResetAll)?o:e.options.autoResetPageIndex)?r:!e.options.manualPagination){if(n)return;n=!0,e._queue(()=>{e.resetPageIndex(),n=!1})}},e.setPagination=t=>null==e.options.onPaginationChange?void 0:e.options.onPaginationChange(e=>r(t,e)),e.resetPagination=t=>{var n;e.setPagination(t?k():null!=(n=e.initialState.pagination)?n:k())},e.setPageIndex=t=>{e.setPagination(n=>{let o=r(t,n.pageIndex);return o=Math.max(0,Math.min(o,void 0===e.options.pageCount||-1===e.options.pageCount?Number.MAX_SAFE_INTEGER:e.options.pageCount-1)),{...n,pageIndex:o}})},e.resetPageIndex=t=>{var n,r;e.setPageIndex(t?0:null!=(n=null==(r=e.initialState)||null==(r=r.pagination)?void 0:r.pageIndex)?n:0)},e.resetPageSize=t=>{var n,r;e.setPageSize(t?10:null!=(n=null==(r=e.initialState)||null==(r=r.pagination)?void 0:r.pageSize)?n:10)},e.setPageSize=t=>{e.setPagination(e=>{let n=Math.max(1,r(t,e.pageSize)),o=Math.floor(e.pageSize*e.pageIndex/n);return{...e,pageIndex:o,pageSize:n}})},e.setPageCount=t=>e.setPagination(n=>{var o;let l=r(t,null!=(o=e.options.pageCount)?o:-1);return"number"==typeof l&&(l=Math.max(-1,l)),{...n,pageCount:l}}),e.getPageOptions=i(()=>[e.getPageCount()],e=>{let t=[];return e&&e>0&&(t=[...Array(e)].fill(null).map((e,t)=>t)),t},a(e.options,"debugTable","getPageOptions")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{let{pageIndex:t}=e.getState().pagination,n=e.getPageCount();return -1===n||0!==n&&t<n-1},e.previousPage=()=>e.setPageIndex(e=>e-1),e.nextPage=()=>e.setPageIndex(e=>e+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel)?e.getPrePaginationRowModel():e._getPaginationRowModel(),e.getPageCount=()=>{var t;return null!=(t=e.options.pageCount)?t:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var t;return null!=(t=e.options.rowCount)?t:e.getPrePaginationRowModel().rows.length}}},{getInitialState:e=>({rowPinning:V(),...e}),getDefaultOptions:e=>({onRowPinningChange:o("rowPinning",e)}),createRow:(e,t)=>{e.pin=(n,r,o)=>{let l=r?e.getLeafRows().map(e=>{let{id:t}=e;return t}):[],i=new Set([...o?e.getParentRows().map(e=>{let{id:t}=e;return t}):[],e.id,...l]);t.setRowPinning(e=>{var t,r,o,l,a,u;return"bottom"===n?{top:(null!=(o=null==e?void 0:e.top)?o:[]).filter(e=>!(null!=i&&i.has(e))),bottom:[...(null!=(l=null==e?void 0:e.bottom)?l:[]).filter(e=>!(null!=i&&i.has(e))),...Array.from(i)]}:"top"===n?{top:[...(null!=(a=null==e?void 0:e.top)?a:[]).filter(e=>!(null!=i&&i.has(e))),...Array.from(i)],bottom:(null!=(u=null==e?void 0:e.bottom)?u:[]).filter(e=>!(null!=i&&i.has(e)))}:{top:(null!=(t=null==e?void 0:e.top)?t:[]).filter(e=>!(null!=i&&i.has(e))),bottom:(null!=(r=null==e?void 0:e.bottom)?r:[]).filter(e=>!(null!=i&&i.has(e)))}})},e.getCanPin=()=>{var n;let{enableRowPinning:r,enablePinning:o}=t.options;return"function"==typeof r?r(e):null==(n=null!=r?r:o)||n},e.getIsPinned=()=>{let n=[e.id],{top:r,bottom:o}=t.getState().rowPinning,l=n.some(e=>null==r?void 0:r.includes(e)),i=n.some(e=>null==o?void 0:o.includes(e));return l?"top":!!i&&"bottom"},e.getPinnedIndex=()=>{var n,r;let o=e.getIsPinned();if(!o)return -1;let l=null==(n="top"===o?t.getTopRows():t.getBottomRows())?void 0:n.map(e=>{let{id:t}=e;return t});return null!=(r=null==l?void 0:l.indexOf(e.id))?r:-1}},createTable:e=>{e.setRowPinning=t=>null==e.options.onRowPinningChange?void 0:e.options.onRowPinningChange(t),e.resetRowPinning=t=>{var n,r;return e.setRowPinning(t?V():null!=(n=null==(r=e.initialState)?void 0:r.rowPinning)?n:V())},e.getIsSomeRowsPinned=t=>{var n,r,o;let l=e.getState().rowPinning;return t?!!(null==(n=l[t])?void 0:n.length):!!((null==(r=l.top)?void 0:r.length)||(null==(o=l.bottom)?void 0:o.length))},e._getPinnedRows=(t,n,r)=>{var o;return(null==(o=e.options.keepPinnedRows)||o?(null!=n?n:[]).map(t=>{let n=e.getRow(t,!0);return n.getIsAllParentsExpanded()?n:null}):(null!=n?n:[]).map(e=>t.find(t=>t.id===e))).filter(Boolean).map(e=>({...e,position:r}))},e.getTopRows=i(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(t,n)=>e._getPinnedRows(t,n,"top"),a(e.options,"debugRows","getTopRows")),e.getBottomRows=i(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(t,n)=>e._getPinnedRows(t,n,"bottom"),a(e.options,"debugRows","getBottomRows")),e.getCenterRows=i(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(e,t,n)=>{let r=new Set([...null!=t?t:[],...null!=n?n:[]]);return e.filter(e=>!r.has(e.id))},a(e.options,"debugRows","getCenterRows"))}},{getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:o("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=t=>null==e.options.onRowSelectionChange?void 0:e.options.onRowSelectionChange(t),e.resetRowSelection=t=>{var n;return e.setRowSelection(t?{}:null!=(n=e.initialState.rowSelection)?n:{})},e.toggleAllRowsSelected=t=>{e.setRowSelection(n=>{t=void 0!==t?t:!e.getIsAllRowsSelected();let r={...n},o=e.getPreGroupedRowModel().flatRows;return t?o.forEach(e=>{e.getCanSelect()&&(r[e.id]=!0)}):o.forEach(e=>{delete r[e.id]}),r})},e.toggleAllPageRowsSelected=t=>e.setRowSelection(n=>{let r=void 0!==t?t:!e.getIsAllPageRowsSelected(),o={...n};return e.getRowModel().rows.forEach(t=>{A(o,t.id,r,!0,e)}),o}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=i(()=>[e.getState().rowSelection,e.getCoreRowModel()],(t,n)=>Object.keys(t).length?D(e,n):{rows:[],flatRows:[],rowsById:{}},a(e.options,"debugTable","getSelectedRowModel")),e.getFilteredSelectedRowModel=i(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(t,n)=>Object.keys(t).length?D(e,n):{rows:[],flatRows:[],rowsById:{}},a(e.options,"debugTable","getFilteredSelectedRowModel")),e.getGroupedSelectedRowModel=i(()=>[e.getState().rowSelection,e.getSortedRowModel()],(t,n)=>Object.keys(t).length?D(e,n):{rows:[],flatRows:[],rowsById:{}},a(e.options,"debugTable","getGroupedSelectedRowModel")),e.getIsAllRowsSelected=()=>{let t=e.getFilteredRowModel().flatRows,{rowSelection:n}=e.getState(),r=!!(t.length&&Object.keys(n).length);return r&&t.some(e=>e.getCanSelect()&&!n[e.id])&&(r=!1),r},e.getIsAllPageRowsSelected=()=>{let t=e.getPaginationRowModel().flatRows.filter(e=>e.getCanSelect()),{rowSelection:n}=e.getState(),r=!!t.length;return r&&t.some(e=>!n[e.id])&&(r=!1),r},e.getIsSomeRowsSelected=()=>{var t;let n=Object.keys(null!=(t=e.getState().rowSelection)?t:{}).length;return n>0&&n<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{let t=e.getPaginationRowModel().flatRows;return!e.getIsAllPageRowsSelected()&&t.filter(e=>e.getCanSelect()).some(e=>e.getIsSelected()||e.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>t=>{e.toggleAllRowsSelected(t.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>t=>{e.toggleAllPageRowsSelected(t.target.checked)}},createRow:(e,t)=>{e.toggleSelected=(n,r)=>{let o=e.getIsSelected();t.setRowSelection(l=>{var i;if(n=void 0!==n?n:!o,e.getCanSelect()&&o===n)return l;let a={...l};return A(a,e.id,n,null==(i=null==r?void 0:r.selectChildren)||i,t),a})},e.getIsSelected=()=>{let{rowSelection:n}=t.getState();return _(e,n)},e.getIsSomeSelected=()=>{let{rowSelection:n}=t.getState();return"some"===j(e,n)},e.getIsAllSubRowsSelected=()=>{let{rowSelection:n}=t.getState();return"all"===j(e,n)},e.getCanSelect=()=>{var n;return"function"==typeof t.options.enableRowSelection?t.options.enableRowSelection(e):null==(n=t.options.enableRowSelection)||n},e.getCanSelectSubRows=()=>{var n;return"function"==typeof t.options.enableSubRowSelection?t.options.enableSubRowSelection(e):null==(n=t.options.enableSubRowSelection)||n},e.getCanMultiSelect=()=>{var n;return"function"==typeof t.options.enableMultiRowSelection?t.options.enableMultiRowSelection(e):null==(n=t.options.enableMultiRowSelection)||n},e.getToggleSelectedHandler=()=>{let t=e.getCanSelect();return n=>{var r;t&&e.toggleSelected(null==(r=n.target)?void 0:r.checked)}}}},{getDefaultColumnDef:()=>E,getInitialState:e=>({columnSizing:{},columnSizingInfo:P(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:o("columnSizing",e),onColumnSizingInfoChange:o("columnSizingInfo",e)}),createColumn:(e,t)=>{e.getSize=()=>{var n,r,o;let l=t.getState().columnSizing[e.id];return Math.min(Math.max(null!=(n=e.columnDef.minSize)?n:E.minSize,null!=(r=null!=l?l:e.columnDef.size)?r:E.size),null!=(o=e.columnDef.maxSize)?o:E.maxSize)},e.getStart=i(e=>[e,I(t,e),t.getState().columnSizing],(t,n)=>n.slice(0,e.getIndex(t)).reduce((e,t)=>e+t.getSize(),0),a(t.options,"debugColumns","getStart")),e.getAfter=i(e=>[e,I(t,e),t.getState().columnSizing],(t,n)=>n.slice(e.getIndex(t)+1).reduce((e,t)=>e+t.getSize(),0),a(t.options,"debugColumns","getAfter")),e.resetSize=()=>{t.setColumnSizing(t=>{let{[e.id]:n,...r}=t;return r})},e.getCanResize=()=>{var n,r;return(null==(n=e.columnDef.enableResizing)||n)&&(null==(r=t.options.enableColumnResizing)||r)},e.getIsResizing=()=>t.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,t)=>{e.getSize=()=>{let t=0,n=e=>{if(e.subHeaders.length)e.subHeaders.forEach(n);else{var r;t+=null!=(r=e.column.getSize())?r:0}};return n(e),t},e.getStart=()=>{if(e.index>0){let t=e.headerGroup.headers[e.index-1];return t.getStart()+t.getSize()}return 0},e.getResizeHandler=n=>{let r=t.getColumn(e.column.id),o=null==r?void 0:r.getCanResize();return l=>{if(!r||!o||(null==l.persist||l.persist(),L(l)&&l.touches&&l.touches.length>1))return;let i=e.getSize(),a=e?e.getLeafHeaders().map(e=>[e.column.id,e.column.getSize()]):[[r.id,r.getSize()]],u=L(l)?Math.round(l.touches[0].clientX):l.clientX,s={},d=(e,n)=>{"number"==typeof n&&(t.setColumnSizingInfo(e=>{var r,o;let l="rtl"===t.options.columnResizeDirection?-1:1,i=(n-(null!=(r=null==e?void 0:e.startOffset)?r:0))*l,a=Math.max(i/(null!=(o=null==e?void 0:e.startSize)?o:0),-.999999);return e.columnSizingStart.forEach(e=>{let[t,n]=e;s[t]=Math.round(100*Math.max(n+n*a,0))/100}),{...e,deltaOffset:i,deltaPercentage:a}}),("onChange"===t.options.columnResizeMode||"end"===e)&&t.setColumnSizing(e=>({...e,...s})))},c=e=>d("move",e),g=e=>{d("end",e),t.setColumnSizingInfo(e=>({...e,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},f=n||("undefined"!=typeof document?document:null),p={moveHandler:e=>c(e.clientX),upHandler:e=>{null==f||f.removeEventListener("mousemove",p.moveHandler),null==f||f.removeEventListener("mouseup",p.upHandler),g(e.clientX)}},h={moveHandler:e=>(e.cancelable&&(e.preventDefault(),e.stopPropagation()),c(e.touches[0].clientX),!1),upHandler:e=>{var t;null==f||f.removeEventListener("touchmove",h.moveHandler),null==f||f.removeEventListener("touchend",h.upHandler),e.cancelable&&(e.preventDefault(),e.stopPropagation()),g(null==(t=e.touches[0])?void 0:t.clientX)}},m=!!function(){if("boolean"==typeof F)return F;let e=!1;try{let t=()=>{};window.addEventListener("test",t,{get passive(){return e=!0,!1}}),window.removeEventListener("test",t)}catch(t){e=!1}return F=e}()&&{passive:!1};L(l)?(null==f||f.addEventListener("touchmove",h.moveHandler,m),null==f||f.addEventListener("touchend",h.upHandler,m)):(null==f||f.addEventListener("mousemove",p.moveHandler,m),null==f||f.addEventListener("mouseup",p.upHandler,m)),t.setColumnSizingInfo(e=>({...e,startOffset:u,startSize:i,deltaOffset:0,deltaPercentage:0,columnSizingStart:a,isResizingColumn:r.id}))}}},createTable:e=>{e.setColumnSizing=t=>null==e.options.onColumnSizingChange?void 0:e.options.onColumnSizingChange(t),e.setColumnSizingInfo=t=>null==e.options.onColumnSizingInfoChange?void 0:e.options.onColumnSizingInfoChange(t),e.resetColumnSizing=t=>{var n;e.setColumnSizing(t?{}:null!=(n=e.initialState.columnSizing)?n:{})},e.resetHeaderSizeInfo=t=>{var n;e.setColumnSizingInfo(t?P():null!=(n=e.initialState.columnSizingInfo)?n:P())},e.getTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getLeftTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getLeftHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getCenterTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getCenterHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getRightTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getRightHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0}}}];function B(e){var t,n;let o=[...G,...null!=(t=e._features)?t:[]],l={_features:o},u=l._features.reduce((e,t)=>Object.assign(e,null==t.getDefaultOptions?void 0:t.getDefaultOptions(l)),{}),s=e=>l.options.mergeOptions?l.options.mergeOptions(u,e):{...u,...e},d={...null!=(n=e.initialState)?n:{}};l._features.forEach(e=>{var t;d=null!=(t=null==e.getInitialState?void 0:e.getInitialState(d))?t:d});let c=[],g=!1,f={_features:o,options:{...u,...e},initialState:d,_queue:e=>{c.push(e),g||(g=!0,Promise.resolve().then(()=>{for(;c.length;)c.shift()();g=!1}).catch(e=>setTimeout(()=>{throw e})))},reset:()=>{l.setState(l.initialState)},setOptions:e=>{let t=r(e,l.options);l.options=s(t)},getState:()=>l.options.state,setState:e=>{null==l.options.onStateChange||l.options.onStateChange(e)},_getRowId:(e,t,n)=>{var r;return null!=(r=null==l.options.getRowId?void 0:l.options.getRowId(e,t,n))?r:`${n?[n.id,t].join("."):t}`},getCoreRowModel:()=>(l._getCoreRowModel||(l._getCoreRowModel=l.options.getCoreRowModel(l)),l._getCoreRowModel()),getRowModel:()=>l.getPaginationRowModel(),getRow:(e,t)=>{let n=(t?l.getPrePaginationRowModel():l.getRowModel()).rowsById[e];if(!n&&!(n=l.getCoreRowModel().rowsById[e]))throw Error();return n},_getDefaultColumnDef:i(()=>[l.options.defaultColumn],e=>{var t;return e=null!=(t=e)?t:{},{header:e=>{let t=e.header.column.columnDef;return t.accessorKey?t.accessorKey:t.accessorFn?t.id:null},cell:e=>{var t,n;return null!=(t=null==(n=e.renderValue())||null==n.toString?void 0:n.toString())?t:null},...l._features.reduce((e,t)=>Object.assign(e,null==t.getDefaultColumnDef?void 0:t.getDefaultColumnDef()),{}),...e}},a(e,"debugColumns","_getDefaultColumnDef")),_getColumnDefs:()=>l.options.columns,getAllColumns:i(()=>[l._getColumnDefs()],e=>{let t=function(e,n,r){return void 0===r&&(r=0),e.map(e=>{let o=function(e,t,n,r){var o,l;let u,s={...e._getDefaultColumnDef(),...t},d=s.accessorKey,c=null!=(o=null!=(l=s.id)?l:d?"function"==typeof String.prototype.replaceAll?d.replaceAll(".","_"):d.replace(/\./g,"_"):void 0)?o:"string"==typeof s.header?s.header:void 0;if(s.accessorFn?u=s.accessorFn:d&&(u=d.includes(".")?e=>{let t=e;for(let e of d.split(".")){var n;t=null==(n=t)?void 0:n[e]}return t}:e=>e[s.accessorKey]),!c)throw Error();let g={id:`${String(c)}`,accessorFn:u,parent:r,depth:n,columnDef:s,columns:[],getFlatColumns:i(()=>[!0],()=>{var e;return[g,...null==(e=g.columns)?void 0:e.flatMap(e=>e.getFlatColumns())]},a(e.options,"debugColumns","column.getFlatColumns")),getLeafColumns:i(()=>[e._getOrderColumnsFn()],e=>{var t;return null!=(t=g.columns)&&t.length?e(g.columns.flatMap(e=>e.getLeafColumns())):[g]},a(e.options,"debugColumns","column.getLeafColumns"))};for(let t of e._features)null==t.createColumn||t.createColumn(g,e);return g}(l,e,r,n);return o.columns=e.columns?t(e.columns,o,r+1):[],o})};return t(e)},a(e,"debugColumns","getAllColumns")),getAllFlatColumns:i(()=>[l.getAllColumns()],e=>e.flatMap(e=>e.getFlatColumns()),a(e,"debugColumns","getAllFlatColumns")),_getAllFlatColumnsById:i(()=>[l.getAllFlatColumns()],e=>e.reduce((e,t)=>(e[t.id]=t,e),{}),a(e,"debugColumns","getAllFlatColumnsById")),getAllLeafColumns:i(()=>[l.getAllColumns(),l._getOrderColumnsFn()],(e,t)=>t(e.flatMap(e=>e.getLeafColumns())),a(e,"debugColumns","getAllLeafColumns")),getColumn:e=>l._getAllFlatColumnsById()[e]};Object.assign(l,f);for(let e=0;e<l._features.length;e++){let t=l._features[e];null==t||null==t.createTable||t.createTable(l)}return l}function W(){return e=>i(()=>[e.options.data],t=>{let n={rows:[],flatRows:[],rowsById:{}},r=function(t,o,l){void 0===o&&(o=0);let i=[];for(let u=0;u<t.length;u++){let s=c(e,e._getRowId(t[u],u,l),t[u],u,o,void 0,null==l?void 0:l.id);if(n.flatRows.push(s),n.rowsById[s.id]=s,i.push(s),e.options.getSubRows){var a;s.originalSubRows=e.options.getSubRows(t[u],u),null!=(a=s.originalSubRows)&&a.length&&(s.subRows=r(s.originalSubRows,o+1,s))}}return i};return n.rows=r(t),n},a(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}function U(){return e=>i(()=>[e.getPreFilteredRowModel(),e.getState().columnFilters,e.getState().globalFilter],(t,n,r)=>{var o,l,i;let a,u;if(!t.rows.length||!(null!=n&&n.length)&&!r){for(let e=0;e<t.flatRows.length;e++)t.flatRows[e].columnFilters={},t.flatRows[e].columnFiltersMeta={};return t}let s=[],d=[];(null!=n?n:[]).forEach(t=>{var n;let r=e.getColumn(t.id);if(!r)return;let o=r.getFilterFn();o&&s.push({id:t.id,filterFn:o,resolvedValue:null!=(n=null==o.resolveFilterValue?void 0:o.resolveFilterValue(t.value))?n:t.value})});let g=(null!=n?n:[]).map(e=>e.id),f=e.getGlobalFilterFn(),p=e.getAllLeafColumns().filter(e=>e.getCanGlobalFilter());r&&f&&p.length&&(g.push("__global__"),p.forEach(e=>{var t;d.push({id:e.id,filterFn:f,resolvedValue:null!=(t=null==f.resolveFilterValue?void 0:f.resolveFilterValue(r))?t:r})}));for(let e=0;e<t.flatRows.length;e++){let n=t.flatRows[e];if(n.columnFilters={},s.length)for(let e=0;e<s.length;e++){let t=(a=s[e]).id;n.columnFilters[t]=a.filterFn(n,t,a.resolvedValue,e=>{n.columnFiltersMeta[t]=e})}if(d.length){for(let e=0;e<d.length;e++){let t=(u=d[e]).id;if(u.filterFn(n,t,u.resolvedValue,e=>{n.columnFiltersMeta[t]=e})){n.columnFilters.__global__=!0;break}}!0!==n.columnFilters.__global__&&(n.columnFilters.__global__=!1)}}return o=t.rows,l=e=>{for(let t=0;t<g.length;t++)if(!1===e.columnFilters[g[t]])return!1;return!0},(i=e).options.filterFromLeafRows?function(e,t,n){var r;let o=[],l={},i=null!=(r=n.options.maxLeafRowFilterDepth)?r:100,a=function(e,r){void 0===r&&(r=0);let u=[];for(let d=0;d<e.length;d++){var s;let g=e[d],f=c(n,g.id,g.original,g.index,g.depth,void 0,g.parentId);if(f.columnFilters=g.columnFilters,null!=(s=g.subRows)&&s.length&&r<i){if(f.subRows=a(g.subRows,r+1),t(g=f)&&!f.subRows.length||t(g)||f.subRows.length){u.push(g),l[g.id]=g,o.push(g);continue}}else t(g=f)&&(u.push(g),l[g.id]=g,o.push(g))}return u};return{rows:a(e),flatRows:o,rowsById:l}}(o,l,i):function(e,t,n){var r;let o=[],l={},i=null!=(r=n.options.maxLeafRowFilterDepth)?r:100,a=function(e,r){void 0===r&&(r=0);let u=[];for(let d=0;d<e.length;d++){let g=e[d];if(t(g)){var s;if(null!=(s=g.subRows)&&s.length&&r<i){let e=c(n,g.id,g.original,g.index,g.depth,void 0,g.parentId);e.subRows=a(g.subRows,r+1),g=e}u.push(g),o.push(g),l[g.id]=g}}return u};return{rows:a(e),flatRows:o,rowsById:l}}(o,l,i)},a(e.options,"debugTable","getFilteredRowModel",()=>e._autoResetPageIndex()))}function q(e){return e=>i(()=>[e.getState().pagination,e.getPrePaginationRowModel(),e.options.paginateExpandedRows?void 0:e.getState().expanded],(t,n)=>{let r;if(!n.rows.length)return n;let{pageSize:o,pageIndex:l}=t,{rows:i,flatRows:a,rowsById:u}=n,s=o*l;i=i.slice(s,s+o),(r=e.options.paginateExpandedRows?{rows:i,flatRows:a,rowsById:u}:function(e){let t=[],n=e=>{var r;t.push(e),null!=(r=e.subRows)&&r.length&&e.getIsExpanded()&&e.subRows.forEach(n)};return e.rows.forEach(n),{rows:t,flatRows:e.flatRows,rowsById:e.rowsById}}({rows:i,flatRows:a,rowsById:u})).flatRows=[];let d=e=>{r.flatRows.push(e),e.subRows.length&&e.subRows.forEach(d)};return r.rows.forEach(d),r},a(e.options,"debugTable","getPaginationRowModel"))}function K(){return e=>i(()=>[e.getState().sorting,e.getPreSortedRowModel()],(t,n)=>{if(!n.rows.length||!(null!=t&&t.length))return n;let r=e.getState().sorting,o=[],l=r.filter(t=>{var n;return null==(n=e.getColumn(t.id))?void 0:n.getCanSort()}),i={};l.forEach(t=>{let n=e.getColumn(t.id);n&&(i[t.id]={sortUndefined:n.columnDef.sortUndefined,invertSorting:n.columnDef.invertSorting,sortingFn:n.getSortingFn()})});let a=e=>{let t=e.map(e=>({...e}));return t.sort((e,t)=>{for(let r=0;r<l.length;r+=1){var n;let o=l[r],a=i[o.id],u=a.sortUndefined,s=null!=(n=null==o?void 0:o.desc)&&n,d=0;if(u){let n=e.getValue(o.id),r=t.getValue(o.id),l=void 0===n,i=void 0===r;if(l||i){if("first"===u)return l?-1:1;if("last"===u)return l?1:-1;d=l&&i?0:l?u:-u}}if(0===d&&(d=a.sortingFn(e,t,o.id)),0!==d)return s&&(d*=-1),a.invertSorting&&(d*=-1),d}return e.index-t.index}),t.forEach(e=>{var t;o.push(e),null!=(t=e.subRows)&&t.length&&(e.subRows=a(e.subRows))}),t};return{rows:a(n.rows),flatRows:o,rowsById:n.rowsById}},a(e.options,"debugTable","getSortedRowModel",()=>e._autoResetPageIndex()))}},97822:(e,t,n)=>{n.d(t,{UC:()=>e_,YJ:()=>eO,In:()=>eA,q7:()=>eH,VF:()=>ez,p4:()=>eN,JU:()=>eT,ZL:()=>eD,bL:()=>eI,wn:()=>eB,PP:()=>eG,wv:()=>eW,l9:()=>ek,WT:()=>eV,LM:()=>ej});var r=n(43210),o=n(51215);function l(e,[t,n]){return Math.min(n,Math.max(t,e))}var i=n(70569),a=n(9510),u=n(98599),s=n(11273),d=n(43),c=n(92010),g=n(1359),f=n(32547),p=n(96963),h=n(57678),m=n(25028),v=n(14163),w=n(8730),b=n(13495),y=n(65551),C=n(66156),x=n(83721),S=n(60687),R=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});r.forwardRef((e,t)=>(0,S.jsx)(v.sG.span,{...e,ref:t,style:{...R,...e.style}})).displayName="VisuallyHidden";var M=n(63376),E=n(42247),P=[" ","Enter","ArrowUp","ArrowDown"],F=[" ","Enter"],L="Select",[I,k,V]=(0,a.N)(L),[A,D]=(0,s.A)(L,[V,h.Bk]),_=(0,h.Bk)(),[j,O]=A(L),[T,H]=A(L),N=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:l,onOpenChange:i,value:a,defaultValue:u,onValueChange:s,dir:c,name:g,autoComplete:f,disabled:m,required:v,form:w}=e,b=_(t),[C,x]=r.useState(null),[R,M]=r.useState(null),[E,P]=r.useState(!1),F=(0,d.jH)(c),[k,V]=(0,y.i)({prop:o,defaultProp:l??!1,onChange:i,caller:L}),[A,D]=(0,y.i)({prop:a,defaultProp:u,onChange:s,caller:L}),O=r.useRef(null),H=!C||w||!!C.closest("form"),[N,z]=r.useState(new Set),G=Array.from(N).map(e=>e.props.value).join(";");return(0,S.jsx)(h.bL,{...b,children:(0,S.jsxs)(j,{required:v,scope:t,trigger:C,onTriggerChange:x,valueNode:R,onValueNodeChange:M,valueNodeHasChildren:E,onValueNodeHasChildrenChange:P,contentId:(0,p.B)(),value:A,onValueChange:D,open:k,onOpenChange:V,dir:F,triggerPointerDownPosRef:O,disabled:m,children:[(0,S.jsx)(I.Provider,{scope:t,children:(0,S.jsx)(T,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{z(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{z(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),H?(0,S.jsxs)(eE,{"aria-hidden":!0,required:v,tabIndex:-1,name:g,autoComplete:f,value:A,onChange:e=>D(e.target.value),disabled:m,form:w,children:[void 0===A?(0,S.jsx)("option",{value:""}):null,Array.from(N)]},G):null]})})};N.displayName=L;var z="SelectTrigger",G=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...l}=e,a=_(n),s=O(z,n),d=s.disabled||o,c=(0,u.s)(t,s.onTriggerChange),g=k(n),f=r.useRef("touch"),[p,m,w]=eF(e=>{let t=g().filter(e=>!e.disabled),n=t.find(e=>e.value===s.value),r=eL(t,e,n);void 0!==r&&s.onValueChange(r.value)}),b=e=>{d||(s.onOpenChange(!0),w()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,S.jsx)(h.Mz,{asChild:!0,...a,children:(0,S.jsx)(v.sG.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":eP(s.value)?"":void 0,...l,ref:c,onClick:(0,i.m)(l.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&b(e)}),onPointerDown:(0,i.m)(l.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(b(e),e.preventDefault())}),onKeyDown:(0,i.m)(l.onKeyDown,e=>{let t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&P.includes(e.key)&&(b(),e.preventDefault())})})})});G.displayName=z;var B="SelectValue",W=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:l,placeholder:i="",...a}=e,s=O(B,n),{onValueNodeHasChildrenChange:d}=s,c=void 0!==l,g=(0,u.s)(t,s.onValueNodeChange);return(0,C.N)(()=>{d(c)},[d,c]),(0,S.jsx)(v.sG.span,{...a,ref:g,style:{pointerEvents:"none"},children:eP(s.value)?(0,S.jsx)(S.Fragment,{children:i}):l})});W.displayName=B;var U=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,S.jsx)(v.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});U.displayName="SelectIcon";var q=e=>(0,S.jsx)(m.Z,{asChild:!0,...e});q.displayName="SelectPortal";var K="SelectContent",Z=r.forwardRef((e,t)=>{let n=O(K,e.__scopeSelect),[l,i]=r.useState();return((0,C.N)(()=>{i(new DocumentFragment)},[]),n.open)?(0,S.jsx)(J,{...e,ref:t}):l?o.createPortal((0,S.jsx)($,{scope:e.__scopeSelect,children:(0,S.jsx)(I.Slot,{scope:e.__scopeSelect,children:(0,S.jsx)("div",{children:e.children})})}),l):null});Z.displayName=K;var[$,X]=A(K),Y=(0,w.TL)("SelectContent.RemoveScroll"),J=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:l,onEscapeKeyDown:a,onPointerDownOutside:s,side:d,sideOffset:p,align:h,alignOffset:m,arrowPadding:v,collisionBoundary:w,collisionPadding:b,sticky:y,hideWhenDetached:C,avoidCollisions:x,...R}=e,P=O(K,n),[F,L]=r.useState(null),[I,V]=r.useState(null),A=(0,u.s)(t,e=>L(e)),[D,_]=r.useState(null),[j,T]=r.useState(null),H=k(n),[N,z]=r.useState(!1),G=r.useRef(!1);r.useEffect(()=>{if(F)return(0,M.Eq)(F)},[F]),(0,g.Oh)();let B=r.useCallback(e=>{let[t,...n]=H().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&I&&(I.scrollTop=0),n===r&&I&&(I.scrollTop=I.scrollHeight),n?.focus(),document.activeElement!==o))return},[H,I]),W=r.useCallback(()=>B([D,F]),[B,D,F]);r.useEffect(()=>{N&&W()},[N,W]);let{onOpenChange:U,triggerPointerDownPosRef:q}=P;r.useEffect(()=>{if(F){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(q.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(q.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():F.contains(n.target)||U(!1),document.removeEventListener("pointermove",t),q.current=null};return null!==q.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[F,U,q]),r.useEffect(()=>{let e=()=>U(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[U]);let[Z,X]=eF(e=>{let t=H().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=eL(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),J=r.useCallback((e,t,n)=>{let r=!G.current&&!n;(void 0!==P.value&&P.value===t||r)&&(_(e),r&&(G.current=!0))},[P.value]),et=r.useCallback(()=>F?.focus(),[F]),en=r.useCallback((e,t,n)=>{let r=!G.current&&!n;(void 0!==P.value&&P.value===t||r)&&T(e)},[P.value]),er="popper"===o?ee:Q,eo=er===ee?{side:d,sideOffset:p,align:h,alignOffset:m,arrowPadding:v,collisionBoundary:w,collisionPadding:b,sticky:y,hideWhenDetached:C,avoidCollisions:x}:{};return(0,S.jsx)($,{scope:n,content:F,viewport:I,onViewportChange:V,itemRefCallback:J,selectedItem:D,onItemLeave:et,itemTextRefCallback:en,focusSelectedItem:W,selectedItemText:j,position:o,isPositioned:N,searchRef:Z,children:(0,S.jsx)(E.A,{as:Y,allowPinchZoom:!0,children:(0,S.jsx)(f.n,{asChild:!0,trapped:P.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,i.m)(l,e=>{P.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,S.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>P.onOpenChange(!1),children:(0,S.jsx)(er,{role:"listbox",id:P.contentId,"data-state":P.open?"open":"closed",dir:P.dir,onContextMenu:e=>e.preventDefault(),...R,...eo,onPlaced:()=>z(!0),ref:A,style:{display:"flex",flexDirection:"column",outline:"none",...R.style},onKeyDown:(0,i.m)(R.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||X(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=H().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>B(t)),e.preventDefault()}})})})})})})});J.displayName="SelectContentImpl";var Q=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...i}=e,a=O(K,n),s=X(K,n),[d,c]=r.useState(null),[g,f]=r.useState(null),p=(0,u.s)(t,e=>f(e)),h=k(n),m=r.useRef(!1),w=r.useRef(!0),{viewport:b,selectedItem:y,selectedItemText:x,focusSelectedItem:R}=s,M=r.useCallback(()=>{if(a.trigger&&a.valueNode&&d&&g&&b&&y&&x){let e=a.trigger.getBoundingClientRect(),t=g.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=x.getBoundingClientRect();if("rtl"!==a.dir){let o=r.left-t.left,i=n.left-o,a=e.left-i,u=e.width+a,s=Math.max(u,t.width),c=l(i,[10,Math.max(10,window.innerWidth-10-s)]);d.style.minWidth=u+"px",d.style.left=c+"px"}else{let o=t.right-r.right,i=window.innerWidth-n.right-o,a=window.innerWidth-e.right-i,u=e.width+a,s=Math.max(u,t.width),c=l(i,[10,Math.max(10,window.innerWidth-10-s)]);d.style.minWidth=u+"px",d.style.right=c+"px"}let i=h(),u=window.innerHeight-20,s=b.scrollHeight,c=window.getComputedStyle(g),f=parseInt(c.borderTopWidth,10),p=parseInt(c.paddingTop,10),v=parseInt(c.borderBottomWidth,10),w=f+p+s+parseInt(c.paddingBottom,10)+v,C=Math.min(5*y.offsetHeight,w),S=window.getComputedStyle(b),R=parseInt(S.paddingTop,10),M=parseInt(S.paddingBottom,10),E=e.top+e.height/2-10,P=y.offsetHeight/2,F=f+p+(y.offsetTop+P);if(F<=E){let e=i.length>0&&y===i[i.length-1].ref.current;d.style.bottom="0px";let t=Math.max(u-E,P+(e?M:0)+(g.clientHeight-b.offsetTop-b.offsetHeight)+v);d.style.height=F+t+"px"}else{let e=i.length>0&&y===i[0].ref.current;d.style.top="0px";let t=Math.max(E,f+b.offsetTop+(e?R:0)+P);d.style.height=t+(w-F)+"px",b.scrollTop=F-E+b.offsetTop}d.style.margin="10px 0",d.style.minHeight=C+"px",d.style.maxHeight=u+"px",o?.(),requestAnimationFrame(()=>m.current=!0)}},[h,a.trigger,a.valueNode,d,g,b,y,x,a.dir,o]);(0,C.N)(()=>M(),[M]);let[E,P]=r.useState();(0,C.N)(()=>{g&&P(window.getComputedStyle(g).zIndex)},[g]);let F=r.useCallback(e=>{e&&!0===w.current&&(M(),R?.(),w.current=!1)},[M,R]);return(0,S.jsx)(et,{scope:n,contentWrapper:d,shouldExpandOnScrollRef:m,onScrollButtonChange:F,children:(0,S.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:E},children:(0,S.jsx)(v.sG.div,{...i,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})});Q.displayName="SelectItemAlignedPosition";var ee=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...l}=e,i=_(n);return(0,S.jsx)(h.UC,{...i,...l,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,en]=A(K,{}),er="SelectViewport",eo=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...l}=e,a=X(er,n),s=en(er,n),d=(0,u.s)(t,a.onViewportChange),c=r.useRef(0);return(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,S.jsx)(I.Slot,{scope:n,children:(0,S.jsx)(v.sG.div,{"data-radix-select-viewport":"",role:"presentation",...l,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...l.style},onScroll:(0,i.m)(l.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=s;if(r?.current&&n){let e=Math.abs(c.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let l=o+e,i=Math.min(r,l),a=l-i;n.style.height=i+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});eo.displayName=er;var el="SelectGroup",[ei,ea]=A(el),eu=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,p.B)();return(0,S.jsx)(ei,{scope:n,id:o,children:(0,S.jsx)(v.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})});eu.displayName=el;var es="SelectLabel",ed=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=ea(es,n);return(0,S.jsx)(v.sG.div,{id:o.id,...r,ref:t})});ed.displayName=es;var ec="SelectItem",[eg,ef]=A(ec),ep=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:l=!1,textValue:a,...s}=e,d=O(ec,n),c=X(ec,n),g=d.value===o,[f,h]=r.useState(a??""),[m,w]=r.useState(!1),b=(0,u.s)(t,e=>c.itemRefCallback?.(e,o,l)),y=(0,p.B)(),C=r.useRef("touch"),x=()=>{l||(d.onValueChange(o),d.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,S.jsx)(eg,{scope:n,value:o,disabled:l,textId:y,isSelected:g,onItemTextChange:r.useCallback(e=>{h(t=>t||(e?.textContent??"").trim())},[]),children:(0,S.jsx)(I.ItemSlot,{scope:n,value:o,disabled:l,textValue:f,children:(0,S.jsx)(v.sG.div,{role:"option","aria-labelledby":y,"data-highlighted":m?"":void 0,"aria-selected":g&&m,"data-state":g?"checked":"unchecked","aria-disabled":l||void 0,"data-disabled":l?"":void 0,tabIndex:l?void 0:-1,...s,ref:b,onFocus:(0,i.m)(s.onFocus,()=>w(!0)),onBlur:(0,i.m)(s.onBlur,()=>w(!1)),onClick:(0,i.m)(s.onClick,()=>{"mouse"!==C.current&&x()}),onPointerUp:(0,i.m)(s.onPointerUp,()=>{"mouse"===C.current&&x()}),onPointerDown:(0,i.m)(s.onPointerDown,e=>{C.current=e.pointerType}),onPointerMove:(0,i.m)(s.onPointerMove,e=>{C.current=e.pointerType,l?c.onItemLeave?.():"mouse"===C.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,i.m)(s.onPointerLeave,e=>{e.currentTarget===document.activeElement&&c.onItemLeave?.()}),onKeyDown:(0,i.m)(s.onKeyDown,e=>{(c.searchRef?.current===""||" "!==e.key)&&(F.includes(e.key)&&x()," "===e.key&&e.preventDefault())})})})})});ep.displayName=ec;var eh="SelectItemText",em=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:l,style:i,...a}=e,s=O(eh,n),d=X(eh,n),c=ef(eh,n),g=H(eh,n),[f,p]=r.useState(null),h=(0,u.s)(t,e=>p(e),c.onItemTextChange,e=>d.itemTextRefCallback?.(e,c.value,c.disabled)),m=f?.textContent,w=r.useMemo(()=>(0,S.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:b,onNativeOptionRemove:y}=g;return(0,C.N)(()=>(b(w),()=>y(w)),[b,y,w]),(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)(v.sG.span,{id:c.textId,...a,ref:h}),c.isSelected&&s.valueNode&&!s.valueNodeHasChildren?o.createPortal(a.children,s.valueNode):null]})});em.displayName=eh;var ev="SelectItemIndicator",ew=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return ef(ev,n).isSelected?(0,S.jsx)(v.sG.span,{"aria-hidden":!0,...r,ref:t}):null});ew.displayName=ev;var eb="SelectScrollUpButton",ey=r.forwardRef((e,t)=>{let n=X(eb,e.__scopeSelect),o=en(eb,e.__scopeSelect),[l,i]=r.useState(!1),a=(0,u.s)(t,o.onScrollButtonChange);return(0,C.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),l?(0,S.jsx)(eS,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ey.displayName=eb;var eC="SelectScrollDownButton",ex=r.forwardRef((e,t)=>{let n=X(eC,e.__scopeSelect),o=en(eC,e.__scopeSelect),[l,i]=r.useState(!1),a=(0,u.s)(t,o.onScrollButtonChange);return(0,C.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),l?(0,S.jsx)(eS,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ex.displayName=eC;var eS=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...l}=e,a=X("SelectScrollButton",n),u=r.useRef(null),s=k(n),d=r.useCallback(()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)},[]);return r.useEffect(()=>()=>d(),[d]),(0,C.N)(()=>{let e=s().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[s]),(0,S.jsx)(v.sG.div,{"aria-hidden":!0,...l,ref:t,style:{flexShrink:0,...l.style},onPointerDown:(0,i.m)(l.onPointerDown,()=>{null===u.current&&(u.current=window.setInterval(o,50))}),onPointerMove:(0,i.m)(l.onPointerMove,()=>{a.onItemLeave?.(),null===u.current&&(u.current=window.setInterval(o,50))}),onPointerLeave:(0,i.m)(l.onPointerLeave,()=>{d()})})}),eR=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,S.jsx)(v.sG.div,{"aria-hidden":!0,...r,ref:t})});eR.displayName="SelectSeparator";var eM="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=_(n),l=O(eM,n),i=X(eM,n);return l.open&&"popper"===i.position?(0,S.jsx)(h.i3,{...o,...r,ref:t}):null}).displayName=eM;var eE=r.forwardRef(({__scopeSelect:e,value:t,...n},o)=>{let l=r.useRef(null),i=(0,u.s)(o,l),a=(0,x.Z)(t);return r.useEffect(()=>{let e=l.current;if(!e)return;let n=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==t&&n){let r=new Event("change",{bubbles:!0});n.call(e,t),e.dispatchEvent(r)}},[a,t]),(0,S.jsx)(v.sG.select,{...n,style:{...R,...n.style},ref:i,defaultValue:t})});function eP(e){return""===e||void 0===e}function eF(e){let t=(0,b.c)(e),n=r.useRef(""),o=r.useRef(0),l=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),i=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,l,i]}function eL(e,t,n){var r,o;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=e,o=Math.max(i,0),r.map((e,t)=>r[(o+t)%r.length]));1===l.length&&(a=a.filter(e=>e!==n));let u=a.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return u!==n?u:void 0}eE.displayName="SelectBubbleInput";var eI=N,ek=G,eV=W,eA=U,eD=q,e_=Z,ej=eo,eO=eu,eT=ed,eH=ep,eN=em,ez=ew,eG=ey,eB=ex,eW=eR}};