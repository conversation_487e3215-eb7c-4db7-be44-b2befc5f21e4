(()=>{var e={};e.id=7453,e.ids=[7453],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11394:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\(main)\\\\library\\\\template\\\\[hashedId]\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\library\\template\\[hashedId]\\settings\\page.tsx","default")},12412:e=>{"use strict";e.exports=require("assert")},13504:(e,t,s)=>{Promise.resolve().then(s.bind(s,78336))},17090:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("file-pen",[["path",{d:"M12.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v9.5",key:"1couwa"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M13.378 15.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1y4qbx"}]])},18714:(e,t,s)=>{Promise.resolve().then(s.bind(s,61225))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20174:(e,t,s)=>{"use strict";s.d(t,{F:()=>i});var r=s(60687),a=s(16189),l=s(85814),n=s.n(l);s(43210);let i=({items:e})=>{let t=(0,a.usePathname)(),s=e=>t.startsWith(e);return(0,r.jsx)("div",{className:"border-y border-neutral-400 rounded-md bg-primary-500 my-4 shadow-md",children:(0,r.jsx)("div",{className:"flex items-center",children:e.map(e=>e.disabled?(0,r.jsxs)("div",{className:"flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-gray-400 cursor-not-allowed",children:[e.icon,e.label]},e.route):(0,r.jsxs)(n(),{href:e.route,className:`flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-neutral-100 ${s(e.route)?"border-neutral-100":"border-transparent hover:border-neutral-400"}`,children:[e.icon,e.label]},e.route))})})}},21820:e=>{"use strict";e.exports=require("os")},27210:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\(main)\\\\library\\\\template\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\library\\template\\layout.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29330:(e,t,s)=>{Promise.resolve().then(s.bind(s,27210))},33873:e=>{"use strict";e.exports=require("path")},49952:(e,t,s)=>{Promise.resolve().then(s.bind(s,11394))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60714:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=s(65239),a=s(48088),l=s(88170),n=s.n(l),i=s(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d={children:["",{children:["(main)",{children:["library",{children:["template",{children:["[hashedId]",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,11394)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\library\\template\\[hashedId]\\settings\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,27210)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\library\\template\\layout.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,19559)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\library\\template\\[hashedId]\\settings\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(main)/library/template/[hashedId]/settings/page",pathname:"/library/template/[hashedId]/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},61225:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(60687),a=s(86429),l=s(17090),n=s(84027),i=s(16189),o=s(20174);let d=()=>{let{hashedId:e}=(0,i.useParams)(),t=[{label:"Form Builder",icon:(0,r.jsx)(l.A,{size:16}),route:`/library/template/${e}/form-builder`},{label:"Settings",icon:(0,r.jsx)(n.A,{size:16}),route:`/library/template/${e}/settings`}];return(0,r.jsx)(o.F,{items:t})};var c=s(21650),u=s(3984),p=s(6986),m=s(29494),x=s(28559),h=s(85814),b=s.n(h),f=s(43210);let y=({children:e})=>{let[t,s]=(0,f.useState)(!1);(0,f.useEffect)(()=>{s(!0)},[]);let{hashedId:l}=(0,i.useParams)(),n=(0,p.D)(l),{user:o}=(0,c.A)(),{data:h,isLoading:y,isError:j}=(0,m.I)({queryKey:["templates",o?.id,n],queryFn:()=>(0,u.J2)({templateId:n}),enabled:!!n&&!!o?.id});return t?y?(0,r.jsx)(a.A,{}):l&&null!==n?j?(0,r.jsx)("p",{className:"text-red-500",children:"Failed to fetch template. Please try again"}):(0,r.jsxs)("div",{className:"section flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h1",{className:"heading-text capitalize",children:h?.name}),(0,r.jsxs)(b(),{href:"/library",className:"flex items-center gap-2",children:[(0,r.jsx)(x.A,{size:16}),"Back to library"]})]}),(0,r.jsx)(d,{}),(0,r.jsx)("div",{className:"px-8",children:e})]}):(0,r.jsxs)("div",{className:"error-message",children:[(0,r.jsx)("h1",{className:"text-red-500",children:"Error: Invalid Template ID (hashedId)."}),(0,r.jsx)("p",{className:"text-neutral-700",children:"Please make sure the URL contains a valid project identifier."})]}):null}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73678:(e,t,s)=>{"use strict";s.d(t,{R:()=>l});var r=s(60687);s(43210);var a=s(38587);let l=({showModal:e,onClose:t,onConfirm:s,title:l,description:n,confirmButtonText:i,cancelButtonText:o,confirmButtonClass:d,children:c})=>(0,r.jsxs)(a.A,{isOpen:e,onClose:t,className:"p-6 rounded-md max-w-xl",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:l}),(0,r.jsx)("div",{className:"text-neutral-700 mt-2",children:n}),c&&(0,r.jsx)("div",{className:"mt-6 space-y-4",children:c}),(0,r.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,r.jsx)("button",{className:"btn-outline",onClick:t,type:"button",children:o||"Cancel"}),(0,r.jsx)("button",{className:`font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ${d}`,onClick:s,type:"button",children:i})]})]})},74075:e=>{"use strict";e.exports=require("zlib")},78336:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>k});var r=s(60687),a=s(43210),l=s(27605),n=s(12810),i=s(16189),o=s(6986),d=s(8693),c=s(29494),u=s(54050),p=s(54864),m=s(19150),x=s(68292),h=s(10022),b=s(11437),f=s(57800),y=s(86429),j=s(32833),v=s(15566),g=s(40480),N=s(21650),q=s(73678),C=s(3984);let P=async({templateId:e,dataToSend:t})=>{let{data:s}=await n.A.patch(`/libraries/${e}`,t);return s},k=()=>{let[e,t]=(0,a.useState)(!1);(0,a.useEffect)(()=>{t(!0)},[]);let{register:s,formState:{isSubmitting:n,errors:k,isSubmitted:E},handleSubmit:A,setValue:w,reset:I}=(0,l.mN)(),D=(0,i.useRouter)(),[z,M]=(0,a.useState)(!1),[S,F]=(0,a.useState)(null),[K,_]=(0,a.useState)(null),[T,O]=(0,a.useState)(!1),[U,R]=(0,a.useState)(!1),[B,$]=(0,a.useState)(null);(0,a.useEffect)(()=>{s("country",{required:"Please select a country"}),s("sector",{required:"Please select a sector"})},[s]),(0,a.useEffect)(()=>{w("country",S,{shouldValidate:E}),w("sector",K,{shouldValidate:E})},[w,S,K]);let{hashedId:G}=(0,i.useParams)(),Q=(0,o.D)(G),{user:V}=(0,N.A)(),H=(0,d.jE)();(0,a.useEffect)(()=>()=>{Q&&V?.id&&H.cancelQueries({queryKey:["templates",V.id,Q]})},[Q,V?.id,H]);let{data:L,isLoading:J,isError:W}=(0,c.I)({queryKey:["templates",V?.id,Q],queryFn:()=>(0,C.J2)({templateId:Q}),enabled:!!Q&&!!V?.id});(0,a.useEffect)(()=>{L&&(I({templateName:L.name||"",description:L.description||"",country:L.country||"",sector:L.sector||""}),F(L.country||null),_(L.sector||null))},[L,I]);let X=(0,p.wA)(),Y=(0,u.n)({mutationFn:P,onSuccess:()=>{H.invalidateQueries({queryKey:["templates"],exact:!1}),X((0,m.Ds)({message:"Template details have been updated",type:"success"}))},onError:e=>{X((0,m.Ds)({message:"Failed to update template details. Please try again."+e.message,type:"error"}))}}),Z=(0,u.n)({mutationFn:()=>(0,C.I7)(Q),onSuccess:()=>{M(!0),R(!1),H.cancelQueries({queryKey:["templates",V?.id,Q]}),H.removeQueries({queryKey:["template",V?.id,Q]}),H.invalidateQueries({queryKey:["templates"],exact:!1}),X((0,m.Ds)({message:"Template has been deleted successfully",type:"success"})),setTimeout(()=>{D.push("/library")},1e3)},onError:e=>{R(!1),console.error("Template deletion error:",e),X((0,m.Ds)({message:"Failed to delete template. Please try again.",type:"error"}))}}),ee=async e=>{Y.mutate({templateId:Q,dataToSend:{name:e.templateName,description:e.description,country:e.country,sector:e.sector}})};return e?z||J?(0,r.jsx)(y.A,{}):G&&null!==Q?W&&!z?(0,r.jsx)("p",{className:"text-red-500",children:"Failed to fetch template. Please try again."}):(0,r.jsxs)("form",{className:"flex flex-col gap-8",onSubmit:A(ee),children:[(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"template-name",className:"label-text",children:[(0,r.jsx)(h.A,{size:16})," Template Name"]}),(0,r.jsx)("input",{...s("templateName",{required:"template name is required."}),id:"template-name",type:"text",className:"input-field",placeholder:"Enter a template name"}),k.templateName&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${k.templateName.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"description",className:"label-text",children:"Description"}),(0,r.jsx)("textarea",{id:"description",...s("description"),className:"input-field resize-none",cols:4,placeholder:"Enter the template description"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,r.jsx)(b.A,{size:16}),"Country"]}),(0,r.jsx)(x.l,{id:"country",options:v,value:S,onChange:F}),k.country&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${k.country.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,r.jsx)(f.A,{size:16})," Sector"]}),(0,r.jsx)(x.l,{id:"sector",options:Object.values(j.b),value:K&&j.b[K]?j.b[K]:"Select an option",onChange:e=>{_((0,g.H)(e,j.b))}}),k.sector&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${k.sector.message}`})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"flex items-center gap-4",children:(0,r.jsx)("button",{onClick:()=>{$({title:"Confirm Delete",description:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:"Are you sure you want to delete this template? This action cannot be undone."}),(0,r.jsxs)("ul",{className:"list-disc list-inside flex flex-col gap-2 mt-2 text-neutral-700",children:[(0,r.jsx)("li",{children:"All data gathered for this template will be deleted."}),(0,r.jsx)("li",{children:"Forms associated with this template will be deleted."}),(0,r.jsx)("li",{children:"You will not be able to recover this template after deletion."})]})]}),confirmButtonText:"Delete",confirmButtonClass:"btn-danger",onConfirm:()=>{Z.mutate()}}),R(!0)},type:"button",className:"btn-danger",children:"delete"})}),(0,r.jsx)("button",{type:"submit",className:"btn-primary self-end",children:n?(0,r.jsxs)("span",{className:"flex items-center gap-2",children:["Saving"," ",(0,r.jsx)("div",{className:"size-4 animate-spin border-x border-neutral-100 rounded-full"})]}):"Save Changes"})]})]}),B&&(0,r.jsx)(q.R,{showModal:U,onClose:()=>R(!1),title:B.title,description:B.description,confirmButtonText:B.confirmButtonText,confirmButtonClass:B.confirmButtonClass,onConfirm:B.onConfirm})]}):(0,r.jsxs)("div",{className:"error-message",children:[(0,r.jsx)("h1",{className:"text-red-500",children:"Error: Invalid Template ID (hashedId)."}),(0,r.jsx)("p",{className:"text-neutral-700",children:"Please make sure the URL contains a valid template identifier."})]}):null}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84027:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,7404,517,7605,5814,551,8581,5841,4677],()=>s(60714));module.exports=r})();