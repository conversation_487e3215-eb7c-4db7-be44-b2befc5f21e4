{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_24e2925a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|images|fonts).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|images|fonts).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vxTVhA8XalJO92PbSKJ7lk2zRCVwDY/NQi5gp+1+9f4=", "__NEXT_PREVIEW_MODE_ID": "1d114abdb052758691821a637e77e36d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7f213d8f48017dc7c4b434ceaabbedde80408f2558dbc636d35e11f4b51e9916", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "dab6a6e0e3e35ecfaadeac0c82006d5cdeb79446cb3ce851f545f8623c1c5022"}}}, "sortedMiddleware": ["/"], "functions": {}}