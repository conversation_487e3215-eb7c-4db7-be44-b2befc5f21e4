(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7960],{14165:(e,t,i)=>{Promise.resolve().then(i.bind(i,91095))},41050:(e,t,i)=>{"use strict";i.d(t,{A:()=>y});let s=e=>[...new Set(e)],n=(e,t)=>e.filter(e=>!t.includes(e)),r=(e,t)=>e.filter(e=>t.includes(e)),h=e=>"bigint"==typeof e||!Number.isNaN(Number(e))&&Math.floor(Number(e))===e,l=e=>"bigint"==typeof e||e>=0&&Number.isSafeInteger(e);function a(e,t){let i;if(0===t.length)return e;let s=[...e];for(let e=s.length-1,n=0,r=0;e>0;e--,n++){n%=t.length,r+=i=t[n].codePointAt(0);let h=(i+n+r)%e,l=s[e],a=s[h];s[h]=l,s[e]=a}return s}let o=(e,t)=>{let i=[],s=e;if("bigint"==typeof s){let e=BigInt(t.length);do i.unshift(t[Number(s%e)]),s/=e;while(s>BigInt(0))}else do i.unshift(t[s%t.length]),s=Math.floor(s/t.length);while(s>0);return i},d=(e,t)=>e.reduce((i,s)=>{let n=t.indexOf(s);if(-1===n)throw Error(`The provided ID (${e.join("")}) is invalid, as it contains characters that do not exist in the alphabet (${t.join("")})`);if("bigint"==typeof i)return i*BigInt(t.length)+BigInt(n);let r=i*t.length+n;return Number.isSafeInteger(r)?r:(m("Unable to decode the provided string, due to lack of support for BigInt numbers in the current environment"),BigInt(i)*BigInt(t.length)+BigInt(n))},0),g=/^\+?\d+$/,p=e=>{if(!g.test(e))return Number.NaN;let t=Number.parseInt(e,10);return Number.isSafeInteger(t)?t:(m("Unable to encode the provided BigInt string without loss of information due to lack of support for BigInt type in the current environment"),BigInt(e))},u=(e,t,i)=>Array.from({length:Math.ceil(e.length/t)},(s,n)=>i(e.slice(n*t,(n+1)*t))),c=e=>new RegExp(e.map(e=>b(e)).sort((e,t)=>t.length-e.length).join("|")),f=e=>RegExp(`^[${e.map(e=>b(e)).sort((e,t)=>t.length-e.length).join("")}]+$`),b=e=>e.replace(/[\s#$()*+,.?[\\\]^{|}-]/g,"\\$&"),m=(e="BigInt is not available in this environment")=>{if("function"!=typeof BigInt)throw TypeError(e)};class y{constructor(e="",t=0,i="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890",h="cfhistuCFHISTU"){let l,o;if(this.minLength=t,"number"!=typeof t)throw TypeError(`Hashids: Provided 'minLength' has to be a number (is ${typeof t})`);if("string"!=typeof e)throw TypeError(`Hashids: Provided 'salt' has to be a string (is ${typeof e})`);if("string"!=typeof i)throw TypeError(`Hashids: Provided alphabet has to be a string (is ${typeof i})`);let d=Array.from(e),g=Array.from(i),p=Array.from(h);this.salt=d;let u=s(g);if(u.length<16)throw Error(`Hashids: alphabet must contain at least 16 unique characters, provided: ${u.join("")}`);this.alphabet=n(u,p);let b=r(p,u);this.seps=a(b,d),(0===this.seps.length||this.alphabet.length/this.seps.length>3.5)&&(l=Math.ceil(this.alphabet.length/3.5))>this.seps.length&&(o=l-this.seps.length,this.seps.push(...this.alphabet.slice(0,o)),this.alphabet=this.alphabet.slice(o)),this.alphabet=a(this.alphabet,d);let m=Math.ceil(this.alphabet.length/12);this.alphabet.length<3?(this.guards=this.seps.slice(0,m),this.seps=this.seps.slice(m)):(this.guards=this.alphabet.slice(0,m),this.alphabet=this.alphabet.slice(m)),this.guardsRegExp=c(this.guards),this.sepsRegExp=c(this.seps),this.allowedCharsRegExp=f([...this.alphabet,...this.guards,...this.seps])}encode(e,...t){let i=Array.isArray(e)?e:[...null!=e?[e]:[],...t];return 0===i.length?"":(i.every(h)||(i=i.map(e=>"bigint"==typeof e||"number"==typeof e?e:p(String(e)))),i.every(l))?this._encode(i).join(""):""}decode(e){return e&&"string"==typeof e&&0!==e.length?this._decode(e):[]}encodeHex(e){let t=e;switch(typeof t){case"bigint":t=t.toString(16);break;case"string":if(!/^[\dA-Fa-f]+$/.test(t))return"";break;default:throw Error(`Hashids: The provided value is neither a string, nor a BigInt (got: ${typeof t})`)}let i=u(t,12,e=>Number.parseInt(`1${e}`,16));return this.encode(i)}decodeHex(e){return this.decode(e).map(e=>e.toString(16).slice(1)).join("")}isValidId(e){return this.allowedCharsRegExp.test(e)}_encode(e){let{alphabet:t}=this,i=e.reduce((e,t,i)=>e+("bigint"==typeof t?Number(t%BigInt(i+100)):t%(i+100)),0),s=[t[i%t.length]],n=[...s],{seps:r}=this,{guards:h}=this;if(e.forEach((i,h)=>{let l=n.concat(this.salt,t),d=o(i,t=a(t,l));if(s.push(...d),h+1<e.length){let e=d[0].codePointAt(0)+h,t="bigint"==typeof i?Number(i%BigInt(e)):i%e;s.push(r[t%r.length])}}),s.length<this.minLength){let e=(i+s[0].codePointAt(0))%h.length;if(s.unshift(h[e]),s.length<this.minLength){let e=(i+s[2].codePointAt(0))%h.length;s.push(h[e])}}let l=Math.floor(t.length/2);for(;s.length<this.minLength;){t=a(t,t),s.unshift(...t.slice(l)),s.push(...t.slice(0,l));let e=s.length-this.minLength;if(e>0){let t=e/2;s=s.slice(t,t+this.minLength)}}return s}_decode(e){if(!this.isValidId(e))throw Error(`The provided ID (${e}) is invalid, as it contains characters that do not exist in the alphabet (${this.guards.join("")}${this.seps.join("")}${this.alphabet.join("")})`);let t=e.split(this.guardsRegExp),i=+(3===t.length||2===t.length),s=t[i];if(0===s.length)return[];let n=s[Symbol.iterator]().next().value,r=s.slice(n.length).split(this.sepsRegExp),h=this.alphabet,l=[];for(let e of r){let t=[n,...this.salt,...h],i=a(h,t.slice(0,h.length));l.push(d(Array.from(e),i)),h=i}return this._encode(l).join("")!==e?[]:l}}},88570:(e,t,i)=>{"use strict";i.d(t,{D:()=>l,l:()=>h});var s=i(41050);let n=i(49509).env.SALT||"rushan-salt",r=new s.A(n,12),h=e=>r.encode(e),l=e=>{let t=r.decode(e)[0];return"bigint"==typeof t?t<Number.MAX_SAFE_INTEGER?Number(t):null:"number"==typeof t?t:null}},91095:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>u});var s=i(95155),n=i(59250),r=i(12115),h=i(19373),l=i(34947),a=i(57799),o=i(35695),d=i(88570),g=i(41232);let p={viewForm:!0,editForm:!0,viewSubmissions:!0,addSubmissions:!0,deleteSubmissions:!0,editSubmissions:!0,manageProject:!0,validateSubmissions:!0};function u(){let[e,t]=(0,r.useState)(!1),{hashedId:i}=(0,o.useParams)(),u=(0,d.D)(i),{data:c,isLoading:f,isError:b}=(0,h.I)({queryKey:["templateQuestions",u],queryFn:()=>(0,l.ej)({templateId:u}),enabled:!!u});return i&&null!==u?f||!c?(0,s.jsx)(a.A,{}):b?(0,s.jsx)("p",{className:"text-sm text-red-500",children:"Error loading form information. Please refresh the page."}):(0,s.jsx)("div",{className:"p-6",children:e?(0,s.jsx)(n.V,{questions:c,questionGroups:[],contextType:"template",onClose:()=>t(!1),hashedId:i}):(0,s.jsx)(g.o,{setIsPreviewMode:t,questions:c,contextType:"template",contextId:u,permissions:p})}):(0,s.jsxs)("div",{className:"error-message",children:[(0,s.jsx)("h1",{className:"text-red-500",children:"Error: Invalid Project ID (hashedId)."}),(0,s.jsx)("p",{className:"text-neutral-700",children:"Please make sure the URL contains a valid project identifier."})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3873,3524,635,1445,6967,6903,4601,2177,4277,556,3481,1467,6539,2050,919,7252,7248,8662,8441,1684,7358],()=>t(14165)),_N_E=e.O()}]);