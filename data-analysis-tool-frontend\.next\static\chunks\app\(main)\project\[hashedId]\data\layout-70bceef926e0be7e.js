(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4056],{19946:(e,r,a)=>{"use strict";a.d(r,{A:()=>i});var t=a(12115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,a)=>a?a.toUpperCase():r.toLowerCase()),l=e=>{let r=n(e);return r.charAt(0).toUpperCase()+r.slice(1)},o=function(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return r.filter((e,r,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===r).join(" ").trim()};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,t.forwardRef)((e,r)=>{let{color:a="currentColor",size:s=24,strokeWidth:n=2,absoluteStrokeWidth:l,className:u="",children:i,iconNode:d,...h}=e;return(0,t.createElement)("svg",{ref:r,...c,width:s,height:s,stroke:a,strokeWidth:l?24*Number(n)/Number(s):n,className:o("lucide",u),...h},[...d.map(e=>{let[r,a]=e;return(0,t.createElement)(r,a)}),...Array.isArray(i)?i:[i]])}),i=(e,r)=>{let a=(0,t.forwardRef)((a,n)=>{let{className:c,...i}=a;return(0,t.createElement)(u,{ref:n,iconNode:r,className:o("lucide-".concat(s(l(e))),"lucide-".concat(e),c),...i})});return a.displayName=l(e),a}},35695:(e,r,a)=>{"use strict";var t=a(18999);a.o(t,"useParams")&&a.d(r,{useParams:function(){return t.useParams}}),a.o(t,"usePathname")&&a.d(r,{usePathname:function(){return t.usePathname}}),a.o(t,"useRouter")&&a.d(r,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(r,{useSearchParams:function(){return t.useSearchParams}})},51778:(e,r,a)=>{Promise.resolve().then(a.bind(a,65754))},65754:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>u});var t=a(95155);a(12115);var s=a(35695),n=a(19946);let l=(0,n.A)("table-2",[["path",{d:"M9 3H5a2 2 0 0 0-2 2v4m6-6h10a2 2 0 0 1 2 2v4M9 3v18m0 0h10a2 2 0 0 0 2-2V9M9 21H5a2 2 0 0 1-2-2V9m0 0h18",key:"gugj83"}]]),o=(0,n.A)("chart-no-axes-column",[["line",{x1:"18",x2:"18",y1:"20",y2:"10",key:"1xfpm4"}],["line",{x1:"12",x2:"12",y1:"20",y2:"4",key:"be30l9"}],["line",{x1:"6",x2:"6",y1:"20",y2:"14",key:"1r4le6"}]]);var c=a(91788);function u(e){let{children:r}=e,{hashedId:a}=(0,s.useParams)(),n=[{label:"Data",href:"/project/".concat(a,"/data"),icon:l},{label:"Reports",href:"/project/".concat(a,"/data/reports"),icon:o},{label:"Downloads",href:"/project/".concat(a,"/data/downloads"),icon:c.A}],u=(0,s.usePathname)(),i=(0,s.useRouter)();return(0,t.jsxs)("div",{className:"flex flex-col min-h-screen bg-neutral-100 p-4",children:[(0,t.jsxs)("div",{className:"flex justify-between mb-4",children:[(0,t.jsx)("h2",{className:"heading-text",children:"Survey Results"}),(0,t.jsxs)("div",{className:"",children:[(0,t.jsx)("h2",{className:"flex flex-col text-sm font-medium text-neutral-700 mb-1",children:"Navigate"}),(0,t.jsxs)("select",{value:n.some(e=>e.href===u)?u:"",onChange:e=>{let r=e.target.value;r!==u&&i.push(r)},className:" p-2 border border-neutral-300 rounded-md shadow-sm cursor-pointer",children:[(0,t.jsx)("option",{value:"",children:u==="/project/".concat(a,"/data")?"DataTable Overview":"Select"}),n.map(e=>{let{label:r,href:a}=e;return(0,t.jsx)("option",{value:a,children:r},r)})]})]})]}),(0,t.jsx)("main",{className:"p-4 bg-neutral-100 rounded-md border border-neutral-300 shadow-sm",children:(0,t.jsx)("div",{children:r})})]})}},91788:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(19946).A)("download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])}},e=>{var r=r=>e(e.s=r);e.O(0,[8441,1684,7358],()=>r(51778)),_N_E=e.O()}]);