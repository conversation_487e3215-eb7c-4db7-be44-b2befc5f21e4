{"/favicon.ico/route": "/favicon.ico", "/_not-found/page": "/_not-found", "/(auth)/page": "/", "/(auth)/reset-password/change-password/page": "/reset-password/change-password", "/(auth)/reset-password/page": "/reset-password", "/(auth)/signup/page": "/signup", "/edit-submission/[hashedId]/[submissionId]/page": "/edit-submission/[hashedId]/[submissionId]", "/form-submission/[hashedId]/page": "/form-submission/[hashedId]", "/form-submission/[hashedId]/sign-in/page": "/form-submission/[hashedId]/sign-in", "/(main)/dashboard/[status]/not-available/page": "/dashboard/[status]/not-available", "/(main)/dashboard/page": "/dashboard", "/(main)/library/asset/page": "/library/asset", "/(main)/library/not-available/page": "/library/not-available", "/(main)/policy/page": "/policy", "/(main)/library/page": "/library", "/(main)/terms/page": "/terms", "/(main)/account/profile/page": "/account/profile", "/(main)/library/template/[hashedId]/settings/page": "/library/template/[hashedId]/settings", "/(main)/library/question-block/form-builder/page": "/library/question-block/form-builder", "/(main)/account/security/page": "/account/security", "/(main)/library/template/[hashedId]/form-builder/page": "/library/template/[hashedId]/form-builder", "/(main)/project/[hashedId]/form-builder/page": "/project/[hashedId]/form-builder", "/(main)/project/[hashedId]/settings/page": "/project/[hashedId]/settings", "/(main)/project/[hashedId]/overview/page": "/project/[hashedId]/overview", "/(main)/project/[hashedId]/data/downloads/page": "/project/[hashedId]/data/downloads", "/(main)/project/[hashedId]/data/gallery/page": "/project/[hashedId]/data/gallery", "/(main)/project/[hashedId]/data/page": "/project/[hashedId]/data", "/(main)/project/[hashedId]/data/map/page": "/project/[hashedId]/data/map", "/(main)/project/[hashedId]/data/reports/page": "/project/[hashedId]/data/reports", "/(main)/project/[hashedId]/data/table/page": "/project/[hashedId]/data/table"}