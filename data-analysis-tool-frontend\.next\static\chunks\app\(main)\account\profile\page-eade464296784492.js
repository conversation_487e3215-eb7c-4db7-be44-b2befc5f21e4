(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8957],{2511:(e,t,a)=>{"use strict";a.d(t,{b:()=>i});let i={information_media:"Information / Media",econommic_social_development:"Economic & Social Development",security_police_peacekeeping:"Security / Police / Peacekeeping",disarmament_and_demobilization:"Disarmament & Demobilization",environment:"Environment",private_sector:"Private Sector",humanitarian_coordination_information_management:"Humanitarian - Coordination & Info Management",humanitarian_multiple_clusters:"Humanitarian - Multiple Clusters",humanitarian_camp_management_and_coordination:"Humanitarian - Camp Management & Coordination",humanitarian_early_recovery:"Humanitarian - Early Recovery",humanitarian_education:"Humanitarian - Education",humanitarian_emergency_shelter:"Humanitarian - Emergency Shelter",humanitarian_emergency_telecoms:"Humanitarian - Emergency Telecoms",humanitarian_food_security:"Humanitarian - Food Security",humanitarian_health:"Humanitarian - Health",humanitarian_logistics:"Humanitarian - Logistics",humanitarian_nutrition:"Humanitarian - Nutrition",humanitarian_protection:"Humanitarian - Protection",humanitarian_sanitation_water_and_hygiene:"Humanitarian - Sanitation / Water / Hygiene",other:"Other"}},4516:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},5041:(e,t,a)=>{"use strict";a.d(t,{n:()=>d});var i=a(12115),s=a(34560),n=a(7165),r=a(25910),o=a(52020),u=class extends r.Q{#e;#t=void 0;#a;#i;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#s()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,o.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#a,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,o.EN)(t.mutationKey)!==(0,o.EN)(this.options.mutationKey)?this.reset():this.#a?.state.status==="pending"&&this.#a.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#a?.removeObserver(this)}onMutationUpdate(e){this.#s(),this.#n(e)}getCurrentResult(){return this.#t}reset(){this.#a?.removeObserver(this),this.#a=void 0,this.#s(),this.#n()}mutate(e,t){return this.#i=t,this.#a?.removeObserver(this),this.#a=this.#e.getMutationCache().build(this.#e,this.options),this.#a.addObserver(this),this.#a.execute(e)}#s(){let e=this.#a?.state??(0,s.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#n(e){n.jG.batch(()=>{if(this.#i&&this.hasListeners()){let t=this.#t.variables,a=this.#t.context;e?.type==="success"?(this.#i.onSuccess?.(e.data,t,a),this.#i.onSettled?.(e.data,null,t,a)):e?.type==="error"&&(this.#i.onError?.(e.error,t,a),this.#i.onSettled?.(void 0,e.error,t,a))}this.listeners.forEach(e=>{e(this.#t)})})}},l=a(26715),c=a(63768);function d(e,t){let a=(0,l.jE)(t),[s]=i.useState(()=>new u(a,e));i.useEffect(()=>{s.setOptions(e)},[s,e]);let r=i.useSyncExternalStore(i.useCallback(e=>s.subscribe(n.jG.batchCalls(e)),[s]),()=>s.getCurrentResult(),()=>s.getCurrentResult()),o=i.useCallback((e,t)=>{s.mutate(e,t).catch(c.l)},[s]);if(r.error&&(0,c.G)(s.options.throwOnError,[r.error]))throw r.error;return{...r,mutate:o,mutateAsync:r.mutate}}},17576:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(19946).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},23491:(e,t,a)=>{"use strict";a.d(t,{i:()=>i});let i={non_profit_organization:"Non-profit Organization",government_institution:"Government Institution",educational_organization:"Educational Organization",a_commercial_or_for_profit_company:"Commercial / For-profit Company",i_am_not_associated_with_any_organization:"Not Associated with any Organization"}},25784:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let i=a(23464).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});i.interceptors.request.use(e=>e,e=>Promise.reject(e)),i.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let s=i},29350:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var i=a(97381),s=a(59362),n=a(25784),r=a(35695),o=a(12115),u=a(34540);let l=e=>{let t=(0,u.wA)(),a=(0,r.useRouter)(),l=(0,r.usePathname)(),{status:c,user:d,error:h}=(0,u.d4)(e=>e.auth),m=async()=>{try{t((0,i.Le)());let e=(await n.A.get("/users/me")).data;t((0,i.tQ)(e))}catch(n){if(t((0,i.x9)()),(0,s.F0)(n)){var e,r,o,u,c;if(console.error("Auth error:",null==(e=n.response)?void 0:e.status,null==(r=n.response)?void 0:r.data),(null==(o=n.response)?void 0:o.status)===401){if(l.startsWith("/form-submission"))return;a.push("/")}else t((0,i.jB)((null==(c=n.response)||null==(u=c.data)?void 0:u.message)||n.message))}else t((0,i.jB)(n instanceof Error?n.message:"An unknown error occurred."))}};return(0,o.useEffect)(()=>{(null==e?void 0:e.skipFetchUser)||m()},[null==e?void 0:e.skipFetchUser]),(0,o.useEffect)(()=>{let e=e=>{if("logout"===e.key&&"true"===e.newValue)if(t((0,i.x9)()),l.startsWith("/form-submission")){let e=l.split("/")[2];e?a.push("/form-submission/".concat(e,"/sign-in")):a.push("/")}else a.push("/")};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[t,a,l]),{status:c,user:d,error:h,isAuthenticated:"authenticated"===c,isLoading:"loading"===c,refreshAuthState:()=>{m()},signin:async(e,t,a)=>{try{await n.A.post("/users/login",e),await m(),null==t||t()}catch(e){if(e instanceof s.pe){var i,r;let t=null==(r=e.response)||null==(i=r.data)?void 0:i.errorType;null==a||a(t)}else null==a||a()}},logout:async()=>{try{await n.A.post("/users/logout"),localStorage.setItem("logout","true"),setTimeout(()=>localStorage.removeItem("logout"),100)}finally{if(t((0,i.x9)()),l.startsWith("/form-submission")){let e=l.split("/")[2];e?a.push("/form-submission/".concat(e,"/sign-in")):a.push("/")}else a.push("/")}}}}},34560:(e,t,a)=>{"use strict";a.d(t,{$:()=>o,s:()=>r});var i=a(7165),s=a(57948),n=a(6784),r=class extends s.k{#r;#o;#u;constructor(e){super(),this.mutationId=e.mutationId,this.#o=e.mutationCache,this.#r=[],this.state=e.state||o(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#r.includes(e)||(this.#r.push(e),this.clearGcTimeout(),this.#o.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#r=this.#r.filter(t=>t!==e),this.scheduleGc(),this.#o.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#r.length||("pending"===this.state.status?this.scheduleGc():this.#o.remove(this))}continue(){return this.#u?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#l({type:"continue"})};this.#u=(0,n.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#l({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#l({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#o.canRun(this)});let a="pending"===this.state.status,i=!this.#u.canStart();try{if(a)t();else{this.#l({type:"pending",variables:e,isPaused:i}),await this.#o.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#l({type:"pending",context:t,variables:e,isPaused:i})}let s=await this.#u.start();return await this.#o.config.onSuccess?.(s,e,this.state.context,this),await this.options.onSuccess?.(s,e,this.state.context),await this.#o.config.onSettled?.(s,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(s,null,e,this.state.context),this.#l({type:"success",data:s}),s}catch(t){try{throw await this.#o.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#o.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#l({type:"error",error:t})}}finally{this.#o.runNext(this)}}#l(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),i.jG.batch(()=>{this.#r.forEach(t=>{t.onMutationUpdate(e)}),this.#o.notify({mutation:this,type:"updated",action:e})})}};function o(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},34869:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(19946).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},46075:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>A});var i=a(95155),s=a(50408),n=a(71007),r=a(34869),o=a(4516),u=a(17576),l=a(19946);let c=(0,l.A)("user-pen",[["path",{d:"M11.5 15H7a4 4 0 0 0-4 4v2",key:"15lzij"}],["path",{d:"M21.378 16.626a1 1 0 0 0-3.004-3.004l-4.01 4.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1817ys"}],["circle",{cx:"10",cy:"7",r:"4",key:"e45bow"}]]),d=(0,l.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var h=a(12115),m=a(62177),p=a(74567),y=a(2511),g=a(23491),b=a(64368),f=a(19373),v=a(26715),x=a(5041),j=a(62334),w=a(57799),S=a(34540),C=a(71402),N=a(29350);let A=()=>{let{register:e,formState:{errors:t,isSubmitting:a,isSubmitted:l},reset:A,setValue:M,handleSubmit:_}=(0,m.mN)();(0,h.useEffect)(()=>{e("country",{required:"Please select a country"}),e("sector",{required:"Please select a sector"}),e("organizationType",{required:"Please select an organization type"})},[e]);let[k,E]=(0,h.useState)(null),[z,P]=(0,h.useState)(null),[R,T]=(0,h.useState)(null);(0,h.useEffect)(()=>{M("country",k,{shouldValidate:l}),M("sector",z,{shouldValidate:l}),M("organizationType",R,{shouldValidate:l})},[k,z,R,M]);let{user:O}=(0,N.A)(),{data:L,isLoading:H,isError:B}=(0,f.I)({queryKey:["profile",null==O?void 0:O.id],queryFn:j.l2,enabled:!!(null==O?void 0:O.id)});(0,h.useEffect)(()=>{L&&(A({name:L.name||"",country:L.country||"",city:L.city||"",sector:L.sector||"",organizationType:L.organizationType||"",bio:L.bio||""}),E(L.country||null),P(L.sector||null),T(L.organizationType||null))},[L,A]);let F=(0,S.wA)(),G=(0,v.jE)(),I=(0,x.n)({mutationFn:j.eg,onSuccess:()=>{G.invalidateQueries({queryKey:["profile",null==O?void 0:O.id]}),F((0,C.Ds)({message:"Profile data has been updated successfully.",type:"success"}))},onError:e=>{F((0,C.Ds)({message:"Failed to update profile information. Please try again."+e.message,type:"error"}))}}),K=async e=>{I.mutate({dataToSend:{name:e.name,country:e.country,city:e.city,sector:e.sector,organizationType:e.organizationType,bio:e.bio}})};return H?(0,i.jsx)(w.A,{}):B?(0,i.jsx)("p",{className:"text-red-500",children:"Error loading profile data. Please try again"}):(0,i.jsxs)("form",{className:"flex flex-col gap-8",onSubmit:_(K),children:[(0,i.jsxs)("div",{className:"flex flex-col gap-2 w-full",children:[(0,i.jsx)("h1",{className:"heading-text",children:"Profile Information"}),(0,i.jsx)("p",{className:"sub-text",children:"Update your personal and professional information"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 tablet:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"label-input-group group",children:[(0,i.jsxs)("label",{htmlFor:"name",className:"label-text",children:[(0,i.jsx)(n.A,{size:16})," Full name"]}),(0,i.jsx)("input",{...e("name",{required:"Please enter your name"}),id:"name",type:"text",className:"input-field",placeholder:"eg: John Doe"}),t.name&&(0,i.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(t.name.message)})]}),(0,i.jsxs)("div",{className:"label-input-group group",children:[(0,i.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,i.jsx)(r.A,{size:16})," Country"]}),(0,i.jsx)(s.l,{id:"country",options:p,value:k,onChange:E}),t.country&&(0,i.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(t.country.message)})]}),(0,i.jsxs)("div",{className:"label-input-group group",children:[(0,i.jsxs)("label",{htmlFor:"city",className:"label-text",children:[(0,i.jsx)(o.A,{size:16})," City"]}),(0,i.jsx)("input",{...e("city"),id:"city",type:"text",className:"input-field",placeholder:"eg: Kathmandu"}),t.city&&(0,i.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(t.city.message)})]}),(0,i.jsxs)("div",{className:"label-input-group group",children:[(0,i.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,i.jsx)(u.A,{size:16})," Sector"]}),(0,i.jsx)(s.l,{id:"sector",options:Object.values(y.b),value:z&&y.b[z]?y.b[z]:"Select an option",onChange:e=>{P((0,b.H)(e,y.b))}}),t.sector&&(0,i.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(t.sector.message)})]}),(0,i.jsxs)("div",{className:"label-input-group group",children:[(0,i.jsxs)("label",{htmlFor:"organizationType",className:"label-text",children:[(0,i.jsx)(u.A,{size:16})," Organization Type"]}),(0,i.jsx)(s.l,{id:"organizationType",options:Object.values(g.i),value:R&&g.i[R]?g.i[R]:"Select an option",onChange:e=>{T((0,b.H)(e,g.i))}}),t.organizationType&&(0,i.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(t.organizationType.message)})]}),(0,i.jsxs)("div",{className:"label-input-group group col-span-2",children:[(0,i.jsxs)("label",{htmlFor:"bio",className:"label-text",children:[(0,i.jsx)(c,{size:16})," Bio"]}),(0,i.jsx)("textarea",{...e("bio"),cols:4,className:"input-field resize-none",placeholder:"Tell us about yourself"})]})]}),(0,i.jsxs)("button",{type:"submit",className:"btn-primary self-end",children:[(0,i.jsx)(d,{size:18}),a?(0,i.jsxs)("span",{className:"flex items-center gap-2",children:["Saving"," ",(0,i.jsx)("div",{className:"size-4 animate-spin border-x border-neutral-100 rounded-full"})]}):(0,i.jsx)("span",{children:"Save"})]})]})}},50408:(e,t,a)=>{"use strict";a.d(t,{l:()=>r});var i=a(95155),s=a(66474),n=a(12115);let r=e=>{let{id:t,options:a,value:r,onChange:o}=e,[u,l]=(0,n.useState)(!1),c=(0,n.useRef)(null),d=(0,n.useRef)([]),h=(0,n.useRef)(null);(0,n.useEffect)(()=>{let e=e=>{h.current&&!h.current.contains(e.target)&&l(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let m=e=>{if(!u)return;let t=e.key.toLowerCase();if(t.match(/[a-z]/)){let e=a.findIndex(e=>e.toLowerCase().startsWith(t));if(-1!==e&&d.current[e]){var i;null==(i=d.current[e])||i.scrollIntoView({behavior:"auto",block:"nearest"})}}};return(0,n.useEffect)(()=>(document.addEventListener("keydown",m),()=>{document.removeEventListener("keydown",m)}),[u,a]),(0,i.jsxs)("div",{className:"relative",ref:h,children:[(0,i.jsxs)("button",{id:t,type:"button",className:"px-4 py-2 flex items-center justify-between rounded-md border border-neutral-400 focus:border-primary-500 duration-300 w-full text-left cursor-pointer",onClick:()=>{l(!u)},children:[(0,i.jsx)("span",{children:r||"Select an option"}),(0,i.jsx)(s.A,{})]}),u&&(0,i.jsx)("ul",{className:"absolute z-10 max-h-[180px] overflow-auto border border-neutral-400 rounded-md bg-neutral-100 w-full mt-1 flex flex-col",ref:c,children:a.map((e,t)=>(0,i.jsx)("li",{ref:e=>{d.current[t]=e},className:"cursor-pointer bg-neutral-100 hover:bg-neutral-200 px-4 py-2",onClick:()=>{o(e),l(!1)},children:e},t))})]})}},57799:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var i=a(95155);a(12115);let s=()=>(0,i.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,i.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},59362:(e,t,a)=>{"use strict";a.d(t,{F0:()=>d,pe:()=>s});let{Axios:i,AxiosError:s,CanceledError:n,isCancel:r,CancelToken:o,VERSION:u,all:l,Cancel:c,isAxiosError:d,spread:h,toFormData:m,AxiosHeaders:p,HttpStatusCode:y,formToJSON:g,getAdapter:b,mergeConfig:f}=a(23464).A},62334:(e,t,a)=>{"use strict";a.d(t,{BZ:()=>n,eg:()=>r,ep:()=>o,kH:()=>u,l2:()=>s});var i=a(25784);let s=async()=>{let{data:e}=await i.A.get("/users/profile");return e.profile},n=async e=>{let{email:t}=e,{data:a}=await i.A.patch("/users/change-email",{email:t});return a},r=async e=>{let{dataToSend:t}=e,{data:a}=await i.A.patch("/users/update",t);return a},o=async()=>{let{data:e}=await i.A.get("/users/sessions");return e.sessions},u=async e=>{let{data:t}=await i.A.post("/users/sendverificationemail",{email:e});return t}},64368:(e,t,a)=>{"use strict";a.d(t,{H:()=>i});let i=(e,t)=>{let a=Object.entries(t).find(t=>{let[a,i]=t;return i===e});return a?a[0]:null}},66474:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},67356:(e,t,a)=>{Promise.resolve().then(a.bind(a,46075))},71007:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},71402:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>r,Ds:()=>s,_b:()=>n});let i=(0,a(51990).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,t)=>{e.message=t.payload.message,e.type=t.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:s,hideNotification:n}=i.actions,r=i.reducer},74567:e=>{"use strict";e.exports=JSON.parse('["Afghanistan","Albania","Algeria","Andorra","Angola","Argentina","Armenia","Australia","Austria","Azerbaijan","Bahamas","Bahrain","Bangladesh","Barbados","Belarus","Belgium","Belize","Benin","Bhutan","Bolivia","Bosnia and Herzegovina","Botswana","Brazil","Brunei","Bulgaria","Burkina Faso","Burundi","Cabo Verde","Cambodia","Cameroon","Canada","Central African Republic","Chad","Chile","China","Colombia","Comoros","Congo (Congo-Brazzaville)","Costa Rica","Croatia","Cuba","Cyprus","Czech Republic","Denmark","Djibouti","Dominica","Dominican Republic","Ecuador","Egypt","El Salvador","Equatorial Guinea","Eritrea","Estonia","Eswatini","Ethiopia","Fiji","Finland","France","Gabon","Gambia","Georgia","Germany","Ghana","Greece","Grenada","Guatemala","Guinea","Guinea-Bissau","Guyana","Haiti","Honduras","Hungary","Iceland","India","Indonesia","Iran","Iraq","Ireland","Israel","Italy","Jamaica","Japan","Jordan","Kazakhstan","Kenya","Kiribati","Kuwait","Kyrgyzstan","Laos","Latvia","Lebanon","Lesotho","Liberia","Libya","Liechtenstein","Lithuania","Luxembourg","Madagascar","Malawi","Malaysia","Maldives","Mali","Malta","Marshall Islands","Mauritania","Mauritius","Mexico","Micronesia","Moldova","Monaco","Mongolia","Montenegro","Morocco","Mozambique","Myanmar","Namibia","Nauru","Nepal","Netherlands","New Zealand","Nicaragua","Niger","Nigeria","North Korea","North Macedonia","Norway","Oman","Pakistan","Palau","Palestine","Panama","Papua New Guinea","Paraguay","Peru","Philippines","Poland","Portugal","Qatar","Romania","Russia","Rwanda","Saint Kitts and Nevis","Saint Lucia","Saint Vincent and the Grenadines","Samoa","San Marino","Sao Tome and Principe","Saudi Arabia","Senegal","Serbia","Seychelles","Sierra Leone","Singapore","Slovakia","Slovenia","Solomon Islands","Somalia","South Africa","South Korea","South Sudan","Spain","Sri Lanka","Sudan","Suriname","Sweden","Switzerland","Syria","Taiwan","Tajikistan","Tanzania","Thailand","Timor-Leste","Togo","Tonga","Trinidad and Tobago","Tunisia","Turkey","Turkmenistan","Tuvalu","Uganda","Ukraine","United Arab Emirates","United Kingdom","United States","Uruguay","Uzbekistan","Vanuatu","Vatican City","Venezuela","Vietnam","Yemen","Zambia","Zimbabwe"]')},97381:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>u,Le:()=>r,jB:()=>o,tQ:()=>s,x9:()=>n});let i=(0,a(51990).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,t)=>{e.status="authenticated",e.user=t.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,t)=>{e.status="unauthenticated",e.error=t.payload,e.user=null}}}),{setAuthenticatedUser:s,setUnauthenticated:n,setAuthLoading:r,setAuthError:o}=i.actions,u=i.reducer}},e=>{var t=t=>e(e.s=t);e.O(0,[635,1445,6967,6903,2177,8441,1684,7358],()=>t(67356)),_N_E=e.O()}]);