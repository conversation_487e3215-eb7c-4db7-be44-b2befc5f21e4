"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1467],{22475:(e,t,n)=>{n.d(t,{UE:()=>ed,ll:()=>el,rD:()=>ep,UU:()=>eu,jD:()=>es,ER:()=>eh,cY:()=>ea,BN:()=>ec,Ej:()=>ef});let r=["top","right","bottom","left"],o=Math.min,i=Math.max,l=Math.round,a=Math.floor,c=e=>({x:e,y:e}),u={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function s(e,t){return"function"==typeof e?e(t):e}function d(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function p(e){return"x"===e?"y":"x"}function m(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(d(e))?"y":"x"}function v(e){return e.replace(/start|end/g,e=>f[e])}function y(e){return e.replace(/left|right|bottom|top/g,e=>u[e])}function w(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function x(e,t,n){let r,{reference:o,floating:i}=e,l=g(t),a=p(g(t)),c=m(a),u=d(t),f="y"===l,s=o.x+o.width/2-i.width/2,v=o.y+o.height/2-i.height/2,y=o[c]/2-i[c]/2;switch(u){case"top":r={x:s,y:o.y-i.height};break;case"bottom":r={x:s,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:v};break;case"left":r={x:o.x-i.width,y:v};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[a]-=y*(n&&f?-1:1);break;case"end":r[a]+=y*(n&&f?-1:1)}return r}let E=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),c=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:f,y:s}=x(u,r,c),d=r,h={},p=0;for(let n=0;n<a.length;n++){let{name:i,fn:m}=a[n],{x:g,y:v,data:y,reset:w}=await m({x:f,y:s,initialPlacement:r,placement:d,strategy:o,middlewareData:h,rects:u,platform:l,elements:{reference:e,floating:t}});f=null!=g?g:f,s=null!=v?v:s,h={...h,[i]:{...h[i],...y}},w&&p<=50&&(p++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(u=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:f,y:s}=x(u,d,c)),n=-1)}return{x:f,y:s,placement:d,strategy:o,middlewareData:h}};async function R(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:c}=e,{boundary:u="clippingAncestors",rootBoundary:f="viewport",elementContext:d="floating",altBoundary:h=!1,padding:p=0}=s(t,e),m=w(p),g=a[h?"floating"===d?"reference":"floating":d],v=b(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(g)))||n?g:g.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:f,strategy:c})),y="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,x=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),E=await (null==i.isElement?void 0:i.isElement(x))&&await (null==i.getScale?void 0:i.getScale(x))||{x:1,y:1},R=b(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:x,strategy:c}):y);return{top:(v.top-R.top+m.top)/E.y,bottom:(R.bottom-v.bottom+m.bottom)/E.y,left:(v.left-R.left+m.left)/E.x,right:(R.right-v.right+m.right)/E.x}}function A(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function S(e){return r.some(t=>e[t]>=0)}async function k(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=d(n),a=h(n),c="y"===g(n),u=["left","top"].includes(l)?-1:1,f=i&&c?-1:1,p=s(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:y}="number"==typeof p?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return a&&"number"==typeof y&&(v="end"===a?-1*y:y),c?{x:v*f,y:m*u}:{x:m*u,y:v*f}}function C(){return"undefined"!=typeof window}function L(e){return M(e)?(e.nodeName||"").toLowerCase():"#document"}function T(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function O(e){var t;return null==(t=(M(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function M(e){return!!C()&&(e instanceof Node||e instanceof T(e).Node)}function P(e){return!!C()&&(e instanceof Element||e instanceof T(e).Element)}function D(e){return!!C()&&(e instanceof HTMLElement||e instanceof T(e).HTMLElement)}function N(e){return!!C()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof T(e).ShadowRoot)}function j(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=I(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function W(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function B(e){let t=F(),n=P(e)?I(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function F(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function H(e){return["html","body","#document"].includes(L(e))}function I(e){return T(e).getComputedStyle(e)}function U(e){return P(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function V(e){if("html"===L(e))return e;let t=e.assignedSlot||e.parentNode||N(e)&&e.host||O(e);return N(t)?t.host:t}function Y(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=V(t);return H(n)?t.ownerDocument?t.ownerDocument.body:t.body:D(n)&&j(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=T(o);if(i){let e=X(l);return t.concat(l,l.visualViewport||[],j(o)?o:[],e&&n?Y(e):[])}return t.concat(o,Y(o,[],n))}function X(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function z(e){let t=I(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=D(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,c=l(n)!==i||l(r)!==a;return c&&(n=i,r=a),{width:n,height:r,$:c}}function _(e){return P(e)?e:e.contextElement}function q(e){let t=_(e);if(!D(t))return c(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=z(t),a=(i?l(n.width):n.width)/r,u=(i?l(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),u&&Number.isFinite(u)||(u=1),{x:a,y:u}}let $=c(0);function Z(e){let t=T(e);return F()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:$}function K(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=_(e),a=c(1);t&&(r?P(r)&&(a=q(r)):a=q(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===T(l))&&o)?Z(l):c(0),f=(i.left+u.x)/a.x,s=(i.top+u.y)/a.y,d=i.width/a.x,h=i.height/a.y;if(l){let e=T(l),t=r&&P(r)?T(r):r,n=e,o=X(n);for(;o&&r&&t!==n;){let e=q(o),t=o.getBoundingClientRect(),r=I(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;f*=e.x,s*=e.y,d*=e.x,h*=e.y,f+=i,s+=l,o=X(n=T(o))}}return b({width:d,height:h,x:f,y:s})}function G(e,t){let n=U(e).scrollLeft;return t?t.left+n:K(O(e)).left+n}function J(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:G(e,r)),y:r.top+t.scrollTop}}function Q(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=T(e),r=O(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,c=0;if(o){i=o.width,l=o.height;let e=F();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,c=o.offsetTop)}return{width:i,height:l,x:a,y:c}}(e,n);else if("document"===t)r=function(e){let t=O(e),n=U(e),r=e.ownerDocument.body,o=i(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),l=i(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+G(e),c=-n.scrollTop;return"rtl"===I(r).direction&&(a+=i(t.clientWidth,r.clientWidth)-o),{width:o,height:l,x:a,y:c}}(O(e));else if(P(t))r=function(e,t){let n=K(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=D(e)?q(e):c(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=Z(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function ee(e){return"static"===I(e).position}function et(e,t){if(!D(e)||"fixed"===I(e).position)return null;if(t)return t(e);let n=e.offsetParent;return O(e)===n&&(n=n.ownerDocument.body),n}function en(e,t){let n=T(e);if(W(e))return n;if(!D(e)){let t=V(e);for(;t&&!H(t);){if(P(t)&&!ee(t))return t;t=V(t)}return n}let r=et(e,t);for(;r&&["table","td","th"].includes(L(r))&&ee(r);)r=et(r,t);return r&&H(r)&&ee(r)&&!B(r)?n:r||function(e){let t=V(e);for(;D(t)&&!H(t);){if(B(t))return t;if(W(t))break;t=V(t)}return null}(e)||n}let er=async function(e){let t=this.getOffsetParent||en,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=D(t),o=O(t),i="fixed"===n,l=K(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=c(0);if(r||!r&&!i)if(("body"!==L(t)||j(o))&&(a=U(t)),r){let e=K(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=G(o));let f=!o||r||i?c(0):J(o,a);return{x:l.left+a.scrollLeft-u.x-f.x,y:l.top+a.scrollTop-u.y-f.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eo={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=O(r),a=!!t&&W(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},f=c(1),s=c(0),d=D(r);if((d||!d&&!i)&&(("body"!==L(r)||j(l))&&(u=U(r)),D(r))){let e=K(r);f=q(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let h=!l||d||i?c(0):J(l,u,!0);return{width:n.width*f.x,height:n.height*f.y,x:n.x*f.x-u.scrollLeft*f.x+s.x+h.x,y:n.y*f.y-u.scrollTop*f.y+s.y+h.y}},getDocumentElement:O,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:l}=e,a=[..."clippingAncestors"===n?W(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=Y(e,[],!1).filter(e=>P(e)&&"body"!==L(e)),o=null,i="fixed"===I(e).position,l=i?V(e):e;for(;P(l)&&!H(l);){let t=I(l),n=B(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||j(l)&&!n&&function e(t,n){let r=V(t);return!(r===n||!P(r)||H(r))&&("fixed"===I(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=V(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],c=a[0],u=a.reduce((e,n)=>{let r=Q(t,n,l);return e.top=i(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=i(r.left,e.left),e},Q(t,c,l));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:en,getElementRects:er,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=z(e);return{width:t,height:n}},getScale:q,isElement:P,isRTL:function(e){return"rtl"===I(e).direction}};function ei(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function el(e,t,n,r){let l;void 0===r&&(r={});let{ancestorScroll:c=!0,ancestorResize:u=!0,elementResize:f="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,h=_(e),p=c||u?[...h?Y(h):[],...Y(t)]:[];p.forEach(e=>{c&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=h&&s?function(e,t){let n,r=null,l=O(e);function c(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function u(f,s){void 0===f&&(f=!1),void 0===s&&(s=1),c();let d=e.getBoundingClientRect(),{left:h,top:p,width:m,height:g}=d;if(f||t(),!m||!g)return;let v=a(p),y=a(l.clientWidth-(h+m)),w={rootMargin:-v+"px "+-y+"px "+-a(l.clientHeight-(p+g))+"px "+-a(h)+"px",threshold:i(0,o(1,s))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==s){if(!b)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||ei(d,e.getBoundingClientRect())||u(),b=!1}try{r=new IntersectionObserver(x,{...w,root:l.ownerDocument})}catch(e){r=new IntersectionObserver(x,w)}r.observe(e)}(!0),c}(h,n):null,g=-1,v=null;f&&(v=new ResizeObserver(e=>{let[r]=e;r&&r.target===h&&v&&(v.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),n()}),h&&!d&&v.observe(h),v.observe(t));let y=d?K(e):null;return d&&function t(){let r=K(e);y&&!ei(y,r)&&n(),y=r,l=requestAnimationFrame(t)}(),n(),()=>{var e;p.forEach(e=>{c&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=v)||e.disconnect(),v=null,d&&cancelAnimationFrame(l)}}let ea=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,c=await k(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+c.x,y:i+c.y,data:{...c,placement:l}}}}},ec=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:l}=t,{mainAxis:a=!0,crossAxis:c=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...f}=s(e,t),h={x:n,y:r},m=await R(t,f),v=g(d(l)),y=p(v),w=h[y],b=h[v];if(a){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+m[e],r=w-m[t];w=i(n,o(w,r))}if(c){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=b+m[e],r=b-m[t];b=i(n,o(b,r))}let x=u.fn({...t,[y]:w,[v]:b});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[y]:a,[v]:c}}}}}},eu=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:c,rects:u,initialPlacement:f,platform:w,elements:b}=t,{mainAxis:x=!0,crossAxis:E=!0,fallbackPlacements:A,fallbackStrategy:S="bestFit",fallbackAxisSideDirection:k="none",flipAlignment:C=!0,...L}=s(e,t);if(null!=(n=c.arrow)&&n.alignmentOffset)return{};let T=d(a),O=g(f),M=d(f)===f,P=await (null==w.isRTL?void 0:w.isRTL(b.floating)),D=A||(M||!C?[y(f)]:function(e){let t=y(e);return[v(e),t,v(t)]}(f)),N="none"!==k;!A&&N&&D.push(...function(e,t,n,r){let o=h(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(d(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(v)))),i}(f,C,k,P));let j=[f,...D],W=await R(t,L),B=[],F=(null==(r=c.flip)?void 0:r.overflows)||[];if(x&&B.push(W[T]),E){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=p(g(e)),i=m(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=y(l)),[l,y(l)]}(a,u,P);B.push(W[e[0]],W[e[1]])}if(F=[...F,{placement:a,overflows:B}],!B.every(e=>e<=0)){let e=((null==(o=c.flip)?void 0:o.index)||0)+1,t=j[e];if(t)return{data:{index:e,overflows:F},reset:{placement:t}};let n=null==(i=F.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(S){case"bestFit":{let e=null==(l=F.filter(e=>{if(N){let t=g(e.placement);return t===O||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=f}if(a!==n)return{reset:{placement:n}}}return{}}}},ef=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let l,a,{placement:c,rects:u,platform:f,elements:p}=t,{apply:m=()=>{},...v}=s(e,t),y=await R(t,v),w=d(c),b=h(c),x="y"===g(c),{width:E,height:A}=u.floating;"top"===w||"bottom"===w?(l=w,a=b===(await (null==f.isRTL?void 0:f.isRTL(p.floating))?"start":"end")?"left":"right"):(a=w,l="end"===b?"top":"bottom");let S=A-y.top-y.bottom,k=E-y.left-y.right,C=o(A-y[l],S),L=o(E-y[a],k),T=!t.middlewareData.shift,O=C,M=L;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(M=k),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(O=S),T&&!b){let e=i(y.left,0),t=i(y.right,0),n=i(y.top,0),r=i(y.bottom,0);x?M=E-2*(0!==e||0!==t?e+t:i(y.left,y.right)):O=A-2*(0!==n||0!==r?n+r:i(y.top,y.bottom))}await m({...t,availableWidth:M,availableHeight:O});let P=await f.getDimensions(p.floating);return E!==P.width||A!==P.height?{reset:{rects:!0}}:{}}}},es=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=s(e,t);switch(r){case"referenceHidden":{let e=A(await R(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:S(e)}}}case"escaped":{let e=A(await R(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:S(e)}}}default:return{}}}}},ed=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:l,rects:a,platform:c,elements:u,middlewareData:f}=t,{element:d,padding:v=0}=s(e,t)||{};if(null==d)return{};let y=w(v),b={x:n,y:r},x=p(g(l)),E=m(x),R=await c.getDimensions(d),A="y"===x,S=A?"clientHeight":"clientWidth",k=a.reference[E]+a.reference[x]-b[x]-a.floating[E],C=b[x]-a.reference[x],L=await (null==c.getOffsetParent?void 0:c.getOffsetParent(d)),T=L?L[S]:0;T&&await (null==c.isElement?void 0:c.isElement(L))||(T=u.floating[S]||a.floating[E]);let O=T/2-R[E]/2-1,M=o(y[A?"top":"left"],O),P=o(y[A?"bottom":"right"],O),D=T-R[E]-P,N=T/2-R[E]/2+(k/2-C/2),j=i(M,o(N,D)),W=!f.arrow&&null!=h(l)&&N!==j&&a.reference[E]/2-(N<M?M:P)-R[E]/2<0,B=W?N<M?N-M:N-D:0;return{[x]:b[x]+B,data:{[x]:j,centerOffset:N-j-B,...W&&{alignmentOffset:B}},reset:W}}}),eh=function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:c=!0,crossAxis:u=!0}=s(e,t),f={x:n,y:r},h=g(o),m=p(h),v=f[m],y=f[h],w=s(a,t),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(c){let e="y"===m?"height":"width",t=i.reference[m]-i.floating[e]+b.mainAxis,n=i.reference[m]+i.reference[e]-b.mainAxis;v<t?v=t:v>n&&(v=n)}if(u){var x,E;let e="y"===m?"width":"height",t=["top","left"].includes(d(o)),n=i.reference[h]-i.floating[e]+(t&&(null==(x=l.offset)?void 0:x[h])||0)+(t?0:b.crossAxis),r=i.reference[h]+i.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[h])||0)-(t?b.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[m]:v,[h]:y}}}},ep=(e,t,n)=>{let r=new Map,o={platform:eo,...n},i={...o.platform,_c:r};return E(e,t,{...o,platform:i})}},38168:(e,t,n)=>{n.d(t,{Eq:()=>f});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,l={},a=0,c=function(e){return e&&(e.host||c(e.parentNode))},u=function(e,t,n,r){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=c(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});l[n]||(l[n]=new WeakMap);var f=l[n],s=[],d=new Set,h=new Set(u),p=function(e){!e||d.has(e)||(d.add(e),p(e.parentNode))};u.forEach(p);var m=function(e){!e||h.has(e)||Array.prototype.forEach.call(e.children,function(e){if(d.has(e))m(e);else try{var t=e.getAttribute(r),l=null!==t&&"false"!==t,a=(o.get(e)||0)+1,c=(f.get(e)||0)+1;o.set(e,a),f.set(e,c),s.push(e),1===a&&l&&i.set(e,!0),1===c&&e.setAttribute(n,"true"),l||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),d.clear(),a++,function(){s.forEach(function(e){var t=o.get(e)-1,l=f.get(e)-1;o.set(e,t),f.set(e,l),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),l||e.removeAttribute(n)}),--a||(o=new WeakMap,o=new WeakMap,i=new WeakMap,l={})}},f=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live]"))),u(o,i,n,"aria-hidden")):function(){return null}}},51595:(e,t,n)=>{n.d(t,{U:()=>i});var r=n(12115),o=n(39033);function i(e,t=globalThis?.document){let n=(0,o.c)(e);r.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}},74466:(e,t,n)=>{n.d(t,{F:()=>l});var r=n(52596);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=r.$,l=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return i(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:l,defaultVariants:a}=t,c=Object.keys(l).map(e=>{let t=null==n?void 0:n[e],r=null==a?void 0:a[e];if(null===t)return null;let i=o(t)||o(r);return l[e][i]}),u=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return i(e,c,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...o}=t;return Object.entries(o).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...a,...u}[t]):({...a,...u})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},84945:(e,t,n)=>{n.d(t,{BN:()=>p,ER:()=>m,Ej:()=>v,UE:()=>w,UU:()=>g,cY:()=>h,jD:()=>y,we:()=>s});var r=n(22475),o=n(12115),i=n(47650),l="undefined"!=typeof document?o.useLayoutEffect:o.useEffect;function a(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!a(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!a(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function c(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function u(e,t){let n=c(e);return Math.round(t*n)/n}function f(e){let t=o.useRef(e);return l(()=>{t.current=e}),t}function s(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:s=[],platform:d,elements:{reference:h,floating:p}={},transform:m=!0,whileElementsMounted:g,open:v}=e,[y,w]=o.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[b,x]=o.useState(s);a(b,s)||x(s);let[E,R]=o.useState(null),[A,S]=o.useState(null),k=o.useCallback(e=>{e!==O.current&&(O.current=e,R(e))},[]),C=o.useCallback(e=>{e!==M.current&&(M.current=e,S(e))},[]),L=h||E,T=p||A,O=o.useRef(null),M=o.useRef(null),P=o.useRef(y),D=null!=g,N=f(g),j=f(d),W=f(v),B=o.useCallback(()=>{if(!O.current||!M.current)return;let e={placement:t,strategy:n,middleware:b};j.current&&(e.platform=j.current),(0,r.rD)(O.current,M.current,e).then(e=>{let t={...e,isPositioned:!1!==W.current};F.current&&!a(P.current,t)&&(P.current=t,i.flushSync(()=>{w(t)}))})},[b,t,n,j,W]);l(()=>{!1===v&&P.current.isPositioned&&(P.current.isPositioned=!1,w(e=>({...e,isPositioned:!1})))},[v]);let F=o.useRef(!1);l(()=>(F.current=!0,()=>{F.current=!1}),[]),l(()=>{if(L&&(O.current=L),T&&(M.current=T),L&&T){if(N.current)return N.current(L,T,B);B()}},[L,T,B,N,D]);let H=o.useMemo(()=>({reference:O,floating:M,setReference:k,setFloating:C}),[k,C]),I=o.useMemo(()=>({reference:L,floating:T}),[L,T]),U=o.useMemo(()=>{let e={position:n,left:0,top:0};if(!I.floating)return e;let t=u(I.floating,y.x),r=u(I.floating,y.y);return m?{...e,transform:"translate("+t+"px, "+r+"px)",...c(I.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,m,I.floating,y.x,y.y]);return o.useMemo(()=>({...y,update:B,refs:H,elements:I,floatingStyles:U}),[y,B,H,I,U])}let d=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:o}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?(0,r.UE)({element:n.current,padding:o}).fn(t):{}:n?(0,r.UE)({element:n,padding:o}).fn(t):{}}}),h=(e,t)=>({...(0,r.cY)(e),options:[e,t]}),p=(e,t)=>({...(0,r.BN)(e),options:[e,t]}),m=(e,t)=>({...(0,r.ER)(e),options:[e,t]}),g=(e,t)=>({...(0,r.UU)(e),options:[e,t]}),v=(e,t)=>({...(0,r.Ej)(e),options:[e,t]}),y=(e,t)=>({...(0,r.jD)(e),options:[e,t]}),w=(e,t)=>({...d(e),options:[e,t]})},92293:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(12115),o=0;function i(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:l()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},93795:(e,t,n)=>{n.d(t,{A:()=>_});var r,o,i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function l(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,n(12115)),c="right-scroll-bar-position",u="width-before-scroll-bar";function f(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var s="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,d=new WeakMap;function h(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,o,l=(t=null,void 0===n&&(n=h),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),r={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),r}}}});return l.options=i({async:!0,ssr:!1},e),l}(),m=function(){},g=a.forwardRef(function(e,t){var n,r,o,c,u=a.useRef(null),h=a.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),g=h[0],v=h[1],y=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,R=e.shards,A=e.sideCar,S=e.noIsolation,k=e.inert,C=e.allowPinchZoom,L=e.as,T=e.gapMode,O=l(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),M=(n=[u,t],r=function(e){return n.forEach(function(t){return f(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,c=o.facade,s(function(){var e=d.get(c);if(e){var t=new Set(e),r=new Set(n),o=c.current;t.forEach(function(e){r.has(e)||f(e,null)}),r.forEach(function(e){t.has(e)||f(e,o)})}d.set(c,n)},[n]),c),P=i(i({},O),g);return a.createElement(a.Fragment,null,E&&a.createElement(A,{sideCar:p,removeScrollBar:x,shards:R,noIsolation:S,inert:k,setCallbacks:v,allowPinchZoom:!!C,lockRef:u,gapMode:T}),y?a.cloneElement(a.Children.only(w),i(i({},P),{ref:M})):a.createElement(void 0===L?"div":L,i({},P,{className:b,ref:M}),w))});g.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},g.classNames={fullWidth:u,zeroRight:c};var v=function(e){var t=e.sideCar,n=l(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,i({},n))};v.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=y();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},R=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},A=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=R(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},S=b(),k="data-scroll-locked",C=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(k,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(c," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(k,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},L=function(){var e=parseInt(document.body.getAttribute(k)||"0",10);return isFinite(e)?e:0},T=function(){a.useEffect(function(){return document.body.setAttribute(k,(L()+1).toString()),function(){var e=L()-1;e<=0?document.body.removeAttribute(k):document.body.setAttribute(k,e.toString())}},[])},O=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;T();var i=a.useMemo(function(){return A(o)},[o]);return a.createElement(S,{styles:C(i,!t,o,n?"":"!important")})},M=!1;if("undefined"!=typeof window)try{var P=Object.defineProperty({},"passive",{get:function(){return M=!0,!0}});window.addEventListener("test",P,P),window.removeEventListener("test",P,P)}catch(e){M=!1}var D=!!M&&{passive:!1},N=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},j=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),W(e,r)){var o=B(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},W=function(e,t){return"v"===e?N(t,"overflowY"):N(t,"overflowX")},B=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},F=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,c=n.target,u=t.contains(c),f=!1,s=a>0,d=0,h=0;do{var p=B(e,c),m=p[0],g=p[1]-p[2]-l*m;(m||g)&&W(e,c)&&(d+=g,h+=m),c=c instanceof ShadowRoot?c.host:c.parentNode}while(!u&&c!==document.body||u&&(t.contains(c)||t===c));return s&&(o&&1>Math.abs(d)||!o&&a>d)?f=!0:!s&&(o&&1>Math.abs(h)||!o&&-a>h)&&(f=!0),f},H=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},I=function(e){return[e.deltaX,e.deltaY]},U=function(e){return e&&"current"in e?e.current:e},V=0,Y=[];let X=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(V++)[0],i=a.useState(b)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(U),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=H(e),a=n.current,c="deltaX"in e?e.deltaX:a[0]-i[0],u="deltaY"in e?e.deltaY:a[1]-i[1],f=e.target,s=Math.abs(c)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===s&&"range"===f.type)return!1;var d=j(s,f);if(!d)return!0;if(d?o=s:(o="v"===s?"h":"v",d=j(s,f)),!d)return!1;if(!r.current&&"changedTouches"in e&&(c||u)&&(r.current=o),!o)return!0;var h=r.current||o;return F(h,t,e,"h"===h?c:u,!0)},[]),u=a.useCallback(function(e){if(Y.length&&Y[Y.length-1]===i){var n="deltaY"in e?I(e):H(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(U).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?c(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),f=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),s=a.useCallback(function(e){n.current=H(e),r.current=void 0},[]),d=a.useCallback(function(t){f(t.type,I(t),t.target,c(t,e.lockRef.current))},[]),h=a.useCallback(function(t){f(t.type,H(t),t.target,c(t,e.lockRef.current))},[]);a.useEffect(function(){return Y.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:h}),document.addEventListener("wheel",u,D),document.addEventListener("touchmove",u,D),document.addEventListener("touchstart",s,D),function(){Y=Y.filter(function(e){return e!==i}),document.removeEventListener("wheel",u,D),document.removeEventListener("touchmove",u,D),document.removeEventListener("touchstart",s,D)}},[]);var p=e.removeScrollBar,m=e.inert;return a.createElement(a.Fragment,null,m?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,p?a.createElement(O,{gapMode:e.gapMode}):null)},p.useMedium(r),v);var z=a.forwardRef(function(e,t){return a.createElement(g,i({},e,{ref:t,sideCar:X}))});z.classNames=g.classNames;let _=z}}]);