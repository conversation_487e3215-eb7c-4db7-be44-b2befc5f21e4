(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7453],{2511:(e,a,t)=>{"use strict";t.d(a,{b:()=>n});let n={information_media:"Information / Media",econommic_social_development:"Economic & Social Development",security_police_peacekeeping:"Security / Police / Peacekeeping",disarmament_and_demobilization:"Disarmament & Demobilization",environment:"Environment",private_sector:"Private Sector",humanitarian_coordination_information_management:"Humanitarian - Coordination & Info Management",humanitarian_multiple_clusters:"Humanitarian - Multiple Clusters",humanitarian_camp_management_and_coordination:"Humanitarian - Camp Management & Coordination",humanitarian_early_recovery:"Humanitarian - Early Recovery",humanitarian_education:"Humanitarian - Education",humanitarian_emergency_shelter:"Humanitarian - Emergency Shelter",humanitarian_emergency_telecoms:"Humanitarian - Emergency Telecoms",humanitarian_food_security:"Humanitarian - Food Security",humanitarian_health:"Humanitarian - Health",humanitarian_logistics:"Humanitarian - Logistics",humanitarian_nutrition:"Humanitarian - Nutrition",humanitarian_protection:"Humanitarian - Protection",humanitarian_sanitation_water_and_hygiene:"Humanitarian - Sanitation / Water / Hygiene",other:"Other"}},13163:(e,a,t)=>{"use strict";t.d(a,{A:()=>o});var n=t(95155),i=t(60760),s=t(44518),r=t(95233),l=t(54416);t(12115);let o=e=>{let{children:a,className:t,isOpen:o,onClose:u,preventOutsideClick:c=!1}=e;return(0,n.jsx)(i.N,{children:o&&(0,n.jsx)(s.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40 p-4 overflow-y-auto",onClick:e=>{c||u()},children:(0,n.jsxs)(s.P.div,{initial:{scale:.6,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.6,opacity:0},transition:{duration:.3,ease:r.am},className:"relative section flex flex-col max-w-4xl max-h-[90vh] overflow-y-auto ".concat(t),onClick:e=>e.stopPropagation(),children:[(0,n.jsx)(l.A,{onClick:u,className:"absolute self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300"}),a]})})})}},25784:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let n=t(23464).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});n.interceptors.request.use(e=>e,e=>Promise.reject(e)),n.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let i=n},29350:(e,a,t)=>{"use strict";t.d(a,{A:()=>u});var n=t(97381),i=t(59362),s=t(25784),r=t(35695),l=t(12115),o=t(34540);let u=e=>{let a=(0,o.wA)(),t=(0,r.useRouter)(),u=(0,r.usePathname)(),{status:c,user:d,error:m}=(0,o.d4)(e=>e.auth),p=async()=>{try{a((0,n.Le)());let e=(await s.A.get("/users/me")).data;a((0,n.tQ)(e))}catch(s){if(a((0,n.x9)()),(0,i.F0)(s)){var e,r,l,o,c;if(console.error("Auth error:",null==(e=s.response)?void 0:e.status,null==(r=s.response)?void 0:r.data),(null==(l=s.response)?void 0:l.status)===401){if(u.startsWith("/form-submission"))return;t.push("/")}else a((0,n.jB)((null==(c=s.response)||null==(o=c.data)?void 0:o.message)||s.message))}else a((0,n.jB)(s instanceof Error?s.message:"An unknown error occurred."))}};return(0,l.useEffect)(()=>{(null==e?void 0:e.skipFetchUser)||p()},[null==e?void 0:e.skipFetchUser]),(0,l.useEffect)(()=>{let e=e=>{if("logout"===e.key&&"true"===e.newValue)if(a((0,n.x9)()),u.startsWith("/form-submission")){let e=u.split("/")[2];e?t.push("/form-submission/".concat(e,"/sign-in")):t.push("/")}else t.push("/")};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[a,t,u]),{status:c,user:d,error:m,isAuthenticated:"authenticated"===c,isLoading:"loading"===c,refreshAuthState:()=>{p()},signin:async(e,a,t)=>{try{await s.A.post("/users/login",e),await p(),null==a||a()}catch(e){if(e instanceof i.pe){var n,r;let a=null==(r=e.response)||null==(n=r.data)?void 0:n.errorType;null==t||t(a)}else null==t||t()}},logout:async()=>{try{await s.A.post("/users/logout"),localStorage.setItem("logout","true"),setTimeout(()=>localStorage.removeItem("logout"),100)}finally{if(a((0,n.x9)()),u.startsWith("/form-submission")){let e=u.split("/")[2];e?t.push("/form-submission/".concat(e,"/sign-in")):t.push("/")}else t.push("/")}}}}},50408:(e,a,t)=>{"use strict";t.d(a,{l:()=>r});var n=t(95155),i=t(66474),s=t(12115);let r=e=>{let{id:a,options:t,value:r,onChange:l}=e,[o,u]=(0,s.useState)(!1),c=(0,s.useRef)(null),d=(0,s.useRef)([]),m=(0,s.useRef)(null);(0,s.useEffect)(()=>{let e=e=>{m.current&&!m.current.contains(e.target)&&u(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let p=e=>{if(!o)return;let a=e.key.toLowerCase();if(a.match(/[a-z]/)){let e=t.findIndex(e=>e.toLowerCase().startsWith(a));if(-1!==e&&d.current[e]){var n;null==(n=d.current[e])||n.scrollIntoView({behavior:"auto",block:"nearest"})}}};return(0,s.useEffect)(()=>(document.addEventListener("keydown",p),()=>{document.removeEventListener("keydown",p)}),[o,t]),(0,n.jsxs)("div",{className:"relative",ref:m,children:[(0,n.jsxs)("button",{id:a,type:"button",className:"px-4 py-2 flex items-center justify-between rounded-md border border-neutral-400 focus:border-primary-500 duration-300 w-full text-left cursor-pointer",onClick:()=>{u(!o)},children:[(0,n.jsx)("span",{children:r||"Select an option"}),(0,n.jsx)(i.A,{})]}),o&&(0,n.jsx)("ul",{className:"absolute z-10 max-h-[180px] overflow-auto border border-neutral-400 rounded-md bg-neutral-100 w-full mt-1 flex flex-col",ref:c,children:t.map((e,a)=>(0,n.jsx)("li",{ref:e=>{d.current[a]=e},className:"cursor-pointer bg-neutral-100 hover:bg-neutral-200 px-4 py-2",onClick:()=>{l(e),u(!1)},children:e},a))})]})}},57799:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});var n=t(95155);t(12115);let i=()=>(0,n.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,n.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},63642:(e,a,t)=>{"use strict";t.d(a,{R:()=>s});var n=t(95155);t(12115);var i=t(13163);let s=e=>{let{showModal:a,onClose:t,onConfirm:s,title:r,description:l,confirmButtonText:o,cancelButtonText:u,confirmButtonClass:c,children:d}=e;return(0,n.jsxs)(i.A,{isOpen:a,onClose:t,className:"p-6 rounded-md max-w-xl",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:r}),(0,n.jsx)("div",{className:"text-neutral-700 mt-2",children:l}),d&&(0,n.jsx)("div",{className:"mt-6 space-y-4",children:d}),(0,n.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,n.jsx)("button",{className:"btn-outline",onClick:t,type:"button",children:u||"Cancel"}),(0,n.jsx)("button",{className:"font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ".concat(c),onClick:s,type:"button",children:o})]})]})}},64368:(e,a,t)=>{"use strict";t.d(a,{H:()=>n});let n=(e,a)=>{let t=Object.entries(a).find(a=>{let[t,n]=a;return n===e});return t?t[0]:null}},71402:(e,a,t)=>{"use strict";t.d(a,{Ay:()=>r,Ds:()=>i,_b:()=>s});let n=(0,t(51990).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,a)=>{e.message=a.payload.message,e.type=a.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:i,hideNotification:s}=n.actions,r=n.reducer},73918:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>C});var n=t(95155),i=t(12115),s=t(62177),r=t(25784),l=t(35695),o=t(88570),u=t(26715),c=t(19373),d=t(5041),m=t(34540),p=t(71402),h=t(50408),y=t(57434),x=t(34869),g=t(17576),f=t(57799),b=t(2511),v=t(74567),j=t(64368),N=t(29350),S=t(63642),w=t(94974);let A=async e=>{let{templateId:a,dataToSend:t}=e,{data:n}=await r.A.patch("/libraries/".concat(a),t);return n},C=()=>{let[e,a]=(0,i.useState)(!1);(0,i.useEffect)(()=>{a(!0)},[]);let{register:t,formState:{isSubmitting:r,errors:C,isSubmitted:E},handleSubmit:_,setValue:k,reset:T}=(0,s.mN)(),L=(0,l.useRouter)(),[P,B]=(0,i.useState)(!1),[M,z]=(0,i.useState)(null),[I,F]=(0,i.useState)(null),[H,R]=(0,i.useState)(!1),[D,K]=(0,i.useState)(!1),[q,G]=(0,i.useState)(null);(0,i.useEffect)(()=>{t("country",{required:"Please select a country"}),t("sector",{required:"Please select a sector"})},[t]),(0,i.useEffect)(()=>{k("country",M,{shouldValidate:E}),k("sector",I,{shouldValidate:E})},[k,M,I]);let{hashedId:U}=(0,l.useParams)(),V=(0,o.D)(U),{user:O}=(0,N.A)(),Q=(0,u.jE)();(0,i.useEffect)(()=>()=>{V&&(null==O?void 0:O.id)&&Q.cancelQueries({queryKey:["templates",O.id,V]})},[V,null==O?void 0:O.id,Q]);let{data:J,isLoading:W,isError:Z}=(0,c.I)({queryKey:["templates",null==O?void 0:O.id,V],queryFn:()=>(0,w.J2)({templateId:V}),enabled:!!V&&!!(null==O?void 0:O.id)});(0,i.useEffect)(()=>{J&&(T({templateName:J.name||"",description:J.description||"",country:J.country||"",sector:J.sector||""}),z(J.country||null),F(J.sector||null))},[J,T]);let X=(0,m.wA)(),Y=(0,d.n)({mutationFn:A,onSuccess:()=>{Q.invalidateQueries({queryKey:["templates"],exact:!1}),X((0,p.Ds)({message:"Template details have been updated",type:"success"}))},onError:e=>{X((0,p.Ds)({message:"Failed to update template details. Please try again."+e.message,type:"error"}))}}),$=(0,d.n)({mutationFn:()=>(0,w.I7)(V),onSuccess:()=>{B(!0),K(!1),Q.cancelQueries({queryKey:["templates",null==O?void 0:O.id,V]}),Q.removeQueries({queryKey:["template",null==O?void 0:O.id,V]}),Q.invalidateQueries({queryKey:["templates"],exact:!1}),X((0,p.Ds)({message:"Template has been deleted successfully",type:"success"})),setTimeout(()=>{L.push("/library")},1e3)},onError:e=>{K(!1),console.error("Template deletion error:",e),X((0,p.Ds)({message:"Failed to delete template. Please try again.",type:"error"}))}}),ee=async e=>{Y.mutate({templateId:V,dataToSend:{name:e.templateName,description:e.description,country:e.country,sector:e.sector}})};return e?P||W?(0,n.jsx)(f.A,{}):U&&null!==V?Z&&!P?(0,n.jsx)("p",{className:"text-red-500",children:"Failed to fetch template. Please try again."}):(0,n.jsxs)("form",{className:"flex flex-col gap-8",onSubmit:_(ee),children:[(0,n.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,n.jsxs)("div",{className:"label-input-group group",children:[(0,n.jsxs)("label",{htmlFor:"template-name",className:"label-text",children:[(0,n.jsx)(y.A,{size:16})," Template Name"]}),(0,n.jsx)("input",{...t("templateName",{required:"template name is required."}),id:"template-name",type:"text",className:"input-field",placeholder:"Enter a template name"}),C.templateName&&(0,n.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(C.templateName.message)})]}),(0,n.jsxs)("div",{className:"label-input-group group",children:[(0,n.jsx)("label",{htmlFor:"description",className:"label-text",children:"Description"}),(0,n.jsx)("textarea",{id:"description",...t("description"),className:"input-field resize-none",cols:4,placeholder:"Enter the template description"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{className:"label-input-group group",children:[(0,n.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,n.jsx)(x.A,{size:16}),"Country"]}),(0,n.jsx)(h.l,{id:"country",options:v,value:M,onChange:z}),C.country&&(0,n.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(C.country.message)})]}),(0,n.jsxs)("div",{className:"label-input-group group",children:[(0,n.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,n.jsx)(g.A,{size:16})," Sector"]}),(0,n.jsx)(h.l,{id:"sector",options:Object.values(b.b),value:I&&b.b[I]?b.b[I]:"Select an option",onChange:e=>{F((0,j.H)(e,b.b))}}),C.sector&&(0,n.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(C.sector.message)})]})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("div",{className:"flex items-center gap-4",children:(0,n.jsx)("button",{onClick:()=>{G({title:"Confirm Delete",description:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("p",{children:"Are you sure you want to delete this template? This action cannot be undone."}),(0,n.jsxs)("ul",{className:"list-disc list-inside flex flex-col gap-2 mt-2 text-neutral-700",children:[(0,n.jsx)("li",{children:"All data gathered for this template will be deleted."}),(0,n.jsx)("li",{children:"Forms associated with this template will be deleted."}),(0,n.jsx)("li",{children:"You will not be able to recover this template after deletion."})]})]}),confirmButtonText:"Delete",confirmButtonClass:"btn-danger",onConfirm:()=>{$.mutate()}}),K(!0)},type:"button",className:"btn-danger",children:"delete"})}),(0,n.jsx)("button",{type:"submit",className:"btn-primary self-end",children:r?(0,n.jsxs)("span",{className:"flex items-center gap-2",children:["Saving"," ",(0,n.jsx)("div",{className:"size-4 animate-spin border-x border-neutral-100 rounded-full"})]}):"Save Changes"})]})]}),q&&(0,n.jsx)(S.R,{showModal:D,onClose:()=>K(!1),title:q.title,description:q.description,confirmButtonText:q.confirmButtonText,confirmButtonClass:q.confirmButtonClass,onConfirm:q.onConfirm})]}):(0,n.jsxs)("div",{className:"error-message",children:[(0,n.jsx)("h1",{className:"text-red-500",children:"Error: Invalid Template ID (hashedId)."}),(0,n.jsx)("p",{className:"text-neutral-700",children:"Please make sure the URL contains a valid template identifier."})]}):null}},74567:e=>{"use strict";e.exports=JSON.parse('["Afghanistan","Albania","Algeria","Andorra","Angola","Argentina","Armenia","Australia","Austria","Azerbaijan","Bahamas","Bahrain","Bangladesh","Barbados","Belarus","Belgium","Belize","Benin","Bhutan","Bolivia","Bosnia and Herzegovina","Botswana","Brazil","Brunei","Bulgaria","Burkina Faso","Burundi","Cabo Verde","Cambodia","Cameroon","Canada","Central African Republic","Chad","Chile","China","Colombia","Comoros","Congo (Congo-Brazzaville)","Costa Rica","Croatia","Cuba","Cyprus","Czech Republic","Denmark","Djibouti","Dominica","Dominican Republic","Ecuador","Egypt","El Salvador","Equatorial Guinea","Eritrea","Estonia","Eswatini","Ethiopia","Fiji","Finland","France","Gabon","Gambia","Georgia","Germany","Ghana","Greece","Grenada","Guatemala","Guinea","Guinea-Bissau","Guyana","Haiti","Honduras","Hungary","Iceland","India","Indonesia","Iran","Iraq","Ireland","Israel","Italy","Jamaica","Japan","Jordan","Kazakhstan","Kenya","Kiribati","Kuwait","Kyrgyzstan","Laos","Latvia","Lebanon","Lesotho","Liberia","Libya","Liechtenstein","Lithuania","Luxembourg","Madagascar","Malawi","Malaysia","Maldives","Mali","Malta","Marshall Islands","Mauritania","Mauritius","Mexico","Micronesia","Moldova","Monaco","Mongolia","Montenegro","Morocco","Mozambique","Myanmar","Namibia","Nauru","Nepal","Netherlands","New Zealand","Nicaragua","Niger","Nigeria","North Korea","North Macedonia","Norway","Oman","Pakistan","Palau","Palestine","Panama","Papua New Guinea","Paraguay","Peru","Philippines","Poland","Portugal","Qatar","Romania","Russia","Rwanda","Saint Kitts and Nevis","Saint Lucia","Saint Vincent and the Grenadines","Samoa","San Marino","Sao Tome and Principe","Saudi Arabia","Senegal","Serbia","Seychelles","Sierra Leone","Singapore","Slovakia","Slovenia","Solomon Islands","Somalia","South Africa","South Korea","South Sudan","Spain","Sri Lanka","Sudan","Suriname","Sweden","Switzerland","Syria","Taiwan","Tajikistan","Tanzania","Thailand","Timor-Leste","Togo","Tonga","Trinidad and Tobago","Tunisia","Turkey","Turkmenistan","Tuvalu","Uganda","Ukraine","United Arab Emirates","United Kingdom","United States","Uruguay","Uzbekistan","Vanuatu","Vatican City","Venezuela","Vietnam","Yemen","Zambia","Zimbabwe"]')},84216:(e,a,t)=>{Promise.resolve().then(t.bind(t,73918))},88570:(e,a,t)=>{"use strict";t.d(a,{D:()=>l,l:()=>r});var n=t(41050);let i=t(49509).env.SALT||"rushan-salt",s=new n.A(i,12),r=e=>s.encode(e),l=e=>{let a=s.decode(e)[0];return"bigint"==typeof a?a<Number.MAX_SAFE_INTEGER?Number(a):null:"number"==typeof a?a:null}},94974:(e,a,t)=>{"use strict";t.d(a,{I7:()=>l,J2:()=>i,QK:()=>r,Xu:()=>s,nh:()=>o});var n=t(25784);let i=async e=>{let{templateId:a}=e,{data:t}=await n.A.get("/libraries/".concat(a));return t.template},s=async e=>{let{data:a}=await n.A.post("/libraries",e);return a},r=async()=>{let{data:e}=await n.A.get("/libraries");return e.templates},l=async e=>{let{data:a}=await n.A.delete("/libraries/".concat(e));return a},o=async e=>{let{templateIds:a}=e;return null}},97381:(e,a,t)=>{"use strict";t.d(a,{Ay:()=>o,Le:()=>r,jB:()=>l,tQ:()=>i,x9:()=>s});let n=(0,t(51990).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,a)=>{e.status="authenticated",e.user=a.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,a)=>{e.status="unauthenticated",e.error=a.payload,e.user=null}}}),{setAuthenticatedUser:i,setUnauthenticated:s,setAuthLoading:r,setAuthError:l}=n.actions,o=n.reducer}},e=>{var a=a=>e(e.s=a);e.O(0,[635,1445,6967,6903,4601,2177,5047,8441,1684,7358],()=>a(84216)),_N_E=e.O()}]);