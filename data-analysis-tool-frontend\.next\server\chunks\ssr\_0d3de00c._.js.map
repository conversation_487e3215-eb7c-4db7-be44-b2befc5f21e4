{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/addUser/AddUser.tsx"], "sourcesContent": ["import React, { useState, useRef } from \"react\";\r\nimport { X } from \"lucide-react\";\r\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { addProjectUser, checkUserExists } from \"@/lib/api/projects\";\r\n\r\n// Available permission options for the user being added\r\nconst permissions = [\r\n  { label: \"View Form \", value: \"viewForm\" },\r\n  { label: \"Edit Form\", value: \"editForm\" },\r\n  { label: \"View Submissions\", value: \"viewSubmissions\" },\r\n  { label: \"Edit submissions\", value: \"editSubmissions\" },\r\n  { label: \"Add submissions\", value: \"addSubmissions\" },\r\n  { label: \"Delete Submissions\", value: \"deleteSubmissions\" },\r\n  { label: \"Validate Submissions\", value: \"validateSubmissions\" },\r\n  { label: \"Manage Project\", value: \"manageProject\" },\r\n];\r\n\r\ninterface AddUserProps {\r\n  onClose: () => void; // Callback when the modal/dialog closes\r\n  projectId?: number; // Project ID to which the user will be added\r\n  onUserAdded?: () => void; // Callback after successful addition\r\n}\r\n\r\nconst AddUser = ({ onClose, projectId, onUserAdded }: AddUserProps) => {\r\n  const [email, setEmail] = useState(\"\");\r\n  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);\r\n  const [error, setError] = useState(\"\");\r\n  const [isVerifying, setIsVerifying] = useState(false);\r\n  const [userExists, setUserExists] = useState<boolean | null>(null);\r\n\r\n  const queryClient = useQueryClient(); // Used to invalidate cached project user list\r\n  const dispatch = useDispatch(); // Redux dispatch for showing notifications\r\n  const emailCheckTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Debounce timer ref\r\n\r\n  // Email validation using basic but reliable regex\r\n  const isValidEmail = (email: string) => {\r\n    return /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/.test(email);\r\n  };\r\n\r\n  // Toggle permissions when checkboxes are clicked\r\n  const handlePermissionChange = (value: string) => {\r\n    setSelectedPermissions(\r\n      (prev) =>\r\n        prev.includes(value)\r\n          ? prev.filter((v) => v !== value) // remove if already selected\r\n          : [...prev, value] // add if not selected\r\n    );\r\n  };\r\n\r\n  // Mutation to verify if user with provided email exists\r\n  const checkUserExistsMutation = useMutation({\r\n    mutationFn: checkUserExists,\r\n    onSuccess: () => {\r\n      setUserExists(true);\r\n      setError(\"\");\r\n    },\r\n    onError: (error: any) => {\r\n      setUserExists(false);\r\n      const errorMessage =\r\n        error.response?.data?.message || \"User with this email does not exist\";\r\n      setError(errorMessage);\r\n    },\r\n    onSettled: () => {\r\n      setIsVerifying(false);\r\n    },\r\n  });\r\n\r\n  // Called on each email input change\r\n  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const newEmail = e.target.value;\r\n    setEmail(newEmail);\r\n    setUserExists(null);\r\n    setError(\"\");\r\n\r\n    if (!newEmail) return;\r\n\r\n    if (!isValidEmail(newEmail)) {\r\n      setError(\"Please enter a valid email address\");\r\n      return;\r\n    }\r\n\r\n    // Cancel any previously scheduled user check\r\n    if (emailCheckTimeoutRef.current) {\r\n      clearTimeout(emailCheckTimeoutRef.current);\r\n    }\r\n\r\n    // Debounced call to verify email after 800ms\r\n    emailCheckTimeoutRef.current = setTimeout(() => {\r\n      setIsVerifying(true);\r\n      checkUserExistsMutation.mutate(newEmail);\r\n    }, 800);\r\n  };\r\n\r\n  // Form validation logic\r\n  const isFormValid = () => {\r\n    if (!email) {\r\n      setError(\"Email is required\");\r\n      return false;\r\n    }\r\n    if (!isValidEmail(email)) {\r\n      setError(\"Please enter a valid email\");\r\n      return false;\r\n    }\r\n    if (!userExists) {\r\n      setError(\"User with this email does not exist\");\r\n      return false;\r\n    }\r\n    if (selectedPermissions.length === 0) {\r\n      setError(\"At least one permission must be selected\");\r\n      return false;\r\n    }\r\n    setError(\"\");\r\n    return true;\r\n  };\r\n\r\n  // Mutation to add user to project with selected permissions\r\n  const addUserMutation = useMutation({\r\n    mutationFn: () => {\r\n      // Convert permission array to object format required by API\r\n      const permissionsObject = selectedPermissions.reduce(\r\n        (accumulator, permission) => {\r\n          accumulator[permission] = true;\r\n          return accumulator;\r\n        },\r\n        {} as Record<string, boolean>\r\n      );\r\n\r\n      return addProjectUser({\r\n        projectId: projectId!,\r\n        email,\r\n        permissions: permissionsObject,\r\n      });\r\n    },\r\n    onSuccess: () => {\r\n      // Invalidate cached project users to refetch fresh list\r\n      queryClient.invalidateQueries({ queryKey: [\"projectUsers\", projectId] });\r\n      dispatch(\r\n        showNotification({\r\n          message: \"User added to project successfully\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n      if (onUserAdded) onUserAdded();\r\n      onClose();\r\n    },\r\n    onError: (error: any) => {\r\n      // Handle and display error message from API or fallback\r\n      let errorMessage: string;\r\n      if (typeof error === \"string\") {\r\n        errorMessage = error;\r\n      } else if (error instanceof Error) {\r\n        errorMessage = error.message;\r\n      } else if (error.response?.data?.message) {\r\n        errorMessage =\r\n          typeof error.response.data.message === \"object\"\r\n            ? JSON.stringify(error.response.data.message)\r\n            : error.response.data.message;\r\n      } else {\r\n        errorMessage = \"Failed to add user\";\r\n      }\r\n\r\n      dispatch(\r\n        showNotification({\r\n          message: errorMessage,\r\n          type: \"error\",\r\n        })\r\n      );\r\n      setError(errorMessage);\r\n    },\r\n  });\r\n\r\n  // Final submit handler for adding the user\r\n  const handleSubmit = () => {\r\n    if (!projectId) {\r\n      setError(\"Project ID is required\");\r\n      return;\r\n    }\r\n\r\n    if (isFormValid()) {\r\n      addUserMutation.mutate();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-neutral-100 p-6 rounded-md\">\r\n      {/* Email input with validation */}\r\n      <div className=\"relative\">\r\n        <input\r\n          className={`w-full border ${\r\n            error ? \"border-red-500\" : \"border-neutral-300\"\r\n          } rounded px-3 py-2 mb-4 focus:outline-none focus:ring-2 focus:ring-primary-200 placeholder:text-neutral-500`}\r\n          placeholder=\"Email address\"\r\n          value={email}\r\n          onChange={handleEmailChange}\r\n        />\r\n        {/* Close button for modal */}\r\n        <button\r\n          className=\"absolute right-2 top-2 text-neutral-700 hover:text-neutral-900\"\r\n          onClick={onClose}\r\n          type=\"button\"\r\n        >\r\n          <X size={22} />\r\n        </button>\r\n        {/* Status messages */}\r\n        {isVerifying && (\r\n          <p className=\"text-neutral-500 text-sm mb-2\">Verifying email...</p>\r\n        )}\r\n        {userExists === true && (\r\n          <p className=\"text-green-500 text-sm mb-2\">User found</p>\r\n        )}\r\n        {error && <p className=\"text-red-500 text-sm mb-2\">{error}</p>}\r\n      </div>\r\n\r\n      {/* Permissions checkboxes */}\r\n      <div className=\"flex flex-col gap-2\">\r\n        {permissions.map((permission) => (\r\n          <div key={permission.value} className=\"flex flex-col\">\r\n            <label className=\"flex items-center gap-2\">\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={selectedPermissions.includes(permission.value)}\r\n                onChange={() => handlePermissionChange(permission.value)}\r\n              />\r\n              {permission.label}\r\n            </label>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Submit button */}\r\n      <button\r\n        className={`mt-6 ${\r\n          addUserMutation.isPending || isVerifying\r\n            ? \"bg-neutral-400\"\r\n            : \"bg-blue-400 hover:bg-blue-500\"\r\n        } text-white px-6 py-2 rounded disabled:opacity-50`}\r\n        disabled={\r\n          addUserMutation.isPending ||\r\n          isVerifying ||\r\n          !email ||\r\n          selectedPermissions.length === 0 ||\r\n          !userExists\r\n        }\r\n        onClick={handleSubmit}\r\n      >\r\n        {addUserMutation.isPending ? \"Adding...\" : \"Grant permissions\"}\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { AddUser };\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;;;;;;;;AAEA,wDAAwD;AACxD,MAAM,cAAc;IAClB;QAAE,OAAO;QAAc,OAAO;IAAW;IACzC;QAAE,OAAO;QAAa,OAAO;IAAW;IACxC;QAAE,OAAO;QAAoB,OAAO;IAAkB;IACtD;QAAE,OAAO;QAAoB,OAAO;IAAkB;IACtD;QAAE,OAAO;QAAmB,OAAO;IAAiB;IACpD;QAAE,OAAO;QAAsB,OAAO;IAAoB;IAC1D;QAAE,OAAO;QAAwB,OAAO;IAAsB;IAC9D;QAAE,OAAO;QAAkB,OAAO;IAAgB;CACnD;AAQD,MAAM,UAAU,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAgB;IAChE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAE7D,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD,KAAK,8CAA8C;IACpF,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,KAAK,2CAA2C;IAC3E,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB,OAAO,qBAAqB;IAEvF,kDAAkD;IAClD,MAAM,eAAe,CAAC;QACpB,OAAO,mDAAmD,IAAI,CAAC;IACjE;IAEA,iDAAiD;IACjD,MAAM,yBAAyB,CAAC;QAC9B,uBACE,CAAC,OACC,KAAK,QAAQ,CAAC,SACV,KAAK,MAAM,CAAC,CAAC,IAAM,MAAM,OAAO,6BAA6B;eAC7D;mBAAI;gBAAM;aAAM,CAAC,sBAAsB;;IAEjD;IAEA,wDAAwD;IACxD,MAAM,0BAA0B,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QAC1C,YAAY,sHAAA,CAAA,kBAAe;QAC3B,WAAW;YACT,cAAc;YACd,SAAS;QACX;QACA,SAAS,CAAC;YACR,cAAc;YACd,MAAM,eACJ,MAAM,QAAQ,EAAE,MAAM,WAAW;YACnC,SAAS;QACX;QACA,WAAW;YACT,eAAe;QACjB;IACF;IAEA,oCAAoC;IACpC,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;QAC/B,SAAS;QACT,cAAc;QACd,SAAS;QAET,IAAI,CAAC,UAAU;QAEf,IAAI,CAAC,aAAa,WAAW;YAC3B,SAAS;YACT;QACF;QAEA,6CAA6C;QAC7C,IAAI,qBAAqB,OAAO,EAAE;YAChC,aAAa,qBAAqB,OAAO;QAC3C;QAEA,6CAA6C;QAC7C,qBAAqB,OAAO,GAAG,WAAW;YACxC,eAAe;YACf,wBAAwB,MAAM,CAAC;QACjC,GAAG;IACL;IAEA,wBAAwB;IACxB,MAAM,cAAc;QAClB,IAAI,CAAC,OAAO;YACV,SAAS;YACT,OAAO;QACT;QACA,IAAI,CAAC,aAAa,QAAQ;YACxB,SAAS;YACT,OAAO;QACT;QACA,IAAI,CAAC,YAAY;YACf,SAAS;YACT,OAAO;QACT;QACA,IAAI,oBAAoB,MAAM,KAAK,GAAG;YACpC,SAAS;YACT,OAAO;QACT;QACA,SAAS;QACT,OAAO;IACT;IAEA,4DAA4D;IAC5D,MAAM,kBAAkB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QAClC,YAAY;YACV,4DAA4D;YAC5D,MAAM,oBAAoB,oBAAoB,MAAM,CAClD,CAAC,aAAa;gBACZ,WAAW,CAAC,WAAW,GAAG;gBAC1B,OAAO;YACT,GACA,CAAC;YAGH,OAAO,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;gBACpB,WAAW;gBACX;gBACA,aAAa;YACf;QACF;QACA,WAAW;YACT,wDAAwD;YACxD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAgB;iBAAU;YAAC;YACtE,SACE,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS;gBACT,MAAM;YACR;YAEF,IAAI,aAAa;YACjB;QACF;QACA,SAAS,CAAC;YACR,wDAAwD;YACxD,IAAI;YACJ,IAAI,OAAO,UAAU,UAAU;gBAC7B,eAAe;YACjB,OAAO,IAAI,iBAAiB,OAAO;gBACjC,eAAe,MAAM,OAAO;YAC9B,OAAO,IAAI,MAAM,QAAQ,EAAE,MAAM,SAAS;gBACxC,eACE,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,KAAK,WACnC,KAAK,SAAS,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,IAC1C,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YACnC,OAAO;gBACL,eAAe;YACjB;YAEA,SACE,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS;gBACT,MAAM;YACR;YAEF,SAAS;QACX;IACF;IAEA,2CAA2C;IAC3C,MAAM,eAAe;QACnB,IAAI,CAAC,WAAW;YACd,SAAS;YACT;QACF;QAEA,IAAI,eAAe;YACjB,gBAAgB,MAAM;QACxB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAW,CAAC,cAAc,EACxB,QAAQ,mBAAmB,qBAC5B,2GAA2G,CAAC;wBAC7G,aAAY;wBACZ,OAAO;wBACP,UAAU;;;;;;kCAGZ,8OAAC;wBACC,WAAU;wBACV,SAAS;wBACT,MAAK;kCAEL,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,MAAM;;;;;;;;;;;oBAGV,6BACC,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;oBAE9C,eAAe,sBACd,8OAAC;wBAAE,WAAU;kCAA8B;;;;;;oBAE5C,uBAAS,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAItD,8OAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,8OAAC;wBAA2B,WAAU;kCACpC,cAAA,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,oBAAoB,QAAQ,CAAC,WAAW,KAAK;oCACtD,UAAU,IAAM,uBAAuB,WAAW,KAAK;;;;;;gCAExD,WAAW,KAAK;;;;;;;uBAPX,WAAW,KAAK;;;;;;;;;;0BAc9B,8OAAC;gBACC,WAAW,CAAC,KAAK,EACf,gBAAgB,SAAS,IAAI,cACzB,mBACA,gCACL,iDAAiD,CAAC;gBACnD,UACE,gBAAgB,SAAS,IACzB,eACA,CAAC,SACD,oBAAoB,MAAM,KAAK,KAC/B,CAAC;gBAEH,SAAS;0BAER,gBAAgB,SAAS,GAAG,cAAc;;;;;;;;;;;;AAInD", "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/ShareProjectModal.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport Modal from \"./Modal\";\r\nimport { LuPlus } from \"react-icons/lu\";\r\nimport { Project } from \"@/types\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport { fetchProjectById } from \"@/lib/api/projects\";\r\nimport { useParams } from \"next/navigation\";\r\nimport { decode } from \"@/lib/encodeDecode\";\r\nimport { useAuth } from \"@/hooks/useAuth\";\r\nimport Spinner from \"../general/Spinner\";\r\nimport { AddUser } from \"../addUser/AddUser\";\r\nimport axios from \"@/lib/axios\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\nconst ShareProjectModal = ({\r\n  showModal,\r\n  onClose,\r\n  onShare,\r\n  selectedProject,\r\n}: {\r\n  showModal: boolean;\r\n  onClose: () => void;\r\n  onShare: () => void;\r\n  selectedProject?: Project;\r\n  selectedProjects?: Project[];\r\n}) => {\r\n  const { hashedId } = useParams();\r\n  const { user } = useAuth();\r\n  const [showAddUser, setShowAddUser] = useState(false);\r\n\r\n  // Get project ID from either selected project or URL\r\n  const urlProjectId = hashedId ? decode(hashedId as string) : null;\r\n  const projectId = selectedProject?.id || urlProjectId;\r\n\r\n  // Fetch project details using the project ID\r\n  const { data: projectData, isLoading: projectLoading } = useQuery<Project>({\r\n    queryKey: [\"project\", projectId],\r\n    queryFn: async () => {\r\n      const data = await fetchProjectById({ projectId: projectId! });\r\n      return data;\r\n    },\r\n    enabled: !!projectId && !!user?.id,\r\n  });\r\n\r\n  // Fetch project users\r\n  const [projectUsers, setProjectUsers] = useState([]);\r\n  const [usersLoading, setUsersLoading] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const fetchUsers = async () => {\r\n      if (!projectId) return;\r\n\r\n      setUsersLoading(true);\r\n      try {\r\n        const response = await axios.get(`/project-users/${projectId}`);\r\n\r\n        if (response.data && response.data.data && response.data.data.AllUser) {\r\n          const users = response.data.data.AllUser || [];\r\n          setProjectUsers(users);\r\n        } else {\r\n          console.warn(\"No users data in response:\", response.data);\r\n          setProjectUsers([]);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching project users:\", error);\r\n        setProjectUsers([]);\r\n      } finally {\r\n        setUsersLoading(false);\r\n      }\r\n    };\r\n\r\n    if (showModal && projectId) {\r\n      fetchUsers();\r\n    }\r\n  }, [projectId, showModal]);\r\n\r\n  // If loading, show spinner\r\n  if (projectLoading) {\r\n    return <Spinner />;\r\n  }\r\n\r\n  // Use the fetched project data or fallback to selected project\r\n  const displayData = projectData || selectedProject;\r\n\r\n  // If we have no data at all, show error\r\n  if (!displayData) {\r\n    return (\r\n      <Modal isOpen={showModal} onClose={onClose} className=\"p-6 rounded-md\">\r\n        <div className=\"text-center py-4\">\r\n          <p className=\"text-red-500\">Project not found</p>\r\n          <Button onClick={onClose} className=\"mt-4\">\r\n            Close\r\n          </Button>\r\n        </div>\r\n      </Modal>\r\n    );\r\n  }\r\n\r\n  // Generate avatar color based on user name\r\n  const getAvatarColor = (name?: string) => {\r\n    if (!name) return \"bg-gray-500\"; // Default color if no name provided\r\n    const colors = [\r\n      \"bg-green-500\",\r\n      \"bg-blue-500\",\r\n      \"bg-red-500\",\r\n      \"bg-purple-500\",\r\n      \"bg-yellow-500\",\r\n      \"bg-pink-500\",\r\n      \"bg-indigo-500\",\r\n      \"bg-orange-500\",\r\n    ];\r\n    const charCode = name.charCodeAt(0);\r\n    return colors[charCode % colors.length];\r\n  };\r\n\r\n  // Get the first letter of the name for the avatar\r\n  const getInitial = (name?: string) => {\r\n    return name ? name.charAt(0).toUpperCase() : \"?\";\r\n  };\r\n\r\n  return (\r\n    <Modal isOpen={showModal} onClose={onClose} className=\"p-6 rounded-md\">\r\n      <h2 className=\"text-lg font-semibold text-neutral-700\">\r\n        {`Sharing Project: ${displayData.name || \"\"}`}\r\n      </h2>\r\n\r\n      <div className=\"w-2xl mt-4 p-4 max-h-[500px] overflow-y-auto\">\r\n        {/* Header */}\r\n        <div className=\"flex justify-between items-center mb-6\">\r\n          <div className=\"text-xl font-semibold\">Who has access</div>\r\n          <div\r\n            className=\"flex items-center border rounded-md px-3 py-1.5 cursor-pointer hover:bg-gray-50\"\r\n            onClick={() => setShowAddUser(true)}\r\n          >\r\n            <LuPlus size={18} className=\"mr-2\" />\r\n            <div className=\"text-sm\">Add user</div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* User List */}\r\n        <div className=\"space-y-4\">\r\n          {/* Project Owner */}\r\n          {displayData.user && (\r\n            <div className=\"flex items-center\">\r\n              <div\r\n                className={`w-10 h-10 rounded-full ${getAvatarColor(\r\n                  displayData.user.name\r\n                )} flex items-center justify-center text-neutral-100 font-medium mr-3`}\r\n              >\r\n                {getInitial(displayData.user.name)}\r\n              </div>\r\n              <div className=\"flex-1\">\r\n                <div className=\"font-medium\">\r\n                  {displayData.user.name ||\r\n                    displayData.user.email ||\r\n                    \"Unknown User\"}\r\n                </div>\r\n                <div className=\"inline-block bg-gray-100 text-xs px-2 py-0.5 rounded\">\r\n                  Owner\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Project Users */}\r\n          {usersLoading ? (\r\n            <div className=\"py-2 text-center\">\r\n              <div className=\"inline-block w-6 h-6 rounded-full border-2 border-t-transparent border-primary-500 animate-spin\"></div>\r\n            </div>\r\n          ) : projectUsers && projectUsers.length > 0 ? (\r\n            projectUsers.map((projectUser: any, index: number) => {\r\n              const userName =\r\n                (projectUser.user && projectUser.user.name) ||\r\n                (projectUser.user && projectUser.user.email) ||\r\n                `User ${projectUser.userId}`;\r\n\r\n              return (\r\n                <div key={index} className=\"flex items-center mt-4\">\r\n                  <div\r\n                    className={`w-10 h-10 rounded-full ${getAvatarColor(\r\n                      userName\r\n                    )} flex items-center justify-center text-neutral-100 font-medium mr-3`}\r\n                  >\r\n                    {getInitial(userName)}\r\n                  </div>\r\n                  <div className=\"flex-1\">\r\n                    <div className=\"font-medium\">{userName}</div>\r\n                    <div className=\"flex flex-wrap gap-1 mt-1\">\r\n                      {projectUser.permission &&\r\n                        Object.entries(projectUser.permission)\r\n                          .filter(([key, value]) => value === true)\r\n                          .map(([key]) => (\r\n                            <div\r\n                              key={key}\r\n                              className=\"inline-block bg-gray-100 text-xs px-2 py-0.5 rounded\"\r\n                            >\r\n                              {key === \"viewForm\"\r\n                                ? \"View Form\"\r\n                                : key === \"editForm\"\r\n                                ? \"Edit Form\"\r\n                                : key === \"viewSubmissions\"\r\n                                ? \"View Submissions\"\r\n                                : key === \"editSubmissions\"\r\n                                ? \"Edit Submissions\"\r\n                                : key === \"addSubmissions\"\r\n                                ? \"Add Submissions\"\r\n                                : key === \"deleteSubmissions\"\r\n                                ? \"Delete Submissions\"\r\n                                : key === \"validateSubmissions\"\r\n                                ? \"Validate Submissions\"\r\n                                : key === \"manageProject\"\r\n                                ? \"Manage Project\"\r\n                                : key}\r\n                            </div>\r\n                          ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              );\r\n            })\r\n          ) : null}\r\n        </div>\r\n\r\n        {/* AddUser form */}\r\n        {showAddUser && projectId && (\r\n          <div className=\"mt-6\">\r\n            <AddUser\r\n              onClose={() => setShowAddUser(false)}\r\n              projectId={projectId}\r\n              onUserAdded={() => {\r\n                // Refetch users when a new user is added\r\n                const fetchUsers = async () => {\r\n                  setUsersLoading(true);\r\n                  try {\r\n                    const response = await axios.get(\r\n                      `/project-users/${projectId}`\r\n                    );\r\n                    if (\r\n                      response.data &&\r\n                      response.data.data &&\r\n                      response.data.data.AllUser\r\n                    ) {\r\n                      const users = response.data.data.AllUser || [];\r\n                      setProjectUsers(users);\r\n                    } else {\r\n                      setProjectUsers([]);\r\n                    }\r\n                  } catch (error) {\r\n                    console.error(\"Error fetching project users:\", error);\r\n                    setProjectUsers([]);\r\n                  } finally {\r\n                    setUsersLoading(false);\r\n                  }\r\n                };\r\n                fetchUsers();\r\n              }}\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        {/* Anonymous Submissions */}\r\n        <div className=\"mt-8 border-t pt-6\">\r\n          <div className=\"flex justify-between items-center\">\r\n            <div>\r\n              <div className=\"font-medium\">Anonymous submissions</div>\r\n              <div className=\"text-sm text-gray-500 mt-1\">\r\n                Allow submissions without a username and password\r\n              </div>\r\n            </div>\r\n            <div className=\"w-12 h-6 bg-gray-200 rounded-full relative cursor-pointer\">\r\n              <div className=\"w-5 h-5 bg-neutral-100 rounded-full absolute top-0.5 left-0.5 shadow\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Copy Team Button */}\r\n        <div className=\"mt-8\">\r\n          <div className=\"inline-block border rounded-md px-4 py-2 text-sm cursor-pointer hover:bg-gray-50\">\r\n            Copy team from another project\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport { ShareProjectModal };\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AAEA,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,OAAO,EACP,OAAO,EACP,eAAe,EAOhB;IACC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAC7B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,iHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qDAAqD;IACrD,MAAM,eAAe,WAAW,CAAA,GAAA,mHAAA,CAAA,SAAM,AAAD,EAAE,YAAsB;IAC7D,MAAM,YAAY,iBAAiB,MAAM;IAEzC,6CAA6C;IAC7C,MAAM,EAAE,MAAM,WAAW,EAAE,WAAW,cAAc,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAW;QACzE,UAAU;YAAC;YAAW;SAAU;QAChC,SAAS;YACP,MAAM,OAAO,MAAM,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;gBAAE,WAAW;YAAW;YAC5D,OAAO;QACT;QACA,SAAS,CAAC,CAAC,aAAa,CAAC,CAAC,MAAM;IAClC;IAEA,sBAAsB;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa;YACjB,IAAI,CAAC,WAAW;YAEhB,gBAAgB;YAChB,IAAI;gBACF,MAAM,WAAW,MAAM,4GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,WAAW;gBAE9D,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;oBACrE,MAAM,QAAQ,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE;oBAC9C,gBAAgB;gBAClB,OAAO;oBACL,QAAQ,IAAI,CAAC,8BAA8B,SAAS,IAAI;oBACxD,gBAAgB,EAAE;gBACpB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,gBAAgB,EAAE;YACpB,SAAU;gBACR,gBAAgB;YAClB;QACF;QAEA,IAAI,aAAa,WAAW;YAC1B;QACF;IACF,GAAG;QAAC;QAAW;KAAU;IAEzB,2BAA2B;IAC3B,IAAI,gBAAgB;QAClB,qBAAO,8OAAC,iIAAA,CAAA,UAAO;;;;;IACjB;IAEA,+DAA+D;IAC/D,MAAM,cAAc,eAAe;IAEnC,wCAAwC;IACxC,IAAI,CAAC,aAAa;QAChB,qBACE,8OAAC,8HAAA,CAAA,UAAK;YAAC,QAAQ;YAAW,SAAS;YAAS,WAAU;sBACpD,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAe;;;;;;kCAC5B,8OAAC,2HAAA,CAAA,SAAM;wBAAC,SAAS;wBAAS,WAAU;kCAAO;;;;;;;;;;;;;;;;;IAMnD;IAEA,2CAA2C;IAC3C,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,MAAM,OAAO,eAAe,oCAAoC;QACrE,MAAM,SAAS;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM,WAAW,KAAK,UAAU,CAAC;QACjC,OAAO,MAAM,CAAC,WAAW,OAAO,MAAM,CAAC;IACzC;IAEA,kDAAkD;IAClD,MAAM,aAAa,CAAC;QAClB,OAAO,OAAO,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK;IAC/C;IAEA,qBACE,8OAAC,8HAAA,CAAA,UAAK;QAAC,QAAQ;QAAW,SAAS;QAAS,WAAU;;0BACpD,8OAAC;gBAAG,WAAU;0BACX,CAAC,iBAAiB,EAAE,YAAY,IAAI,IAAI,IAAI;;;;;;0BAG/C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;0CACvC,8OAAC;gCACC,WAAU;gCACV,SAAS,IAAM,eAAe;;kDAE9B,8OAAC,8IAAA,CAAA,SAAM;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC5B,8OAAC;wCAAI,WAAU;kDAAU;;;;;;;;;;;;;;;;;;kCAK7B,8OAAC;wBAAI,WAAU;;4BAEZ,YAAY,IAAI,kBACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAW,CAAC,uBAAuB,EAAE,eACnC,YAAY,IAAI,CAAC,IAAI,EACrB,mEAAmE,CAAC;kDAErE,WAAW,YAAY,IAAI,CAAC,IAAI;;;;;;kDAEnC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,YAAY,IAAI,CAAC,IAAI,IACpB,YAAY,IAAI,CAAC,KAAK,IACtB;;;;;;0DAEJ,8OAAC;gDAAI,WAAU;0DAAuD;;;;;;;;;;;;;;;;;;4BAQ3E,6BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;uCAEf,gBAAgB,aAAa,MAAM,GAAG,IACxC,aAAa,GAAG,CAAC,CAAC,aAAkB;gCAClC,MAAM,WACJ,AAAC,YAAY,IAAI,IAAI,YAAY,IAAI,CAAC,IAAI,IACzC,YAAY,IAAI,IAAI,YAAY,IAAI,CAAC,KAAK,IAC3C,CAAC,KAAK,EAAE,YAAY,MAAM,EAAE;gCAE9B,qBACE,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CACC,WAAW,CAAC,uBAAuB,EAAE,eACnC,UACA,mEAAmE,CAAC;sDAErE,WAAW;;;;;;sDAEd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAe;;;;;;8DAC9B,8OAAC;oDAAI,WAAU;8DACZ,YAAY,UAAU,IACrB,OAAO,OAAO,CAAC,YAAY,UAAU,EAClC,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,UAAU,MACnC,GAAG,CAAC,CAAC,CAAC,IAAI,iBACT,8OAAC;4DAEC,WAAU;sEAET,QAAQ,aACL,cACA,QAAQ,aACR,cACA,QAAQ,oBACR,qBACA,QAAQ,oBACR,qBACA,QAAQ,mBACR,oBACA,QAAQ,sBACR,uBACA,QAAQ,wBACR,yBACA,QAAQ,kBACR,mBACA;2DAnBC;;;;;;;;;;;;;;;;;mCAhBT;;;;;4BA0Cd,KACE;;;;;;;oBAIL,eAAe,2BACd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iIAAA,CAAA,UAAO;4BACN,SAAS,IAAM,eAAe;4BAC9B,WAAW;4BACX,aAAa;gCACX,yCAAyC;gCACzC,MAAM,aAAa;oCACjB,gBAAgB;oCAChB,IAAI;wCACF,MAAM,WAAW,MAAM,4GAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,CAAC,eAAe,EAAE,WAAW;wCAE/B,IACE,SAAS,IAAI,IACb,SAAS,IAAI,CAAC,IAAI,IAClB,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,EAC1B;4CACA,MAAM,QAAQ,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE;4CAC9C,gBAAgB;wCAClB,OAAO;4CACL,gBAAgB,EAAE;wCACpB;oCACF,EAAE,OAAO,OAAO;wCACd,QAAQ,KAAK,CAAC,iCAAiC;wCAC/C,gBAAgB,EAAE;oCACpB,SAAU;wCACR,gBAAgB;oCAClB;gCACF;gCACA;4BACF;;;;;;;;;;;kCAMN,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAc;;;;;;sDAC7B,8OAAC;4CAAI,WAAU;sDAA6B;;;;;;;;;;;;8CAI9C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAMrB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCAAmF;;;;;;;;;;;;;;;;;;;;;;;AAO5G", "debugId": null}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/ConfirmationModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Modal from \"./Modal\";\r\n\r\nconst ConfirmationModal = ({\r\n  showModal,\r\n  onClose,\r\n  onConfirm,\r\n  title,\r\n  description,\r\n  confirmButtonText,\r\n  cancelButtonText,\r\n  confirmButtonClass,\r\n  children,\r\n}: {\r\n  showModal: boolean;\r\n  onClose: () => void;\r\n  onConfirm: () => void;\r\n  title: string;\r\n  description: React.ReactNode; // Accept ReactNode for flexible content\r\n  confirmButtonText: string;\r\n  cancelButtonText?: string;\r\n  confirmButtonClass?: string;\r\n  children?: React.ReactNode; // Additional content like warnings or icons\r\n}) => {\r\n  return (\r\n    <Modal\r\n      isOpen={showModal}\r\n      onClose={onClose}\r\n      className=\"p-6 rounded-md max-w-xl\"\r\n    >\r\n      <h2 className=\"text-lg font-semibold text-neutral-700\">{title}</h2>\r\n      <div className=\"text-neutral-700 mt-2\">{description}</div>\r\n      {children && <div className=\"mt-6 space-y-4\">{children}</div>}\r\n      <div className=\"flex justify-end gap-4 mt-6\">\r\n        <button className=\"btn-outline\" onClick={onClose} type=\"button\">\r\n          {cancelButtonText || \"Cancel\"}\r\n        </button>\r\n        <button\r\n          className={`font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ${confirmButtonClass}`}\r\n          onClick={onConfirm}\r\n          type=\"button\"\r\n        >\r\n          {confirmButtonText}\r\n        </button>\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport { ConfirmationModal };\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,OAAO,EACP,SAAS,EACT,KAAK,EACL,WAAW,EACX,iBAAiB,EACjB,gBAAgB,EAChB,kBAAkB,EAClB,QAAQ,EAWT;IACC,qBACE,8OAAC,8HAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,WAAU;;0BAEV,8OAAC;gBAAG,WAAU;0BAA0C;;;;;;0BACxD,8OAAC;gBAAI,WAAU;0BAAyB;;;;;;YACvC,0BAAY,8OAAC;gBAAI,WAAU;0BAAkB;;;;;;0BAC9C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAO,WAAU;wBAAc,SAAS;wBAAS,MAAK;kCACpD,oBAAoB;;;;;;kCAEvB,8OAAC;wBACC,WAAW,CAAC,+IAA+I,EAAE,oBAAoB;wBACjL,SAAS;wBACT,MAAK;kCAEJ;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 846, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/app/%28main%29/project/%5BhashedId%5D/settings/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { JS<PERSON>, useEffect, useState } from \"react\";\r\nimport { FieldValues, useForm } from \"react-hook-form\";\r\nimport axios from \"@/lib/axios\";\r\nimport { use<PERSON>ara<PERSON>, useRouter } from \"next/navigation\";\r\nimport { decode } from \"@/lib/encodeDecode\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { Project } from \"@/types\";\r\nimport { deployProject, fetchProjectById } from \"@/lib/api/projects\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { Select } from \"@/components/general/Select\";\r\nimport { Globe, Briefcase, FileText } from \"lucide-react\";\r\nimport Spinner from \"@/components/general/Spinner\";\r\nimport { SectorLabelMap } from \"@/constants/sectors\";\r\nimport countries from \"@/constants/countryNames.json\";\r\nimport { labelToKey } from \"@/lib/labelToKey\";\r\nimport { useAuth } from \"@/hooks/useAuth\";\r\nimport { ShareProjectModal } from \"@/components/modals/ShareProjectModal\";\r\nimport { ConfirmationModal } from \"@/components/modals/ConfirmationModal\";\r\nimport { deleteProject } from \"@/lib/api/projects\";\r\nimport { archiveProject } from \"@/lib/api/projects\";\r\n// import { addProjectUser } from \"@/lib/api/projects\";\r\n\r\nconst updateProject = async ({\r\n  projectId,\r\n  dataToSend,\r\n}: {\r\n  projectId: number;\r\n  dataToSend: {\r\n    name: string;\r\n    description: string;\r\n    sector: string;\r\n    country: string;\r\n  };\r\n}) => {\r\n  const { data } = await axios.patch(`/projects/${projectId}`, dataToSend);\r\n  return data;\r\n};\r\n\r\nconst ProjectSettingsPage = () => {\r\n  const [hasMounted, setHasMounted] = useState<boolean>(false);\r\n\r\n  useEffect(() => {\r\n    setHasMounted(true);\r\n  }, []);\r\n\r\n  const {\r\n    register,\r\n    formState: { isSubmitting, errors, isSubmitted },\r\n    handleSubmit,\r\n    setValue,\r\n    reset,\r\n  } = useForm();\r\n\r\n  const router = useRouter();\r\n  const [isDeleted, setIsDeleted] = useState(false);\r\n\r\n  // registering dropdown elements manually\r\n  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);\r\n  const [selectedSector, setSelectedSector] = useState<string | null>(null);\r\n  const [showShareModal, setShowShareModal] = useState(false);\r\n  const [showConfirmationModal, setShowConfirmationModal] = useState(false);\r\n  const [confirmationModalContent, setConfirmationModalContent] = useState<{\r\n    title: string;\r\n    description: string | JSX.Element;\r\n    confirmButtonText: string;\r\n    confirmButtonClass: string;\r\n    onConfirm: () => void;\r\n  } | null>(null);\r\n\r\n  const handleShareModal = () => {\r\n    setShowShareModal(true);\r\n  };\r\n\r\n  useEffect(() => {\r\n    register(\"country\", { required: \"Please select a country\" });\r\n    register(\"sector\", { required: \"Please select a sector\" });\r\n  }, [register]);\r\n\r\n  useEffect(() => {\r\n    setValue(\"country\", selectedCountry, { shouldValidate: isSubmitted });\r\n    setValue(\"sector\", selectedSector, { shouldValidate: isSubmitted });\r\n  }, [setValue, selectedCountry, selectedSector]);\r\n\r\n  // getting hashed project id and decoding it for api call\r\n  const { hashedId } = useParams();\r\n  const hashedIdString = hashedId as string;\r\n  const projectId = decode(hashedIdString);\r\n\r\n  const { user } = useAuth();\r\n\r\n  const queryClient = useQueryClient();\r\n\r\n  // Cancel queries on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (projectId && user?.id) {\r\n        queryClient.cancelQueries({\r\n          queryKey: [\"projects\", user.id, projectId],\r\n        });\r\n      }\r\n    };\r\n  }, [projectId, user?.id, queryClient]);\r\n\r\n  const {\r\n    data: projectData,\r\n    isLoading: projectLoading,\r\n    isError: projectError,\r\n  } = useQuery<Project>({\r\n    queryKey: [\"projects\", user?.id, projectId],\r\n    queryFn: () => fetchProjectById({ projectId: projectId! }),\r\n    enabled: !!projectId && !!user?.id && !isDeleted,\r\n  });\r\n\r\n  // populating form fields with project data\r\n  useEffect(() => {\r\n    if (projectData) {\r\n      reset({\r\n        projectName: projectData.name || \"\",\r\n        description: projectData.description || \"\",\r\n        country: projectData.country || \"\",\r\n        sector: projectData.sector || \"\",\r\n      });\r\n\r\n      setSelectedCountry(projectData.country || null);\r\n      setSelectedSector(projectData.sector || null);\r\n    }\r\n  }, [projectData, reset]);\r\n\r\n  const dispatch = useDispatch();\r\n\r\n  // mutation function for updating project data\r\n  const projectMutation = useMutation({\r\n    mutationFn: updateProject,\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: [\"projects\", user?.id],\r\n        exact: false,\r\n      });\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Project details have been updated\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n    },\r\n    onError: (error) => {\r\n      dispatch(\r\n        showNotification({\r\n          message:\r\n            \"Failed to update project details. Please try again.\" +\r\n            error.message,\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n  });\r\n\r\n  //deploy mutation\r\n  const deployProjectMutation = useMutation<unknown, Error, { isUnarchive?: boolean }>({\r\n    mutationFn: (variables) => deployProject(projectId!, variables?.isUnarchive || false),\r\n    onSuccess: (_, variables) => {\r\n      const isUnarchive = variables?.isUnarchive || false;\r\n      queryClient.invalidateQueries({ queryKey: [\"projects\", user?.id] });\r\n      dispatch(\r\n        showNotification({\r\n          message: isUnarchive \r\n            ? \"Project unarchived successfully\" \r\n            : \"Project deployed successfully\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n      setShowConfirmationModal(false);\r\n    },\r\n    onError: (error: any) => {\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Failed to deploy project. Please try again\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n      setShowConfirmationModal(false);\r\n    },\r\n  });\r\n\r\n  //mutation function for archiving project\r\n  const archiveProjectMutation = useMutation({\r\n    mutationFn: () => archiveProject(projectId!),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: [\"projects\", user?.id, projectId],\r\n      });\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Project has been archived successfully\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n      setShowConfirmationModal(false);\r\n      router.push(\"/dashboard\"); // optional: redirect\r\n\r\n      // Also invalidate the projects list\r\n      queryClient.invalidateQueries({ queryKey: [\"projects\", user?.id] });\r\n    },\r\n    onError: (error: any) => {\r\n      console.error(\"Project archive error:\", error);\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Failed to archive project. Please try again.\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n      setShowConfirmationModal(false);\r\n    },\r\n  });\r\n\r\n  // mutation function for deleting project\r\n  const deleteProjectMutation = useMutation({\r\n    mutationFn: () => deleteProject(projectId!),\r\n    onSuccess: () => {\r\n      setIsDeleted(true);\r\n      setShowConfirmationModal(false);\r\n\r\n      // Cancel all queries for this project\r\n      queryClient.cancelQueries({\r\n        queryKey: [\"projects\", user?.id, projectId],\r\n      });\r\n\r\n      // Completely remove the query from the cache\r\n      queryClient.removeQueries({\r\n        queryKey: [\"projects\", user?.id, projectId],\r\n      });\r\n\r\n      // Also invalidate the projects list\r\n      queryClient.invalidateQueries({ queryKey: [\"projects\", user?.id] });\r\n\r\n      // Show success notification\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Project has been deleted successfully\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n\r\n      // Redirect after short delay for notification to be seen\r\n      setTimeout(() => {\r\n        router.push(\"/dashboard\");\r\n      }, 1000);\r\n    },\r\n    onError: (error: any) => {\r\n      setShowConfirmationModal(false);\r\n      console.error(\"Project deletion error:\", error);\r\n\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Failed to delete project. Please try again.\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n  });\r\n\r\n  const handleArchiveClick = () => {\r\n    setConfirmationModalContent({\r\n      title: \"Confirm Archive\",\r\n      description: (\r\n        <>\r\n          Are you sure you want to archive this project? Your form will not\r\n          accept submissions while it is archived.\r\n        </>\r\n      ),\r\n      confirmButtonText: \"Archive\",\r\n      confirmButtonClass: \"btn-primary\",\r\n      onConfirm: () => {\r\n        archiveProjectMutation.mutate();\r\n      },\r\n    });\r\n    setShowConfirmationModal(true);\r\n  };\r\n\r\n  const handleDeployClick = () => {\r\n    const isUnarchiving = projectData?.status === \"archived\";\r\n\r\n    let description = \"Are you sure you want to deploy this project?\";\r\n  \r\n    if (projectData?.status === \"deployed\") {\r\n      description = \"Are you sure you want to redeploy this project?\";\r\n    } else if (projectData?.status === \"archived\") {\r\n      description = \"Are you sure you want to Deploy this project?\";\r\n    }\r\n  \r\n    setConfirmationModalContent({\r\n      title: isUnarchiving ? \"Confirm Deploy\" : \"Confirm Unarchive\",\r\n      description,\r\n      confirmButtonText: isUnarchiving ? \"Deploy\" : \"Unarchive\",\r\n      confirmButtonClass: \"btn-primary\",\r\n      onConfirm: () => {\r\n        deployProjectMutation.mutate({ isUnarchive: isUnarchiving });\r\n      },\r\n    });\r\n  \r\n    setShowConfirmationModal(true);\r\n  };\r\n\r\n  const onSubmit = async (data: FieldValues) => {\r\n    projectMutation.mutate({\r\n      projectId: projectId!,\r\n      dataToSend: {\r\n        name: data.projectName,\r\n        description: data.description,\r\n        country: data.country,\r\n        sector: data.sector,\r\n      },\r\n    });\r\n  };\r\n\r\n  const handleDeleteClick = () => {\r\n    setConfirmationModalContent({\r\n      title: \"Confirm Delete\",\r\n      description: (\r\n        <>\r\n          <p>\r\n            Are you sure you want to delete this project? This action cannot be\r\n            undone.\r\n          </p>\r\n          <ul className=\"list-disc list-inside flex flex-col gap-2 mt-2 text-neutral-700\">\r\n            <li>All data gathered for this project will be deleted.</li>\r\n            <li>Forms associated with this project will be deleted.</li>\r\n            <li>\r\n              You will not be able to recover this project after deletion.\r\n            </li>\r\n          </ul>\r\n        </>\r\n      ),\r\n      confirmButtonText: \"Delete\",\r\n      confirmButtonClass: \"btn-danger\",\r\n      onConfirm: () => {\r\n        deleteProjectMutation.mutate();\r\n      },\r\n    });\r\n    setShowConfirmationModal(true);\r\n  };\r\n\r\n  // To prevent errors from showing when the component is not fully mounted.\r\n  if (!hasMounted) return null;\r\n\r\n  if (isDeleted) {\r\n    return <Spinner />;\r\n  }\r\n\r\n  if (projectLoading) {\r\n    return <Spinner />;\r\n  }\r\n\r\n  // If hashedId is missing, show an error\r\n  if (!hashedId || projectId === null) {\r\n    return (\r\n      <div className=\"error-message\">\r\n        <h1 className=\"text-red-500\">Error: Invalid Project ID (hashedId).</h1>\r\n        <p className=\"text-neutral-700\">\r\n          Please make sure the URL contains a valid project identifier.\r\n        </p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (projectError && !isDeleted) {\r\n    return (\r\n      <p className=\"text-red-500\">Failed to fetch project. Please try again.</p>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <form className=\"flex flex-col gap-8\" onSubmit={handleSubmit(onSubmit)}>\r\n      <div className=\"flex flex-col gap-4\">\r\n        {/* Project Name */}\r\n        <div className=\"label-input-group group\">\r\n          <label htmlFor=\"project-name\" className=\"label-text\">\r\n            <FileText size={16} /> Project Name\r\n          </label>\r\n          <input\r\n            {...register(\"projectName\", {\r\n              required: \"Project name is required.\",\r\n            })}\r\n            id=\"project-name\"\r\n            type=\"text\"\r\n            className=\"input-field\"\r\n            placeholder=\"Enter a project name\"\r\n          />\r\n          {errors.projectName && (\r\n            <p className=\"text-red-500 text-sm\">{`${errors.projectName.message}`}</p>\r\n          )}\r\n        </div>\r\n        {/* Project Description */}\r\n        <div className=\"label-input-group group\">\r\n          <label htmlFor=\"description\" className=\"label-text\">\r\n            Description\r\n          </label>\r\n          <textarea\r\n            id=\"description\"\r\n            {...register(\"description\")}\r\n            className=\"input-field resize-none\"\r\n            cols={4}\r\n            placeholder=\"Enter the project description\"\r\n          />\r\n        </div>\r\n        {/* Country and Sector */}\r\n        <div className=\"grid grid-cols-2 gap-4\">\r\n          <div className=\"label-input-group group\">\r\n            <label htmlFor=\"country\" className=\"label-text\">\r\n              <Globe size={16} />\r\n              Country\r\n            </label>\r\n            <Select\r\n              id=\"country\"\r\n              options={countries}\r\n              value={selectedCountry}\r\n              onChange={setSelectedCountry}\r\n            />\r\n            {errors.country && (\r\n              <p className=\"text-red-500 text-sm\">{`${errors.country.message}`}</p>\r\n            )}\r\n          </div>\r\n          <div className=\"label-input-group group\">\r\n            <label htmlFor=\"sector\" className=\"label-text\">\r\n              <Briefcase size={16} /> Sector\r\n            </label>\r\n            <Select\r\n              id={`sector`}\r\n              options={Object.values(SectorLabelMap)} // Display labels\r\n              value={\r\n                selectedSector && SectorLabelMap[selectedSector]\r\n                  ? SectorLabelMap[selectedSector]\r\n                  : \"Select an option\"\r\n              }\r\n              onChange={(label) => {\r\n                const selectedKey = labelToKey(label, SectorLabelMap);\r\n                setSelectedSector(selectedKey); // Set the enum key for storage\r\n              }}\r\n            />\r\n            {errors.sector && (\r\n              <p className=\"text-red-500 text-sm\">{`${errors.sector.message}`}</p>\r\n            )}\r\n          </div>\r\n        </div>\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"flex items-center gap-4\">\r\n            {projectData?.status === \"deployed\" && (\r\n              <button\r\n                onClick={handleArchiveClick}\r\n                type=\"button\"\r\n                className=\"btn-outline\"\r\n              >\r\n                Archive\r\n              </button>\r\n            )}\r\n\r\n            {projectData?.status === \"deployed\" && (\r\n              <button\r\n                onClick={handleDeployClick}\r\n                type=\"button\"\r\n                className=\"btn-outline\"\r\n              >\r\n                ReDeploy\r\n              </button>\r\n            )}\r\n\r\n            {projectData?.status === \"archived\" && (\r\n              <button\r\n                onClick={handleDeployClick}\r\n                type=\"button\"\r\n                className=\"btn-outline\" //Unarchive button\r\n              >\r\n                Deploy\r\n              </button>\r\n            )}\r\n\r\n            {projectData?.status === \"draft\" && (\r\n              <button\r\n                onClick={handleDeployClick}\r\n                type=\"button\"\r\n                className=\"btn-outline\"\r\n              >\r\n                Deploy\r\n              </button>\r\n            )}\r\n\r\n            <button\r\n              type=\"button\"\r\n              className=\"btn-outline\"\r\n              onClick={handleShareModal}\r\n            >\r\n              Share\r\n            </button>\r\n\r\n            <button\r\n              onClick={handleDeleteClick}\r\n              type=\"button\"\r\n              className=\"btn-danger\"\r\n            >\r\n              Delete\r\n            </button>\r\n          </div>\r\n          <button type=\"submit\" className=\"btn-primary self-end\">\r\n            {isSubmitting ? (\r\n              <span className=\"flex items-center gap-2\">\r\n                Saving{\" \"}\r\n                <div className=\"size-4 animate-spin border-x border-neutral-100 rounded-full\"></div>\r\n              </span>\r\n            ) : (\r\n              \"Save Changes\"\r\n            )}\r\n          </button>\r\n        </div>\r\n      </div>\r\n      <ShareProjectModal\r\n        showModal={showShareModal}\r\n        onClose={() => setShowShareModal(false)}\r\n        onShare={() => {\r\n          setShowShareModal(false);\r\n        }}\r\n      />\r\n\r\n      {confirmationModalContent && (\r\n        <ConfirmationModal\r\n          showModal={showConfirmationModal}\r\n          onClose={() => setShowConfirmationModal(false)}\r\n          title={confirmationModalContent.title}\r\n          description={confirmationModalContent.description}\r\n          confirmButtonText={confirmationModalContent.confirmButtonText}\r\n          confirmButtonClass={confirmationModalContent.confirmButtonClass}\r\n          onConfirm={confirmationModalContent.onConfirm}\r\n        />\r\n      )}\r\n    </form>\r\n  );\r\n};\r\n\r\nexport default ProjectSettingsPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AApBA;;;;;;;;;;;;;;;;;;;;;;AAuBA,uDAAuD;AAEvD,MAAM,gBAAgB,OAAO,EAC3B,SAAS,EACT,UAAU,EASX;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE;IAC7D,OAAO;AACT;AAEA,MAAM,sBAAsB;IAC1B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAEtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;IAChB,GAAG,EAAE;IAEL,MAAM,EACJ,QAAQ,EACR,WAAW,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,EAChD,YAAY,EACZ,QAAQ,EACR,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD;IAEV,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,yCAAyC;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAM7D;IAEV,MAAM,mBAAmB;QACvB,kBAAkB;IACpB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,WAAW;YAAE,UAAU;QAA0B;QAC1D,SAAS,UAAU;YAAE,UAAU;QAAyB;IAC1D,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,WAAW,iBAAiB;YAAE,gBAAgB;QAAY;QACnE,SAAS,UAAU,gBAAgB;YAAE,gBAAgB;QAAY;IACnE,GAAG;QAAC;QAAU;QAAiB;KAAe;IAE9C,yDAAyD;IACzD,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAC7B,MAAM,iBAAiB;IACvB,MAAM,YAAY,CAAA,GAAA,mHAAA,CAAA,SAAM,AAAD,EAAE;IAEzB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,iHAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,aAAa,MAAM,IAAI;gBACzB,YAAY,aAAa,CAAC;oBACxB,UAAU;wBAAC;wBAAY,KAAK,EAAE;wBAAE;qBAAU;gBAC5C;YACF;QACF;IACF,GAAG;QAAC;QAAW,MAAM;QAAI;KAAY;IAErC,MAAM,EACJ,MAAM,WAAW,EACjB,WAAW,cAAc,EACzB,SAAS,YAAY,EACtB,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAW;QACpB,UAAU;YAAC;YAAY,MAAM;YAAI;SAAU;QAC3C,SAAS,IAAM,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;gBAAE,WAAW;YAAW;QACxD,SAAS,CAAC,CAAC,aAAa,CAAC,CAAC,MAAM,MAAM,CAAC;IACzC;IAEA,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;YACf,MAAM;gBACJ,aAAa,YAAY,IAAI,IAAI;gBACjC,aAAa,YAAY,WAAW,IAAI;gBACxC,SAAS,YAAY,OAAO,IAAI;gBAChC,QAAQ,YAAY,MAAM,IAAI;YAChC;YAEA,mBAAmB,YAAY,OAAO,IAAI;YAC1C,kBAAkB,YAAY,MAAM,IAAI;QAC1C;IACF,GAAG;QAAC;QAAa;KAAM;IAEvB,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAE3B,8CAA8C;IAC9C,MAAM,kBAAkB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QAClC,YAAY;QACZ,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC;oBAAY,MAAM;iBAAG;gBAChC,OAAO;YACT;YACA,SACE,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS;gBACT,MAAM;YACR;QAEJ;QACA,SAAS,CAAC;YACR,SACE,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SACE,wDACA,MAAM,OAAO;gBACf,MAAM;YACR;QAEJ;IACF;IAEA,iBAAiB;IACjB,MAAM,wBAAwB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAA6C;QACnF,YAAY,CAAC,YAAc,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,WAAY,WAAW,eAAe;QAC/E,WAAW,CAAC,GAAG;YACb,MAAM,cAAc,WAAW,eAAe;YAC9C,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAY,MAAM;iBAAG;YAAC;YACjE,SACE,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS,cACL,oCACA;gBACJ,MAAM;YACR;YAEF,yBAAyB;QAC3B;QACA,SAAS,CAAC;YACR,SACE,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS;gBACT,MAAM;YACR;YAEF,yBAAyB;QAC3B;IACF;IAEA,yCAAyC;IACzC,MAAM,yBAAyB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACzC,YAAY,IAAM,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;QACjC,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC;oBAAY,MAAM;oBAAI;iBAAU;YAC7C;YACA,SACE,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS;gBACT,MAAM;YACR;YAEF,yBAAyB;YACzB,OAAO,IAAI,CAAC,eAAe,qBAAqB;YAEhD,oCAAoC;YACpC,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAY,MAAM;iBAAG;YAAC;QACnE;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SACE,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS;gBACT,MAAM;YACR;YAEF,yBAAyB;QAC3B;IACF;IAEA,yCAAyC;IACzC,MAAM,wBAAwB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACxC,YAAY,IAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE;QAChC,WAAW;YACT,aAAa;YACb,yBAAyB;YAEzB,sCAAsC;YACtC,YAAY,aAAa,CAAC;gBACxB,UAAU;oBAAC;oBAAY,MAAM;oBAAI;iBAAU;YAC7C;YAEA,6CAA6C;YAC7C,YAAY,aAAa,CAAC;gBACxB,UAAU;oBAAC;oBAAY,MAAM;oBAAI;iBAAU;YAC7C;YAEA,oCAAoC;YACpC,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAY,MAAM;iBAAG;YAAC;YAEjE,4BAA4B;YAC5B,SACE,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS;gBACT,MAAM;YACR;YAGF,yDAAyD;YACzD,WAAW;gBACT,OAAO,IAAI,CAAC;YACd,GAAG;QACL;QACA,SAAS,CAAC;YACR,yBAAyB;YACzB,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,SACE,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS;gBACT,MAAM;YACR;QAEJ;IACF;IAEA,MAAM,qBAAqB;QACzB,4BAA4B;YAC1B,OAAO;YACP,2BACE;0BAAE;;YAKJ,mBAAmB;YACnB,oBAAoB;YACpB,WAAW;gBACT,uBAAuB,MAAM;YAC/B;QACF;QACA,yBAAyB;IAC3B;IAEA,MAAM,oBAAoB;QACxB,MAAM,gBAAgB,aAAa,WAAW;QAE9C,IAAI,cAAc;QAElB,IAAI,aAAa,WAAW,YAAY;YACtC,cAAc;QAChB,OAAO,IAAI,aAAa,WAAW,YAAY;YAC7C,cAAc;QAChB;QAEA,4BAA4B;YAC1B,OAAO,gBAAgB,mBAAmB;YAC1C;YACA,mBAAmB,gBAAgB,WAAW;YAC9C,oBAAoB;YACpB,WAAW;gBACT,sBAAsB,MAAM,CAAC;oBAAE,aAAa;gBAAc;YAC5D;QACF;QAEA,yBAAyB;IAC3B;IAEA,MAAM,WAAW,OAAO;QACtB,gBAAgB,MAAM,CAAC;YACrB,WAAW;YACX,YAAY;gBACV,MAAM,KAAK,WAAW;gBACtB,aAAa,KAAK,WAAW;gBAC7B,SAAS,KAAK,OAAO;gBACrB,QAAQ,KAAK,MAAM;YACrB;QACF;IACF;IAEA,MAAM,oBAAoB;QACxB,4BAA4B;YAC1B,OAAO;YACP,2BACE;;kCACE,8OAAC;kCAAE;;;;;;kCAIH,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;;;;;;;;;YAMV,mBAAmB;YACnB,oBAAoB;YACpB,WAAW;gBACT,sBAAsB,MAAM;YAC9B;QACF;QACA,yBAAyB;IAC3B;IAEA,0EAA0E;IAC1E,IAAI,CAAC,YAAY,OAAO;IAExB,IAAI,WAAW;QACb,qBAAO,8OAAC,iIAAA,CAAA,UAAO;;;;;IACjB;IAEA,IAAI,gBAAgB;QAClB,qBAAO,8OAAC,iIAAA,CAAA,UAAO;;;;;IACjB;IAEA,wCAAwC;IACxC,IAAI,CAAC,YAAY,cAAc,MAAM;QACnC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAe;;;;;;8BAC7B,8OAAC;oBAAE,WAAU;8BAAmB;;;;;;;;;;;;IAKtC;IAEA,IAAI,gBAAgB,CAAC,WAAW;QAC9B,qBACE,8OAAC;YAAE,WAAU;sBAAe;;;;;;IAEhC;IAEA,qBACE,8OAAC;QAAK,WAAU;QAAsB,UAAU,aAAa;;0BAC3D,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,SAAQ;gCAAe,WAAU;;kDACtC,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;oCAAM;;;;;;;0CAExB,8OAAC;gCACE,GAAG,SAAS,eAAe;oCAC1B,UAAU;gCACZ,EAAE;gCACF,IAAG;gCACH,MAAK;gCACL,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,WAAW,kBACjB,8OAAC;gCAAE,WAAU;0CAAwB,GAAG,OAAO,WAAW,CAAC,OAAO,EAAE;;;;;;;;;;;;kCAIxE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,SAAQ;gCAAc,WAAU;0CAAa;;;;;;0CAGpD,8OAAC;gCACC,IAAG;gCACF,GAAG,SAAS,cAAc;gCAC3B,WAAU;gCACV,MAAM;gCACN,aAAY;;;;;;;;;;;;kCAIhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,SAAQ;wCAAU,WAAU;;0DACjC,8OAAC,oMAAA,CAAA,QAAK;gDAAC,MAAM;;;;;;4CAAM;;;;;;;kDAGrB,8OAAC,gIAAA,CAAA,SAAM;wCACL,IAAG;wCACH,SAAS,iGAAA,CAAA,UAAS;wCAClB,OAAO;wCACP,UAAU;;;;;;oCAEX,OAAO,OAAO,kBACb,8OAAC;wCAAE,WAAU;kDAAwB,GAAG,OAAO,OAAO,CAAC,OAAO,EAAE;;;;;;;;;;;;0CAGpE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,SAAQ;wCAAS,WAAU;;0DAChC,8OAAC,4MAAA,CAAA,YAAS;gDAAC,MAAM;;;;;;4CAAM;;;;;;;kDAEzB,8OAAC,gIAAA,CAAA,SAAM;wCACL,IAAI,CAAC,MAAM,CAAC;wCACZ,SAAS,OAAO,MAAM,CAAC,oHAAA,CAAA,iBAAc;wCACrC,OACE,kBAAkB,oHAAA,CAAA,iBAAc,CAAC,eAAe,GAC5C,oHAAA,CAAA,iBAAc,CAAC,eAAe,GAC9B;wCAEN,UAAU,CAAC;4CACT,MAAM,cAAc,CAAA,GAAA,iHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,oHAAA,CAAA,iBAAc;4CACpD,kBAAkB,cAAc,+BAA+B;wCACjE;;;;;;oCAED,OAAO,MAAM,kBACZ,8OAAC;wCAAE,WAAU;kDAAwB,GAAG,OAAO,MAAM,CAAC,OAAO,EAAE;;;;;;;;;;;;;;;;;;kCAIrE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCACZ,aAAa,WAAW,4BACvB,8OAAC;wCACC,SAAS;wCACT,MAAK;wCACL,WAAU;kDACX;;;;;;oCAKF,aAAa,WAAW,4BACvB,8OAAC;wCACC,SAAS;wCACT,MAAK;wCACL,WAAU;kDACX;;;;;;oCAKF,aAAa,WAAW,4BACvB,8OAAC;wCACC,SAAS;wCACT,MAAK;wCACL,WAAU,cAAc,kBAAkB;;kDAC3C;;;;;;oCAKF,aAAa,WAAW,yBACvB,8OAAC;wCACC,SAAS;wCACT,MAAK;wCACL,WAAU;kDACX;;;;;;kDAKH,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS;kDACV;;;;;;kDAID,8OAAC;wCACC,SAAS;wCACT,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;0CAIH,8OAAC;gCAAO,MAAK;gCAAS,WAAU;0CAC7B,6BACC,8OAAC;oCAAK,WAAU;;wCAA0B;wCACjC;sDACP,8OAAC;4CAAI,WAAU;;;;;;;;;;;2CAGjB;;;;;;;;;;;;;;;;;;0BAKR,8OAAC,0IAAA,CAAA,oBAAiB;gBAChB,WAAW;gBACX,SAAS,IAAM,kBAAkB;gBACjC,SAAS;oBACP,kBAAkB;gBACpB;;;;;;YAGD,0CACC,8OAAC,0IAAA,CAAA,oBAAiB;gBAChB,WAAW;gBACX,SAAS,IAAM,yBAAyB;gBACxC,OAAO,yBAAyB,KAAK;gBACrC,aAAa,yBAAyB,WAAW;gBACjD,mBAAmB,yBAAyB,iBAAiB;gBAC7D,oBAAoB,yBAAyB,kBAAkB;gBAC/D,WAAW,yBAAyB,SAAS;;;;;;;;;;;;AAKvD;uCAEe", "debugId": null}}]}