(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5972],{5287:(e,t,s)=>{"use strict";s.d(t,{GN:()=>i,J6:()=>n,O8:()=>r,s4:()=>a});var l=s(25784);let r=async(e,t)=>{try{let{data:s}=await l.A.delete("/form-submissions/".concat(e,"?projectId=").concat(t));return s}catch(e){throw console.error("Error deleting form submission:",e),e}},n=async(e,t)=>{try{let s=e.map(e=>l.A.delete("/form-submissions/".concat(e,"?projectId=").concat(t)));return(await Promise.all(s)).map(e=>e.data)}catch(e){throw console.error("Error deleting multiple form submissions:",e),e}},a=async(e,t)=>{try{if(!e.submissionId||!e.questionId)throw Error("submissionId and questionId are required");let s={...e};null===s.questionOptionId?delete s.questionOptionId:Array.isArray(s.questionOptionId)&&(s.questionOptionId=s.questionOptionId.filter(e=>null!=e),0===s.questionOptionId.length&&delete s.questionOptionId);let{data:r}=await l.A.patch("/answers/".concat(e.questionId,"?projectId=").concat(t),s);return r}catch(e){throw console.error("Error updating answer:",e),e}},i=async(e,t)=>{try{let{data:s}=await l.A.patch("/answers/multiple?projectId=".concat(t),e);return s}catch(e){throw console.error("Error updating multiple answers with endpoint:",e),e}}},8501:(e,t,s)=>{Promise.resolve().then(s.bind(s,91958))},34947:(e,t,s)=>{"use strict";s.d(t,{Af:()=>i,K4:()=>n,ae:()=>m,dI:()=>d,ej:()=>a,ku:()=>c,sr:()=>u,ul:()=>o});var l=s(25784);let r=e=>{if("project"===e)return"/questions";if("template"===e)return"/template-questions";if("questionBlock"===e)return"/question-blocks";throw Error("Unsupported context type")},n=async e=>{let{projectId:t}=e,{data:s}=await l.A.get("/questions/".concat(t));return s.questions},a=async e=>{let{templateId:t}=e,{data:s}=await l.A.get("/template-questions/".concat(t));return s.questions},i=async e=>{var t,s,n,a,i,o;let{contextType:c,contextId:u,dataToSend:d,position:m}=e,p="questionBlock"===c?"".concat(r(c)):"".concat(r(c),"/").concat(u);if(!d.label||!d.inputType)throw Error("Label and inputType are required");let h=["selectone","selectmany"].includes(d.inputType),x=d.file instanceof File,g=Array.isArray(d.questionOptions)&&d.questionOptions.length>0;if(h&&!x&&!g)throw Error("Options are required for select input types");if(x){let e=new FormData;e.append("label",d.label),e.append("isRequired",d.isRequired?"true":"false"),e.append("inputType",d.inputType),d.hint&&e.append("hint",d.hint),d.placeholder&&e.append("placeholder",d.placeholder),e.append("position",String(m||1)),e.append("file",d.file);try{let{data:t}=await l.A.post(p,e,{headers:{"Content-Type":"multipart/form-data"}});return t}catch(e){throw console.error("Upload error details:",(null==(t=e.response)?void 0:t.data)||e.message),Error("Failed to upload question with file: ".concat((null==(n=e.response)||null==(s=n.data)?void 0:s.message)||e.message))}}try{let{data:e}=await l.A.post(p,{label:d.label,isRequired:d.isRequired,hint:d.hint,placeholder:d.placeholder,inputType:d.inputType,questionOptions:d.questionOptions,position:m||1});return e}catch(e){throw console.error("API error details:",(null==(a=e.response)?void 0:a.data)||e.message),Error("Failed to add question: ".concat((null==(o=e.response)||null==(i=o.data)?void 0:i.message)||e.message))}},o=async e=>{let{contextType:t,id:s,projectId:n}=e,{data:a}=await l.A.delete("".concat(r(t),"/").concat(s,"?projectId=").concat(n));return a},c=async e=>{let{id:t,contextType:s,contextId:n}=e,{data:a}=await l.A.post("".concat(r(s),"/duplicate/").concat(t,"?projectId=").concat(n),"questionBlock"===s?{}:"project"===s?{projectId:n}:{templateId:n});return a},u=async e=>{let{id:t,contextType:s,dataToSend:n,contextId:a}=e,{data:i}=await l.A.patch("".concat(r(s),"/").concat(t,"?projectId=").concat(a),n);return i},d=async()=>{try{return(await l.A.get("/question-blocks")).data.questions||[]}catch(e){throw console.error("Error fetching question block questions:",e),e}},m=async e=>{let{contextType:t,contextId:s,questionPositions:n}=e;if("project"!==t)throw Error("Question position updates are only supported for projects");let a="".concat(r(t),"/positions?projectId=").concat(s);try{let{data:e}=await l.A.patch(a,{questionPositions:n});return e}catch(e){var i,o,c,u,d,m;throw console.error("Update failed - Full error:",e),console.error("Update failed - Error details:",{status:null==(i=e.response)?void 0:i.status,statusText:null==(o=e.response)?void 0:o.statusText,data:null==(c=e.response)?void 0:c.data,message:e.message,config:{url:null==(u=e.config)?void 0:u.url,method:null==(d=e.config)?void 0:d.method,data:null==(m=e.config)?void 0:m.data}}),e}}},63642:(e,t,s)=>{"use strict";s.d(t,{R:()=>n});var l=s(95155);s(12115);var r=s(13163);let n=e=>{let{showModal:t,onClose:s,onConfirm:n,title:a,description:i,confirmButtonText:o,cancelButtonText:c,confirmButtonClass:u,children:d}=e;return(0,l.jsxs)(r.A,{isOpen:t,onClose:s,className:"p-6 rounded-md max-w-xl",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:a}),(0,l.jsx)("div",{className:"text-neutral-700 mt-2",children:i}),d&&(0,l.jsx)("div",{className:"mt-6 space-y-4",children:d}),(0,l.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,l.jsx)("button",{className:"btn-outline",onClick:s,type:"button",children:c||"Cancel"}),(0,l.jsx)("button",{className:"font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ".concat(u),onClick:n,type:"button",children:o})]})]})}},71402:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>a,Ds:()=>r,_b:()=>n});let l=(0,s(51990).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,t)=>{e.message=t.payload.message,e.type=t.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:r,hideNotification:n}=l.actions,a=l.reducer},91958:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>J});var l=s(95155),r=s(95139);let n=(0,s(19946).A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]);var a=s(92657),i=s(53999),o=s(12115),c=s(13163),u=s(88524);let d=e=>{var t,s,r;let{isOpen:n,onClose:a,title:i,tableData:d,uniqueColumns:m,uniqueRows:p,rowsMap:h,useParentChildColumns:x=!1,loading:g=!1,tableStructure:b}=e,f=o.useMemo(()=>{if(!d||0===d.length)return[];let e=[];return d.forEach(t=>{let s=parseInt(t.column),l=parseInt(t.row);isNaN(s)||isNaN(l)||e.push({columnId:s,rowsId:l,value:t.value})}),e},[d]),y=o.useMemo(()=>{if(!(null==b?void 0:b.tableColumns)||0===b.tableColumns.length)return{parentColumns:[],columnMap:new Map,hasChildColumns:!1};let e=b.tableColumns.filter(e=>void 0===e.parentColumnId||null===e.parentColumnId),t=new Map;e.forEach(e=>{let s=b.tableColumns.filter(t=>t.parentColumnId===e.id);t.set(e.id,s)});let s=e.some(e=>(t.get(e.id)||[]).length>0);return{parentColumns:e,columnMap:t,hasChildColumns:s}},[b]),w=o.useMemo(()=>{let e=new Map;return f.forEach(t=>{e.set("".concat(t.columnId,"_").concat(t.rowsId),t.value)}),e},[f]);return(0,l.jsx)(c.A,{isOpen:n,onClose:a,className:"p-6 rounded-md max-w-4xl w-[95%] mobile:w-[85%] tablet:w-[75%] laptop:w-[60%]",children:(0,l.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-neutral-700",children:i}),g?(0,l.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"}),(0,l.jsx)("span",{className:"ml-2 text-neutral-600",children:"Loading table data..."})]}):(null==b?void 0:b.tableColumns)&&0!==b.tableColumns.length?(0,l.jsx)("div",{className:"overflow-auto max-h-[70vh]",children:(0,l.jsxs)(u.XI,{className:"border-collapse border border-amber-700",children:[(0,l.jsxs)(u.A0,{className:"bg-amber-100",children:[(0,l.jsxs)(u.Hj,{children:[(0,l.jsx)(u.nd,{className:"px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider border border-amber-700 bg-amber-100",rowSpan:y.hasChildColumns?2:1,children:i}),y.parentColumns.map(e=>{let t=(y.columnMap.get(e.id)||[]).length||1;return(0,l.jsx)(u.nd,{colSpan:t,className:"px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider text-center border border-amber-700 bg-amber-100",children:e.columnName},e.id)})]}),y.hasChildColumns&&(0,l.jsx)(u.Hj,{children:y.parentColumns.map(e=>{let t=y.columnMap.get(e.id)||[];return 0===t.length?null:t.map(e=>(0,l.jsx)(u.nd,{className:"px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider border border-amber-700 bg-amber-50",children:e.columnName},e.id))})})]}),(0,l.jsx)(u.BF,{children:null==(r=b.tableRows)?void 0:r.map((e,t)=>(0,l.jsxs)(u.Hj,{className:"bg-white",children:[(0,l.jsx)(u.nA,{className:"px-3 py-2 text-xs font-medium border border-amber-700 bg-amber-50",children:e.rowsName}),y.parentColumns.map(t=>{let s=y.columnMap.get(t.id)||[];return 0===s.length?(0,l.jsx)(u.nA,{className:"px-3 py-2 text-xs border border-amber-700",children:w.get("".concat(t.id,"_").concat(e.id))||""},"cell-".concat(t.id,"-").concat(e.id)):s.map(t=>(0,l.jsx)(u.nA,{className:"px-3 py-2 text-xs border border-amber-700",children:w.get("".concat(t.id,"_").concat(e.id))||""},"cell-".concat(t.id,"-").concat(e.id)))})]},e.id))})]})}):(0,l.jsxs)("div",{className:"py-4 text-center text-amber-600",children:[(0,l.jsx)("p",{children:"No table structure available."}),(0,l.jsx)("p",{className:"text-sm mt-2",children:"Debug info:"}),(0,l.jsx)("pre",{className:"text-xs mt-2 bg-gray-100 p-2 rounded overflow-auto max-h-40",children:JSON.stringify({hasTableStructure:!!b,tableColumnsLength:(null==b||null==(t=b.tableColumns)?void 0:t.length)||0,tableRowsLength:(null==b||null==(s=b.tableRows)?void 0:s.length)||0,tableDataLength:(null==d?void 0:d.length)||0,useParentChildColumns:x},null,2)})]}),(0,l.jsx)("div",{className:"flex justify-end mt-4",children:(0,l.jsx)("button",{onClick:a,className:"px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 transition-colors",children:"Close"})})]})})};var m=s(25784),p=s(27859);let h=(e,t)=>{if(null==e)return"-";if("boolean"==typeof e)return e?"Yes":"No";if(e instanceof Date)return(0,i.Y)(e);if("date"===t&&"string"==typeof e)try{return(0,i.Y)(new Date(e))}catch(t){return e}return String(e)},x=async e=>{try{let t=null;try{let{data:s}=await m.A.get("/table-questions/".concat(e));s&&s.success&&s.data&&(t=s.data.question)}catch(e){console.error("Error fetching from /table-questions/ endpoint:",e)}if(t&&t.tableColumns&&!t.tableRows)try{let{data:s}=await m.A.get("/table-rows/".concat(e));s&&s.data&&s.data.tableRows&&(t.tableRows=s.data.tableRows)}catch(e){console.error("Error fetching table rows separately:",e)}if(!t)return{id:e,label:"Table Data",tableColumns:[],tableRows:[{id:1,rowsName:"Row 1"},{id:2,rowsName:"Row 2"},{id:3,rowsName:"Row 3"}]};if(t.tableColumns&&Array.isArray(t.tableColumns)){let e=[];t.tableColumns.forEach(t=>{e.push({id:t.id,columnName:t.columnName,parentColumnId:t.parentColumnId||null}),t.childColumns&&Array.isArray(t.childColumns)&&t.childColumns.forEach(s=>{e.push({id:s.id,columnName:s.columnName,parentColumnId:s.parentColumnId||t.id})})}),t.tableColumns=e}else console.error("tableColumns is missing or not an array, creating default tableColumns"),t.tableColumns=[];return t.tableRows&&Array.isArray(t.tableRows)||(console.error("tableRows is missing or not an array, creating default tableRows"),t.tableColumns&&t.tableColumns.length>0?t.tableRows=t.tableColumns.map(e=>({id:e.id,rowsName:"Row ".concat(e.id)})):t.tableRows=[{id:1,rowsName:"Row 1"},{id:2,rowsName:"Row 2"},{id:3,rowsName:"Row 3"}]),t}catch(t){return console.error("Error fetching table structure:",t),{id:e,label:"Table Data",tableColumns:[],tableRows:[{id:1,rowsName:"Row 1"},{id:2,rowsName:"Row 2"},{id:3,rowsName:"Row 3"}]}}},g=(e,t,s,i)=>{var c;let u=[{id:"select",header:e=>{let{table:t}=e;return(0,l.jsx)(r.S,{className:"w-5 h-5 data-[state=checked]:bg-white data-[state=checked]:text-primary-500 border-neutral-100 cursor-pointer",checked:t.getIsAllPageRowsSelected()||t.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:e=>t.toggleAllPageRowsSelected(!!e),"aria-label":"Select all"})},cell:e=>{let{row:t}=e;return(0,l.jsx)(r.S,{className:"w-5 h-5 bg-neutral-100 border-neutral-400 data-[state=checked]:bg-white data-[state=checked]:text-primary-500 cursor-pointer",checked:t.getIsSelected(),onCheckedChange:e=>t.toggleSelected(!!e),"aria-label":"Select row"})},enableHiding:!1},{id:"id",header:e=>{let{column:t}=e;return(0,l.jsxs)("div",{className:"flex items-center hover:text-neutral-300",children:[(0,l.jsx)("span",{children:"ID"}),(0,l.jsx)("span",{className:"ml-2 flex-shrink-0 w-4 h-4",children:(0,l.jsx)(n,{className:"w-full h-full",onClick:()=>t.toggleSorting("asc"===t.getIsSorted())})})]})},accessorFn:(e,t)=>t+1,enableSorting:!0,cell:t=>{let{row:r}=t;return(0,l.jsxs)("div",{className:"flex items-center gap-8 font-medium text-neutral-700",children:[r.index+1,(0,l.jsxs)("span",{className:"flex items-center gap-2",children:[(0,l.jsx)(a.A,{onClick:()=>e(r.original),className:"w-4 h-4 cursor-pointer hover:text-primary-500"}),(0,l.jsx)(p.JBV,{className:"w-4 h-4 cursor-pointer hover:text-primary-500",title:"Edit",onClick:()=>{let e=r.original.id;e&&s&&window.open("/edit-submission/".concat(s,"/").concat(e),"_blank")}})]})]})}},{id:"validation",header:"Validation",accessorKey:"validation"}],m=[];if(i&&i.length>0)m=i;else{if(!t||null==(c=t.answers)||!c.length)return u;m=Array.from(new Map(t.answers.map(e=>[e.question.id,e.question])).values())}return[...u,...m.map(e=>({id:"".concat(e.label),header:t=>{let{column:s}=t;return(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{children:e.label}),(0,l.jsx)(n,{className:"ml-1 h-4 w-4 cursor-pointer opacity-60",onClick:()=>s.toggleSorting("asc"===s.getIsSorted())})]})},accessorFn:t=>{var s,l;let r=t.answers.filter(t=>{var s;return(null==(s=t.question)?void 0:s.id)===e.id});if(0===r.length)return console.log('No answer found for question "'.concat(e.label,'" (ID: ').concat(e.id,") in submission ").concat(t.id," - likely conditional question not triggered")),null;if("selectmany"===e.inputType){let e=r.map(e=>e.value).filter(e=>e&&""!==String(e).trim()).sort();return e.length>0?e.join(", "):null}return null!=(l=null==(s=r[0])?void 0:s.value)?l:null},cell:t=>{let{getValue:s}=t,r=s();if("table"===e.inputType)try{let t,s="string"==typeof r?r:String(r);if(s.startsWith("[")&&s.includes("{"))try{t=JSON.parse(s)}catch(e){console.error("Failed to parse JSON string:",e),s=s.replace(/\\"/g,'"').replace(/^"/,"").replace(/"$/,"");try{t=JSON.parse(s)}catch(l){console.error("Failed second parse attempt:",l);let e=s.match(/\[([\s\S]*)\]/);if(e&&e[0])try{t=JSON.parse(e[0])}catch(s){console.error("Failed third parse attempt:",s);try{let s=e[0].replace(/'/g,'"');t=JSON.parse(s)}catch(e){console.error("Failed fourth parse attempt:",e)}}}}if(!t&&s.includes("columnId")&&s.includes("rowsId"))try{let e=s.replace(/'/g,'"');t=JSON.parse(e)}catch(e){console.error("Failed custom format parsing:",e)}if(Array.isArray(t)){let s=t.map(e=>{let t="";e.columnName?t=e.columnName:e.column&&e.column.columnName?t=e.column.columnName:e.columnId&&(t=e.name?e.name:e.label?e.label:String(e.columnId));let s="";e.rowsName?s=e.rowsName:e.row&&e.row.rowsName?s=e.row.rowsName:e.rowsId&&(s=e.name?e.name:e.label?e.label:String(e.rowsId));let l=void 0!==e.value?e.value:"";return{column:t,row:s,value:l}}),r=new Map,n=new Set;s.forEach(e=>{var t;n.add(String(e.column)),r.has(String(e.row))||r.set(String(e.row),new Map),null==(t=r.get(String(e.row)))||t.set(String(e.column),String(e.value))});let i=Array.from(n),c=Array.from(r.keys());return(0,l.jsx)(()=>{let[t,n]=(0,o.useState)(!1),[u,m]=(0,o.useState)(null),[p,h]=(0,o.useState)(!1);(0,o.useEffect)(()=>{},[u]);let g=async()=>{if(!e.id)return void console.error("No question ID available");h(!0);try{let t=await x(e.id);if(t&&(t.tableRows||console.error("tableRows is missing from the structure!"),m(t),t.tableColumns&&t.tableRows)){let e=new Map,l=new Map;t.tableColumns.forEach(t=>{e.set(t.id,t.columnName),e.set(String(t.id),t.columnName)}),t.tableRows.forEach(e=>{l.set(e.id,e.rowsName),l.set(String(e.id),e.rowsName)}),s.forEach(s=>{if(s.column&&!isNaN(Number(s.column))){let l=s.column;if(e.has(l))s.column,s.column=e.get(l);else{let e=t.tableColumns.find(e=>String(e.id)===String(l));e&&(s.column,s.column=e.columnName)}}if(s.row&&!isNaN(Number(s.row))){let e=s.row;if(l.has(e))s.row,s.row=l.get(e);else{let l=t.tableRows.find(t=>String(t.id)===String(e));l&&(s.row,s.row=l.rowsName)}}});let n=new Map,a=new Set;s.forEach(e=>{var t;a.add(String(e.column)),n.has(String(e.row))||n.set(String(e.row),new Map),null==(t=n.get(String(e.row)))||t.set(String(e.column),String(e.value))}),i.length=0,c.length=0,a.forEach(e=>i.push(e)),n.forEach((e,t)=>c.push(t)),r.clear(),n.forEach((e,t)=>{r.set(t,e)})}}catch(e){console.error("Error fetching table structure:",e)}finally{h(!1)}};return(0,l.jsxs)("div",{className:"font-medium text-neutral-700",children:[(0,l.jsxs)("a",{href:"#",onClick:async e=>{e.preventDefault(),h(!0),n(!0),await g()},className:"inline-flex items-center gap-1 text-primary-500 hover:text-primary-700 hover:underline whitespace-nowrap",children:[(0,l.jsx)(a.A,{size:12,className:"inline"})," Click to view table"]}),null,(0,l.jsx)(d,{isOpen:t,onClose:()=>n(!1),title:e.label||"Table Data",tableData:s,uniqueColumns:i,uniqueRows:c,rowsMap:r,useParentChildColumns:!0,loading:p,tableStructure:u})]})},{})}}catch(e){console.error("Error parsing table data:",e,"Value:",r)}return null==r||""===r?(0,l.jsx)("div",{className:"font-medium text-neutral-400 italic",children:"-"}):(0,l.jsx)("div",{className:"font-medium text-neutral-700",children:String(r)})},enableSorting:!0})),{id:"submissionTime",header:e=>{let{column:t}=e;return(0,l.jsxs)("div",{className:"flex items-center gap-4 hover:text-neutral-300",children:[(0,l.jsx)("span",{children:"Submission Time"}),(0,l.jsx)("span",{className:"ml-2 flex-shrink-0 w-4 h-4",children:(0,l.jsx)(n,{className:"w-full h-full",onClick:()=>t.toggleSorting("asc"===t.getIsSorted())})})]})},accessorKey:"submissionTime",cell:e=>{let{getValue:t}=e,s=t();return(0,l.jsx)("div",{className:"font-medium text-neutral-700",children:h(s,"date")||"Not recorded"})},enableSorting:!0},{id:"submittedBy",header:e=>{let{column:t}=e;return(0,l.jsxs)("div",{className:"flex items-center gap-4 hover:text-neutral-300",children:[(0,l.jsx)("span",{children:"Submitted By"}),(0,l.jsx)("span",{className:"ml-2 flex-shrink-0 w-4 h-4",children:(0,l.jsx)(n,{className:"w-full h-full",onClick:()=>t.toggleSorting("asc"===t.getIsSorted())})})]})},accessorKey:"submittedBy",accessorFn:e=>{var t;return(null==(t=e.user)?void 0:t.name)||"Anonymous"},cell:e=>{let{getValue:t}=e,s=t();return(0,l.jsx)("div",{className:"font-medium text-neutral-700",children:s})},enableSorting:!0}]};var b=s(89852),f=s(93347),y=s(29911),w=s(14549),v=s(36268),j=s(11032),N=s(34540),S=s(71402),C=s(5287),I=s(26715),q=s(5041);let k=e=>{if(!e||!e.answers)return[];let t=new Map;return e.answers.forEach(e=>{var s,l,r;let n=null==(s=e.question)?void 0:s.id,a=(null==(l=e.question)?void 0:l.label)||"Unknown";if(n)if(t.has(n)){let s=t.get(n);s.answers.push(e.value),s.originalData.push(e)}else t.set(n,{type:(null==(r=e.question)?void 0:r.inputType)||"text",question:a,questionObject:e.question,answers:[e.value],originalData:[e]})}),Array.from(t.values())},A=e=>{var t;let{showModal:s,onClose:r,onConfirm:n,submission:a,isMultipleSelection:i=!1,selectedSubmissions:d=[],projectId:m}=e,[p,h]=(0,o.useState)(!1),[x,g]=(0,o.useState)(null),[f,y]=(0,o.useState)(""),[w,A]=(0,o.useState)(null),E=(0,N.wA)(),O=(0,I.jE)(),R=(0,o.useRef)(null),D=(0,o.useRef)(null),[T,F]=(0,o.useState)(""),[M,B]=(0,o.useState)(""),K=o.useMemo(()=>{if(i&&w){let e=d.find(e=>e.id===w);return e?k(e):[]}return k(a)},[a,i,w,d]);(0,o.useEffect)(()=>{if(i&&d.length>0&&!w){var e;let t=null==(e=d[0])?void 0:e.id;void 0!==t&&A(t)}},[i,d,w]);let P=o.useMemo(()=>K.filter(e=>{let t=e.question.toLowerCase().includes(T.toLowerCase())||!T,s=String(e.answers).toLowerCase().includes(M.toLowerCase())||!M;return t&&s}),[K,T,M]),L=(0,v.N4)({data:P,columns:[{accessorKey:"type",header:"Type"},{accessorKey:"question",header:()=>(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{children:"Question"}),(0,l.jsx)(b.p,{ref:R,placeholder:"Search questions...",value:T,onChange:e=>F(e.target.value),className:"bg-neutral-100 text-neutral-700 mt-2 h-8"})]})},{accessorKey:"answers",header:()=>(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{children:"Answer"}),(0,l.jsx)(b.p,{ref:D,placeholder:"Search answers...",value:M,onChange:e=>B(e.target.value),className:"bg-neutral-100 text-neutral-700 mt-2 h-8"})]}),cell:e=>{let{row:t}=e,s=t.original.answers;return i?(0,l.jsx)("div",{className:"flex flex-col",children:(0,l.jsx)("div",{className:"text-neutral-800 italic",children:"Multiple responses"})}):(0,l.jsx)("div",{className:"flex flex-col",children:s.map((e,t)=>(0,l.jsx)("div",{className:"",children:String(e)},t))})}},{accessorKey:"action",header:"Action",cell:e=>{let{row:t}=e;return(0,l.jsx)("button",{className:"btn-primary",onClick:()=>J(t.original),children:"Edit"})}}],getCoreRowModel:(0,j.HT)()}),J=e=>{h(!0),g(e),y(e.answers.join("\n"))},V=(0,q.n)({mutationFn:e=>{if(!e.questionId)throw Error("Question ID is required");if(!e.submissionId)throw Error("Submission ID is required");let t={submissionId:e.submissionId,questionId:e.questionId,answerType:e.answerType,value:e.value};return"selectmany"===e.answerType?t.questionOptionId=Array.isArray(e.questionOptionId)?e.questionOptionId:e.questionOptionId?[e.questionOptionId]:[]:void 0!==e.questionOptionId&&(t.questionOptionId=Array.isArray(e.questionOptionId)?e.questionOptionId[0]:e.questionOptionId),"number"===e.answerType||"decimal"===e.answerType?t.value="string"==typeof e.value?parseFloat(e.value):"number"==typeof e.value?e.value:0:"selectmany"===e.answerType?t.value=Array.isArray(e.value)?e.value.map(e=>String(e)):[String(e.value)]:t.value=String(e.value),(0,C.s4)(t,m)},onSuccess:e=>{E((0,S.Ds)({message:"Answer updated successfully",type:"success"})),O.invalidateQueries({queryKey:["formSubmissions"]}),h(!1),g(null),y(""),n()},onError:e=>{var t,s,l,r,n,a,i;console.error("Error updating answer:",e),console.error("Error details:",{response:null==e||null==(t=e.response)?void 0:t.data,status:null==e||null==(s=e.response)?void 0:s.status,headers:null==e||null==(l=e.response)?void 0:l.headers});let o=(null==e||null==(n=e.response)||null==(r=n.data)?void 0:r.message)||(null==e||null==(i=e.response)||null==(a=i.data)?void 0:a.errors)||"Failed to update answer";E((0,S.Ds)({message:"string"==typeof o?o:"Validation error in the form submission",type:"error"}))}}),H=async()=>{var e,t,s,l;if(!x)return;let r=i&&w&&d.find(e=>e.id===w)||a;if(!(null==r?void 0:r.id))return void E((0,S.Ds)({message:"Submission ID is missing",type:"error"}));let n=f.split("\n").map(e=>e.trim()).filter(e=>e);if(0===n.length)return void E((0,S.Ds)({message:"Please enter a valid response",type:"error"}));let o=null==(e=x.questionObject)?void 0:e.id;if(!o)return void E((0,S.Ds)({message:"Question ID is missing",type:"error"}));let c=x.type||"text";try{let e;if("selectmany"===c){let s=null==(t=x.originalData)?void 0:t.map(e=>e.questionOptionId).filter(Boolean);if(s&&0!==s.length)if(s.length!==n.length)for(e=[...s];e.length<n.length;)e.push(e[0]||null);else e=s;else e=n.map(()=>null)}else e=(null==(l=x.originalData)||null==(s=l[0])?void 0:s.questionOptionId)||null;let a={submissionId:r.id,questionId:o,answerType:c,value:"selectmany"===c?n:n[0],questionOptionId:e};V.mutate(a)}catch(e){console.error("Form validation error:",e),E((0,S.Ds)({message:"Please check your input and try again",type:"error"}))}};return(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(c.A,{isOpen:s,onClose:r,className:"flex flex-col gap-5 p-6 rounded-md",children:p?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("h2",{className:"text-lg font-semibold text-neutral-700",children:["Editing Question: ",null==x?void 0:x.question]}),(0,l.jsx)("p",{className:"text-sm text-neutral-700",children:"You are about to edit responses for one or multiple submissions at once. Use the XML syntax in the text box below. You can also select one of the existing responses from the table of responses. Learn more about how to edit specific responses for one or multiple submissions"}),(0,l.jsx)("textarea",{value:f,onChange:e=>y(e.target.value),className:"mt-4 border border-neutral-400 rounded-md p-2 w-full h-24 focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"Enter new response..."}),(0,l.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,l.jsx)("button",{className:"btn-outline",onClick:()=>{h(!1),g(null),y("")},disabled:V.isPending,children:"Cancel"}),(0,l.jsx)("button",{className:"btn-primary",onClick:H,disabled:V.isPending,children:V.isPending?"Saving...":"Confirm & Save"})]})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"flex flex-col gap-4 max-h-[500px] overflow-y-auto",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:i?"Edit Selected Submission":"Edit Submission"}),(0,l.jsx)("div",{children:i&&d.length>0&&(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("label",{htmlFor:"submission-selector",className:"text-sm font-medium text-neutral-700",children:"Select submission to edit:"}),(0,l.jsx)("select",{id:"submission-selector",className:"border border-neutral-300 rounded-md p-1 text-sm bg-white",value:w||"",onChange:e=>{A(Number(e.target.value))},children:d.map(e=>(0,l.jsxs)("option",{value:e.id,children:["ID: ",e.id]},e.id))})]})})]}),i&&w&&(0,l.jsxs)("div",{className:"bg-primary-500 border text-neutral-100 rounded-md p-3 text-sm",children:[(0,l.jsxs)("p",{className:"font-medium",children:["Editing Submission ID: ",w]}),(0,l.jsx)("p",{className:"text-xs mt-1",children:"You are editing one submission from your multiple selections. Changes will only apply to this specific submission."})]}),(0,l.jsx)("p",{className:"text-sm text-neutral-700",children:i?"You have multiple submissions selected. Choose which specific submission to edit from the dropdown above.":"You are editing a single submission. Make your changes and click Confirm & Save when done."}),(0,l.jsx)("div",{className:"rounded-md border border-neutral-400 max-h-[450px] overflow-auto",children:(0,l.jsxs)(u.XI,{children:[(0,l.jsx)(u.A0,{className:"h-20",children:L.getHeaderGroups().map(e=>(0,l.jsx)(u.Hj,{className:"text-sm border-neutral-400",children:e.headers.map(e=>(0,l.jsx)(u.nd,{className:"py-1 px-6 text-left bg-primary-500 text-neutral-100 font-semibold",children:e.isPlaceholder?null:(0,v.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,l.jsx)(u.BF,{children:(null==(t=L.getRowModel().rows)?void 0:t.length)?L.getRowModel().rows.map(e=>(0,l.jsx)(u.Hj,{className:" text-sm border-neutral-400","data-state":e.getIsSelected()&&"selected",children:e.getVisibleCells().map(e=>(0,l.jsx)(u.nA,{className:"py-4 px-6",children:(0,v.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,l.jsx)(u.Hj,{children:(0,l.jsx)(u.nA,{colSpan:L.getAllColumns().length,className:"h-24 text-center",children:"No results."})})})]})}),(0,l.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,l.jsx)("button",{className:"btn-outline",onClick:r,children:"Cancel"}),(0,l.jsx)("button",{className:"btn-primary",onClick:H,disabled:V.isPending,children:V.isPending?"Saving...":"Confirm & Save"})]})]})})})};var E=s(66163);let O=e=>{var t;let{isOpen:s,onClose:r,submission:n}=e,[a,i]=(0,o.useState)(!1),u=(0,o.useRef)(null),d=new Map;return n.answers.forEach(e=>{let t=e.question.label;d.has(t)||d.set(t,[]),d.get(t).push(e.value)}),(0,l.jsx)(c.A,{isOpen:s,onClose:r,className:"p-6 rounded-md max-w-4xl w-full ",children:(0,l.jsxs)("div",{ref:u,className:"flex flex-col gap-4 transition-all duration-300 ".concat(a?"fixed inset-0 bg-neutral-100 w-screen h-screen z-50 p-6 overflow-auto":""),children:[(0,l.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:"Submission Details"}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("span",{className:"text-sm text-neutral-600",children:"Validation status:"}),(0,l.jsxs)("select",{className:"px-3 py-1 border border-neutral-500 rounded-md text-sm",children:[(0,l.jsx)("option",{value:"",children:"Select..."}),(0,l.jsx)("option",{value:"valid",children:"Valid"}),(0,l.jsx)("option",{value:"invalid",children:"Invalid"}),(0,l.jsx)("option",{value:"pending",children:"Pending"})]}),(0,l.jsxs)("button",{onClick:()=>{i(e=>!e)},className:"btn-primary",children:[(0,l.jsx)(w.D4o,{className:"w-5 h-5"}),a?"Exit Fullscreen":"Fullscreen"]})]})]}),(0,l.jsx)("div",{className:"overflow-x-auto rounded-md border border-neutral-200 bg-neutral-100",children:(0,l.jsxs)("table",{className:"min-w-full divide-y divide-neutral-200",children:[(0,l.jsx)("thead",{className:"bg-primary-500 text-neutral-100",children:(0,l.jsxs)("tr",{children:[(0,l.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium  uppercase tracking-wider",children:"Question"}),(0,l.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium uppercase tracking-wider",children:"Response"})]})}),(0,l.jsx)("tbody",{className:"bg-neutral-100 divide-y divide-neutral-200",children:[...d.entries()].map(e=>{let[t,s]=e,r=n.answers.find(e=>e.question.label===t),a=(null==r?void 0:r.question.inputType)==="table";return(0,l.jsxs)("tr",{children:[(0,l.jsx)("td",{className:"px-4 py-2 align-top",children:t}),(0,l.jsx)("td",{className:"px-4 py-2",children:a?(0,l.jsx)("div",{className:"text-neutral-600 italic",children:"Table data (view in data table section)"}):s.join(", ")})]},t)})})]})}),(0,l.jsxs)("div",{className:"flex justify-between items-center mt-4",children:[(0,l.jsxs)("div",{className:"text-sm text-neutral-600 font-semibold",children:[(0,l.jsxs)("p",{children:["Submitted by: ",null==(t=n.user)?void 0:t.name]}),(0,l.jsxs)("p",{children:["Submission time: ",n.submissionTime]})]}),(0,l.jsx)("div",{children:(0,l.jsx)("button",{className:"btn-primary",onClick:r,children:"Close"})})]})]})})};var R=s(34947),D=s(67133),T=s(97168),F=s(63642),M=s(35695),B=s(88570),K=s(19373);let P=async e=>{let{data:t}=await m.A.get("/form-submissions/".concat(e));return t.data.formSubmissions},L="data-table-column-visibility",J=()=>{let{hashedId:e}=(0,M.useParams)(),t=Number((0,B.D)(e)),s=(0,N.wA)(),r=(0,I.jE)(),{data:n=[],isLoading:a,refetch:i}=(0,K.I)({queryKey:["formSubmissions",t],queryFn:()=>P(t),enabled:null!==t,refetchInterval:1e3,staleTime:0,gcTime:0}),{data:c=[],isLoading:u}=(0,K.I)({queryKey:["allQuestions",t],queryFn:()=>(0,R.K4)({projectId:t}),enabled:null!==t,staleTime:3e5}),[d,m]=(0,o.useState)(!1),[h,x]=(0,o.useState)(!1),[v,j]=(0,o.useState)(""),[k,J]=(0,o.useState)(!1),[V,H]=o.useState(null),[Q,_]=(0,o.useState)(!1),[U,Y]=o.useState({}),[z,G]=o.useState(null),[X,$]=o.useState({}),[W,Z]=(0,o.useState)(!1),[ee,et]=(0,o.useState)(null),es=n.length>0&&c.length>0?g(e=>{et(e),Z(!0)},n[0],e,c):[],[el,er]=o.useState(!1),[en,ea]=(0,o.useState)(!1),ei=(0,o.useRef)(null),eo=(0,o.useRef)(null),ec=(0,q.n)({mutationFn:e=>(0,C.O8)(e,t),onSuccess:()=>{s((0,S.Ds)({message:"Submission deleted successfully",type:"success"})),r.invalidateQueries({queryKey:["formSubmissions",t]}),$({}),J(!1),et(null),x(!1)},onError:e=>{console.error("Error deleting submission:",e),s((0,S.Ds)({message:"Failed to delete submission",type:"error"})),x(!1)}}),eu=(0,q.n)({mutationFn:e=>(0,C.J6)(e,t),onSuccess:(e,l)=>{s((0,S.Ds)({message:"".concat(l.length," submissions deleted successfully"),type:"success"})),r.invalidateQueries({queryKey:["formSubmissions",t]}),$({}),J(!1),et(null),x(!1)},onError:e=>{console.error("Error deleting multiple submissions:",e),s((0,S.Ds)({message:"Failed to delete submissions",type:"error"})),x(!1)}});return(0,o.useEffect)(()=>{let e=e=>{ei.current&&!ei.current.contains(e.target)&&ea(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,o.useEffect)(()=>{try{let e=localStorage.getItem(L);if(e){let t=JSON.parse(e);t&&"object"==typeof t&&!Array.isArray(t)?Y(t):console.warn("Invalid format in localstorage for column visibility")}}catch(e){console.error("Error loading column visibility:",e)}},[]),(0,o.useEffect)(()=>{if(Object.keys(U).length>0)try{localStorage.setItem(L,JSON.stringify(U))}catch(e){console.error("Error saving column visibility:",e)}},[U]),(0,o.useEffect)(()=>{z&&0===Object.keys(X).length&&z.resetRowSelection()},[X,z]),(0,l.jsxs)("div",{ref:eo,className:"flex flex-col gap-4 transition-all duration-300 ".concat(d?"fixed inset-0 bg-neutral-100 w-screen h-screen z-50 p-6 overflow-auto":""),children:[(0,l.jsxs)("div",{className:"flex flex-col desktop:flex-row justify-between gap-8 items-center py-4",children:[(0,l.jsxs)("div",{className:"flex items-center gap-4",children:[(0,l.jsx)(b.p,{placeholder:"Search all columns...",value:v,onChange:e=>j(e.target.value)}),z&&(0,l.jsxs)(D.rI,{open:el,onOpenChange:e=>er(e),children:[(0,l.jsx)(D.ty,{asChild:!0,children:(0,l.jsxs)(T.$,{variant:"outline",className:"flex items-center gap-2 cursor-pointer",children:["Show/Hide Columns",el?(0,l.jsx)(y.Ucs,{className:"w-3 h-3"}):(0,l.jsx)(y.Vr3,{className:"w-3 h-3"})]})}),(0,l.jsx)(D.SQ,{align:"start",className:"bg-neutral-100 border border-neutral-200 shadow-md",children:z.getAllColumns().filter(e=>e.getCanHide()).map(e=>{var t;return(0,l.jsx)(D.hO,{className:"capitalize cursor-pointer hover:bg-neutral-200",checked:null==(t=U[e.id])||t,onCheckedChange:t=>Y(s=>({...s,[e.id]:t})),children:e.id},e.id)})})]})]}),(0,l.jsxs)("div",{ref:ei,className:"flex relative items-center gap-4 text-neutral-800",children:[(0,l.jsxs)("button",{onClick:()=>{m(e=>!e)},className:"btn-primary",children:[(0,l.jsx)(w.D4o,{className:"w-5 h-5"}),d?"Exit Fullscreen":"Fullscreen"]}),(0,l.jsxs)("button",{className:" bg-primary-500 font-medium rounded-lg shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2  active:scale-95 transition-all duration-300 ".concat(k?"hover:bg-primary-600  cursor-pointer":"opacity-50"),onClick:k?()=>{ea(e=>!e)}:void 0,children:["Status",en?(0,l.jsx)(y.Ucs,{className:"w-3 h-3"}):(0,l.jsx)(y.Vr3,{className:"w-3 h-3"})]}),en&&(0,l.jsx)("div",{className:"absolute left-30 top-10 mt-2 w-64 bg-neutral-100 border border-gray-200 shadow-md rounded-md p-2 z-40",children:(0,l.jsxs)("div",{className:"flex flex-col  gap-2",children:[(0,l.jsx)("div",{className:"hover:bg-neutral-200 cursor-pointer p-2 text-neutral-800 rounded-sm",children:"Set on: Approved"}),(0,l.jsx)("div",{className:"hover:bg-neutral-200 cursor-pointer p-2 text-neutral-800 rounded-sm",children:"Set on: Not Approved"}),(0,l.jsx)("div",{className:"hover:bg-neutral-200 cursor-pointer p-2 text-neutral-800 rounded-sm",children:"Set on: On Hold"})]})}),(0,l.jsxs)("button",{className:" bg-primary-500 font-medium rounded-lg shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2  active:scale-95 transition-all duration-300 ".concat(k?"hover:bg-primary-600  cursor-pointer":"opacity-50"),onClick:k?()=>{let e=Object.keys(X);if(0===e.length)return void s((0,S.Ds)({message:"No submission selected for editing",type:"error"}));let t=e.map(e=>n[Number(e)]).filter(Boolean);if(1===e.length){et(t[0]),_(!0);return}e.length>1&&(et(t[0]),_(!0))}:void 0,children:[(0,l.jsx)(p.JBV,{className:"h-4 w-4"}),"Edit"]}),(0,l.jsxs)("button",{className:" bg-primary-500 font-medium rounded-lg shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2  active:scale-95 transition-all duration-300 ".concat(k?"hover:bg-primary-600  cursor-pointer":"opacity-50"),onClick:k?()=>{let e=Object.keys(X).map(e=>{var t;return(null==(t=n[parseInt(e)])?void 0:t.id)||0}).filter(e=>e>0);H({title:"Confirm Deletion",description:(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)("p",{children:["Are you sure you want to delete"," ",e.length>1?"these ".concat(e.length," submissions"):"this submission","? It is not possible to recover deleted submissions."]})}),confirmButtonText:"Delete",confirmButtonClass:"bg-red-500 hover:bg-red-600 cursor-pointer",onConfirm:()=>{1===e.length?ec.mutate(e[0]):e.length>1&&eu.mutate(e)}}),x(!0)}:void 0,children:[(0,l.jsx)(f.hJ0,{className:"h-4 w-4"}),"Delete"]})]})]}),a||u?(0,l.jsx)("div",{className:"flex justify-center items-center py-12",children:(0,l.jsx)("div",{className:"text-muted-foreground",children:"Loading data..."})}):(0,l.jsx)(E.x,{columns:es,data:n,globalFilter:v,setGlobalFilter:j,onTableInit:e=>{G(e),Object.keys(U).length>0&&e.setColumnVisibility(U)},columnVisibility:U,setColumnVisibility:e=>{Y(e)},onRowSelectionChange:e=>{J(Object.keys(e).length>0),$(e),1===Object.keys(e).length?et(n[Number(Object.keys(e)[0])]):0===Object.keys(e).length&&et(null)},rowSelection:X}),Q&&ee&&(0,l.jsx)(A,{showModal:Q,projectId:t,onClose:()=>{_(!1),et(null),$({}),J(!1)},onConfirm:()=>{r.invalidateQueries({queryKey:["formSubmissions",t]}),$({}),J(!1),_(!1),et(null)},submission:ee,isMultipleSelection:Object.keys(X).length>1,selectedSubmissions:Object.keys(X).length>1?Object.keys(X).map(e=>n[Number(e)]).filter(Boolean):[]}),V&&(0,l.jsx)(F.R,{showModal:h,onClose:()=>x(!1),onConfirm:V.onConfirm,title:V.title,description:V.description,confirmButtonText:V.confirmButtonText,confirmButtonClass:V.confirmButtonClass}),ee&&(0,l.jsx)(O,{isOpen:W,onClose:()=>Z(!1),submission:ee})]})}},92657:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[2150,6711,3873,9204,635,1445,6967,6903,4601,4277,556,3481,1467,6539,6268,4695,9660,8441,1684,7358],()=>t(8501)),_N_E=e.O()}]);