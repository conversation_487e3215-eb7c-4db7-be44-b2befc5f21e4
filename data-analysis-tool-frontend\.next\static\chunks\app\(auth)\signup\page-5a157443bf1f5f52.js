(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2271],{2511:(e,a,t)=>{"use strict";t.d(a,{b:()=>n});let n={information_media:"Information / Media",econommic_social_development:"Economic & Social Development",security_police_peacekeeping:"Security / Police / Peacekeeping",disarmament_and_demobilization:"Disarmament & Demobilization",environment:"Environment",private_sector:"Private Sector",humanitarian_coordination_information_management:"Humanitarian - Coordination & Info Management",humanitarian_multiple_clusters:"Humanitarian - Multiple Clusters",humanitarian_camp_management_and_coordination:"Humanitarian - Camp Management & Coordination",humanitarian_early_recovery:"Humanitarian - Early Recovery",humanitarian_education:"Humanitarian - Education",humanitarian_emergency_shelter:"Humanitarian - Emergency Shelter",humanitarian_emergency_telecoms:"Humanitarian - Emergency Telecoms",humanitarian_food_security:"Humanitarian - Food Security",humanitarian_health:"Humanitarian - Health",humanitarian_logistics:"Humanitarian - Logistics",humanitarian_nutrition:"Humanitarian - Nutrition",humanitarian_protection:"Humanitarian - Protection",humanitarian_sanitation_water_and_hygiene:"Humanitarian - Sanitation / Water / Hygiene",other:"Other"}},17576:(e,a,t)=>{"use strict";t.d(a,{A:()=>n});let n=(0,t(19946).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},23491:(e,a,t)=>{"use strict";t.d(a,{i:()=>n});let n={non_profit_organization:"Non-profit Organization",government_institution:"Government Institution",educational_organization:"Educational Organization",a_commercial_or_for_profit_company:"Commercial / For-profit Company",i_am_not_associated_with_any_organization:"Not Associated with any Organization"}},25784:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let n=t(23464).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});n.interceptors.request.use(e=>e,e=>Promise.reject(e)),n.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let i=n},34869:(e,a,t)=>{"use strict";t.d(a,{A:()=>n});let n=(0,t(19946).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},35695:(e,a,t)=>{"use strict";var n=t(18999);t.o(n,"useParams")&&t.d(a,{useParams:function(){return n.useParams}}),t.o(n,"usePathname")&&t.d(a,{usePathname:function(){return n.usePathname}}),t.o(n,"useRouter")&&t.d(a,{useRouter:function(){return n.useRouter}}),t.o(n,"useSearchParams")&&t.d(a,{useSearchParams:function(){return n.useSearchParams}})},50408:(e,a,t)=>{"use strict";t.d(a,{l:()=>r});var n=t(95155),i=t(66474),s=t(12115);let r=e=>{let{id:a,options:t,value:r,onChange:o}=e,[l,c]=(0,s.useState)(!1),u=(0,s.useRef)(null),d=(0,s.useRef)([]),m=(0,s.useRef)(null);(0,s.useEffect)(()=>{let e=e=>{m.current&&!m.current.contains(e.target)&&c(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let p=e=>{if(!l)return;let a=e.key.toLowerCase();if(a.match(/[a-z]/)){let e=t.findIndex(e=>e.toLowerCase().startsWith(a));if(-1!==e&&d.current[e]){var n;null==(n=d.current[e])||n.scrollIntoView({behavior:"auto",block:"nearest"})}}};return(0,s.useEffect)(()=>(document.addEventListener("keydown",p),()=>{document.removeEventListener("keydown",p)}),[l,t]),(0,n.jsxs)("div",{className:"relative",ref:m,children:[(0,n.jsxs)("button",{id:a,type:"button",className:"px-4 py-2 flex items-center justify-between rounded-md border border-neutral-400 focus:border-primary-500 duration-300 w-full text-left cursor-pointer",onClick:()=>{c(!l)},children:[(0,n.jsx)("span",{children:r||"Select an option"}),(0,n.jsx)(i.A,{})]}),l&&(0,n.jsx)("ul",{className:"absolute z-10 max-h-[180px] overflow-auto border border-neutral-400 rounded-md bg-neutral-100 w-full mt-1 flex flex-col",ref:u,children:t.map((e,a)=>(0,n.jsx)("li",{ref:e=>{d.current[a]=e},className:"cursor-pointer bg-neutral-100 hover:bg-neutral-200 px-4 py-2",onClick:()=>{o(e),c(!1)},children:e},a))})]})}},59362:(e,a,t)=>{"use strict";t.d(a,{F0:()=>d,pe:()=>i});let{Axios:n,AxiosError:i,CanceledError:s,isCancel:r,CancelToken:o,VERSION:l,all:c,Cancel:u,isAxiosError:d,spread:m,toFormData:p,AxiosHeaders:h,HttpStatusCode:g,formToJSON:x,getAdapter:y,mergeConfig:b}=t(23464).A},64368:(e,a,t)=>{"use strict";t.d(a,{H:()=>n});let n=(e,a)=>{let t=Object.entries(a).find(a=>{let[t,n]=a;return n===e});return t?t[0]:null}},66474:(e,a,t)=>{"use strict";t.d(a,{A:()=>n});let n=(0,t(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},71402:(e,a,t)=>{"use strict";t.d(a,{Ay:()=>r,Ds:()=>i,_b:()=>s});let n=(0,t(51990).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,a)=>{e.message=a.payload.message,e.type=a.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:i,hideNotification:s}=n.actions,r=n.reducer},74439:(e,a,t)=>{Promise.resolve().then(t.bind(t,90208))},74567:e=>{"use strict";e.exports=JSON.parse('["Afghanistan","Albania","Algeria","Andorra","Angola","Argentina","Armenia","Australia","Austria","Azerbaijan","Bahamas","Bahrain","Bangladesh","Barbados","Belarus","Belgium","Belize","Benin","Bhutan","Bolivia","Bosnia and Herzegovina","Botswana","Brazil","Brunei","Bulgaria","Burkina Faso","Burundi","Cabo Verde","Cambodia","Cameroon","Canada","Central African Republic","Chad","Chile","China","Colombia","Comoros","Congo (Congo-Brazzaville)","Costa Rica","Croatia","Cuba","Cyprus","Czech Republic","Denmark","Djibouti","Dominica","Dominican Republic","Ecuador","Egypt","El Salvador","Equatorial Guinea","Eritrea","Estonia","Eswatini","Ethiopia","Fiji","Finland","France","Gabon","Gambia","Georgia","Germany","Ghana","Greece","Grenada","Guatemala","Guinea","Guinea-Bissau","Guyana","Haiti","Honduras","Hungary","Iceland","India","Indonesia","Iran","Iraq","Ireland","Israel","Italy","Jamaica","Japan","Jordan","Kazakhstan","Kenya","Kiribati","Kuwait","Kyrgyzstan","Laos","Latvia","Lebanon","Lesotho","Liberia","Libya","Liechtenstein","Lithuania","Luxembourg","Madagascar","Malawi","Malaysia","Maldives","Mali","Malta","Marshall Islands","Mauritania","Mauritius","Mexico","Micronesia","Moldova","Monaco","Mongolia","Montenegro","Morocco","Mozambique","Myanmar","Namibia","Nauru","Nepal","Netherlands","New Zealand","Nicaragua","Niger","Nigeria","North Korea","North Macedonia","Norway","Oman","Pakistan","Palau","Palestine","Panama","Papua New Guinea","Paraguay","Peru","Philippines","Poland","Portugal","Qatar","Romania","Russia","Rwanda","Saint Kitts and Nevis","Saint Lucia","Saint Vincent and the Grenadines","Samoa","San Marino","Sao Tome and Principe","Saudi Arabia","Senegal","Serbia","Seychelles","Sierra Leone","Singapore","Slovakia","Slovenia","Solomon Islands","Somalia","South Africa","South Korea","South Sudan","Spain","Sri Lanka","Sudan","Suriname","Sweden","Switzerland","Syria","Taiwan","Tajikistan","Tanzania","Thailand","Timor-Leste","Togo","Tonga","Trinidad and Tobago","Tunisia","Turkey","Turkmenistan","Tuvalu","Uganda","Ukraine","United Arab Emirates","United Kingdom","United States","Uruguay","Uzbekistan","Vanuatu","Vatican City","Venezuela","Vietnam","Yemen","Zambia","Zimbabwe"]')},90208:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>P});var n=t(95155),i=t(90221),s=t(90232),r=t(78749),o=t(92657),l=t(34869),c=t(17576),u=t(6874),d=t.n(u),m=t(35695),p=t(12115),h=t(62177),g=t(55594),x=t(59362),y=t(34540),b=t(71402),f=t(50408),w=t(74567),N=t(2511),v=t(23491),j=t(64368),S=t(25784);let _=g.z.object({name:g.z.string().min(1,"Full name is required"),email:g.z.string().min(1,"Email is required").email("Please enter a valid email address"),password:g.z.string().min(1,"Password is required").min(8,"Password must be at least 8 characters").max(32,"Password must be less than 32 characters").regex(/[A-Z]/,"Password must contain at least one uppercase letter").regex(/[a-z]/,"Password must contain at least one lowercase letter").regex(/[0-9]/,"Password must contain at least one number").regex(/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/,"Password must contain at least one special character"),confirmPassword:g.z.string().min(1,"Please confirm your password"),country:g.z.string().min(1,"Please select a country"),sector:g.z.string().min(1,"Please select a sector"),organizationType:g.z.string().min(1,"Please select an organization type")}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]}),P=()=>{let{register:e,formState:{errors:a,isSubmitting:t,isSubmitted:u},setValue:g,handleSubmit:P,setError:z,watch:C}=(0,h.mN)({resolver:(0,i.u)(_)}),A=C("password"),k=C("confirmPassword");(0,p.useEffect)(()=>{e("country",{required:"Please select a country"}),e("sector",{required:"Please select a sector"}),e("organizationType",{required:"Please select an organization type"})},[e]);let[E,M]=(0,p.useState)(""),[T,H]=(0,p.useState)(""),[L,B]=(0,p.useState)(""),[R,F]=(0,p.useState)(!1),[I,G]=(0,p.useState)(!1);(0,p.useEffect)(()=>{g("country",E,{shouldValidate:u}),g("sector",T,{shouldValidate:u}),g("organizationType",L,{shouldValidate:u})},[E,T,L,g]);let O=(0,m.useRouter)(),V=(0,y.wA)(),q=async e=>{try{await S.A.post("/users/signup",e),O.push("/"),V((0,b.Ds)({message:"Sign-up successful! We've sent a verification email to your inbox. Please verify your email before logging in.",type:"success"}))}catch(e){if(e instanceof x.pe){var a,t;z(null==(a=e.response)?void 0:a.data.errorField,{message:null==(t=e.response)?void 0:t.data.message})}else V((0,b.Ds)({message:"An unexpected error occured while trying to sign up. Please try again",type:"error"}))}};return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,n.jsxs)("div",{className:"flex flex-col gap-8 section w-11/12 mobile:w-4/5 tablet:w-2xl my-8 tablet:my-16",children:[(0,n.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,n.jsx)(s.A,{size:36}),(0,n.jsx)("h1",{className:"text-2xl tablet:text-3xl font-semibold text-center",children:"Create your account"}),(0,n.jsx)("p",{className:"text-neutral-700 text-center",children:"Get started with data analysis tool"})]}),(0,n.jsxs)("form",{className:"flex flex-col gap-4",onSubmit:P(q),children:[(0,n.jsxs)("div",{className:"group label-input-group",children:[(0,n.jsx)("label",{htmlFor:"name",className:"label-text",children:"Full Name"}),(0,n.jsx)("input",{...e("name"),id:"name",type:"text",placeholder:"Enter your full name",className:"input-field"}),a.name&&(0,n.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(a.name.message)})]}),(0,n.jsxs)("div",{className:"group label-input-group",children:[(0,n.jsx)("label",{htmlFor:"email",className:"label-text",children:"Email"}),(0,n.jsx)("input",{...e("email"),id:"email",type:"email",placeholder:"Enter your email address",className:"input-field"}),a.email&&(0,n.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(a.email.message)})]}),(0,n.jsxs)("div",{className:"group label-input-group",children:[(0,n.jsx)("label",{htmlFor:"password",className:"label-text",children:"Password"}),(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("input",{...e("password"),id:"password",type:R?"text":"password",placeholder:"Enter your password",className:"input-field w-full pr-10"}),A&&A.length>0&&(0,n.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>F(!R),children:[R?(0,n.jsx)(r.A,{className:"h-4 w-4"}):(0,n.jsx)(o.A,{className:"h-4 w-4"}),(0,n.jsxs)("span",{className:"sr-only",children:[R?"Hide":"Show"," password"]})]})]}),a.password&&(0,n.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(a.password.message)})]}),(0,n.jsxs)("div",{className:"group label-input-group",children:[(0,n.jsx)("label",{htmlFor:"confirm-password",className:"label-text",children:"Confirm Password"}),(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("input",{...e("confirmPassword"),id:"confirm-password",type:I?"text":"password",placeholder:"Confirm your password",className:"input-field w-full pr-10"}),k&&k.length>0&&(0,n.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>G(!I),children:[I?(0,n.jsx)(r.A,{className:"h-4 w-4"}):(0,n.jsx)(o.A,{className:"h-4 w-4"}),(0,n.jsxs)("span",{className:"sr-only",children:[I?"Hide":"Show"," password"]})]})]}),a.confirmPassword&&(0,n.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(a.confirmPassword.message)})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 tablet:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{className:"label-input-group group",children:[(0,n.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,n.jsx)(l.A,{size:16})," Country"]}),(0,n.jsx)(f.l,{id:"country",options:w,value:E,onChange:M}),a.country&&(0,n.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(a.country.message)})]}),(0,n.jsxs)("div",{className:"label-input-group group",children:[(0,n.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,n.jsx)(c.A,{size:16})," Sector"]}),(0,n.jsx)(f.l,{id:"sector",options:Object.values(N.b),value:T&&N.b[T]?N.b[T]:"Select an option",onChange:e=>{let a=(0,j.H)(e,N.b);H(null!=a?a:"")}}),a.sector&&(0,n.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(a.sector.message)})]}),(0,n.jsxs)("div",{className:"label-input-group group",children:[(0,n.jsxs)("label",{htmlFor:"organizationType",className:"label-text",children:[(0,n.jsx)(c.A,{size:16})," Organization Type"]}),(0,n.jsx)(f.l,{id:"organizationType",options:Object.values(v.i),value:L&&v.i[L]?v.i[L]:"Select an option",onChange:e=>{let a=(0,j.H)(e,v.i);B(null!=a?a:"")}}),a.organizationType&&(0,n.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(a.organizationType.message)})]})]}),(0,n.jsx)("button",{type:"submit",className:"btn-primary",disabled:t,children:t?(0,n.jsxs)("span",{className:"flex items-center gap-2",children:["Signing up"," ",(0,n.jsx)("div",{className:"size-4 rounded-full border-x-2 animate-spin"})]}):"Sign up"})]}),(0,n.jsxs)("div",{className:"text-neutral-700 flex items-center gap-2",children:[(0,n.jsx)("span",{children:"Already have an account?"}),(0,n.jsx)(d(),{href:"/",className:"font-medium hover:text-neutral-900 duration-300",children:"Sign in"})]})]})})}},90232:(e,a,t)=>{"use strict";t.d(a,{A:()=>n});let n=(0,t(19946).A)("shield-check",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])}},e=>{var a=a=>e(e.s=a);e.O(0,[635,1445,2177,6874,2050,8441,1684,7358],()=>a(74439)),_N_E=e.O()}]);