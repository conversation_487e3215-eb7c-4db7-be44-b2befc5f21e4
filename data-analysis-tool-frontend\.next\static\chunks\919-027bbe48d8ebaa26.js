"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[919],{4884:(e,t,r)=>{r.d(t,{bL:()=>S,zi:()=>C});var n=r(12115),l=r(85185),o=r(6101),a=r(46081),i=r(5845),s=r(45503),d=r(11275),u=r(63655),c=r(95155),p="Switch",[f,v]=(0,a.A)(p),[h,m]=f(p),w=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:a,checked:s,defaultChecked:d,required:f,disabled:v,value:m="on",onCheckedChange:w,form:y,...g}=e,[S,C]=n.useState(null),k=(0,o.s)(t,e=>C(e)),j=n.useRef(!1),R=!S||y||!!S.closest("form"),[E,N]=(0,i.i)({prop:s,defaultProp:null!=d&&d,onChange:w,caller:p});return(0,c.jsxs)(h,{scope:r,checked:E,disabled:v,children:[(0,c.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":E,"aria-required":f,"data-state":b(E),"data-disabled":v?"":void 0,disabled:v,value:m,...g,ref:k,onClick:(0,l.m)(e.onClick,e=>{N(e=>!e),R&&(j.current=e.isPropagationStopped(),j.current||e.stopPropagation())})}),R&&(0,c.jsx)(x,{control:S,bubbles:!j.current,name:a,value:m,checked:E,required:f,disabled:v,form:y,style:{transform:"translateX(-100%)"}})]})});w.displayName=p;var y="SwitchThumb",g=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,l=m(y,r);return(0,c.jsx)(u.sG.span,{"data-state":b(l.checked),"data-disabled":l.disabled?"":void 0,...n,ref:t})});g.displayName=y;var x=n.forwardRef((e,t)=>{let{__scopeSwitch:r,control:l,checked:a,bubbles:i=!0,...u}=e,p=n.useRef(null),f=(0,o.s)(p,t),v=(0,s.Z)(a),h=(0,d.X)(l);return n.useEffect(()=>{let e=p.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(v!==a&&t){let r=new Event("click",{bubbles:i});t.call(e,a),e.dispatchEvent(r)}},[v,a,i]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...u,tabIndex:-1,ref:f,style:{...u.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function b(e){return e?"checked":"unchecked"}x.displayName="SwitchBubbleInput";var S=w,C=g},9428:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},38715:(e,t,r)=>{r.d(t,{UC:()=>eA,YJ:()=>eH,In:()=>eD,q7:()=>e_,VF:()=>eO,p4:()=>eV,JU:()=>eB,ZL:()=>eM,bL:()=>eI,wn:()=>eK,PP:()=>eF,wv:()=>eq,l9:()=>eP,WT:()=>eL,LM:()=>eG});var n=r(12115),l=r(47650);function o(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(85185),i=r(37328),s=r(6101),d=r(46081),u=r(94315),c=r(58434),p=r(92293),f=r(25519),v=r(61285),h=r(63753),m=r(34378),w=r(63655),y=r(99708),g=r(39033),x=r(5845),b=r(52712),S=r(45503),C=r(95155),k=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});n.forwardRef((e,t)=>(0,C.jsx)(w.sG.span,{...e,ref:t,style:{...k,...e.style}})).displayName="VisuallyHidden";var j=r(38168),R=r(93795),E=[" ","Enter","ArrowUp","ArrowDown"],N=[" ","Enter"],T="Select",[I,P,L]=(0,i.N)(T),[D,M]=(0,d.A)(T,[L,h.Bk]),A=(0,h.Bk)(),[G,H]=D(T),[B,_]=D(T),V=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:d,dir:c,name:p,autoComplete:f,disabled:m,required:w,form:y}=e,g=A(t),[b,S]=n.useState(null),[k,j]=n.useState(null),[R,E]=n.useState(!1),N=(0,u.jH)(c),[P,L]=(0,x.i)({prop:l,defaultProp:null!=o&&o,onChange:a,caller:T}),[D,M]=(0,x.i)({prop:i,defaultProp:s,onChange:d,caller:T}),H=n.useRef(null),_=!b||y||!!b.closest("form"),[V,O]=n.useState(new Set),F=Array.from(V).map(e=>e.props.value).join(";");return(0,C.jsx)(h.bL,{...g,children:(0,C.jsxs)(G,{required:w,scope:t,trigger:b,onTriggerChange:S,valueNode:k,onValueNodeChange:j,valueNodeHasChildren:R,onValueNodeHasChildrenChange:E,contentId:(0,v.B)(),value:D,onValueChange:M,open:P,onOpenChange:L,dir:N,triggerPointerDownPosRef:H,disabled:m,children:[(0,C.jsx)(I.Provider,{scope:t,children:(0,C.jsx)(B,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{O(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{O(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),_?(0,C.jsxs)(eR,{"aria-hidden":!0,required:w,tabIndex:-1,name:p,autoComplete:f,value:D,onChange:e=>M(e.target.value),disabled:m,form:y,children:[void 0===D?(0,C.jsx)("option",{value:""}):null,Array.from(V)]},F):null]})})};V.displayName=T;var O="SelectTrigger",F=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,i=A(r),d=H(O,r),u=d.disabled||l,c=(0,s.s)(t,d.onTriggerChange),p=P(r),f=n.useRef("touch"),[v,m,y]=eN(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===d.value),n=eT(t,e,r);void 0!==n&&d.onValueChange(n.value)}),g=e=>{u||(d.onOpenChange(!0),y()),e&&(d.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,C.jsx)(h.Mz,{asChild:!0,...i,children:(0,C.jsx)(w.sG.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":eE(d.value)?"":void 0,...o,ref:c,onClick:(0,a.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&g(e)}),onPointerDown:(0,a.m)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:(0,a.m)(o.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&E.includes(e.key)&&(g(),e.preventDefault())})})})});F.displayName=O;var K="SelectValue",q=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,d=H(K,r),{onValueNodeHasChildrenChange:u}=d,c=void 0!==o,p=(0,s.s)(t,d.onValueNodeChange);return(0,b.N)(()=>{u(c)},[u,c]),(0,C.jsx)(w.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:eE(d.value)?(0,C.jsx)(C.Fragment,{children:a}):o})});q.displayName=K;var U=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,C.jsx)(w.sG.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});U.displayName="SelectIcon";var W=e=>(0,C.jsx)(m.Z,{asChild:!0,...e});W.displayName="SelectPortal";var z="SelectContent",X=n.forwardRef((e,t)=>{let r=H(z,e.__scopeSelect),[o,a]=n.useState();return((0,b.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,C.jsx)(Q,{...e,ref:t}):o?l.createPortal((0,C.jsx)(Z,{scope:e.__scopeSelect,children:(0,C.jsx)(I.Slot,{scope:e.__scopeSelect,children:(0,C.jsx)("div",{children:e.children})})}),o):null});X.displayName=z;var[Z,Y]=D(z),J=(0,y.TL)("SelectContent.RemoveScroll"),Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:d,side:u,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:y,collisionPadding:g,sticky:x,hideWhenDetached:b,avoidCollisions:S,...k}=e,E=H(z,r),[N,T]=n.useState(null),[I,L]=n.useState(null),D=(0,s.s)(t,e=>T(e)),[M,A]=n.useState(null),[G,B]=n.useState(null),_=P(r),[V,O]=n.useState(!1),F=n.useRef(!1);n.useEffect(()=>{if(N)return(0,j.Eq)(N)},[N]),(0,p.Oh)();let K=n.useCallback(e=>{let[t,...r]=_().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&I&&(I.scrollTop=0),r===n&&I&&(I.scrollTop=I.scrollHeight),null==r||r.focus(),document.activeElement!==l))return},[_,I]),q=n.useCallback(()=>K([M,N]),[K,M,N]);n.useEffect(()=>{V&&q()},[V,q]);let{onOpenChange:U,triggerPointerDownPosRef:W}=E;n.useEffect(()=>{if(N){let e={x:0,y:0},t=t=>{var r,n,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!=(l=null==(r=W.current)?void 0:r.x)?l:0)),y:Math.abs(Math.round(t.pageY)-(null!=(o=null==(n=W.current)?void 0:n.y)?o:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():N.contains(r.target)||U(!1),document.removeEventListener("pointermove",t),W.current=null};return null!==W.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[N,U,W]),n.useEffect(()=>{let e=()=>U(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[U]);let[X,Y]=eN(e=>{let t=_().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eT(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),Q=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==E.value&&E.value===t||n)&&(A(e),n&&(F.current=!0))},[E.value]),et=n.useCallback(()=>null==N?void 0:N.focus(),[N]),er=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==E.value&&E.value===t||n)&&B(e)},[E.value]),en="popper"===l?ee:$,el=en===ee?{side:u,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:y,collisionPadding:g,sticky:x,hideWhenDetached:b,avoidCollisions:S}:{};return(0,C.jsx)(Z,{scope:r,content:N,viewport:I,onViewportChange:L,itemRefCallback:Q,selectedItem:M,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:q,selectedItemText:G,position:l,isPositioned:V,searchRef:X,children:(0,C.jsx)(R.A,{as:J,allowPinchZoom:!0,children:(0,C.jsx)(f.n,{asChild:!0,trapped:E.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(o,e=>{var t;null==(t=E.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,C.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>E.onOpenChange(!1),children:(0,C.jsx)(en,{role:"listbox",id:E.contentId,"data-state":E.open?"open":"closed",dir:E.dir,onContextMenu:e=>e.preventDefault(),...k,...el,onPlaced:()=>O(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...k.style},onKeyDown:(0,a.m)(k.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=_().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>K(t)),e.preventDefault()}})})})})})})});Q.displayName="SelectContentImpl";var $=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=H(z,r),d=Y(z,r),[u,c]=n.useState(null),[p,f]=n.useState(null),v=(0,s.s)(t,e=>f(e)),h=P(r),m=n.useRef(!1),y=n.useRef(!0),{viewport:g,selectedItem:x,selectedItemText:S,focusSelectedItem:k}=d,j=n.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&p&&g&&x&&S){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=S.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,s=e.width+i,d=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.left=c+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,s=e.width+i,d=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.right=c+"px"}let a=h(),s=window.innerHeight-20,d=g.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),v=parseInt(c.paddingTop,10),w=parseInt(c.borderBottomWidth,10),y=f+v+d+parseInt(c.paddingBottom,10)+w,b=Math.min(5*x.offsetHeight,y),C=window.getComputedStyle(g),k=parseInt(C.paddingTop,10),j=parseInt(C.paddingBottom,10),R=e.top+e.height/2-10,E=x.offsetHeight/2,N=f+v+(x.offsetTop+E);if(N<=R){let e=a.length>0&&x===a[a.length-1].ref.current;u.style.bottom="0px";let t=Math.max(s-R,E+(e?j:0)+(p.clientHeight-g.offsetTop-g.offsetHeight)+w);u.style.height=N+t+"px"}else{let e=a.length>0&&x===a[0].ref.current;u.style.top="0px";let t=Math.max(R,f+g.offsetTop+(e?k:0)+E);u.style.height=t+(y-N)+"px",g.scrollTop=N-R+g.offsetTop}u.style.margin="".concat(10,"px 0"),u.style.minHeight=b+"px",u.style.maxHeight=s+"px",null==l||l(),requestAnimationFrame(()=>m.current=!0)}},[h,i.trigger,i.valueNode,u,p,g,x,S,i.dir,l]);(0,b.N)(()=>j(),[j]);let[R,E]=n.useState();(0,b.N)(()=>{p&&E(window.getComputedStyle(p).zIndex)},[p]);let N=n.useCallback(e=>{e&&!0===y.current&&(j(),null==k||k(),y.current=!1)},[j,k]);return(0,C.jsx)(et,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:m,onScrollButtonChange:N,children:(0,C.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,C.jsx)(w.sG.div,{...a,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});$.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=A(r);return(0,C.jsx)(h.UC,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=D(z,{}),en="SelectViewport",el=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=Y(en,r),d=er(en,r),u=(0,s.s)(t,i.onViewportChange),c=n.useRef(0);return(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,C.jsx)(I.Slot,{scope:r,children:(0,C.jsx)(w.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=d;if((null==n?void 0:n.current)&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});el.displayName=en;var eo="SelectGroup",[ea,ei]=D(eo),es=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,v.B)();return(0,C.jsx)(ea,{scope:r,id:l,children:(0,C.jsx)(w.sG.div,{role:"group","aria-labelledby":l,...n,ref:t})})});es.displayName=eo;var ed="SelectLabel",eu=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=ei(ed,r);return(0,C.jsx)(w.sG.div,{id:l.id,...n,ref:t})});eu.displayName=ed;var ec="SelectItem",[ep,ef]=D(ec),ev=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...d}=e,u=H(ec,r),c=Y(ec,r),p=u.value===l,[f,h]=n.useState(null!=i?i:""),[m,y]=n.useState(!1),g=(0,s.s)(t,e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,l,o)}),x=(0,v.B)(),b=n.useRef("touch"),S=()=>{o||(u.onValueChange(l),u.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,C.jsx)(ep,{scope:r,value:l,disabled:o,textId:x,isSelected:p,onItemTextChange:n.useCallback(e=>{h(t=>{var r;return t||(null!=(r=null==e?void 0:e.textContent)?r:"").trim()})},[]),children:(0,C.jsx)(I.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,C.jsx)(w.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...d,ref:g,onFocus:(0,a.m)(d.onFocus,()=>y(!0)),onBlur:(0,a.m)(d.onBlur,()=>y(!1)),onClick:(0,a.m)(d.onClick,()=>{"mouse"!==b.current&&S()}),onPointerUp:(0,a.m)(d.onPointerUp,()=>{"mouse"===b.current&&S()}),onPointerDown:(0,a.m)(d.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.m)(d.onPointerMove,e=>{if(b.current=e.pointerType,o){var t;null==(t=c.onItemLeave)||t.call(c)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(d.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=c.onItemLeave)||t.call(c)}}),onKeyDown:(0,a.m)(d.onKeyDown,e=>{var t;((null==(t=c.searchRef)?void 0:t.current)===""||" "!==e.key)&&(N.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});ev.displayName=ec;var eh="SelectItemText",em=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,d=H(eh,r),u=Y(eh,r),c=ef(eh,r),p=_(eh,r),[f,v]=n.useState(null),h=(0,s.s)(t,e=>v(e),c.onItemTextChange,e=>{var t;return null==(t=u.itemTextRefCallback)?void 0:t.call(u,e,c.value,c.disabled)}),m=null==f?void 0:f.textContent,y=n.useMemo(()=>(0,C.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:g,onNativeOptionRemove:x}=p;return(0,b.N)(()=>(g(y),()=>x(y)),[g,x,y]),(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)(w.sG.span,{id:c.textId,...i,ref:h}),c.isSelected&&d.valueNode&&!d.valueNodeHasChildren?l.createPortal(i.children,d.valueNode):null]})});em.displayName=eh;var ew="SelectItemIndicator",ey=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ef(ew,r).isSelected?(0,C.jsx)(w.sG.span,{"aria-hidden":!0,...n,ref:t}):null});ey.displayName=ew;var eg="SelectScrollUpButton",ex=n.forwardRef((e,t)=>{let r=Y(eg,e.__scopeSelect),l=er(eg,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,C.jsx)(eC,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ex.displayName=eg;var eb="SelectScrollDownButton",eS=n.forwardRef((e,t)=>{let r=Y(eb,e.__scopeSelect),l=er(eb,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,C.jsx)(eC,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eS.displayName=eb;var eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=Y("SelectScrollButton",r),s=n.useRef(null),d=P(r),u=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>u(),[u]),(0,b.N)(()=>{var e;let t=d().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[d]),(0,C.jsx)(w.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,a.m)(o.onPointerMove,()=>{var e;null==(e=i.onItemLeave)||e.call(i),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,a.m)(o.onPointerLeave,()=>{u()})})}),ek=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,C.jsx)(w.sG.div,{"aria-hidden":!0,...n,ref:t})});ek.displayName="SelectSeparator";var ej="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=A(r),o=H(ej,r),a=Y(ej,r);return o.open&&"popper"===a.position?(0,C.jsx)(h.i3,{...l,...n,ref:t}):null}).displayName=ej;var eR=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,...o}=e,a=n.useRef(null),i=(0,s.s)(t,a),d=(0,S.Z)(l);return n.useEffect(()=>{let e=a.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(d!==l&&t){let r=new Event("change",{bubbles:!0});t.call(e,l),e.dispatchEvent(r)}},[d,l]),(0,C.jsx)(w.sG.select,{...o,style:{...k,...o.style},ref:i,defaultValue:l})});function eE(e){return""===e||void 0===e}function eN(e){let t=(0,g.c)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function eT(e,t,r){var n,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,i=(n=e,l=Math.max(a,0),n.map((e,t)=>n[(l+t)%n.length]));1===o.length&&(i=i.filter(e=>e!==r));let s=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}eR.displayName="SelectBubbleInput";var eI=V,eP=F,eL=q,eD=U,eM=W,eA=X,eG=el,eH=es,eB=eu,e_=ev,eV=em,eO=ey,eF=ex,eK=eS,eq=ek},40968:(e,t,r)=>{r.d(t,{b:()=>i});var n=r(12115),l=r(63655),o=r(95155),a=n.forwardRef((e,t)=>(0,o.jsx)(l.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var i=a},47863:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},54059:(e,t,r)=>{r.d(t,{C1:()=>_,bL:()=>H,q7:()=>B});var n=r(12115),l=r(85185),o=r(6101),a=r(46081),i=r(63655),s=r(89196),d=r(5845),u=r(94315),c=r(11275),p=r(45503),f=r(28905),v=r(95155),h="Radio",[m,w]=(0,a.A)(h),[y,g]=m(h),x=n.forwardRef((e,t)=>{let{__scopeRadio:r,name:a,checked:s=!1,required:d,disabled:u,value:c="on",onCheck:p,form:f,...h}=e,[m,w]=n.useState(null),g=(0,o.s)(t,e=>w(e)),x=n.useRef(!1),b=!m||f||!!m.closest("form");return(0,v.jsxs)(y,{scope:r,checked:s,disabled:u,children:[(0,v.jsx)(i.sG.button,{type:"button",role:"radio","aria-checked":s,"data-state":k(s),"data-disabled":u?"":void 0,disabled:u,value:c,...h,ref:g,onClick:(0,l.m)(e.onClick,e=>{s||null==p||p(),b&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())})}),b&&(0,v.jsx)(C,{control:m,bubbles:!x.current,name:a,value:c,checked:s,required:d,disabled:u,form:f,style:{transform:"translateX(-100%)"}})]})});x.displayName=h;var b="RadioIndicator",S=n.forwardRef((e,t)=>{let{__scopeRadio:r,forceMount:n,...l}=e,o=g(b,r);return(0,v.jsx)(f.C,{present:n||o.checked,children:(0,v.jsx)(i.sG.span,{"data-state":k(o.checked),"data-disabled":o.disabled?"":void 0,...l,ref:t})})});S.displayName=b;var C=n.forwardRef((e,t)=>{let{__scopeRadio:r,control:l,checked:a,bubbles:s=!0,...d}=e,u=n.useRef(null),f=(0,o.s)(u,t),h=(0,p.Z)(a),m=(0,c.X)(l);return n.useEffect(()=>{let e=u.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==a&&t){let r=new Event("click",{bubbles:s});t.call(e,a),e.dispatchEvent(r)}},[h,a,s]),(0,v.jsx)(i.sG.input,{type:"radio","aria-hidden":!0,defaultChecked:a,...d,tabIndex:-1,ref:f,style:{...d.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function k(e){return e?"checked":"unchecked"}C.displayName="RadioBubbleInput";var j=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],R="RadioGroup",[E,N]=(0,a.A)(R,[s.RG,w]),T=(0,s.RG)(),I=w(),[P,L]=E(R),D=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,name:n,defaultValue:l,value:o,required:a=!1,disabled:c=!1,orientation:p,dir:f,loop:h=!0,onValueChange:m,...w}=e,y=T(r),g=(0,u.jH)(f),[x,b]=(0,d.i)({prop:o,defaultProp:null!=l?l:"",onChange:m,caller:R});return(0,v.jsx)(P,{scope:r,name:n,required:a,disabled:c,value:x,onValueChange:b,children:(0,v.jsx)(s.bL,{asChild:!0,...y,orientation:p,dir:g,loop:h,children:(0,v.jsx)(i.sG.div,{role:"radiogroup","aria-required":a,"aria-orientation":p,"data-disabled":c?"":void 0,dir:g,...w,ref:t})})})});D.displayName=R;var M="RadioGroupItem",A=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,disabled:a,...i}=e,d=L(M,r),u=d.disabled||a,c=T(r),p=I(r),f=n.useRef(null),h=(0,o.s)(t,f),m=d.value===i.value,w=n.useRef(!1);return n.useEffect(()=>{let e=e=>{j.includes(e.key)&&(w.current=!0)},t=()=>w.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,v.jsx)(s.q7,{asChild:!0,...c,focusable:!u,active:m,children:(0,v.jsx)(x,{disabled:u,required:d.required,checked:m,...p,...i,name:d.name,ref:h,onCheck:()=>d.onValueChange(i.value),onKeyDown:(0,l.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,l.m)(i.onFocus,()=>{var e;w.current&&(null==(e=f.current)||e.click())})})})});A.displayName=M;var G=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,...n}=e,l=I(r);return(0,v.jsx)(S,{...l,...n,ref:t})});G.displayName="RadioGroupIndicator";var H=D,B=A,_=G},66474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])}}]);