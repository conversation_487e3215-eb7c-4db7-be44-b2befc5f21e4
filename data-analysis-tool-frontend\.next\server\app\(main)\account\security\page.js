(()=>{var e={};e.id=5066,e.ids=[5066],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4441:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\(main)\\\\account\\\\security\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\account\\security\\page.tsx","default")},9379:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\(main)\\\\account\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\account\\layout.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12597:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19169:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},20174:(e,s,t)=>{"use strict";t.d(s,{F:()=>i});var r=t(60687),a=t(16189),n=t(85814),l=t.n(n);t(43210);let i=({items:e})=>{let s=(0,a.usePathname)(),t=e=>s.startsWith(e);return(0,r.jsx)("div",{className:"border-y border-neutral-400 rounded-md bg-primary-500 my-4 shadow-md",children:(0,r.jsx)("div",{className:"flex items-center",children:e.map(e=>e.disabled?(0,r.jsxs)("div",{className:"flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-gray-400 cursor-not-allowed",children:[e.icon,e.label]},e.route):(0,r.jsxs)(l(),{href:e.route,className:`flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-neutral-100 ${t(e.route)?"border-neutral-100":"border-transparent hover:border-neutral-400"}`,children:[e.icon,e.label]},e.route))})})}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34091:(e,s,t)=>{"use strict";t.d(s,{BZ:()=>n,eg:()=>l,ep:()=>i,kH:()=>o,l2:()=>a});var r=t(12810);let a=async()=>{let{data:e}=await r.A.get("/users/profile");return e.profile},n=async({email:e})=>{let{data:s}=await r.A.patch("/users/change-email",{email:e});return s},l=async({dataToSend:e})=>{let{data:s}=await r.A.patch("/users/update",e);return s},i=async()=>{let{data:e}=await r.A.get("/users/sessions");return e.sessions},o=async e=>{let{data:s}=await r.A.post("/users/sendverificationemail",{email:e});return s}},43690:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>M});var r=t(60687),a=t(43210),n=t.n(a),l=t(62688);let i=(0,l.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),o=(0,l.A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var d=t(19169),c=t(19150),u=t(12810),p=t(12597),m=t(13861),x=t(27605),h=t(54864);let f=()=>{let[e,s]=(0,a.useState)(!1),[t,n]=(0,a.useState)(!1),[l,i]=(0,a.useState)(!1),[o,d]=(0,a.useState)(!1),f=(0,h.wA)(),{register:y,handleSubmit:b,formState:{errors:j},setValue:w,watch:g}=(0,x.mN)({defaultValues:{currentPassword:"",newPassword:"",confirmPassword:"",email:""}}),v=g("newPassword");g("confirmPassword");let N=async e=>{let{currentPassword:t,newPassword:r,confirmPassword:a}=e;if(r!==a)return void f((0,c.Ds)({message:"New password and confirm password do not match.",type:"error"}));try{let e=await u.A.post("/users/changepassword",{currentPassword:t,newPassword:r,confirmPassword:a});200===e.status&&(f((0,c.Ds)({message:"Password changed successfully",type:"success"})),s(!1),w("currentPassword",""),w("newPassword",""),w("confirmPassword",""))}catch(e){f((0,c.Ds)({message:e.response?.data?.message||"Server error",type:"error"}))}};return(0,r.jsx)("div",{children:e?(0,r.jsxs)("form",{onSubmit:b(N),className:"flex-col flex gap-4",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"current-password",className:"label-text",children:"Current Password"}),(0,r.jsxs)("div",{className:"relative laptop:w-1/3",children:[(0,r.jsx)("input",{id:"current-password",type:t?"text":"password",placeholder:"Enter current password",className:"input-field w-full pr-10",...y("currentPassword",{required:"Current password is required"})}),(0,r.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>n(!t),children:[t?(0,r.jsx)(p.A,{className:"h-4 w-4"}):(0,r.jsx)(m.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{className:"sr-only",children:[t?"Hide":"Show"," password"]})]})]}),j.currentPassword&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:j.currentPassword.message})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"new-password",className:"label-text",children:"New Password"}),(0,r.jsxs)("div",{className:"relative laptop:w-1/3",children:[(0,r.jsx)("input",{id:"new-password",type:l?"text":"password",placeholder:"Enter new password",className:"input-field w-full pr-10",...y("newPassword",{required:"New password is required",minLength:{value:6,message:"Password must be at least 6 characters"},validate:e=>e!==g("currentPassword")||"New password must be different from current password"})}),(0,r.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>i(!l),children:[l?(0,r.jsx)(p.A,{className:"h-4 w-4"}):(0,r.jsx)(m.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{className:"sr-only",children:[l?"Hide":"Show"," password"]})]})]}),j.newPassword&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:j.newPassword.message})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"confirm-password",className:"label-text",children:"Confirm Password"}),(0,r.jsxs)("div",{className:"relative laptop:w-1/3",children:[(0,r.jsx)("input",{id:"confirm-password",type:o?"text":"password",placeholder:"Enter confirm password",className:"input-field w-full pr-10",...y("confirmPassword",{required:"Please confirm your password",validate:e=>e===v||"Passwords do not match"})}),(0,r.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>d(!o),children:[o?(0,r.jsx)(p.A,{className:"h-4 w-4"}):(0,r.jsx)(m.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{className:"sr-only",children:[o?"Hide":"Show"," password"]})]})]}),j.confirmPassword&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:j.confirmPassword.message})]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("button",{type:"submit",className:"btn-primary",children:"Update Password"}),(0,r.jsx)("button",{className:"btn-outline",onClick:()=>s(!1),children:"Cancel"})]})]}):(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsx)("button",{className:"btn-primary",onClick:()=>s(!0),children:"Change password"})})})};var y=t(56090),b=t(93772),j=t(96752);function w({columns:e,data:s}){let[t,a]=n().useState({pageIndex:0,pageSize:8}),l=(0,y.N4)({data:s,columns:e,onPaginationChange:a,getPaginationRowModel:(0,b.kW)(),getCoreRowModel:(0,b.HT)(),state:{pagination:t}});return(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"rounded-md border border-neutral-400 overflow-hidden",children:(0,r.jsxs)(j.XI,{children:[(0,r.jsx)(j.A0,{children:l.getHeaderGroups().map(e=>(0,r.jsx)(j.Hj,{className:"text-sm border-neutral-400",children:e.headers.map(e=>(0,r.jsx)(j.nd,{className:"py-1 px-6 text-left bg-primary-500 text-neutral-100 font-semibold",children:e.isPlaceholder?null:(0,y.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,r.jsx)(j.BF,{children:l.getRowModel().rows?.length?l.getRowModel().rows.map(e=>(0,r.jsx)(j.Hj,{className:" text-sm border-neutral-400","data-state":e.getIsSelected()&&"selected",children:e.getVisibleCells().map(e=>(0,r.jsx)(j.nA,{className:"py-4 px-6",children:(0,y.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,r.jsx)(j.Hj,{children:(0,r.jsx)(j.nA,{colSpan:e.length,className:"h-24 text-center",children:"No results."})})})]})}),s.length>t.pageSize&&(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-2 py-4",children:[(0,r.jsx)("button",{className:"btn-primary",onClick:()=>l.previousPage(),disabled:!l.getCanPreviousPage(),children:"Previous"}),(0,r.jsx)("button",{className:"btn-primary",onClick:()=>l.nextPage(),disabled:!l.getCanNextPage(),children:"Next"})]})]})}let g=[{accessorKey:"deviceInfo",header:"Device"},{accessorKey:"browserInfo",header:"Browser"},{accessorKey:"updatedAt",header:"Last Activity"},{accessorKey:"ipAddress",header:"IP Address"},{accessorKey:"isActive",header:"Status",cell:({getValue:e})=>e()?"Active":"Inactive"}];var v=t(21650),N=t(29494),P=t(8693),C=t(54050),k=t(34091),A=t(86429),q=t(73678),E=t(1510);let M=()=>{let[e,s]=(0,a.useState)(!1),{user:t,logout:n}=(0,v.A)(),{register:l,formState:{errors:u},handleSubmit:p,getValues:m,reset:y,setError:b}=(0,x.mN)(),{data:j,isLoading:M,isError:S}=(0,N.I)({queryKey:["sessions",t?.id],queryFn:k.ep,enabled:!!t?.id}),K=(0,h.wA)(),D=(0,P.jE)(),[z,_]=(0,a.useState)(!1),I=(0,C.n)({mutationFn:k.BZ,onSuccess:async()=>{try{await D.invalidateQueries({queryKey:["profile",t?.id]}),await (0,k.kH)(m("email")),s(!1),K((0,c.Ds)({message:"Email changed successfully. A verification email has been sent to your new address.",type:"success"})),n()}catch(e){K((0,c.Ds)({message:"Email changed, but we couldn't send the verification email. Please try again manually.",type:"warning"}))}},onError:e=>{e instanceof E.pe?b(e.response?.data.errorField,{message:e.response?.data.message}):K((0,c.Ds)({message:"Failed to change email. Please try again",type:"error"}))}});(0,a.useEffect)(()=>{e||y()},[e,y]);let[F,H]=(0,a.useState)(!1);(0,a.useEffect)(()=>{H(!0)},[]);let O=async e=>{I.mutate({email:e.email})};return F?M?(0,r.jsx)(A.A,{}):S||!j?(0,r.jsx)("p",{className:"text-sm text-red-500",children:"Error loading data"}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(q.R,{showModal:z,onClose:()=>_(!1),onConfirm:()=>{p(O)(),_(!1)},title:"Confirm email change?",description:"Changing email will log you out and you will need to verify your new email before logging in. The verification email will be sent to your new email address.",confirmButtonText:"Change",confirmButtonClass:"btn-primary"}),(0,r.jsxs)("div",{className:"flex flex-col gap-10",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(i,{className:"h-8 w-8"}),(0,r.jsx)("h2",{className:"heading-text",children:"Security Settings"})]}),(0,r.jsx)("p",{className:"sub-text",children:"Manage your account security settings and preferences"})]}),(0,r.jsxs)("div",{className:"flex-col gap-10 flex",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-5 shadow-sm border-muted p-4",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(o,{className:"h-5 w-5"}),(0,r.jsx)("h2",{className:"sub-heading-text",children:"Password"})]}),(0,r.jsx)("p",{className:"sub-text",children:"Update your password to keep your account secure"})]}),(0,r.jsx)("div",{children:(0,r.jsx)(f,{})})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-5 shadow-sm border-muted p-4",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-2 ",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(d.A,{className:"h-5 w-5"}),(0,r.jsx)("h2",{className:"sub-heading-text",children:"Email Address"})]}),(0,r.jsx)("p",{className:"sub-text",children:"Your email address is used for notifications and account recovery"})]}),(0,r.jsx)("div",{children:e?(0,r.jsxs)("form",{className:"space-y-4",noValidate:!0,onSubmit:e=>e.preventDefault(),children:[(0,r.jsx)("input",{...l("email",{required:"Please enter your new email."}),type:"email",placeholder:"eg: <EMAIL>",className:"input-field"}),u.email&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:`${u.email?.message}`}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("button",{type:"button",className:"btn-primary",onClick:()=>_(!0),children:"save"}),(0,r.jsx)("button",{type:"button",className:"btn-outline",onClick:()=>s(!1),children:"Cancel"})]})]}):(0,r.jsxs)("div",{className:"flex justify-between items-center ",children:[(0,r.jsx)("span",{children:t?.email}),(0,r.jsx)("button",{type:"button",className:"btn-primary",onClick:()=>s(!0),children:"Change"})]})})]})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsx)("h2",{className:"heading-text",children:"Recent Account Activity"}),(0,r.jsx)(w,{columns:g,data:j})]})]})]}):null}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58869:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},60570:(e,s,t)=>{Promise.resolve().then(t.bind(t,71797))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71013:(e,s,t)=>{Promise.resolve().then(t.bind(t,4441))},71797:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var r=t(60687),a=t(86429),n=t(58869);let l=(0,t(62688).A)("shield-alert",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"M12 8v4",key:"1got3b"}],["path",{d:"M12 16h.01",key:"1drbdi"}]]);t(43210);var i=t(20174);let o=()=>{let e=[{label:"Profile",icon:(0,r.jsx)(n.A,{size:16}),route:"/account/profile"},{label:"Security",icon:(0,r.jsx)(l,{size:16}),route:"/account/security"}];return(0,r.jsx)(i.F,{items:e})};var d=t(21650),c=t(34091),u=t(29494),p=t(28559),m=t(85814),x=t.n(m);let h=({children:e})=>{let{user:s}=(0,d.A)(),{data:t,isLoading:n,isError:l}=(0,u.I)({queryKey:["profile",s?.id],queryFn:c.l2,enabled:!!s?.id});return n?(0,r.jsx)(a.A,{}):l?(0,r.jsx)("p",{className:"text-red-500",children:"Error loading profile data. Please try again"}):(0,r.jsxs)("div",{className:"section flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h1",{className:"heading-text capitalize",children:t?.name}),(0,r.jsxs)(x(),{href:"/dashboard",className:"flex items-center gap-2",children:[(0,r.jsx)(p.A,{size:16}),"Back to dashboard"]})]}),(0,r.jsx)(o,{}),(0,r.jsx)("div",{className:"px-8",children:e})]})}},73678:(e,s,t)=>{"use strict";t.d(s,{R:()=>n});var r=t(60687);t(43210);var a=t(38587);let n=({showModal:e,onClose:s,onConfirm:t,title:n,description:l,confirmButtonText:i,cancelButtonText:o,confirmButtonClass:d,children:c})=>(0,r.jsxs)(a.A,{isOpen:e,onClose:s,className:"p-6 rounded-md max-w-xl",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:n}),(0,r.jsx)("div",{className:"text-neutral-700 mt-2",children:l}),c&&(0,r.jsx)("div",{className:"mt-6 space-y-4",children:c}),(0,r.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,r.jsx)("button",{className:"btn-outline",onClick:s,type:"button",children:o||"Cancel"}),(0,r.jsx)("button",{className:`font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ${d}`,onClick:t,type:"button",children:i})]})]})},74075:e=>{"use strict";e.exports=require("zlib")},77172:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=t(65239),a=t(48088),n=t(88170),l=t.n(n),i=t(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(s,o);let d={children:["",{children:["(main)",{children:["account",{children:["security",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4441)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\account\\security\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,9379)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\account\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,19559)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\account\\security\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(main)/account/security/page",pathname:"/account/security",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84165:(e,s,t)=>{Promise.resolve().then(t.bind(t,43690))},94735:e=>{"use strict";e.exports=require("events")},97362:(e,s,t)=>{Promise.resolve().then(t.bind(t,9379))}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,7404,517,7605,5814,551,8581,5841,4677],()=>t(77172));module.exports=r})();