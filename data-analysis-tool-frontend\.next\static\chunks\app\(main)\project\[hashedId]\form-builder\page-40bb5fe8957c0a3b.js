(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[803],{88899:(e,r,s)=>{Promise.resolve().then(s.bind(s,89702))},89702:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>m});var t=s(95155),a=s(59250),n=s(12115),o=s(19373),i=s(34947),d=s(10150),l=s(57799),u=s(35695),c=s(88570),p=s(41232),h=s(86817),j=s(77361),x=s(29350);function m(){let[e,r]=(0,n.useState)(!1),{hashedId:s}=(0,u.useParams)(),{user:m}=(0,x.A)(),I=Number((0,c.D)(s)),{data:q,isLoading:y,isError:v,error:f}=(0,o.I)({queryKey:["questions",I],queryFn:()=>(0,i.K4)({projectId:I}),enabled:!!I}),{data:N=[]}=(0,o.I)({queryKey:["questionGroups",I],queryFn:()=>(0,d.pr)({projectId:I}),enabled:!!I}),{data:b,isLoading:E,isError:k}=(0,o.I)({queryKey:["projects",null==m?void 0:m.id,I],queryFn:()=>(0,j.kf)({projectId:I}),enabled:!!I&&!!(null==m?void 0:m.id)}),P=(0,h.F)({projectData:b,user:m});return s&&null!==I?y||!q?(0,t.jsx)(l.A,{}):v?(0,t.jsx)("p",{className:"text-sm text-red-500",children:"Error loading form information. Please refresh the page."}):(0,t.jsx)("div",{className:"p-6",children:e?(0,t.jsx)(a.V,{questions:q,questionGroups:N,contextType:"project",onClose:()=>r(!1),hashedId:s}):(0,t.jsx)(p.o,{setIsPreviewMode:r,questions:q,contextType:"project",contextId:I,permissions:P})}):(0,t.jsxs)("div",{className:"error-message",children:[(0,t.jsx)("h1",{className:"text-red-500",children:"Error: Invalid Project ID (hashedId)."}),(0,t.jsx)("p",{className:"text-neutral-700",children:"Please make sure the URL contains a valid project identifier."})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[3873,3524,635,1445,6967,6903,4601,2177,4277,556,3481,1467,6539,2050,919,7252,7248,8662,2137,8441,1684,7358],()=>r(88899)),_N_E=e.O()}]);