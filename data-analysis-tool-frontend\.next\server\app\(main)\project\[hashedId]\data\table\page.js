(()=>{var e={};e.id=5972,e.ids=[5972],e.modules={1986:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(60687);s(43210);var a=s(16189),n=s(62688);let l=(0,n.A)("table-2",[["path",{d:"M9 3H5a2 2 0 0 0-2 2v4m6-6h10a2 2 0 0 1 2 2v4M9 3v18m0 0h10a2 2 0 0 0 2-2V9M9 21H5a2 2 0 0 1-2-2V9m0 0h18",key:"gugj83"}]]),i=(0,n.A)("chart-no-axes-column",[["line",{x1:"18",x2:"18",y1:"20",y2:"10",key:"1xfpm4"}],["line",{x1:"12",x2:"12",y1:"20",y2:"4",key:"be30l9"}],["line",{x1:"6",x2:"6",y1:"20",y2:"14",key:"1r4le6"}]]);var o=s(31158);function d({children:e}){let{hashedId:t}=(0,a.useParams)(),s=[{label:"Data",href:`/project/${t}/data`,icon:l},{label:"Reports",href:`/project/${t}/data/reports`,icon:i},{label:"Downloads",href:`/project/${t}/data/downloads`,icon:o.A}],n=(0,a.usePathname)(),d=(0,a.useRouter)();return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-neutral-100 p-4",children:[(0,r.jsxs)("div",{className:"flex justify-between mb-4",children:[(0,r.jsx)("h2",{className:"heading-text",children:"Survey Results"}),(0,r.jsxs)("div",{className:"",children:[(0,r.jsx)("h2",{className:"flex flex-col text-sm font-medium text-neutral-700 mb-1",children:"Navigate"}),(0,r.jsxs)("select",{value:s.some(e=>e.href===n)?n:"",onChange:e=>{let t=e.target.value;t!==n&&d.push(t)},className:" p-2 border border-neutral-300 rounded-md shadow-sm cursor-pointer",children:[(0,r.jsx)("option",{value:"",children:n===`/project/${t}/data`?"DataTable Overview":"Select"}),s.map(({label:e,href:t})=>(0,r.jsx)("option",{value:t,children:e},e))]})]})]}),(0,r.jsx)("main",{className:"p-4 bg-neutral-100 rounded-md border border-neutral-300 shadow-sm",children:(0,r.jsx)("div",{children:e})})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20174:(e,t,s)=>{"use strict";s.d(t,{F:()=>i});var r=s(60687),a=s(16189),n=s(85814),l=s.n(n);s(43210);let i=({items:e})=>{let t=(0,a.usePathname)(),s=e=>t.startsWith(e);return(0,r.jsx)("div",{className:"border-y border-neutral-400 rounded-md bg-primary-500 my-4 shadow-md",children:(0,r.jsx)("div",{className:"flex items-center",children:e.map(e=>e.disabled?(0,r.jsxs)("div",{className:"flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-gray-400 cursor-not-allowed",children:[e.icon,e.label]},e.route):(0,r.jsxs)(l(),{href:e.route,className:`flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-neutral-100 ${s(e.route)?"border-neutral-100":"border-transparent hover:border-neutral-400"}`,children:[e.icon,e.label]},e.route))})})}},21820:e=>{"use strict";e.exports=require("os")},22028:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var r=s(60687),a=s(86429),n=s(86757),l=s(17090),i=s(61611),o=s(84027),d=s(16189),c=s(20174);let u=({permissions:e})=>{let{hashedId:t}=(0,d.useParams)(),s=e.manageProject,a=s||e.viewForm||e.editForm,u=s||e.viewSubmissions||e.editSubmissions||e.addSubmissions||e.deleteSubmissions,m=[{label:"Overview",icon:(0,r.jsx)(n.A,{size:16}),route:`/project/${t}/overview`,disabled:!1},{label:"Form Builder",icon:(0,r.jsx)(l.A,{size:16}),route:`/project/${t}/form-builder`,disabled:!a},{label:"Data",icon:(0,r.jsx)(i.A,{size:16}),route:`/project/${t}/data`,disabled:!u},{label:"Settings",icon:(0,r.jsx)(o.A,{size:16}),route:`/project/${t}/settings`,disabled:!s}];return(0,r.jsx)(c.F,{items:m})};var m=s(21650),p=s(78407),h=s(71845),x=s(6986),b=s(29494),f=s(28559),g=s(85814),y=s.n(g),j=s(43210);let w=({children:e})=>{let{hashedId:t}=(0,d.useParams)(),{user:s}=(0,m.A)(),n=(0,j.useMemo)(()=>(0,x.D)(t),[t]),{data:l,isLoading:i,isError:o}=(0,b.I)({queryKey:["projects",s?.id,n],queryFn:()=>(0,h.kf)({projectId:n}),enabled:!!n&&!!s?.id}),c=(0,p.F)({projectData:l,user:s});return t&&null!==n?i?(0,r.jsx)(a.A,{}):o?(0,r.jsx)("p",{className:"text-red-500",children:"Failed to fetch project. Please try again"}):(0,r.jsxs)("div",{className:"section flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h1",{className:"heading-text capitalize",children:l?.name}),(0,r.jsxs)(y(),{href:"/dashboard",className:"flex items-center gap-2",children:[(0,r.jsx)(f.A,{size:16}),"Back to dashboard"]})]}),(0,r.jsx)(u,{permissions:c}),(0,r.jsx)("div",{className:"px-8",children:e})]}):(0,r.jsxs)("div",{className:"error-message",children:[(0,r.jsx)("h1",{className:"text-red-500",children:"Error: Invalid Project ID (hashedId)."}),(0,r.jsx)("p",{className:"text-neutral-700",children:"Please make sure the URL contains a valid project identifier."})]})}},23163:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\(main)\\\\project\\\\[hashedId]\\\\data\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\project\\[hashedId]\\data\\layout.tsx","default")},26422:(e,t,s)=>{Promise.resolve().then(s.bind(s,1986))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},33873:e=>{"use strict";e.exports=require("path")},35987:(e,t,s)=>{Promise.resolve().then(s.bind(s,58879))},40164:(e,t,s)=>{Promise.resolve().then(s.bind(s,70154))},50154:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>V});var r=s(60687),a=s(93437);let n=(0,s(62688).A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]);var l=s(13861),i=s(96241),o=s(43210),d=s.n(o),c=s(38587),u=s(96752);let m=({isOpen:e,onClose:t,title:s,tableData:a,uniqueColumns:n,uniqueRows:l,rowsMap:i,useParentChildColumns:o=!1,loading:m=!1,tableStructure:p})=>{let h=d().useMemo(()=>{if(!a||0===a.length)return[];let e=[];return a.forEach(t=>{let s=parseInt(t.column),r=parseInt(t.row);isNaN(s)||isNaN(r)||e.push({columnId:s,rowsId:r,value:t.value})}),e},[a]),x=d().useMemo(()=>{if(!p?.tableColumns||0===p.tableColumns.length)return{parentColumns:[],columnMap:new Map,hasChildColumns:!1};let e=p.tableColumns.filter(e=>void 0===e.parentColumnId||null===e.parentColumnId),t=new Map;e.forEach(e=>{let s=p.tableColumns.filter(t=>t.parentColumnId===e.id);t.set(e.id,s)});let s=e.some(e=>(t.get(e.id)||[]).length>0);return{parentColumns:e,columnMap:t,hasChildColumns:s}},[p]),b=d().useMemo(()=>{let e=new Map;return h.forEach(t=>{e.set(`${t.columnId}_${t.rowsId}`,t.value)}),e},[h]);return(0,r.jsx)(c.A,{isOpen:e,onClose:t,className:"p-6 rounded-md max-w-4xl w-[95%] mobile:w-[85%] tablet:w-[75%] laptop:w-[60%]",children:(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-neutral-700",children:s}),m?(0,r.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"}),(0,r.jsx)("span",{className:"ml-2 text-neutral-600",children:"Loading table data..."})]}):p?.tableColumns&&0!==p.tableColumns.length?(0,r.jsx)("div",{className:"overflow-auto max-h-[70vh]",children:(0,r.jsxs)(u.XI,{className:"border-collapse border border-amber-700",children:[(0,r.jsxs)(u.A0,{className:"bg-amber-100",children:[(0,r.jsxs)(u.Hj,{children:[(0,r.jsx)(u.nd,{className:"px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider border border-amber-700 bg-amber-100",rowSpan:x.hasChildColumns?2:1,children:s}),x.parentColumns.map(e=>{let t=(x.columnMap.get(e.id)||[]).length||1;return(0,r.jsx)(u.nd,{colSpan:t,className:"px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider text-center border border-amber-700 bg-amber-100",children:e.columnName},e.id)})]}),x.hasChildColumns&&(0,r.jsx)(u.Hj,{children:x.parentColumns.map(e=>{let t=x.columnMap.get(e.id)||[];return 0===t.length?null:t.map(e=>(0,r.jsx)(u.nd,{className:"px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider border border-amber-700 bg-amber-50",children:e.columnName},e.id))})})]}),(0,r.jsx)(u.BF,{children:p.tableRows?.map((e,t)=>(0,r.jsxs)(u.Hj,{className:"bg-white",children:[(0,r.jsx)(u.nA,{className:"px-3 py-2 text-xs font-medium border border-amber-700 bg-amber-50",children:e.rowsName}),x.parentColumns.map(t=>{let s=x.columnMap.get(t.id)||[];return 0===s.length?(0,r.jsx)(u.nA,{className:"px-3 py-2 text-xs border border-amber-700",children:b.get(`${t.id}_${e.id}`)||""},`cell-${t.id}-${e.id}`):s.map(t=>(0,r.jsx)(u.nA,{className:"px-3 py-2 text-xs border border-amber-700",children:b.get(`${t.id}_${e.id}`)||""},`cell-${t.id}-${e.id}`))})]},e.id))})]})}):(0,r.jsxs)("div",{className:"py-4 text-center text-amber-600",children:[(0,r.jsx)("p",{children:"No table structure available."}),(0,r.jsx)("p",{className:"text-sm mt-2",children:"Debug info:"}),(0,r.jsx)("pre",{className:"text-xs mt-2 bg-gray-100 p-2 rounded overflow-auto max-h-40",children:JSON.stringify({hasTableStructure:!!p,tableColumnsLength:p?.tableColumns?.length||0,tableRowsLength:p?.tableRows?.length||0,tableDataLength:a?.length||0,useParentChildColumns:o},null,2)})]}),(0,r.jsx)("div",{className:"flex justify-end mt-4",children:(0,r.jsx)("button",{onClick:t,className:"px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 transition-colors",children:"Close"})})]})})};var p=s(12810),h=s(76847);let x=(e,t)=>{if(null==e)return"-";if("boolean"==typeof e)return e?"Yes":"No";if(e instanceof Date)return(0,i.Y)(e);if("date"===t&&"string"==typeof e)try{return(0,i.Y)(new Date(e))}catch{return e}return String(e)},b=async e=>{try{let t=null;try{let{data:s}=await p.A.get(`/table-questions/${e}`);s&&s.success&&s.data&&(t=s.data.question)}catch(e){console.error("Error fetching from /table-questions/ endpoint:",e)}if(t&&t.tableColumns&&!t.tableRows)try{let{data:s}=await p.A.get(`/table-rows/${e}`);s&&s.data&&s.data.tableRows&&(t.tableRows=s.data.tableRows)}catch(e){console.error("Error fetching table rows separately:",e)}if(!t)return{id:e,label:"Table Data",tableColumns:[],tableRows:[{id:1,rowsName:"Row 1"},{id:2,rowsName:"Row 2"},{id:3,rowsName:"Row 3"}]};if(t.tableColumns&&Array.isArray(t.tableColumns)){let e=[];t.tableColumns.forEach(t=>{e.push({id:t.id,columnName:t.columnName,parentColumnId:t.parentColumnId||null}),t.childColumns&&Array.isArray(t.childColumns)&&t.childColumns.forEach(s=>{e.push({id:s.id,columnName:s.columnName,parentColumnId:s.parentColumnId||t.id})})}),t.tableColumns=e}else console.error("tableColumns is missing or not an array, creating default tableColumns"),t.tableColumns=[];return t.tableRows&&Array.isArray(t.tableRows)||(console.error("tableRows is missing or not an array, creating default tableRows"),t.tableColumns&&t.tableColumns.length>0?t.tableRows=t.tableColumns.map(e=>({id:e.id,rowsName:`Row ${e.id}`})):t.tableRows=[{id:1,rowsName:"Row 1"},{id:2,rowsName:"Row 2"},{id:3,rowsName:"Row 3"}]),t}catch(t){return console.error("Error fetching table structure:",t),{id:e,label:"Table Data",tableColumns:[],tableRows:[{id:1,rowsName:"Row 1"},{id:2,rowsName:"Row 2"},{id:3,rowsName:"Row 3"}]}}},f=(e,t,s,i)=>{let d=[{id:"select",header:({table:e})=>(0,r.jsx)(a.S,{className:"w-5 h-5 data-[state=checked]:bg-white data-[state=checked]:text-primary-500 border-neutral-100 cursor-pointer",checked:e.getIsAllPageRowsSelected()||e.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:t=>e.toggleAllPageRowsSelected(!!t),"aria-label":"Select all"}),cell:({row:e})=>(0,r.jsx)(a.S,{className:"w-5 h-5 bg-neutral-100 border-neutral-400 data-[state=checked]:bg-white data-[state=checked]:text-primary-500 cursor-pointer",checked:e.getIsSelected(),onCheckedChange:t=>e.toggleSelected(!!t),"aria-label":"Select row"}),enableHiding:!1},{id:"id",header:({column:e})=>(0,r.jsxs)("div",{className:"flex items-center hover:text-neutral-300",children:[(0,r.jsx)("span",{children:"ID"}),(0,r.jsx)("span",{className:"ml-2 flex-shrink-0 w-4 h-4",children:(0,r.jsx)(n,{className:"w-full h-full",onClick:()=>e.toggleSorting("asc"===e.getIsSorted())})})]}),accessorFn:(e,t)=>t+1,enableSorting:!0,cell:({row:t})=>(0,r.jsxs)("div",{className:"flex items-center gap-8 font-medium text-neutral-700",children:[t.index+1,(0,r.jsxs)("span",{className:"flex items-center gap-2",children:[(0,r.jsx)(l.A,{onClick:()=>e(t.original),className:"w-4 h-4 cursor-pointer hover:text-primary-500"}),(0,r.jsx)(h.JBV,{className:"w-4 h-4 cursor-pointer hover:text-primary-500",title:"Edit",onClick:()=>{let e=t.original.id;e&&s&&window.open(`/edit-submission/${s}/${e}`,"_blank")}})]})]})},{id:"validation",header:"Validation",accessorKey:"validation"}],c=[];if(i&&i.length>0)c=i;else{if(!(t&&t.answers?.length))return d;c=Array.from(new Map(t.answers.map(e=>[e.question.id,e.question])).values())}return[...d,...c.map(e=>({id:`${e.label}`,header:({column:t})=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:e.label}),(0,r.jsx)(n,{className:"ml-1 h-4 w-4 cursor-pointer opacity-60",onClick:()=>t.toggleSorting("asc"===t.getIsSorted())})]}),accessorFn:t=>{let s=t.answers.filter(t=>t.question?.id===e.id);if(0===s.length)return console.log(`No answer found for question "${e.label}" (ID: ${e.id}) in submission ${t.id} - likely conditional question not triggered`),null;if("selectmany"===e.inputType){let e=s.map(e=>e.value).filter(e=>e&&""!==String(e).trim()).sort();return e.length>0?e.join(", "):null}return s[0]?.value??null},cell:({getValue:t})=>{let s=t();if("table"===e.inputType)try{let t,a="string"==typeof s?s:String(s);if(a.startsWith("[")&&a.includes("{"))try{t=JSON.parse(a)}catch(e){console.error("Failed to parse JSON string:",e),a=a.replace(/\\"/g,'"').replace(/^"/,"").replace(/"$/,"");try{t=JSON.parse(a)}catch(s){console.error("Failed second parse attempt:",s);let e=a.match(/\[([\s\S]*)\]/);if(e&&e[0])try{t=JSON.parse(e[0])}catch(s){console.error("Failed third parse attempt:",s);try{let s=e[0].replace(/'/g,'"');t=JSON.parse(s)}catch(e){console.error("Failed fourth parse attempt:",e)}}}}if(!t&&a.includes("columnId")&&a.includes("rowsId"))try{let e=a.replace(/'/g,'"');t=JSON.parse(e)}catch(e){console.error("Failed custom format parsing:",e)}if(Array.isArray(t)){let s=t.map(e=>{let t="";e.columnName?t=e.columnName:e.column&&e.column.columnName?t=e.column.columnName:e.columnId&&(t=e.name?e.name:e.label?e.label:String(e.columnId));let s="";e.rowsName?s=e.rowsName:e.row&&e.row.rowsName?s=e.row.rowsName:e.rowsId&&(s=e.name?e.name:e.label?e.label:String(e.rowsId));let r=void 0!==e.value?e.value:"";return{column:t,row:s,value:r}}),a=new Map,n=new Set;s.forEach(e=>{n.add(String(e.column)),a.has(String(e.row))||a.set(String(e.row),new Map),a.get(String(e.row))?.set(String(e.column),String(e.value))});let i=Array.from(n),d=Array.from(a.keys());return(0,r.jsx)(()=>{let[t,n]=(0,o.useState)(!1),[c,u]=(0,o.useState)(null),[p,h]=(0,o.useState)(!1);(0,o.useEffect)(()=>{},[c]);let x=async()=>{if(!e.id)return void console.error("No question ID available");h(!0);try{let t=await b(e.id);if(t&&(t.tableRows||console.error("tableRows is missing from the structure!"),u(t),t.tableColumns&&t.tableRows)){let e=new Map,r=new Map;t.tableColumns.forEach(t=>{e.set(t.id,t.columnName),e.set(String(t.id),t.columnName)}),t.tableRows.forEach(e=>{r.set(e.id,e.rowsName),r.set(String(e.id),e.rowsName)}),s.forEach(s=>{if(s.column&&!isNaN(Number(s.column))){let r=s.column;if(e.has(r))s.column,s.column=e.get(r);else{let e=t.tableColumns.find(e=>String(e.id)===String(r));e&&(s.column,s.column=e.columnName)}}if(s.row&&!isNaN(Number(s.row))){let e=s.row;if(r.has(e))s.row,s.row=r.get(e);else{let r=t.tableRows.find(t=>String(t.id)===String(e));r&&(s.row,s.row=r.rowsName)}}});let n=new Map,l=new Set;s.forEach(e=>{l.add(String(e.column)),n.has(String(e.row))||n.set(String(e.row),new Map),n.get(String(e.row))?.set(String(e.column),String(e.value))}),i.length=0,d.length=0,l.forEach(e=>i.push(e)),n.forEach((e,t)=>d.push(t)),a.clear(),n.forEach((e,t)=>{a.set(t,e)})}}catch(e){console.error("Error fetching table structure:",e)}finally{h(!1)}};return(0,r.jsxs)("div",{className:"font-medium text-neutral-700",children:[(0,r.jsxs)("a",{href:"#",onClick:async e=>{e.preventDefault(),h(!0),n(!0),await x()},className:"inline-flex items-center gap-1 text-primary-500 hover:text-primary-700 hover:underline whitespace-nowrap",children:[(0,r.jsx)(l.A,{size:12,className:"inline"})," Click to view table"]}),null,(0,r.jsx)(m,{isOpen:t,onClose:()=>n(!1),title:e.label||"Table Data",tableData:s,uniqueColumns:i,uniqueRows:d,rowsMap:a,useParentChildColumns:!0,loading:p,tableStructure:c})]})},{})}}catch(e){console.error("Error parsing table data:",e,"Value:",s)}return null==s||""===s?(0,r.jsx)("div",{className:"font-medium text-neutral-400 italic",children:"-"}):(0,r.jsx)("div",{className:"font-medium text-neutral-700",children:String(s)})},enableSorting:!0})),{id:"submissionTime",header:({column:e})=>(0,r.jsxs)("div",{className:"flex items-center gap-4 hover:text-neutral-300",children:[(0,r.jsx)("span",{children:"Submission Time"}),(0,r.jsx)("span",{className:"ml-2 flex-shrink-0 w-4 h-4",children:(0,r.jsx)(n,{className:"w-full h-full",onClick:()=>e.toggleSorting("asc"===e.getIsSorted())})})]}),accessorKey:"submissionTime",cell:({getValue:e})=>{let t=e();return(0,r.jsx)("div",{className:"font-medium text-neutral-700",children:x(t,"date")||"Not recorded"})},enableSorting:!0},{id:"submittedBy",header:({column:e})=>(0,r.jsxs)("div",{className:"flex items-center gap-4 hover:text-neutral-300",children:[(0,r.jsx)("span",{children:"Submitted By"}),(0,r.jsx)("span",{className:"ml-2 flex-shrink-0 w-4 h-4",children:(0,r.jsx)(n,{className:"w-full h-full",onClick:()=>e.toggleSorting("asc"===e.getIsSorted())})})]}),accessorKey:"submittedBy",accessorFn:e=>e.user?.name||"Anonymous",cell:({getValue:e})=>{let t=e();return(0,r.jsx)("div",{className:"font-medium text-neutral-700",children:t})},enableSorting:!0}]};var g=s(68988),y=s(44255),j=s(69587),w=s(26273),v=s(56090),N=s(93772),S=s(54864),C=s(19150),I=s(80967),k=s(8693),q=s(54050);let A=e=>{if(!e||!e.answers)return[];let t=new Map;return e.answers.forEach(e=>{let s=e.question?.id,r=e.question?.label||"Unknown";if(s)if(t.has(s)){let r=t.get(s);r.answers.push(e.value),r.originalData.push(e)}else t.set(s,{type:e.question?.inputType||"text",question:r,questionObject:e.question,answers:[e.value],originalData:[e]})}),Array.from(t.values())},E=({showModal:e,onClose:t,onConfirm:s,submission:a,isMultipleSelection:n=!1,selectedSubmissions:l=[],projectId:i})=>{let[m,p]=(0,o.useState)(!1),[h,x]=(0,o.useState)(null),[b,f]=(0,o.useState)(""),[y,j]=(0,o.useState)(null),w=(0,S.wA)(),E=(0,k.jE)(),O=(0,o.useRef)(null),$=(0,o.useRef)(null),[D,M]=(0,o.useState)(""),[R,P]=(0,o.useState)(""),F=d().useMemo(()=>{if(n&&y){let e=l.find(e=>e.id===y);return e?A(e):[]}return A(a)},[a,n,y,l]);(0,o.useEffect)(()=>{if(n&&l.length>0&&!y){let e=l[0]?.id;void 0!==e&&j(e)}},[n,l,y]);let T=d().useMemo(()=>F.filter(e=>{let t=e.question.toLowerCase().includes(D.toLowerCase())||!D,s=String(e.answers).toLowerCase().includes(R.toLowerCase())||!R;return t&&s}),[F,D,R]),K=(0,v.N4)({data:T,columns:[{accessorKey:"type",header:"Type"},{accessorKey:"question",header:()=>(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{children:"Question"}),(0,r.jsx)(g.p,{ref:O,placeholder:"Search questions...",value:D,onChange:e=>M(e.target.value),className:"bg-neutral-100 text-neutral-700 mt-2 h-8"})]})},{accessorKey:"answers",header:()=>(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{children:"Answer"}),(0,r.jsx)(g.p,{ref:$,placeholder:"Search answers...",value:R,onChange:e=>P(e.target.value),className:"bg-neutral-100 text-neutral-700 mt-2 h-8"})]}),cell:({row:e})=>{let t=e.original.answers;return n?(0,r.jsx)("div",{className:"flex flex-col",children:(0,r.jsx)("div",{className:"text-neutral-800 italic",children:"Multiple responses"})}):(0,r.jsx)("div",{className:"flex flex-col",children:t.map((e,t)=>(0,r.jsx)("div",{className:"",children:String(e)},t))})}},{accessorKey:"action",header:"Action",cell:({row:e})=>(0,r.jsx)("button",{className:"btn-primary",onClick:()=>U(e.original),children:"Edit"})}],getCoreRowModel:(0,N.HT)()}),U=e=>{p(!0),x(e),f(e.answers.join("\n"))},B=(0,q.n)({mutationFn:e=>{if(!e.questionId)throw Error("Question ID is required");if(!e.submissionId)throw Error("Submission ID is required");let t={submissionId:e.submissionId,questionId:e.questionId,answerType:e.answerType,value:e.value};return"selectmany"===e.answerType?t.questionOptionId=Array.isArray(e.questionOptionId)?e.questionOptionId:e.questionOptionId?[e.questionOptionId]:[]:void 0!==e.questionOptionId&&(t.questionOptionId=Array.isArray(e.questionOptionId)?e.questionOptionId[0]:e.questionOptionId),"number"===e.answerType||"decimal"===e.answerType?t.value="string"==typeof e.value?parseFloat(e.value):"number"==typeof e.value?e.value:0:"selectmany"===e.answerType?t.value=Array.isArray(e.value)?e.value.map(e=>String(e)):[String(e.value)]:t.value=String(e.value),(0,I.s4)(t,i)},onSuccess:e=>{w((0,C.Ds)({message:"Answer updated successfully",type:"success"})),E.invalidateQueries({queryKey:["formSubmissions"]}),p(!1),x(null),f(""),s()},onError:e=>{console.error("Error updating answer:",e),console.error("Error details:",{response:e?.response?.data,status:e?.response?.status,headers:e?.response?.headers});let t=e?.response?.data?.message||e?.response?.data?.errors||"Failed to update answer";w((0,C.Ds)({message:"string"==typeof t?t:"Validation error in the form submission",type:"error"}))}}),V=async()=>{if(!h)return;let e=n&&y&&l.find(e=>e.id===y)||a;if(!e?.id)return void w((0,C.Ds)({message:"Submission ID is missing",type:"error"}));let t=b.split("\n").map(e=>e.trim()).filter(e=>e);if(0===t.length)return void w((0,C.Ds)({message:"Please enter a valid response",type:"error"}));let s=h.questionObject?.id;if(!s)return void w((0,C.Ds)({message:"Question ID is missing",type:"error"}));let r=h.type||"text";try{let a;if("selectmany"===r){let e=h.originalData?.map(e=>e.questionOptionId).filter(Boolean);if(e&&0!==e.length)if(e.length!==t.length)for(a=[...e];a.length<t.length;)a.push(a[0]||null);else a=e;else a=t.map(()=>null)}else a=h.originalData?.[0]?.questionOptionId||null;let n={submissionId:e.id,questionId:s,answerType:r,value:"selectmany"===r?t:t[0],questionOptionId:a};B.mutate(n)}catch(e){console.error("Form validation error:",e),w((0,C.Ds)({message:"Please check your input and try again",type:"error"}))}};return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(c.A,{isOpen:e,onClose:t,className:"flex flex-col gap-5 p-6 rounded-md",children:m?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold text-neutral-700",children:["Editing Question: ",h?.question]}),(0,r.jsx)("p",{className:"text-sm text-neutral-700",children:"You are about to edit responses for one or multiple submissions at once. Use the XML syntax in the text box below. You can also select one of the existing responses from the table of responses. Learn more about how to edit specific responses for one or multiple submissions"}),(0,r.jsx)("textarea",{value:b,onChange:e=>f(e.target.value),className:"mt-4 border border-neutral-400 rounded-md p-2 w-full h-24 focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"Enter new response..."}),(0,r.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,r.jsx)("button",{className:"btn-outline",onClick:()=>{p(!1),x(null),f("")},disabled:B.isPending,children:"Cancel"}),(0,r.jsx)("button",{className:"btn-primary",onClick:V,disabled:B.isPending,children:B.isPending?"Saving...":"Confirm & Save"})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex flex-col gap-4 max-h-[500px] overflow-y-auto",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:n?"Edit Selected Submission":"Edit Submission"}),(0,r.jsx)("div",{children:n&&l.length>0&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("label",{htmlFor:"submission-selector",className:"text-sm font-medium text-neutral-700",children:"Select submission to edit:"}),(0,r.jsx)("select",{id:"submission-selector",className:"border border-neutral-300 rounded-md p-1 text-sm bg-white",value:y||"",onChange:e=>{j(Number(e.target.value))},children:l.map(e=>(0,r.jsxs)("option",{value:e.id,children:["ID: ",e.id]},e.id))})]})})]}),n&&y&&(0,r.jsxs)("div",{className:"bg-primary-500 border text-neutral-100 rounded-md p-3 text-sm",children:[(0,r.jsxs)("p",{className:"font-medium",children:["Editing Submission ID: ",y]}),(0,r.jsx)("p",{className:"text-xs mt-1",children:"You are editing one submission from your multiple selections. Changes will only apply to this specific submission."})]}),(0,r.jsx)("p",{className:"text-sm text-neutral-700",children:n?"You have multiple submissions selected. Choose which specific submission to edit from the dropdown above.":"You are editing a single submission. Make your changes and click Confirm & Save when done."}),(0,r.jsx)("div",{className:"rounded-md border border-neutral-400 max-h-[450px] overflow-auto",children:(0,r.jsxs)(u.XI,{children:[(0,r.jsx)(u.A0,{className:"h-20",children:K.getHeaderGroups().map(e=>(0,r.jsx)(u.Hj,{className:"text-sm border-neutral-400",children:e.headers.map(e=>(0,r.jsx)(u.nd,{className:"py-1 px-6 text-left bg-primary-500 text-neutral-100 font-semibold",children:e.isPlaceholder?null:(0,v.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,r.jsx)(u.BF,{children:K.getRowModel().rows?.length?K.getRowModel().rows.map(e=>(0,r.jsx)(u.Hj,{className:" text-sm border-neutral-400","data-state":e.getIsSelected()&&"selected",children:e.getVisibleCells().map(e=>(0,r.jsx)(u.nA,{className:"py-4 px-6",children:(0,v.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,r.jsx)(u.Hj,{children:(0,r.jsx)(u.nA,{colSpan:K.getAllColumns().length,className:"h-24 text-center",children:"No results."})})})]})}),(0,r.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,r.jsx)("button",{className:"btn-outline",onClick:t,children:"Cancel"}),(0,r.jsx)("button",{className:"btn-primary",onClick:V,disabled:B.isPending,children:B.isPending?"Saving...":"Confirm & Save"})]})]})})})};var O=s(93617);let $=({isOpen:e,onClose:t,submission:s})=>{let[a,n]=(0,o.useState)(!1),l=(0,o.useRef)(null),i=new Map;return s.answers.forEach(e=>{let t=e.question.label;i.has(t)||i.set(t,[]),i.get(t).push(e.value)}),(0,r.jsx)(c.A,{isOpen:e,onClose:t,className:"p-6 rounded-md max-w-4xl w-full ",children:(0,r.jsxs)("div",{ref:l,className:`flex flex-col gap-4 transition-all duration-300 ${a?"fixed inset-0 bg-neutral-100 w-screen h-screen z-50 p-6 overflow-auto":""}`,children:[(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:"Submission Details"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-sm text-neutral-600",children:"Validation status:"}),(0,r.jsxs)("select",{className:"px-3 py-1 border border-neutral-500 rounded-md text-sm",children:[(0,r.jsx)("option",{value:"",children:"Select..."}),(0,r.jsx)("option",{value:"valid",children:"Valid"}),(0,r.jsx)("option",{value:"invalid",children:"Invalid"}),(0,r.jsx)("option",{value:"pending",children:"Pending"})]}),(0,r.jsxs)("button",{onClick:()=>{n(e=>!e)},className:"btn-primary",children:[(0,r.jsx)(w.D4o,{className:"w-5 h-5"}),a?"Exit Fullscreen":"Fullscreen"]})]})]}),(0,r.jsx)("div",{className:"overflow-x-auto rounded-md border border-neutral-200 bg-neutral-100",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-neutral-200",children:[(0,r.jsx)("thead",{className:"bg-primary-500 text-neutral-100",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium  uppercase tracking-wider",children:"Question"}),(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium uppercase tracking-wider",children:"Response"})]})}),(0,r.jsx)("tbody",{className:"bg-neutral-100 divide-y divide-neutral-200",children:[...i.entries()].map(([e,t])=>{let a=s.answers.find(t=>t.question.label===e),n=a?.question.inputType==="table";return(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-4 py-2 align-top",children:e}),(0,r.jsx)("td",{className:"px-4 py-2",children:n?(0,r.jsx)("div",{className:"text-neutral-600 italic",children:"Table data (view in data table section)"}):t.join(", ")})]},e)})})]})}),(0,r.jsxs)("div",{className:"flex justify-between items-center mt-4",children:[(0,r.jsxs)("div",{className:"text-sm text-neutral-600 font-semibold",children:[(0,r.jsxs)("p",{children:["Submitted by: ",s.user?.name]}),(0,r.jsxs)("p",{children:["Submission time: ",s.submissionTime]})]}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{className:"btn-primary",onClick:t,children:"Close"})})]})]})})};var D=s(75531),M=s(55629),R=s(24934),P=s(73678),F=s(16189),T=s(6986),K=s(29494);let U=async e=>{let{data:t}=await p.A.get(`/form-submissions/${e}`);return t.data.formSubmissions},B="data-table-column-visibility",V=()=>{let{hashedId:e}=(0,F.useParams)(),t=Number((0,T.D)(e)),s=(0,S.wA)(),a=(0,k.jE)(),{data:n=[],isLoading:l,refetch:i}=(0,K.I)({queryKey:["formSubmissions",t],queryFn:()=>U(t),enabled:null!==t,refetchInterval:1e3,staleTime:0,gcTime:0}),{data:c=[],isLoading:u}=(0,K.I)({queryKey:["allQuestions",t],queryFn:()=>(0,D.K4)({projectId:t}),enabled:null!==t,staleTime:3e5}),[m,p]=(0,o.useState)(!1),[x,b]=(0,o.useState)(!1),[v,N]=(0,o.useState)(""),[A,V]=(0,o.useState)(!1),[z,_]=d().useState(null),[L,H]=(0,o.useState)(!1),[J,Q]=d().useState({}),[G,Y]=d().useState(null),[X,W]=d().useState({}),[Z,ee]=(0,o.useState)(!1),[et,es]=(0,o.useState)(null),er=n.length>0&&c.length>0?f(e=>{es(e),ee(!0)},n[0],e,c):[],[ea,en]=d().useState(!1),[el,ei]=(0,o.useState)(!1),eo=(0,o.useRef)(null),ed=(0,o.useRef)(null),ec=(0,q.n)({mutationFn:e=>(0,I.O8)(e,t),onSuccess:()=>{s((0,C.Ds)({message:"Submission deleted successfully",type:"success"})),a.invalidateQueries({queryKey:["formSubmissions",t]}),W({}),V(!1),es(null),b(!1)},onError:e=>{console.error("Error deleting submission:",e),s((0,C.Ds)({message:"Failed to delete submission",type:"error"})),b(!1)}}),eu=(0,q.n)({mutationFn:e=>(0,I.J6)(e,t),onSuccess:(e,r)=>{s((0,C.Ds)({message:`${r.length} submissions deleted successfully`,type:"success"})),a.invalidateQueries({queryKey:["formSubmissions",t]}),W({}),V(!1),es(null),b(!1)},onError:e=>{console.error("Error deleting multiple submissions:",e),s((0,C.Ds)({message:"Failed to delete submissions",type:"error"})),b(!1)}});return(0,o.useEffect)(()=>{let e=e=>{eo.current&&!eo.current.contains(e.target)&&ei(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,o.useEffect)(()=>{try{let e=localStorage.getItem(B);if(e){let t=JSON.parse(e);t&&"object"==typeof t&&!Array.isArray(t)?Q(t):console.warn("Invalid format in localstorage for column visibility")}}catch(e){console.error("Error loading column visibility:",e)}},[]),(0,o.useEffect)(()=>{if(Object.keys(J).length>0)try{localStorage.setItem(B,JSON.stringify(J))}catch(e){console.error("Error saving column visibility:",e)}},[J]),(0,o.useEffect)(()=>{G&&0===Object.keys(X).length&&G.resetRowSelection()},[X,G]),(0,r.jsxs)("div",{ref:ed,className:`flex flex-col gap-4 transition-all duration-300 ${m?"fixed inset-0 bg-neutral-100 w-screen h-screen z-50 p-6 overflow-auto":""}`,children:[(0,r.jsxs)("div",{className:"flex flex-col desktop:flex-row justify-between gap-8 items-center py-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(g.p,{placeholder:"Search all columns...",value:v,onChange:e=>N(e.target.value)}),G&&(0,r.jsxs)(M.rI,{open:ea,onOpenChange:e=>en(e),children:[(0,r.jsx)(M.ty,{asChild:!0,children:(0,r.jsxs)(R.$,{variant:"outline",className:"flex items-center gap-2 cursor-pointer",children:["Show/Hide Columns",ea?(0,r.jsx)(j.Ucs,{className:"w-3 h-3"}):(0,r.jsx)(j.Vr3,{className:"w-3 h-3"})]})}),(0,r.jsx)(M.SQ,{align:"start",className:"bg-neutral-100 border border-neutral-200 shadow-md",children:G.getAllColumns().filter(e=>e.getCanHide()).map(e=>(0,r.jsx)(M.hO,{className:"capitalize cursor-pointer hover:bg-neutral-200",checked:J[e.id]??!0,onCheckedChange:t=>Q(s=>({...s,[e.id]:t})),children:e.id},e.id))})]})]}),(0,r.jsxs)("div",{ref:eo,className:"flex relative items-center gap-4 text-neutral-800",children:[(0,r.jsxs)("button",{onClick:()=>{p(e=>!e)},className:"btn-primary",children:[(0,r.jsx)(w.D4o,{className:"w-5 h-5"}),m?"Exit Fullscreen":"Fullscreen"]}),(0,r.jsxs)("button",{className:` bg-primary-500 font-medium rounded-lg shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2  active:scale-95 transition-all duration-300 ${A?"hover:bg-primary-600  cursor-pointer":"opacity-50"}`,onClick:A?()=>{ei(e=>!e)}:void 0,children:["Status",el?(0,r.jsx)(j.Ucs,{className:"w-3 h-3"}):(0,r.jsx)(j.Vr3,{className:"w-3 h-3"})]}),el&&(0,r.jsx)("div",{className:"absolute left-30 top-10 mt-2 w-64 bg-neutral-100 border border-gray-200 shadow-md rounded-md p-2 z-40",children:(0,r.jsxs)("div",{className:"flex flex-col  gap-2",children:[(0,r.jsx)("div",{className:"hover:bg-neutral-200 cursor-pointer p-2 text-neutral-800 rounded-sm",children:"Set on: Approved"}),(0,r.jsx)("div",{className:"hover:bg-neutral-200 cursor-pointer p-2 text-neutral-800 rounded-sm",children:"Set on: Not Approved"}),(0,r.jsx)("div",{className:"hover:bg-neutral-200 cursor-pointer p-2 text-neutral-800 rounded-sm",children:"Set on: On Hold"})]})}),(0,r.jsxs)("button",{className:` bg-primary-500 font-medium rounded-lg shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2  active:scale-95 transition-all duration-300 ${A?"hover:bg-primary-600  cursor-pointer":"opacity-50"}`,onClick:A?()=>{let e=Object.keys(X);if(0===e.length)return void s((0,C.Ds)({message:"No submission selected for editing",type:"error"}));let t=e.map(e=>n[Number(e)]).filter(Boolean);if(1===e.length){es(t[0]),H(!0);return}e.length>1&&(es(t[0]),H(!0))}:void 0,children:[(0,r.jsx)(h.JBV,{className:"h-4 w-4"}),"Edit"]}),(0,r.jsxs)("button",{className:` bg-primary-500 font-medium rounded-lg shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2  active:scale-95 transition-all duration-300 ${A?"hover:bg-primary-600  cursor-pointer":"opacity-50"}`,onClick:A?()=>{let e=Object.keys(X).map(e=>{let t=parseInt(e);return n[t]?.id||0}).filter(e=>e>0);_({title:"Confirm Deletion",description:(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("p",{children:["Are you sure you want to delete"," ",e.length>1?`these ${e.length} submissions`:"this submission","? It is not possible to recover deleted submissions."]})}),confirmButtonText:"Delete",confirmButtonClass:"bg-red-500 hover:bg-red-600 cursor-pointer",onConfirm:()=>{1===e.length?ec.mutate(e[0]):e.length>1&&eu.mutate(e)}}),b(!0)}:void 0,children:[(0,r.jsx)(y.hJ0,{className:"h-4 w-4"}),"Delete"]})]})]}),l||u?(0,r.jsx)("div",{className:"flex justify-center items-center py-12",children:(0,r.jsx)("div",{className:"text-muted-foreground",children:"Loading data..."})}):(0,r.jsx)(O.x,{columns:er,data:n,globalFilter:v,setGlobalFilter:N,onTableInit:e=>{Y(e),Object.keys(J).length>0&&e.setColumnVisibility(J)},columnVisibility:J,setColumnVisibility:e=>{Q(e)},onRowSelectionChange:e=>{V(Object.keys(e).length>0),W(e),1===Object.keys(e).length?es(n[Number(Object.keys(e)[0])]):0===Object.keys(e).length&&es(null)},rowSelection:X}),L&&et&&(0,r.jsx)(E,{showModal:L,projectId:t,onClose:()=>{H(!1),es(null),W({}),V(!1)},onConfirm:()=>{a.invalidateQueries({queryKey:["formSubmissions",t]}),W({}),V(!1),H(!1),es(null)},submission:et,isMultipleSelection:Object.keys(X).length>1,selectedSubmissions:Object.keys(X).length>1?Object.keys(X).map(e=>n[Number(e)]).filter(Boolean):[]}),z&&(0,r.jsx)(P.R,{showModal:x,onClose:()=>b(!1),onConfirm:z.onConfirm,title:z.title,description:z.description,confirmButtonText:z.confirmButtonText,confirmButtonClass:z.confirmButtonClass}),et&&(0,r.jsx)($,{isOpen:Z,onClose:()=>ee(!1),submission:et})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58879:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\(main)\\\\project\\\\[hashedId]\\\\data\\\\table\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\project\\[hashedId]\\data\\table\\page.tsx","default")},61611:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63374:(e,t,s)=>{Promise.resolve().then(s.bind(s,23163))},70020:(e,t,s)=>{Promise.resolve().then(s.bind(s,22028))},70154:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\(main)\\\\project\\\\[hashedId]\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\project\\[hashedId]\\layout.tsx","default")},72939:(e,t,s)=>{Promise.resolve().then(s.bind(s,50154))},73678:(e,t,s)=>{"use strict";s.d(t,{R:()=>n});var r=s(60687);s(43210);var a=s(38587);let n=({showModal:e,onClose:t,onConfirm:s,title:n,description:l,confirmButtonText:i,cancelButtonText:o,confirmButtonClass:d,children:c})=>(0,r.jsxs)(a.A,{isOpen:e,onClose:t,className:"p-6 rounded-md max-w-xl",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:n}),(0,r.jsx)("div",{className:"text-neutral-700 mt-2",children:l}),c&&(0,r.jsx)("div",{className:"mt-6 space-y-4",children:c}),(0,r.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,r.jsx)("button",{className:"btn-outline",onClick:t,type:"button",children:o||"Cancel"}),(0,r.jsx)("button",{className:`font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ${d}`,onClick:s,type:"button",children:i})]})]})},74075:e=>{"use strict";e.exports=require("zlib")},75206:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(65239),a=s(48088),n=s(88170),l=s.n(n),i=s(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d={children:["",{children:["(main)",{children:["project",{children:["[hashedId]",{children:["data",{children:["table",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,58879)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\project\\[hashedId]\\data\\table\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,23163)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\project\\[hashedId]\\data\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,70154)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\project\\[hashedId]\\layout.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,19559)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\project\\[hashedId]\\data\\table\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(main)/project/[hashedId]/data/table/page",pathname:"/project/[hashedId]/data/table",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},75531:(e,t,s)=>{"use strict";s.d(t,{Af:()=>i,K4:()=>n,ae:()=>m,dI:()=>u,ej:()=>l,ku:()=>d,sr:()=>c,ul:()=>o});var r=s(12810);let a=e=>{if("project"===e)return"/questions";if("template"===e)return"/template-questions";if("questionBlock"===e)return"/question-blocks";throw Error("Unsupported context type")},n=async({projectId:e})=>{let{data:t}=await r.A.get(`/questions/${e}`);return t.questions},l=async({templateId:e})=>{let{data:t}=await r.A.get(`/template-questions/${e}`);return t.questions},i=async({contextType:e,contextId:t,dataToSend:s,position:n})=>{let l="questionBlock"===e?`${a(e)}`:`${a(e)}/${t}`;if(!s.label||!s.inputType)throw Error("Label and inputType are required");let i=["selectone","selectmany"].includes(s.inputType),o=s.file instanceof File,d=Array.isArray(s.questionOptions)&&s.questionOptions.length>0;if(i&&!o&&!d)throw Error("Options are required for select input types");if(o){let e=new FormData;e.append("label",s.label),e.append("isRequired",s.isRequired?"true":"false"),e.append("inputType",s.inputType),s.hint&&e.append("hint",s.hint),s.placeholder&&e.append("placeholder",s.placeholder),e.append("position",String(n||1)),e.append("file",s.file);try{let{data:t}=await r.A.post(l,e,{headers:{"Content-Type":"multipart/form-data"}});return t}catch(e){throw console.error("Upload error details:",e.response?.data||e.message),Error(`Failed to upload question with file: ${e.response?.data?.message||e.message}`)}}try{let{data:e}=await r.A.post(l,{label:s.label,isRequired:s.isRequired,hint:s.hint,placeholder:s.placeholder,inputType:s.inputType,questionOptions:s.questionOptions,position:n||1});return e}catch(e){throw console.error("API error details:",e.response?.data||e.message),Error(`Failed to add question: ${e.response?.data?.message||e.message}`)}},o=async({contextType:e,id:t,projectId:s})=>{let{data:n}=await r.A.delete(`${a(e)}/${t}?projectId=${s}`);return n},d=async({id:e,contextType:t,contextId:s})=>{let{data:n}=await r.A.post(`${a(t)}/duplicate/${e}?projectId=${s}`,"questionBlock"===t?{}:"project"===t?{projectId:s}:{templateId:s});return n},c=async({id:e,contextType:t,dataToSend:s,contextId:n})=>{let{data:l}=await r.A.patch(`${a(t)}/${e}?projectId=${n}`,s);return l},u=async()=>{try{return(await r.A.get("/question-blocks")).data.questions||[]}catch(e){throw console.error("Error fetching question block questions:",e),e}},m=async({contextType:e,contextId:t,questionPositions:s})=>{if("project"!==e)throw Error("Question position updates are only supported for projects");let n=`${a(e)}/positions?projectId=${t}`;try{let{data:e}=await r.A.patch(n,{questionPositions:s});return e}catch(e){throw console.error("Update failed - Full error:",e),console.error("Update failed - Error details:",{status:e.response?.status,statusText:e.response?.statusText,data:e.response?.data,message:e.message,config:{url:e.config?.url,method:e.config?.method,data:e.config?.data}}),e}}},78407:(e,t,s)=>{"use strict";s.d(t,{F:()=>a});var r=s(43210);let a=({projectData:e,user:t})=>(0,r.useMemo)(()=>{let s=t?.id===e?.user?.id,r=e?.projectUser?.[0],a=r?.permission||{};return{viewForm:s||a.viewForm||!1,editForm:s||a.editForm||!1,viewSubmissions:s||a.viewSubmissions||!1,addSubmissions:s||a.addSubmissions||!1,deleteSubmissions:s||a.deleteSubmissions||!1,validateSubmissions:s||a.validateSubmissions||!1,editSubmissions:s||a.editSubmissions||!1,manageProject:s||a.manageProject||!1}},[t?.id,e])},79551:e=>{"use strict";e.exports=require("url")},80967:(e,t,s)=>{"use strict";s.d(t,{GN:()=>i,J6:()=>n,O8:()=>a,s4:()=>l});var r=s(12810);let a=async(e,t)=>{try{let{data:s}=await r.A.delete(`/form-submissions/${e}?projectId=${t}`);return s}catch(e){throw console.error("Error deleting form submission:",e),e}},n=async(e,t)=>{try{let s=e.map(e=>r.A.delete(`/form-submissions/${e}?projectId=${t}`));return(await Promise.all(s)).map(e=>e.data)}catch(e){throw console.error("Error deleting multiple form submissions:",e),e}},l=async(e,t)=>{try{if(!e.submissionId||!e.questionId)throw Error("submissionId and questionId are required");let s={...e};null===s.questionOptionId?delete s.questionOptionId:Array.isArray(s.questionOptionId)&&(s.questionOptionId=s.questionOptionId.filter(e=>null!=e),0===s.questionOptionId.length&&delete s.questionOptionId);let{data:a}=await r.A.patch(`/answers/${e.questionId}?projectId=${t}`,s);return a}catch(e){throw console.error("Error updating answer:",e),e}},i=async(e,t)=>{try{let{data:s}=await r.A.patch(`/answers/multiple?projectId=${t}`,e);return s}catch(e){throw console.error("Error updating multiple answers with endpoint:",e),e}}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84027:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},86757:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("panels-top-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M9 21V9",key:"1oto5p"}]])},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,7404,517,7605,5814,551,8581,6886,5841,4677],()=>s(75206));module.exports=r})();