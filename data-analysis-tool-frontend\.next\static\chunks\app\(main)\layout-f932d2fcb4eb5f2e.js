(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2076],{2511:(e,t,a)=>{"use strict";a.d(t,{b:()=>r});let r={information_media:"Information / Media",econommic_social_development:"Economic & Social Development",security_police_peacekeeping:"Security / Police / Peacekeeping",disarmament_and_demobilization:"Disarmament & Demobilization",environment:"Environment",private_sector:"Private Sector",humanitarian_coordination_information_management:"Humanitarian - Coordination & Info Management",humanitarian_multiple_clusters:"Humanitarian - Multiple Clusters",humanitarian_camp_management_and_coordination:"Humanitarian - Camp Management & Coordination",humanitarian_early_recovery:"Humanitarian - Early Recovery",humanitarian_education:"Humanitarian - Education",humanitarian_emergency_shelter:"Humanitarian - Emergency Shelter",humanitarian_emergency_telecoms:"Humanitarian - Emergency Telecoms",humanitarian_food_security:"Humanitarian - Food Security",humanitarian_health:"Humanitarian - Health",humanitarian_logistics:"Humanitarian - Logistics",humanitarian_nutrition:"Humanitarian - Nutrition",humanitarian_protection:"Humanitarian - Protection",humanitarian_sanitation_water_and_hygiene:"Humanitarian - Sanitation / Water / Hygiene",other:"Other"}},17576:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},26862:(e,t,a)=>{"use strict";a.d(t,{Sc:()=>h.S,dO:()=>p}),a(97168),a(89852),a(82714),a(99474);var r=a(95155),s=a(12115),l=a(38715),n=a(66474),i=a(47863),o=a(5196),c=a(53999);l.bL,l.YJ,l.WT,s.forwardRef((e,t)=>{let{className:a,children:s,...i}=e;return(0,r.jsxs)(l.l9,{ref:t,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm ring-offset-neutral-100 placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 dark:border-gray-700 dark:bg-gray-900 dark:ring-offset-gray-900 dark:placeholder:text-gray-500",a),...i,children:[s,(0,r.jsx)(l.In,{asChild:!0,children:(0,r.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})}).displayName=l.l9.displayName;let d=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(l.PP,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,r.jsx)(i.A,{className:"h-4 w-4"})})});d.displayName=l.PP.displayName;let u=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(l.wn,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,r.jsx)(n.A,{className:"h-4 w-4"})})});u.displayName=l.wn.displayName,s.forwardRef((e,t)=>{let{className:a,children:s,position:n="popper",...i}=e;return(0,r.jsx)(l.ZL,{children:(0,r.jsxs)(l.UC,{ref:t,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border border-gray-200 bg-neutral-100 text-slate-700 shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:border-gray-700 dark:bg-gray-900 dark:text-slate-200","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:n,...i,children:[(0,r.jsx)(d,{}),(0,r.jsx)(l.LM,{className:(0,c.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,r.jsx)(u,{})]})})}).displayName=l.UC.displayName,s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(l.JU,{ref:t,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...s})}).displayName=l.JU.displayName,s.forwardRef((e,t)=>{let{className:a,children:s,...n}=e;return(0,r.jsxs)(l.q7,{ref:t,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-gray-100 focus:text-gray-900 data-[disabled]:pointer-events-none data-[disabled]:opacity-50 dark:focus:bg-gray-800 dark:focus:text-gray-50",a),...n,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(l.VF,{children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})}),(0,r.jsx)(l.p4,{children:s})]})}).displayName=l.q7.displayName,s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(l.wv,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-gray-200 dark:bg-gray-700",a),...s})}).displayName=l.wv.displayName;var m=a(4884);let p=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(m.bL,{className:(0,c.cn)("peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 focus-visible:ring-offset-neutral-100 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 dark:focus-visible:ring-offset-gray-900",a),...s,ref:t,children:(0,r.jsx)(m.zi,{className:(0,c.cn)("pointer-events-none block h-5 w-5 rounded-full shadow-lg ring-0 transition-transform data-[state=checked]:bg-neutral-100 data-[state=unchecked]:bg-primary-500 data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});p.displayName=m.bL.displayName;var h=a(95139);a(55747)},29350:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var r=a(97381),s=a(59362),l=a(25784),n=a(35695),i=a(12115),o=a(34540);let c=e=>{let t=(0,o.wA)(),a=(0,n.useRouter)(),c=(0,n.usePathname)(),{status:d,user:u,error:m}=(0,o.d4)(e=>e.auth),p=async()=>{try{t((0,r.Le)());let e=(await l.A.get("/users/me")).data;t((0,r.tQ)(e))}catch(l){if(t((0,r.x9)()),(0,s.F0)(l)){var e,n,i,o,d;if(console.error("Auth error:",null==(e=l.response)?void 0:e.status,null==(n=l.response)?void 0:n.data),(null==(i=l.response)?void 0:i.status)===401){if(c.startsWith("/form-submission"))return;a.push("/")}else t((0,r.jB)((null==(d=l.response)||null==(o=d.data)?void 0:o.message)||l.message))}else t((0,r.jB)(l instanceof Error?l.message:"An unknown error occurred."))}};return(0,i.useEffect)(()=>{(null==e?void 0:e.skipFetchUser)||p()},[null==e?void 0:e.skipFetchUser]),(0,i.useEffect)(()=>{let e=e=>{if("logout"===e.key&&"true"===e.newValue)if(t((0,r.x9)()),c.startsWith("/form-submission")){let e=c.split("/")[2];e?a.push("/form-submission/".concat(e,"/sign-in")):a.push("/")}else a.push("/")};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[t,a,c]),{status:d,user:u,error:m,isAuthenticated:"authenticated"===d,isLoading:"loading"===d,refreshAuthState:()=>{p()},signin:async(e,t,a)=>{try{await l.A.post("/users/login",e),await p(),null==t||t()}catch(e){if(e instanceof s.pe){var r,n;let t=null==(n=e.response)||null==(r=n.data)?void 0:r.errorType;null==a||a(t)}else null==a||a()}},logout:async()=>{try{await l.A.post("/users/logout"),localStorage.setItem("logout","true"),setTimeout(()=>localStorage.removeItem("logout"),100)}finally{if(t((0,r.x9)()),c.startsWith("/form-submission")){let e=c.split("/")[2];e?a.push("/form-submission/".concat(e,"/sign-in")):a.push("/")}else a.push("/")}}}}},34869:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},39286:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>n,Gl:()=>s,th:()=>l});let r=(0,a(51990).Z0)({name:"createProject",initialState:{visible:!1},reducers:{showCreateProjectModal:e=>{e.visible=!0},hideCreateProjectModal:e=>{e.visible=!1}}}),{showCreateProjectModal:s,hideCreateProjectModal:l}=r.actions,n=r.reducer},50408:(e,t,a)=>{"use strict";a.d(t,{l:()=>n});var r=a(95155),s=a(66474),l=a(12115);let n=e=>{let{id:t,options:a,value:n,onChange:i}=e,[o,c]=(0,l.useState)(!1),d=(0,l.useRef)(null),u=(0,l.useRef)([]),m=(0,l.useRef)(null);(0,l.useEffect)(()=>{let e=e=>{m.current&&!m.current.contains(e.target)&&c(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let p=e=>{if(!o)return;let t=e.key.toLowerCase();if(t.match(/[a-z]/)){let e=a.findIndex(e=>e.toLowerCase().startsWith(t));if(-1!==e&&u.current[e]){var r;null==(r=u.current[e])||r.scrollIntoView({behavior:"auto",block:"nearest"})}}};return(0,l.useEffect)(()=>(document.addEventListener("keydown",p),()=>{document.removeEventListener("keydown",p)}),[o,a]),(0,r.jsxs)("div",{className:"relative",ref:m,children:[(0,r.jsxs)("button",{id:t,type:"button",className:"px-4 py-2 flex items-center justify-between rounded-md border border-neutral-400 focus:border-primary-500 duration-300 w-full text-left cursor-pointer",onClick:()=>{c(!o)},children:[(0,r.jsx)("span",{children:n||"Select an option"}),(0,r.jsx)(s.A,{})]}),o&&(0,r.jsx)("ul",{className:"absolute z-10 max-h-[180px] overflow-auto border border-neutral-400 rounded-md bg-neutral-100 w-full mt-1 flex flex-col",ref:d,children:a.map((e,t)=>(0,r.jsx)("li",{ref:e=>{u.current[t]=e},className:"cursor-pointer bg-neutral-100 hover:bg-neutral-200 px-4 py-2",onClick:()=>{i(e),c(!1)},children:e},t))})]})}},55747:(e,t,a)=>{"use strict";a.d(t,{C:()=>c,z:()=>o});var r=a(95155),s=a(12115),l=a(54059),n=a(9428),i=a(53999);let o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(l.bL,{className:(0,i.cn)("grid gap-2",a),...s,ref:t})});o.displayName=l.bL.displayName;let c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(l.q7,{ref:t,className:(0,i.cn)("aspect-square h-4 w-4 rounded-full border text-gray-900 shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-gray-950 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus-visible:ring-gray-300",a),...s,children:(0,r.jsx)(l.C1,{className:"flex items-center justify-center",children:(0,r.jsx)(n.A,{className:"h-2.5 w-2.5 fill-current text-current"})})})});c.displayName=l.q7.displayName},57434:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},57799:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(95155);a(12115);let s=()=>(0,r.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,r.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},59362:(e,t,a)=>{"use strict";a.d(t,{F0:()=>u,pe:()=>s});let{Axios:r,AxiosError:s,CanceledError:l,isCancel:n,CancelToken:i,VERSION:o,all:c,Cancel:d,isAxiosError:u,spread:m,toFormData:p,AxiosHeaders:h,HttpStatusCode:x,formToJSON:b,getAdapter:f,mergeConfig:y}=a(23464).A},62672:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>n,l:()=>l,yg:()=>s});let r=(0,a(51990).Z0)({name:"createLibraryItem",initialState:{visible:!1},reducers:{showCreateLibraryModal:e=>{e.visible=!0},hideCreateLibraryModal:e=>{e.visible=!1}}}),{showCreateLibraryModal:s,hideCreateLibraryModal:l}=r.actions,n=r.reducer},64368:(e,t,a)=>{"use strict";a.d(t,{H:()=>r});let r=(e,t)=>{let a=Object.entries(t).find(t=>{let[a,r]=t;return r===e});return a?a[0]:null}},71402:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>n,Ds:()=>s,_b:()=>l});let r=(0,a(51990).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,t)=>{e.message=t.payload.message,e.type=t.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:s,hideNotification:l}=r.actions,n=r.reducer},74567:e=>{"use strict";e.exports=JSON.parse('["Afghanistan","Albania","Algeria","Andorra","Angola","Argentina","Armenia","Australia","Austria","Azerbaijan","Bahamas","Bahrain","Bangladesh","Barbados","Belarus","Belgium","Belize","Benin","Bhutan","Bolivia","Bosnia and Herzegovina","Botswana","Brazil","Brunei","Bulgaria","Burkina Faso","Burundi","Cabo Verde","Cambodia","Cameroon","Canada","Central African Republic","Chad","Chile","China","Colombia","Comoros","Congo (Congo-Brazzaville)","Costa Rica","Croatia","Cuba","Cyprus","Czech Republic","Denmark","Djibouti","Dominica","Dominican Republic","Ecuador","Egypt","El Salvador","Equatorial Guinea","Eritrea","Estonia","Eswatini","Ethiopia","Fiji","Finland","France","Gabon","Gambia","Georgia","Germany","Ghana","Greece","Grenada","Guatemala","Guinea","Guinea-Bissau","Guyana","Haiti","Honduras","Hungary","Iceland","India","Indonesia","Iran","Iraq","Ireland","Israel","Italy","Jamaica","Japan","Jordan","Kazakhstan","Kenya","Kiribati","Kuwait","Kyrgyzstan","Laos","Latvia","Lebanon","Lesotho","Liberia","Libya","Liechtenstein","Lithuania","Luxembourg","Madagascar","Malawi","Malaysia","Maldives","Mali","Malta","Marshall Islands","Mauritania","Mauritius","Mexico","Micronesia","Moldova","Monaco","Mongolia","Montenegro","Morocco","Mozambique","Myanmar","Namibia","Nauru","Nepal","Netherlands","New Zealand","Nicaragua","Niger","Nigeria","North Korea","North Macedonia","Norway","Oman","Pakistan","Palau","Palestine","Panama","Papua New Guinea","Paraguay","Peru","Philippines","Poland","Portugal","Qatar","Romania","Russia","Rwanda","Saint Kitts and Nevis","Saint Lucia","Saint Vincent and the Grenadines","Samoa","San Marino","Sao Tome and Principe","Saudi Arabia","Senegal","Serbia","Seychelles","Sierra Leone","Singapore","Slovakia","Slovenia","Solomon Islands","Somalia","South Africa","South Korea","South Sudan","Spain","Sri Lanka","Sudan","Suriname","Sweden","Switzerland","Syria","Taiwan","Tajikistan","Tanzania","Thailand","Timor-Leste","Togo","Tonga","Trinidad and Tobago","Tunisia","Turkey","Turkmenistan","Tuvalu","Uganda","Ukraine","United Arab Emirates","United Kingdom","United States","Uruguay","Uzbekistan","Vanuatu","Vatican City","Venezuela","Vietnam","Yemen","Zambia","Zimbabwe"]')},75173:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>n,dQ:()=>s,g7:()=>l});let r=(0,a(51990).Z0)({initialState:{visible:!1,option:""},name:"createLibraryItem",reducers:{showCreateLibraryItemModal:(e,t)=>{e.visible=!0,e.option=t.payload},hideCreateLibraryItemModal:e=>{e.visible=!1,e.option=""}}}),{showCreateLibraryItemModal:s,hideCreateLibraryItemModal:l}=r.actions,n=r.reducer},77361:(e,t,a)=>{"use strict";a.d(t,{D_:()=>u,Im:()=>c,Oo:()=>m,c3:()=>l,kf:()=>s,lj:()=>h,or:()=>o,pf:()=>d,vj:()=>n,wI:()=>p,xx:()=>i});var r=a(25784);let s=async e=>{let{projectId:t}=e,{data:a}=await r.A.get("/projects/".concat(t));return a.project},l=async e=>{let{data:t}=await r.A.post("/projects/from-template",e);return t},n=async()=>{try{let{data:e}=await r.A.get("/projects");return e.projects}catch(e){throw console.error("Error fetching projects:",e),e}},i=async e=>{let{data:t}=await r.A.delete("/projects/delete/".concat(e));return t},o=async e=>{try{let{data:t}=await r.A.delete("/projects/delete-multiple",{data:{projectIds:e}});return t}catch(e){throw console.error("Error deleting multiple projects:",e),e}},c=async e=>{try{let{data:t}=await r.A.patch("/projects/change-status/".concat(e),{status:"archived"});return t}catch(e){throw console.error("Error archiving project:",e),e}},d=async function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{let{data:t}=await r.A.patch("/projects/change-status/".concat(e),{status:"deployed"});return t}catch(e){throw console.error("Error deploying project:",e),e}},u=async e=>{try{let{data:t}=await r.A.patch("/projects/update-many-status",{projectIds:e,status:"archived"});return t}catch(e){throw console.error("Error archiving multiple projects:",e),e}},m=async e=>{try{let{data:t}=await r.A.post("/users/check-email",{email:e});return t}catch(e){var t,a,s,l,n,i;throw Error("object"==typeof(null==(a=e.response)||null==(t=a.data)?void 0:t.message)?JSON.stringify(null==(l=e.response)||null==(s=l.data)?void 0:s.message):(null==(i=e.response)||null==(n=i.data)?void 0:n.message)||e.message||"Failed to check user")}},p=async e=>{let{projectId:t,email:a,permissions:s}=e;try{let e=await m(a);if(!e||!e.success)throw Error((null==e?void 0:e.message)||"User not found");let{data:l}=await r.A.post("/project-users",{userId:e.user.id,projectId:t,permission:s});return l}catch(e){var l,n,i,o,c,d;throw console.error("Error adding user to project:",e),Error("object"==typeof(null==(n=e.response)||null==(l=n.data)?void 0:l.message)?JSON.stringify(null==(o=e.response)||null==(i=o.data)?void 0:i.message):(null==(d=e.response)||null==(c=d.data)?void 0:c.message)||e.message||"Failed to add user")}},h=async e=>{try{let{data:t}=await r.A.post("/answers/multiple",e);return t}catch(e){throw console.error("Error creating answer submission:",e),e}}},82714:(e,t,a)=>{"use strict";a.d(t,{J:()=>i});var r=a(95155),s=a(12115),l=a(40968),n=a(53999);let i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(l.b,{ref:t,className:(0,n.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",a),...s})});i.displayName=l.b.displayName},89853:(e,t,a)=>{Promise.resolve().then(a.bind(a,98396))},94974:(e,t,a)=>{"use strict";a.d(t,{I7:()=>i,J2:()=>s,QK:()=>n,Xu:()=>l,nh:()=>o});var r=a(25784);let s=async e=>{let{templateId:t}=e,{data:a}=await r.A.get("/libraries/".concat(t));return a.template},l=async e=>{let{data:t}=await r.A.post("/libraries",e);return t},n=async()=>{let{data:e}=await r.A.get("/libraries");return e.templates},i=async e=>{let{data:t}=await r.A.delete("/libraries/".concat(e));return t},o=async e=>{let{templateIds:t}=e;return null}},97381:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>o,Le:()=>n,jB:()=>i,tQ:()=>s,x9:()=>l});let r=(0,a(51990).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,t)=>{e.status="authenticated",e.user=t.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,t)=>{e.status="unauthenticated",e.error=t.payload,e.user=null}}}),{setAuthenticatedUser:s,setUnauthenticated:l,setAuthLoading:n,setAuthError:i}=r.actions,o=r.reducer},98396:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>et});var r=a(95155),s=a(12115),l=a(29350),n=a(34869);let i=(0,a(19946).A)("log-out",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);var o=a(6874),c=a.n(o),d=a(35695),u=a(11906);let m=e=>{let{toggleSidebar:t,navbarRef:a}=e,[o,m]=(0,s.useState)(!1),p=(0,s.useRef)(null);(0,d.useRouter)(),(0,s.useEffect)(()=>{let e=e=>{p.current&&!p.current.contains(e.target)&&m(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let{user:h,logout:x}=(0,l.A)();return(0,r.jsx)("header",{className:"bg-primary-800 p-4 sticky top-0 z-40",ref:a,children:(0,r.jsxs)("div",{className:" flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("button",{onClick:t,className:"laptop:hidden text-neutral-100 p-2 rounded hover:bg-primary-700 ",children:(0,r.jsx)(u.$4x,{size:24})}),(0,r.jsx)(c(),{href:"/dashboard",className:"text-neutral-100 text-2xl font-bold cursor-pointer hover:text-neutral-300 transition-all ease-in-out",children:"Data Analysis"})]}),(0,r.jsxs)("div",{className:"relative",ref:p,children:[(0,r.jsx)("button",{onClick:()=>m(!o),className:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-neutral-100 font-semibold cursor-pointer",children:null==h?void 0:h.name[0].toUpperCase()}),o&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-neutral-100 rounded-md shadow-lg p-4 flex flex-col gap-2 ",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"size-10 bg-orange-500 rounded-full flex items-center justify-center text-neutral-100 text-xl font-semibold shrink-0",children:null==h?void 0:h.name[0].toUpperCase()}),(0,r.jsxs)("div",{className:"flex flex-col min-w-0",children:[(0,r.jsx)("div",{className:"font-medium capitalize",children:null==h?void 0:h.name}),(0,r.jsx)("div",{className:"text-sm text-neutral-700 truncate",children:null==h?void 0:h.email})]})]}),(0,r.jsx)(c(),{href:"/account/profile",className:"btn-primary",onClick:()=>{m(!1)},children:"Profile"})]}),(0,r.jsx)("hr",{className:"border-neutral-400"}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)(c(),{onClick:()=>m(!1),href:"/terms",className:"text-sm text-neutral-700 hover:bg-neutral-700/10 px-4 py-1 rounded-md",children:"Terms of Service"}),(0,r.jsx)(c(),{onClick:()=>m(!1),href:"/policy",className:"block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-200",children:"Privacy Policy"})]}),(0,r.jsx)("hr",{className:"border-neutral-400"}),(0,r.jsxs)(c(),{href:"#",className:"flex items-center px-4 py-2 gap-2 text-sm rounded-md text-neutral-700 hover:bg-neutral-700/10 transition-all duration-300",children:[(0,r.jsx)(n.A,{size:16}),"Language"]}),(0,r.jsx)("hr",{className:"border-neutral-400"}),(0,r.jsxs)("button",{onClick:x,className:"flex items-center text-neutral-700 gap-2 px-4 py-2 hover:bg-neutral-700/10 rounded-md active:scale-95 transition-all duration-300 w-full cursor-pointer ",children:[(0,r.jsx)(i,{size:16}),"Logout"]})]})]})]})})};var p=a(93347),h=a(14549),x=a(34540),b=a(39286),f=a(62672),y=a(15305),g=a(12515),j=a(29911),v=a(19373),N=a(77361);let w=()=>{let{user:e}=(0,l.A)(),{data:t,isLoading:a,isError:r}=(0,v.I)({queryKey:["projects",null==e?void 0:e.id],queryFn:N.vj,enabled:!!(null==e?void 0:e.id)}),s=[],n=[],i=[];return!a&&t&&(s=t.filter(e=>"deployed"===e.status),n=t.filter(e=>"draft"===e.status),i=t.filter(e=>"archived"===e.status)),{navItems:[{id:1,icon:y.nko,label:"Deployed",count:s.length||0,href:"/dashboard/deployed",category:"project"},{id:2,icon:g.fzI,label:"Draft",count:n.length||0,href:"/dashboard/draft",category:"project"},{id:3,icon:j.Wlj,label:"Archived",count:i.length||0,href:"/dashboard/archived",category:"project"},{id:4,icon:h.rjU,label:"My Library",count:0,href:"/library",category:"library"},{id:5,icon:g.Blu,label:"Collections",count:0,href:"/library/#",category:"library"}],deployedProjects:s,draftStatusProjects:n,archivedProjects:i}};var k=a(88570);let C=e=>{let{href:t,label:a,icon:l,count:n}=e,i=(0,d.usePathname)(),o=(0,d.useRouter)(),{deployedProjects:u,draftStatusProjects:m,archivedProjects:p}=w(),[h,x]=(0,s.useState)(!1),b=e=>{let t=(0,k.l)(e);o.push("/project/".concat(t,"/overview"))},f=e=>{let t=(0,k.l)(e);return"/project/".concat(t,"/overview")},y=(()=>{switch(a){case"Draft":return m;case"Deployed":return u;case"Archived":return p;default:return[]}})();return(0,r.jsxs)("li",{children:[(0,r.jsxs)(c(),{href:t,onClick:e=>{("Deployed"===a||"Draft"===a||"Archived"===a)&&(n>0?(e.preventDefault(),x(!h)):(e.preventDefault(),o.push("".concat(t,"/not-available"))))},className:"flex items-center px-4 py-3 hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300 ease-in-out rounded-md text-neutral-800",children:[(0,r.jsx)(l,{className:"mr-2",size:20}),(0,r.jsx)("span",{className:"text-sm font-bold",children:a}),(0,r.jsx)("span",{className:"ml-auto bg-neutral-200 text-neutral-700 rounded-full px-2 py-0.5 text-xs",children:n})]}),h&&y.length>0&&(0,r.jsx)("div",{className:"ml-6 mt-1 space-y-1",children:y.map(e=>(0,r.jsx)("div",{onClick:()=>b(e.id),className:"flex items-center px-4 py-2 text-sm hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300 ease-in-out rounded-md cursor-pointer ".concat(i===f(e.id)?"bg-primary-500 text-neutral-100":""),children:e.name},e.id))})]})},A=e=>{let{isOpen:t,topOffset:a,onNewProject:s}=e,l=(0,d.usePathname)(),n=(0,x.wA)(),{navItems:i,deployedProjects:o,draftStatusProjects:u,archivedProjects:m}=w(),y=l.includes("/library")?"library":"project",g=i.filter(e=>e.category===y);return(0,r.jsxs)("aside",{style:{top:"".concat(a,"px"),height:"calc(100vh - ".concat(a,"px)")},className:"\n        ".concat(t?"translate-x-0":"-translate-x-full","\n        fixed left-0 h-full w-64 shadow-xl bg-neutral-100 z-30\n        transform transition-all duration-300 ease-in-out\n        laptop:translate-x-0 laptop:static laptop:flex flex\n      "),children:[(0,r.jsx)("div",{className:"w-1/5 bg-neutral-200 p-4",children:(0,r.jsxs)("div",{className:"flex flex-col gap-5 items-center",children:[(0,r.jsx)(c(),{href:"/dashboard","aria-label":"Projects",className:"p-2  hover:bg-primary-500 hover:rounded-full transition-all duration-300 ease-in-out cursor-pointer\n              ".concat("project"===y?"bg-primary-500 text-neutral-100 rounded-full":"text-neutral-700 hover:bg-primary-500 hover:text-neutral-100","\n            "),children:(0,r.jsx)(p._zY,{size:20,title:"Projects"})}),(0,r.jsx)(c(),{href:"/library","aria-label":"Library",className:"p-2 hover:bg-primary-500 hover:rounded-full transition-all duration-300 ease-in-out cursor-pointer\n              ".concat("library"===y?"bg-primary-500 text-neutral-100 rounded-full":"text-neutral-700 hover:bg-primary-500 hover:text-neutral-100","\n            "),children:(0,r.jsx)(h.rjU,{size:20,title:"Library"})})]})}),(0,r.jsxs)("div",{className:"w-4/5 bg-neutral-100 flex-1",children:[(0,r.jsx)("div",{className:"p-4",children:(0,r.jsx)("button",{onClick:()=>{"project"===y?s?s():n((0,b.Gl)()):n((0,f.yg)())},className:"btn-primary w-full",children:"project"===y?"NEW PROJECT":"NEW ITEM"})}),(0,r.jsx)("nav",{className:"mt-2",children:(0,r.jsx)("ul",{className:"space-y-1",children:g.map(e=>(0,r.jsx)(C,{href:e.href,label:e.label,icon:e.icon,count:e.count},e.id))})})]})]})};var S=a(13163),E=a(62177),z=a(50408),L=a(74567),P=a(2511),M=a(57434),_=a(17576),q=a(25784),F=a(26715),I=a(5041),R=a(71402),T=a(64368);let B=async e=>{let{name:t,description:a,sector:r,country:s}=e,{data:l}=await q.A.post("/projects",{name:t,description:a,sector:r,country:s});return l},H=function(){let{isOpen:e,onClose:t,onBack:a}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},l=(0,x.d4)(e=>e.createProject.visible),i=(0,x.wA)(),{register:o,formState:{isSubmitting:c,errors:u,isSubmitted:m},handleSubmit:p,setValue:h}=(0,E.mN)(),[f,y]=(0,s.useState)(null),[g,j]=(0,s.useState)(null);(0,s.useEffect)(()=>{o("country",{required:"Please select a country"}),o("sector",{required:"Please select a sector"})},[o]),(0,s.useEffect)(()=>{h("country",f,{shouldValidate:m}),h("sector",g,{shouldValidate:m})},[h,f,g]);let[v,N]=(0,s.useState)(!1),w=()=>{N(!0),setTimeout(()=>{i((0,b.th)())},300)},k=(0,d.useRouter)(),C=(0,F.jE)(),A=(0,I.n)({mutationFn:B,onSuccess:()=>{C.invalidateQueries({queryKey:["projects"],exact:!1}),w(),k.push("/dashboard"),i((0,R.Ds)({message:"Project has been created successfully.",type:"success"}))},onError:()=>{i((0,R.Ds)({message:"Failed to create project",type:"error"}))}}),q=async e=>{A.mutate({name:e.projectName,description:e.description,sector:e.sector,country:e.country})};return(0,r.jsxs)(S.A,{isOpen:l&&!v,onClose:w,className:"w-4/5 laptop:w-3/5 ",children:[(0,r.jsx)("h1",{className:"heading-text",children:"Create a new project"}),(0,r.jsx)("form",{className:"flex flex-col gap-8 max-h-[600px] overflow-y-auto p-4",onSubmit:p(q),children:(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"project-name",className:"label-text",children:[(0,r.jsx)(M.A,{size:16})," Project Name"]}),(0,r.jsx)("input",{...o("projectName",{required:"Project name is required."}),id:"project-name",type:"text",className:"input-field",placeholder:"Enter a project name"}),u.projectName&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(u.projectName.message)})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"description",className:"label-text",children:"Description"}),(0,r.jsx)("textarea",{id:"description",...o("description",{required:"Please enter the project description"}),className:"input-field resize-none",cols:4,placeholder:"Enter the project description"}),u.description&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(u.description.message)})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,r.jsx)(n.A,{size:16}),"Country"]}),(0,r.jsx)(z.l,{id:"country",options:L,value:f,onChange:y}),u.country&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(u.country.message)})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,r.jsx)(_.A,{size:16})," Sector"]}),(0,r.jsx)(z.l,{id:"sector",options:Object.values(P.b),value:g&&P.b[g]?P.b[g]:"Select an option",onChange:e=>{j((0,T.H)(e,P.b))}}),u.sector&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(u.sector.message)})]})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-3 mt-4",children:[a&&(0,r.jsx)("button",{type:"button",onClick:()=>{a&&a()},className:"btn-outline",children:"Back"}),(0,r.jsx)("button",{type:"submit",className:"btn-primary",children:c?(0,r.jsxs)("span",{className:"flex items-center gap-2",children:["Creating"," ",(0,r.jsx)("div",{className:"size-4 animate-spin border-x border-neutral-100 rounded-full"})]}):"Create Project"})]})]})})]})};var D=a(75173),O=a(94974);let U=e=>{let{handleClose:t}=e,a=(0,x.wA)(),{register:i,formState:{isSubmitting:o,errors:c,isSubmitted:d},handleSubmit:u,setValue:m}=(0,E.mN)(),[p,h]=(0,s.useState)(null),[b,f]=(0,s.useState)(null);(0,s.useEffect)(()=>{i("country",{required:"Please select a country"}),i("sector",{required:"Please select a sector"})},[i]),(0,s.useEffect)(()=>{m("country",p,{shouldValidate:d}),m("sector",b,{shouldValidate:d})},[m,p,b]);let y=(0,F.jE)(),{user:g}=(0,l.A)(),j=(0,I.n)({mutationFn:O.Xu,onSuccess:()=>{y.invalidateQueries({queryKey:["templates",null==g?void 0:g.id]}),t(),a((0,R.Ds)({type:"success",message:"Template created successfully"}))},onError:()=>{a((0,R.Ds)({type:"error",message:"Failed to create template. Please try again"}))}}),v=async e=>{let t={name:e.name,description:e.description,sector:e.sector,country:e.country};j.mutate(t)};return(0,r.jsxs)("form",{className:"flex flex-col gap-8",onSubmit:u(v),children:[(0,r.jsx)("h1",{className:"heading-text capitalize",children:"Create new project template"}),(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"project-name",className:"label-text",children:[(0,r.jsx)(M.A,{size:16})," Template Name"]}),(0,r.jsx)("input",{...i("name",{required:"Template name is required."}),id:"project-name",type:"text",className:"input-field",placeholder:"Enter a project name"}),c.name&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(c.name.message)})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"description",className:"label-text",children:"Description"}),(0,r.jsx)("textarea",{id:"description",...i("description",{required:"Please enter the template description"}),className:"input-field resize-none",cols:4,placeholder:"Enter the project description"}),c.description&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(c.description.message)})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,r.jsx)(n.A,{size:16}),"Country"]}),(0,r.jsx)(z.l,{id:"country",options:L,value:p,onChange:h}),c.country&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(c.country.message)})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,r.jsx)(_.A,{size:16})," Sector"]}),(0,r.jsx)(z.l,{id:"sector",options:Object.values(P.b),value:b&&P.b[b]?P.b[b]:"Select an option",onChange:e=>{f((0,T.H)(e,P.b))}}),c.sector&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(c.sector.message)})]})]}),(0,r.jsx)("button",{type:"submit",className:"btn-primary self-end",children:o?(0,r.jsxs)("span",{className:"flex items-center gap-2",children:["Creating"," ",(0,r.jsx)("div",{className:"size-4 animate-spin border-x border-neutral-100 rounded-full"})]}):"Create Project"})]})]})},K=()=>{let{visible:e,option:t}=(0,x.d4)(e=>e.createLibraryItem),a=(0,x.wA)(),[l,n]=(0,s.useState)(!1),i=()=>{n(!0),setTimeout(()=>{a((0,D.g7)())},300)};return(0,r.jsx)(S.A,{isOpen:e&&!l,onClose:i,className:"w-3/5",children:(()=>{switch(t){case"question-block":return(0,r.jsx)("div",{children:"Question Block"});case"template":return(0,r.jsx)(U,{handleClose:i});case"upload":return(0,r.jsx)("div",{children:"Upload"});case"collection":return(0,r.jsx)("div",{children:"Collection"});default:return null}})()})};var V=a(51013),G=a(57799),Q=a(66163),J=a(26862);let Z=e=>{let{selectedRowId:t,setSelectedRowId:a}=e;return[{id:"select",header:"",cell:e=>{let{row:s}=e,l=s.original.id;return(0,r.jsx)(J.Sc,{className:"w-6 h-6 bg-neutral-100 rounded border border-neutral-400 data-[state=checked]:bg-neutral-100 data-[state=checked]:text-primary-500 data-[state=checked]:border-primary-500 cursor-pointer",checked:l===t,onCheckedChange:e=>a(e?l:null),"aria-label":"Select row"})}},{accessorKey:"name",header:"Name"},{id:"owner",accessorFn:e=>{var t,a;return null!=(a=null==(t=e.user)?void 0:t.name)?a:"unknown"},header:"Owner",cell:e=>{let{getValue:t}=e;return t()}},{id:"questions",accessorFn:e=>{var t,a;return null!=(a=null==(t=e.libraryQuestions)?void 0:t.length.toString())?a:"0"},header:"Questions",cell:e=>{let{getValue:t}=e;return t()}},{accessorKey:"updatedAt",header:"Last Modified"}]},W=e=>{let{showModal:t,closeModal:a,back:i,templateId:o}=e,{register:c,formState:{isSubmitting:u,errors:m,isSubmitted:p},handleSubmit:h,setValue:b,reset:f}=(0,E.mN)(),y=(0,x.wA)(),g=(0,d.useRouter)(),[j,w]=(0,s.useState)(null),[k,C]=(0,s.useState)(null);(0,s.useEffect)(()=>{c("country",{required:"Please select a country"}),c("sector",{required:"Please select a sector"})},[c]),(0,s.useEffect)(()=>{b("country",j,{shouldValidate:p}),b("sector",k,{shouldValidate:p})},[b,j,k]);let{user:A,isLoading:q}=(0,l.A)(),{data:B,isLoading:H,isError:D}=(0,v.I)({queryKey:["templates",null==A?void 0:A.id,o],queryFn:()=>(0,O.J2)({templateId:o}),enabled:!!(null==A?void 0:A.id)});(0,s.useEffect)(()=>{B&&(f({projectName:B.name,description:B.description,sector:B.sector,country:B.country}),w(B.country),C(B.sector))},[B,f]);let U=(0,F.jE)(),K=(0,I.n)({mutationFn:N.c3,onSuccess:()=>{U.invalidateQueries({queryKey:["projects"],exact:!1}),a(),g.push("/dashboard"),y((0,R.Ds)({message:"Project has been created successfully.",type:"success"}))},onError:e=>{y((0,R.Ds)({message:"Failed to create project"+e.message,type:"error"}))}}),V=async e=>{let t={templateId:o,name:e.projectName,description:e.description,sector:e.sector,country:e.country};K.mutate(t)};return q||H||D?null:(0,r.jsx)(S.A,{isOpen:t,onClose:a,className:"w-3/6",children:(0,r.jsxs)("form",{className:"flex flex-col gap-8",onSubmit:h(V),children:[(0,r.jsx)("h1",{className:"heading-text",children:"Create a new project"}),(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"project-name",className:"label-text",children:[(0,r.jsx)(M.A,{size:16})," Project Name"]}),(0,r.jsx)("input",{...c("projectName",{required:"Project name is required."}),id:"project-name",type:"text",className:"input-field",placeholder:"Enter a project name"}),m.projectName&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(m.projectName.message)})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"description",className:"label-text",children:"Description"}),(0,r.jsx)("textarea",{id:"description",...c("description",{required:"Please enter the project description"}),className:"input-field resize-none",cols:4,placeholder:"Enter the project description"}),m.description&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(m.description.message)})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,r.jsx)(n.A,{size:16}),"Country"]}),(0,r.jsx)(z.l,{id:"country",options:L,value:j,onChange:w}),m.country&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(m.country.message)})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,r.jsx)(_.A,{size:16})," Sector"]}),(0,r.jsx)(z.l,{id:"sector",options:Object.values(P.b),value:k&&P.b[k]?P.b[k]:"Select an option",onChange:e=>{C((0,T.H)(e,P.b))}}),m.sector&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(m.sector.message)})]})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-3 mt-4",children:[(0,r.jsx)("button",{type:"button",onClick:i,className:"btn-outline",children:"Back"}),(0,r.jsx)("button",{type:"submit",className:"btn-primary",children:u?(0,r.jsxs)("span",{className:"flex items-center gap-2",children:["Creating"," ",(0,r.jsx)("div",{className:"size-4 animate-spin border-x border-neutral-100 rounded-full"})]}):"Create Project"})]})]})]})})},Y=e=>{let{isOpen:t,back:a,onClose:n}=e,{user:i}=(0,l.A)(),{data:o,isLoading:c,isError:d}=(0,v.I)({queryFn:O.QK,queryKey:["templates",null==i?void 0:i.id],enabled:!!(null==i?void 0:i.id)}),[u,m]=(0,s.useState)(null),[p,h]=(0,s.useState)(!1),[x,b]=(0,s.useState)(!1),f=Z({selectedRowId:u,setSelectedRowId:m}),y=e=>{b(!0),setTimeout(()=>{h(!1),m(null),"close"===e&&n(),b(!1)},300)};return o?c?(0,r.jsx)(G.A,{}):d?(0,r.jsx)("div",{children:"Error loading data, please try again"}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(S.A,{isOpen:t&&!p,onClose:()=>{m(null),n()},className:"bg-neutral-100 p-4 mobile:p-6 rounded-lg w-[95%] mobile:w-[85%] tablet:w-[75%] laptop:w-[60%] desktop:w-[50%] max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,r.jsx)("div",{className:"flex justify-between items-center",children:(0,r.jsx)("h2",{className:"text-xl font-semibold text-neutral-700",children:"Select a Template"})}),(0,r.jsx)(Q.x,{data:o,columns:f}),(0,r.jsxs)("div",{className:"flex justify-end gap-3 mt-4",children:[(0,r.jsx)("button",{onClick:()=>{m(null),a()},className:"btn-outline",children:"Back"}),(0,r.jsxs)("button",{type:"button",disabled:!u,className:"btn-primary",onClick:()=>{u&&h(!0)},children:["Next",(0,r.jsx)(V.ZK4,{})]})]})]})}),(p||x)&&null!==u&&(0,r.jsx)(W,{showModal:p&&!x,closeModal:()=>y("close"),back:()=>y("back"),templateId:u})]}):null},X=e=>{let{isOpen:t,onClose:a}=e,l=(0,x.wA)(),[n,i]=(0,s.useState)(!1),o=e=>{i(!1),"close"===e&&a()};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(S.A,{isOpen:t&&!n,onClose:a,className:"bg-neutral-100 p-4 mobile:p-6 rounded-lg w-[95%] mobile:w-[85%] tablet:w-[75%] laptop:w-[60%] desktop:w-[50%] max-w-3xl mx-auto",children:(0,r.jsxs)("div",{className:"flex flex-col gap-4 mobile:gap-6",children:[(0,r.jsx)("div",{className:"flex justify-between items-center",children:(0,r.jsx)("h2",{className:"text-lg mobile:text-xl font-semibold text-neutral-700",children:"Create project: Choose a source"})}),(0,r.jsx)("p",{className:"text-sm mobile:text-base text-neutral-600",children:"Choose one of the options below to continue. You will be prompted to enter name and other details in further steps."}),(0,r.jsxs)("div",{className:"grid grid-cols-1 mobile:grid-cols-2 gap-3 mobile:gap-4",children:[(0,r.jsxs)("div",{onClick:()=>{a(),l((0,b.Gl)())},className:"bg-neutral-200 rounded-lg p-4 mobile:p-6 tablet:p-8 flex flex-col items-center justify-center gap-2 mobile:gap-3 cursor-pointer hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300",children:[(0,r.jsx)(g.Yvo,{className:"w-5 h-5 mobile:w-6 mobile:h-6"}),(0,r.jsx)("span",{className:"text-sm mobile:text-base text-center",children:"Build from scratch"})]}),(0,r.jsxs)("div",{onClick:()=>{i(!0)},className:"bg-neutral-200 rounded-lg p-4 mobile:p-6 tablet:p-8 flex flex-col items-center justify-center gap-2 mobile:gap-3 cursor-pointer hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300",children:[(0,r.jsx)(h.S1H,{className:"w-5 h-5 mobile:w-6 mobile:h-6"}),(0,r.jsx)("span",{className:"text-sm mobile:text-base text-center",children:"Use a template"})]})]})]})}),(0,r.jsx)(Y,{isOpen:n,onClose:()=>o("close"),back:()=>o("back")})]})};var $=a(10351);let ee=()=>{let e=(0,x.wA)(),t=(0,x.d4)(e=>e.createLibrary.visible),[a,l]=(0,s.useState)(!1),n=()=>{l(!0),setTimeout(()=>{e((0,f.l)())},300)},i=(0,d.useRouter)(),o=[{id:"question-block",title:"Question Block",icon:p.Kt4,onClick:()=>{n(),i.push("/library/question-block/form-builder")}},{id:"template",title:"Template",icon:u.zFA,onClick:()=>{n(),e((0,D.dQ)("template"))}},{id:"upload",title:"Upload",icon:$.B88,onClick:()=>{n()}},{id:"collection",title:"Collection",icon:j.M1W,onClick:()=>{n()}}];return(0,r.jsxs)(S.A,{isOpen:t&&!a,onClose:n,className:"p-6 rounded-md w-3/5",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700 mb-4",children:"Create Library Item"}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-4",children:o.map(e=>(0,r.jsxs)("button",{onClick:e.onClick,className:"flex flex-col gap-2 items-center justify-center p-6 bg-neutral-200 rounded-md hover:bg-primary-500 hover:text-neutral-100 cursor-pointer transition-all duration-300",children:[(0,r.jsx)(e.icon,{size:24,className:""}),(0,r.jsx)("span",{className:"",children:e.title})]},e.id))})]})};function et(e){let{children:t}=e,[a,l]=(0,s.useState)(!1),[n,i]=(0,s.useState)(0),[o,c]=(0,s.useState)(!1),d=(0,s.useRef)(null),u=()=>l(!a);(0,s.useLayoutEffect)(()=>{let e=new ResizeObserver(()=>{d.current&&i(d.current.offsetHeight)});return d.current&&e.observe(d.current),()=>e.disconnect()},[]);let p=(0,x.d4)(e=>e.createProject.visible),h=(0,x.d4)(e=>e.createLibrary.visible),b=(0,x.d4)(e=>e.createLibraryItem.visible);return(0,r.jsxs)("div",{className:"min-h-screen flex flex-col",children:[p&&(0,r.jsx)(H,{}),h&&(0,r.jsx)(ee,{}),b&&(0,r.jsx)(K,{}),(0,r.jsx)(X,{isOpen:o,onClose:()=>c(!1)}),(0,r.jsx)(m,{toggleSidebar:u,isSidebarOpen:a,navbarRef:d}),(0,r.jsxs)("div",{className:"flex flex-1 overflow-hidden",children:[(0,r.jsx)(A,{isOpen:a,toggleSidebar:u,topOffset:n,onNewProject:()=>{c(!0)}}),(0,r.jsx)("main",{className:"flex-1 p-6 overflow-y-auto",style:{height:"calc(100vh - ".concat(n,"px)")},children:t})]})]})}},99474:(e,t,a)=>{"use strict";a.d(t,{T:()=>n});var r=a(95155),s=a(12115),l=a(53999);let n=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700 dark:bg-gray-900 dark:placeholder:text-gray-500",a),ref:t,...s})});n.displayName="Textarea"}},e=>{var t=t=>e(e.s=t);e.O(0,[2150,6711,9204,8087,5897,5479,512,844,635,1445,6967,6903,4601,2177,4277,6874,556,3481,1467,6539,6268,919,4695,9660,8441,1684,7358],()=>t(89853)),_N_E=e.O()}]);