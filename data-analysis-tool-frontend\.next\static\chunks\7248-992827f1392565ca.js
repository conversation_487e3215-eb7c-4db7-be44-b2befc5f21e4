"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7248],{3587:(e,t,r)=>{r.d(t,{OD:()=>d,Tr:()=>i,UL:()=>n,WK:()=>c});let a=(e,t)=>{if(!e.questionOptions||0===e.questionOptions.length)return null;if("selectone"===e.inputType&&"string"==typeof t){let r=e.questionOptions.find(e=>e.label===t);return(null==r?void 0:r.nextQuestionId)||null}if("selectmany"===e.inputType&&Array.isArray(t))for(let r of t){let t=e.questionOptions.find(e=>e.label===r);if(null==t?void 0:t.nextQuestionId)return t.nextQuestionId}return null},o=e=>e.questionOptions&&0!==e.questionOptions.length?e.questionOptions.map(e=>e.nextQuestionId).filter(e=>null!=e):[],n=(e,t)=>{let r=new Set,n=new Set;return e.forEach(e=>{o(e).forEach(e=>n.add(e))}),e.forEach(e=>{n.has(e.id)||r.add(e.id)}),Object.entries(t).forEach(t=>{let[o,n]=t,s=parseInt(o),l=e.find(e=>e.id===s);if(l&&n){let e=a(l,n);e&&r.add(e)}}),e.filter(e=>r.has(e.id))},s=(e,t)=>{let r=t.find(t=>t.id===e);if(!r)return[];let a=o(r);return t.filter(e=>a.includes(e.id))},l=(e,t)=>t.some(t=>o(t).includes(e)),i=(e,t)=>{let r=new Set(n(e,t).map(e=>e.id));return e.filter(t=>!l(t.id,e)).sort((e,t)=>e.position-t.position).map(t=>{let a=s(t.id,e).sort((e,t)=>e.position-t.position);return{question:t,isVisible:r.has(t.id),isFollowUp:!1,followUps:a.map(e=>({question:e,isVisible:r.has(e.id)}))}}).filter(e=>e.isVisible||e.followUps.some(e=>e.isVisible))},d=(e,t)=>{let r=new Set(t.map(e=>e.id)),a={};return Object.entries(e).forEach(e=>{let[t,o]=e;r.has(parseInt(t))&&(a[t]=o)}),a},c=(e,t)=>{let r={};return e.forEach(e=>{if(e.isRequired){let a=t[e.id];("string"==typeof a&&!a.trim()||Array.isArray(a)&&0===a.length||null==a)&&(r[e.id]="".concat(e.label," is required"))}}),r}},10150:(e,t,r)=>{r.d(t,{BU:()=>l,IF:()=>n,YQ:()=>d,_U:()=>c,lr:()=>s,pr:()=>o,yb:()=>i});var a=r(25784);let o=async e=>{var t,r,o;let{projectId:n}=e;try{let{data:e}=await a.A.get("/projects/form/".concat(n));return(null==(r=e.data)||null==(t=r.project)?void 0:t.questionGroup)||[]}catch(e){console.error("Error fetching question groups from project endpoint:",e);try{let{data:e}=await a.A.post("/question-groups",{projectId:n});return(null==(o=e.data)?void 0:o.projectGroup)||[]}catch(e){return console.error("Error in fallback fetch:",e),[]}}},n=async e=>{let{title:t,order:r,projectId:o,parentGroupId:n,selectedQuestionIds:s}=e;try{let{data:e}=await a.A.post("/question-groups",{title:t,order:r,projectId:o,parentGroupId:n,selectedQuestionIds:s||[]});return e}catch(e){throw console.error("Error creating question group:",e),e}},s=async e=>{let{id:t,title:r,order:o,parentGroupId:n,selectedQuestionIds:s}=e;try{let{data:e}=await a.A.patch("/question-groups",{id:t,title:r,order:o,parentGroupId:n,selectedQuestionIds:s});return e}catch(e){throw console.error("Error updating question group:",e),e}},l=async e=>{let{id:t}=e;try{let{data:e}=await a.A.delete("/question-groups/".concat(t));return e}catch(e){throw console.error("Error deleting question group:",e),e}},i=async e=>{let{id:t}=e;try{let{data:e}=await a.A.delete("/question-groups/group/question/".concat(t));return e}catch(e){throw console.error("Error deleting question group and questions:",e),e}},d=async e=>{let{childGroupId:t,parentGroupId:r}=e;try{let{data:e}=await a.A.patch("/question-groups/group/add",{childGroupId:t,ParentGroupId:r});return e}catch(e){throw console.error("Error moving group to parent:",e),e}},c=async e=>{let{questionId:t,fromGroupId:r,toGroupId:o}=e;try{let{data:e}=await a.A.patch("/question-groups/question/move",{questionId:t,fromGroupId:r,toGroupId:o});return e}catch(e){throw console.error("Error moving question between groups:",e),e}}},13388:(e,t,r)=>{r.d(t,{A:()=>n});var a=r(95155);r(12115);var o=r(82714);let n=e=>{let{questionGroup:t,renderQuestionInput:r,errors:n,className:s=""}=e,{question:l,isVisible:i,followUps:d}=t;return i||d.some(e=>e.isVisible)?(0,a.jsxs)("div",{className:"".concat(s),children:[i&&(0,a.jsxs)("div",{className:"border border-neutral-500 dark:border-gray-700 rounded-md p-4 bg-neutral-100 dark:bg-gray-800",children:[(0,a.jsxs)("div",{className:"mb-2",children:[(0,a.jsxs)(o.J,{className:"text-base font-medium",children:[l.label,l.isRequired&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),l.hint&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:l.hint}),n[l.id]&&(0,a.jsx)("p",{className:"text-sm text-red-500 mt-1",children:n[l.id]})]}),(0,a.jsx)("div",{className:"mt-2",children:r(l)}),d.some(e=>e.isVisible)&&(0,a.jsx)("div",{className:"mt-4 ml-4 space-y-3 border-l-2 border-blue-200 dark:border-blue-700 pl-4",children:d.map(e=>{let{question:t,isVisible:s}=e;return s&&(0,a.jsxs)("div",{className:"border border-gray-100 dark:border-gray-600 rounded-md p-3 bg-blue-50 dark:bg-blue-900/20",children:[(0,a.jsxs)("div",{className:"mb-2",children:[(0,a.jsxs)(o.J,{className:"text-sm font-medium text-blue-900 dark:text-blue-100",children:[t.label,t.isRequired&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),t.hint&&(0,a.jsx)("p",{className:"text-xs text-blue-700 dark:text-blue-300 mt-1",children:t.hint}),n[t.id]&&(0,a.jsx)("p",{className:"text-xs text-red-500 mt-1",children:n[t.id]})]}),(0,a.jsx)("div",{className:"mt-2",children:r(t)})]},t.id)})})]}),!i&&d.some(e=>e.isVisible)&&(0,a.jsx)("div",{className:"space-y-3",children:d.map(e=>{let{question:t,isVisible:s}=e;return s&&(0,a.jsxs)("div",{className:"border border-gray-200 dark:border-gray-700 rounded-md p-4 bg-white dark:bg-gray-800",children:[(0,a.jsxs)("div",{className:"mb-2",children:[(0,a.jsxs)(o.J,{className:"text-base font-medium",children:[t.label,t.isRequired&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),t.hint&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:t.hint}),n[t.id]&&(0,a.jsx)("p",{className:"text-sm text-red-500 mt-1",children:n[t.id]})]}),(0,a.jsx)("div",{className:"mt-2",children:r(t)})]},t.id)})})]}):null}},16112:(e,t,r)=>{r.d(t,{N:()=>i});var a=r(95155),o=r(12115),n=r(88524),s=r(89852),l=r(95749);function i(e){let{questionId:t,value:r,onChange:i,required:d=!1,tableLabel:c}=e,[u,p]=(0,o.useState)([]),[m,h]=(0,o.useState)([]),[f,b]=(0,o.useState)({}),[g,y]=(0,o.useState)(!0),[x,w]=(0,o.useState)(null),[v,N]=(0,o.useState)({}),j=e=>{if(!e||!e.tableColumns)return[];let t=[];return e.tableColumns.filter(e=>null===e.parentColumnId||void 0===e.parentColumnId).forEach(e=>{t.push(e),e.childColumns&&e.childColumns.length>0&&e.childColumns.forEach(e=>{t.push({id:e.id,columnName:e.columnName,parentColumnId:e.parentColumnId})})}),t},q=o.useMemo(()=>{if(0===u.length)return{parentColumns:[],columnMap:new Map,hasChildColumns:!1};let e=u.filter(e=>void 0===e.parentColumnId||null===e.parentColumnId),t=new Map;e.forEach(e=>{let r=u.filter(t=>t.parentColumnId===e.id);t.set(e.id,r)});let r=e.some(e=>(t.get(e.id)||[]).length>0);return{parentColumns:e,columnMap:t,hasChildColumns:r}},[u]);(0,o.useEffect)(()=>{(async()=>{try{y(!0);let e=await (0,l.q7)(t);if(e){e.tableColumns&&e.tableRows||console.error("Missing tableColumns or tableRows in response:",e);let t=j(e);p(t),h(e.tableRows||[]),e.label&&N({label:e.label})}else console.error("No table data returned"),w("Failed to load table structure")}catch(e){console.error("Error fetching table structure:",e),w("Failed to load table structure")}finally{y(!1)}})()},[t]),(0,o.useEffect)(()=>{if(g)return;let e={},t=[];if("string"==typeof r)if(r&&""!==r.trim())try{t=JSON.parse(r)}catch(e){console.error("Error parsing cell data:",e),t=[]}else console.error("Empty string value, using empty array");else Array.isArray(r)&&(t=r);t.forEach(t=>{e["".concat(t.columnId,"_").concat(t.rowsId)]=t.value}),!r||"string"==typeof r&&""===r.trim()||Array.isArray(r)&&0===r.length?b({}):Object.keys(e).length>0&&b(t=>({...e,...t}))},[r,g]);let E=(e,t,r)=>{let a="".concat(e,"_").concat(t);b(e=>({...e,[a]:r})),setTimeout(()=>{let e={...f,[a]:r},t=[];Object.entries(e).forEach(e=>{let[r,a]=e;if(""!==a.trim()){let[e,o]=r.split("_").map(Number);t.push({columnId:e,rowsId:o,value:a})}}),i(t)},0)},A=0===u.length;return(0,a.jsx)("div",{className:"overflow-x-auto",children:g?(0,a.jsx)("div",{className:"py-4 text-center",children:"Loading table..."}):x?(0,a.jsx)("div",{className:"py-4 text-center text-red-500",children:x}):A?(0,a.jsx)("div",{className:"py-4 text-center text-amber-600",children:"This table has no columns defined. Please configure the table question first."}):(0,a.jsxs)(n.XI,{className:"border-collapse",children:[(0,a.jsxs)(n.A0,{children:[(0,a.jsx)(n.Hj,{children:q.parentColumns.map(e=>{let t=(q.columnMap.get(e.id)||[]).length||1;return(0,a.jsx)(n.nd,{colSpan:t,className:"text-center border bg-blue-50 font-medium",children:e.columnName},e.id)})}),q.hasChildColumns&&(0,a.jsx)(n.Hj,{children:q.parentColumns.map(e=>{let t=q.columnMap.get(e.id)||[];return 0===t.length?(0,a.jsx)(n.nd,{className:"border bg-blue-50/50 text-sm"},"empty-".concat(e.id)):t.map(e=>(0,a.jsx)(n.nd,{className:"border bg-blue-50/50 text-sm",children:e.columnName},e.id))})})]}),(0,a.jsx)(n.BF,{children:m.length>0?m.map((e,t)=>(0,a.jsx)(n.Hj,{className:t%2==0?"bg-white":"bg-gray-50",children:q.parentColumns.map(t=>{let r=q.columnMap.get(t.id)||[];return 0===r.length?(0,a.jsx)(n.nA,{className:"border p-1",children:(0,a.jsx)(s.p,{value:f["".concat(t.id,"_").concat(e.id)]||"",onChange:r=>E(t.id,e.id,r.target.value),className:"w-full",required:d,placeholder:"Enter value"})},"cell-".concat(t.id,"-").concat(e.id)):r.map(t=>(0,a.jsx)(n.nA,{className:"border p-1",children:(0,a.jsx)(s.p,{value:f["".concat(t.id,"_").concat(e.id)]||"",onChange:r=>E(t.id,e.id,r.target.value),className:"w-full",required:d,placeholder:"Enter value"})},"cell-".concat(t.id,"-").concat(e.id)))})},e.id)):(0,a.jsx)(n.Hj,{children:q.parentColumns.map(e=>{let t=q.columnMap.get(e.id)||[];return 0===t.length?(0,a.jsx)(n.nA,{className:"border p-1",children:(0,a.jsx)(s.p,{value:f["".concat(e.id,"_no_row")]||"",onChange:t=>E(e.id,"no_row",t.target.value),className:"w-full",required:d,placeholder:"Enter value"})},"cell-".concat(e.id,"-no-row")):t.map(e=>(0,a.jsx)(n.nA,{className:"border p-1",children:(0,a.jsx)(s.p,{value:f["".concat(e.id,"_no_row")]||"",onChange:t=>E(e.id,"no_row",t.target.value),className:"w-full",required:d,placeholder:"Enter value"})},"cell-".concat(e.id,"-no-row")))})})})]})})}},25784:(e,t,r)=>{r.d(t,{A:()=>o});let a=r(23464).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});a.interceptors.request.use(e=>e,e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let o=a},34947:(e,t,r)=>{r.d(t,{Af:()=>l,K4:()=>n,ae:()=>p,dI:()=>u,ej:()=>s,ku:()=>d,sr:()=>c,ul:()=>i});var a=r(25784);let o=e=>{if("project"===e)return"/questions";if("template"===e)return"/template-questions";if("questionBlock"===e)return"/question-blocks";throw Error("Unsupported context type")},n=async e=>{let{projectId:t}=e,{data:r}=await a.A.get("/questions/".concat(t));return r.questions},s=async e=>{let{templateId:t}=e,{data:r}=await a.A.get("/template-questions/".concat(t));return r.questions},l=async e=>{var t,r,n,s,l,i;let{contextType:d,contextId:c,dataToSend:u,position:p}=e,m="questionBlock"===d?"".concat(o(d)):"".concat(o(d),"/").concat(c);if(!u.label||!u.inputType)throw Error("Label and inputType are required");let h=["selectone","selectmany"].includes(u.inputType),f=u.file instanceof File,b=Array.isArray(u.questionOptions)&&u.questionOptions.length>0;if(h&&!f&&!b)throw Error("Options are required for select input types");if(f){let e=new FormData;e.append("label",u.label),e.append("isRequired",u.isRequired?"true":"false"),e.append("inputType",u.inputType),u.hint&&e.append("hint",u.hint),u.placeholder&&e.append("placeholder",u.placeholder),e.append("position",String(p||1)),e.append("file",u.file);try{let{data:t}=await a.A.post(m,e,{headers:{"Content-Type":"multipart/form-data"}});return t}catch(e){throw console.error("Upload error details:",(null==(t=e.response)?void 0:t.data)||e.message),Error("Failed to upload question with file: ".concat((null==(n=e.response)||null==(r=n.data)?void 0:r.message)||e.message))}}try{let{data:e}=await a.A.post(m,{label:u.label,isRequired:u.isRequired,hint:u.hint,placeholder:u.placeholder,inputType:u.inputType,questionOptions:u.questionOptions,position:p||1});return e}catch(e){throw console.error("API error details:",(null==(s=e.response)?void 0:s.data)||e.message),Error("Failed to add question: ".concat((null==(i=e.response)||null==(l=i.data)?void 0:l.message)||e.message))}},i=async e=>{let{contextType:t,id:r,projectId:n}=e,{data:s}=await a.A.delete("".concat(o(t),"/").concat(r,"?projectId=").concat(n));return s},d=async e=>{let{id:t,contextType:r,contextId:n}=e,{data:s}=await a.A.post("".concat(o(r),"/duplicate/").concat(t,"?projectId=").concat(n),"questionBlock"===r?{}:"project"===r?{projectId:n}:{templateId:n});return s},c=async e=>{let{id:t,contextType:r,dataToSend:n,contextId:s}=e,{data:l}=await a.A.patch("".concat(o(r),"/").concat(t,"?projectId=").concat(s),n);return l},u=async()=>{try{return(await a.A.get("/question-blocks")).data.questions||[]}catch(e){throw console.error("Error fetching question block questions:",e),e}},p=async e=>{let{contextType:t,contextId:r,questionPositions:n}=e;if("project"!==t)throw Error("Question position updates are only supported for projects");let s="".concat(o(t),"/positions?projectId=").concat(r);try{let{data:e}=await a.A.patch(s,{questionPositions:n});return e}catch(e){var l,i,d,c,u,p;throw console.error("Update failed - Full error:",e),console.error("Update failed - Error details:",{status:null==(l=e.response)?void 0:l.status,statusText:null==(i=e.response)?void 0:i.statusText,data:null==(d=e.response)?void 0:d.data,message:e.message,config:{url:null==(c=e.config)?void 0:c.url,method:null==(u=e.config)?void 0:u.method,data:null==(p=e.config)?void 0:p.data}}),e}}},53999:(e,t,r)=>{r.d(t,{Y:()=>s,cn:()=>n});var a=r(52596),o=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,a.$)(t))}function s(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"short";if(!e)return"";try{let r="string"==typeof e?new Date(e):e;if(isNaN(r.getTime()))return"";switch(t){case"short":return r.toLocaleDateString(void 0,{year:"numeric",month:"short",day:"numeric"});case"long":return r.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});case"full":return r.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",weekday:"long",hour:"2-digit",minute:"2-digit",second:"2-digit"});default:return r.toLocaleDateString()}}catch(t){return console.error("Error formatting date:",t),String(e)}}},55747:(e,t,r)=>{r.d(t,{C:()=>d,z:()=>i});var a=r(95155),o=r(12115),n=r(54059),s=r(9428),l=r(53999);let i=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,a.jsx)(n.bL,{className:(0,l.cn)("grid gap-2",r),...o,ref:t})});i.displayName=n.bL.displayName;let d=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,a.jsx)(n.q7,{ref:t,className:(0,l.cn)("aspect-square h-4 w-4 rounded-full border text-gray-900 shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-gray-950 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus-visible:ring-gray-300",r),...o,children:(0,a.jsx)(n.C1,{className:"flex items-center justify-center",children:(0,a.jsx)(s.A,{className:"h-2.5 w-2.5 fill-current text-current"})})})});d.displayName=n.q7.displayName},57799:(e,t,r)=>{r.d(t,{A:()=>o});var a=r(95155);r(12115);let o=()=>(0,a.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,a.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},71402:(e,t,r)=>{r.d(t,{Ay:()=>s,Ds:()=>o,_b:()=>n});let a=(0,r(51990).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,t)=>{e.message=t.payload.message,e.type=t.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:o,hideNotification:n}=a.actions,s=a.reducer},82714:(e,t,r)=>{r.d(t,{J:()=>l});var a=r(95155),o=r(12115),n=r(40968),s=r(53999);let l=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,a.jsx)(n.b,{ref:t,className:(0,s.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",r),...o})});l.displayName=n.b.displayName},88524:(e,t,r)=>{r.d(t,{A0:()=>s,BF:()=>l,Hj:()=>i,XI:()=>n,nA:()=>c,nd:()=>d});var a=r(95155);r(12115);var o=r(53999);function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,o.cn)("w-full caption-bottom text-sm",t),...r})})}function s(e){let{className:t,...r}=e;return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,o.cn)("[&_tr]:border-b",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,o.cn)("[&_tr:last-child]:border-0",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,o.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,o.cn)("text-foreground h-10 px-2 text-left align-middle font-medium neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,o.cn)("p-2 align-middle neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...r})}},89852:(e,t,r)=>{r.d(t,{p:()=>n});var a=r(95155);r(12115);var o=r(53999);function n(e){let{className:t,type:r,...n}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,o.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-primary-500 focus-visible:ring-[1px]","focus-visible:outline-none","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},95139:(e,t,r)=>{r.d(t,{S:()=>l});var a=r(95155);r(12115);var o=r(76981),n=r(5196),s=r(53999);function l(e){let{className:t,...r}=e;return(0,a.jsx)(o.bL,{"data-slot":"checkbox",className:(0,s.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...r,children:(0,a.jsx)(o.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,a.jsx)(n.A,{className:"size-3.5"})})})}},95749:(e,t,r)=>{r.d(t,{ZR:()=>n,am:()=>s,q7:()=>o});var a=r(25784);let o=async e=>{try{if(!e||isNaN(e))throw console.error("Invalid questionId:",e),Error("Invalid question ID provided");try{let t=await a.A.get("/table-questions/".concat(e));if(t.data&&t.data.data&&t.data.data.question)return t.data.data.question;if(t.data&&t.data.data)return t.data.data;if(t.data&&t.data.success)return t.data}catch(e){console.error("Error from /table-questions/ endpoint:",e)}try{let t=await a.A.get("/questions/".concat(e));if(t.data&&t.data.data)return t.data.data}catch(e){console.error("Error from /questions/ endpoint:",e)}try{let t=await a.A.get("/tables/".concat(e));if(t.data&&t.data.data&&t.data.data.question)return t.data.data.question}catch(e){console.error("Error from /tables/ endpoint:",e)}throw console.error("All endpoints failed to return valid data"),Error("Failed to fetch table structure from any endpoint")}catch(e){throw console.error("Error fetching table structure:",e),e}},n=async(e,t,r,o)=>{try{if(!e||!e.trim())throw Error("Table label is required");if(!t||isNaN(t))throw Error("Valid project ID is required");if(!r||!Array.isArray(r)||0===r.length)throw Error("At least one column is required");if(o&&!Array.isArray(o))throw Error("Rows must be an array if provided");if(r.filter(e=>!e.columnName||!e.columnName.trim()).length>0)throw Error("All columns must have valid names");if(o&&o.filter(e=>!e.rowsName||!e.rowsName.trim()).length>0)throw Error("All rows must have valid names");let n=r.map(e=>({columnName:e.columnName,parentColumnId:e.parentColumnId})),{data:s}=await a.A.post("/table-questions",{label:e,projectId:t,columns:n,rows:o||[]});if(!s||!s.success)throw Error((null==s?void 0:s.message)||"Failed to create table");return s.data}catch(e){throw console.error("Error creating table:",e),e.response&&(console.error("Response status:",e.response.status),console.error("Response data:",e.response.data),e.response.data&&e.response.data.message&&(e.message=e.response.data.message)),e}},s=async(e,t,r,o)=>{try{if(!t||!t.trim())throw Error("Table label is required");if(!e||isNaN(e))throw Error("Valid table ID is required");if(!r||!Array.isArray(r)||0===r.length)throw Error("At least one column is required");if(o&&!Array.isArray(o))throw Error("Rows must be an array if provided");if(r.filter(e=>!e.columnName||!e.columnName.trim()).length>0)throw Error("All columns must have valid names");if(o&&o.filter(e=>!e.rowsName||!e.rowsName.trim()).length>0)throw Error("All rows must have valid names");let n=new Map,s=new Map;for(let e of(r.forEach((e,t)=>{e.id&&n.set(e.id,e),s.set(t+1,e)}),r))if(e.parentColumnId){if(e.parentColumnId<=0)throw Error("Invalid parent column ID: ".concat(e.parentColumnId,". Must be a positive number."));let t=r.find(t=>t.id===e.parentColumnId);if(!t&&e.parentColumnId<=r.length&&(t=s.get(e.parentColumnId)),!t)throw Error("Parent column with ID/position ".concat(e.parentColumnId," not found in the columns array."));if(t.parentColumnId)throw Error("Cannot create more than 2 levels of nested columns (parent → child → grandchild)")}let l=r.map(e=>{let t={columnName:e.columnName.trim()};return e.id&&(t.id=e.id),void 0!==e.parentColumnId&&(t.parentColumnId=e.parentColumnId),t});try{let{data:r}=await a.A.patch("/table-questions/".concat(e),{label:t.trim(),columns:l,rows:o?o.map(e=>({...e,rowsName:e.rowsName.trim()})):[]});if(!r||!r.success)throw Error((null==r?void 0:r.message)||"Failed to update table");return r.data}catch(e){if(console.error("API error updating table:",e),e.response&&(console.error("Response status:",e.response.status),console.error("Response data:",e.response.data),e.response.data&&e.response.data.message))throw Error(e.response.data.message);throw e}}catch(e){if(console.error("Error updating table:",e),e.message)throw Error("Failed to update table: ".concat(e.message));throw Error("Failed to update table due to an unknown error")}}},99474:(e,t,r)=>{r.d(t,{T:()=>s});var a=r(95155),o=r(12115),n=r(53999);let s=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,a.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700 dark:bg-gray-900 dark:placeholder:text-gray-500",r),ref:t,...o})});s.displayName="Textarea"}}]);