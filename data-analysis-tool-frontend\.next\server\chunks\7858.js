"use strict";exports.id=7858,exports.ids=[7858],exports.modules={58857:(e,s,t)=>{t.d(s,{m:()=>y});var a=t(60687),r=t(43210),l=t(38587),i=t(26273),n=t(29494),d=t(71845),o=t(16189),c=t(6986),m=t(21650),u=t(86429),x=t(11860),p=t(8693),b=t(54050),h=t(54864),j=t(19150);let f=[{label:"View Form ",value:"viewForm"},{label:"Edit Form",value:"editForm"},{label:"View Submissions",value:"viewSubmissions"},{label:"Edit submissions",value:"editSubmissions"},{label:"Add submissions",value:"addSubmissions"},{label:"Delete Submissions",value:"deleteSubmissions"},{label:"Validate Submissions",value:"validateSubmissions"},{label:"Manage Project",value:"manageProject"}],v=({onClose:e,projectId:s,onUserAdded:t})=>{let[l,i]=(0,r.useState)(""),[n,o]=(0,r.useState)([]),[c,m]=(0,r.useState)(""),[u,v]=(0,r.useState)(!1),[g,N]=(0,r.useState)(null),y=(0,p.jE)(),w=(0,h.wA)(),S=(0,r.useRef)(null),A=e=>/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(e),C=e=>{o(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},k=(0,b.n)({mutationFn:d.Oo,onSuccess:()=>{N(!0),m("")},onError:e=>{N(!1),m(e.response?.data?.message||"User with this email does not exist")},onSettled:()=>{v(!1)}}),U=()=>l?A(l)?g?0===n.length?(m("At least one permission must be selected"),!1):(m(""),!0):(m("User with this email does not exist"),!1):(m("Please enter a valid email"),!1):(m("Email is required"),!1),E=(0,b.n)({mutationFn:()=>{let e=n.reduce((e,s)=>(e[s]=!0,e),{});return(0,d.wI)({projectId:s,email:l,permissions:e})},onSuccess:()=>{y.invalidateQueries({queryKey:["projectUsers",s]}),w((0,j.Ds)({message:"User added to project successfully",type:"success"})),t&&t(),e()},onError:e=>{let s;s="string"==typeof e?e:e instanceof Error?e.message:e.response?.data?.message?"object"==typeof e.response.data.message?JSON.stringify(e.response.data.message):e.response.data.message:"Failed to add user",w((0,j.Ds)({message:s,type:"error"})),m(s)}});return(0,a.jsxs)("div",{className:"bg-neutral-100 p-6 rounded-md",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{className:`w-full border ${c?"border-red-500":"border-neutral-300"} rounded px-3 py-2 mb-4 focus:outline-none focus:ring-2 focus:ring-primary-200 placeholder:text-neutral-500`,placeholder:"Email address",value:l,onChange:e=>{let s=e.target.value;if(i(s),N(null),m(""),s){if(!A(s))return void m("Please enter a valid email address");S.current&&clearTimeout(S.current),S.current=setTimeout(()=>{v(!0),k.mutate(s)},800)}}}),(0,a.jsx)("button",{className:"absolute right-2 top-2 text-neutral-700 hover:text-neutral-900",onClick:e,type:"button",children:(0,a.jsx)(x.A,{size:22})}),u&&(0,a.jsx)("p",{className:"text-neutral-500 text-sm mb-2",children:"Verifying email..."}),!0===g&&(0,a.jsx)("p",{className:"text-green-500 text-sm mb-2",children:"User found"}),c&&(0,a.jsx)("p",{className:"text-red-500 text-sm mb-2",children:c})]}),(0,a.jsx)("div",{className:"flex flex-col gap-2",children:f.map(e=>(0,a.jsx)("div",{className:"flex flex-col",children:(0,a.jsxs)("label",{className:"flex items-center gap-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:n.includes(e.value),onChange:()=>C(e.value)}),e.label]})},e.value))}),(0,a.jsx)("button",{className:`mt-6 ${E.isPending||u?"bg-neutral-400":"bg-blue-400 hover:bg-blue-500"} text-white px-6 py-2 rounded disabled:opacity-50`,disabled:E.isPending||u||!l||0===n.length||!g,onClick:()=>{if(!s)return void m("Project ID is required");U()&&E.mutate()},children:E.isPending?"Adding...":"Grant permissions"})]})};var g=t(12810),N=t(24934);let y=({showModal:e,onClose:s,onShare:t,selectedProject:x})=>{let{hashedId:p}=(0,o.useParams)(),{user:b}=(0,m.A)(),[h,j]=(0,r.useState)(!1),f=p?(0,c.D)(p):null,y=x?.id||f,{data:w,isLoading:S}=(0,n.I)({queryKey:["project",y],queryFn:async()=>await (0,d.kf)({projectId:y}),enabled:!!y&&!!b?.id}),[A,C]=(0,r.useState)([]),[k,U]=(0,r.useState)(!1);if((0,r.useEffect)(()=>{let s=async()=>{if(y){U(!0);try{let e=await g.A.get(`/project-users/${y}`);if(e.data&&e.data.data&&e.data.data.AllUser){let s=e.data.data.AllUser||[];C(s)}else console.warn("No users data in response:",e.data),C([])}catch(e){console.error("Error fetching project users:",e),C([])}finally{U(!1)}}};e&&y&&s()},[y,e]),S)return(0,a.jsx)(u.A,{});let E=w||x;if(!E)return(0,a.jsx)(l.A,{isOpen:e,onClose:s,className:"p-6 rounded-md",children:(0,a.jsxs)("div",{className:"text-center py-4",children:[(0,a.jsx)("p",{className:"text-red-500",children:"Project not found"}),(0,a.jsx)(N.$,{onClick:s,className:"mt-4",children:"Close"})]})});let P=e=>{if(!e)return"bg-gray-500";let s=["bg-green-500","bg-blue-500","bg-red-500","bg-purple-500","bg-yellow-500","bg-pink-500","bg-indigo-500","bg-orange-500"];return s[e.charCodeAt(0)%s.length]},F=e=>e?e.charAt(0).toUpperCase():"?";return(0,a.jsxs)(l.A,{isOpen:e,onClose:s,className:"p-6 rounded-md",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:`Sharing Project: ${E.name||""}`}),(0,a.jsxs)("div",{className:"w-2xl mt-4 p-4 max-h-[500px] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("div",{className:"text-xl font-semibold",children:"Who has access"}),(0,a.jsxs)("div",{className:"flex items-center border rounded-md px-3 py-1.5 cursor-pointer hover:bg-gray-50",onClick:()=>j(!0),children:[(0,a.jsx)(i._rf,{size:18,className:"mr-2"}),(0,a.jsx)("div",{className:"text-sm",children:"Add user"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[E.user&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:`w-10 h-10 rounded-full ${P(E.user.name)} flex items-center justify-center text-neutral-100 font-medium mr-3`,children:F(E.user.name)}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"font-medium",children:E.user.name||E.user.email||"Unknown User"}),(0,a.jsx)("div",{className:"inline-block bg-gray-100 text-xs px-2 py-0.5 rounded",children:"Owner"})]})]}),k?(0,a.jsx)("div",{className:"py-2 text-center",children:(0,a.jsx)("div",{className:"inline-block w-6 h-6 rounded-full border-2 border-t-transparent border-primary-500 animate-spin"})}):A&&A.length>0?A.map((e,s)=>{let t=e.user&&e.user.name||e.user&&e.user.email||`User ${e.userId}`;return(0,a.jsxs)("div",{className:"flex items-center mt-4",children:[(0,a.jsx)("div",{className:`w-10 h-10 rounded-full ${P(t)} flex items-center justify-center text-neutral-100 font-medium mr-3`,children:F(t)}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"font-medium",children:t}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1 mt-1",children:e.permission&&Object.entries(e.permission).filter(([e,s])=>!0===s).map(([e])=>(0,a.jsx)("div",{className:"inline-block bg-gray-100 text-xs px-2 py-0.5 rounded",children:"viewForm"===e?"View Form":"editForm"===e?"Edit Form":"viewSubmissions"===e?"View Submissions":"editSubmissions"===e?"Edit Submissions":"addSubmissions"===e?"Add Submissions":"deleteSubmissions"===e?"Delete Submissions":"validateSubmissions"===e?"Validate Submissions":"manageProject"===e?"Manage Project":e},e))})]})]},s)}):null]}),h&&y&&(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsx)(v,{onClose:()=>j(!1),projectId:y,onUserAdded:()=>{(async()=>{U(!0);try{let e=await g.A.get(`/project-users/${y}`);if(e.data&&e.data.data&&e.data.data.AllUser){let s=e.data.data.AllUser||[];C(s)}else C([])}catch(e){console.error("Error fetching project users:",e),C([])}finally{U(!1)}})()}})}),(0,a.jsx)("div",{className:"mt-8 border-t pt-6",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"Anonymous submissions"}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-1",children:"Allow submissions without a username and password"})]}),(0,a.jsx)("div",{className:"w-12 h-6 bg-gray-200 rounded-full relative cursor-pointer",children:(0,a.jsx)("div",{className:"w-5 h-5 bg-neutral-100 rounded-full absolute top-0.5 left-0.5 shadow"})})]})}),(0,a.jsx)("div",{className:"mt-8",children:(0,a.jsx)("div",{className:"inline-block border rounded-md px-4 py-2 text-sm cursor-pointer hover:bg-gray-50",children:"Copy team from another project"})})]})]})}},73678:(e,s,t)=>{t.d(s,{R:()=>l});var a=t(60687);t(43210);var r=t(38587);let l=({showModal:e,onClose:s,onConfirm:t,title:l,description:i,confirmButtonText:n,cancelButtonText:d,confirmButtonClass:o,children:c})=>(0,a.jsxs)(r.A,{isOpen:e,onClose:s,className:"p-6 rounded-md max-w-xl",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:l}),(0,a.jsx)("div",{className:"text-neutral-700 mt-2",children:i}),c&&(0,a.jsx)("div",{className:"mt-6 space-y-4",children:c}),(0,a.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,a.jsx)("button",{className:"btn-outline",onClick:s,type:"button",children:d||"Cancel"}),(0,a.jsx)("button",{className:`font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ${o}`,onClick:t,type:"button",children:n})]})]})}};