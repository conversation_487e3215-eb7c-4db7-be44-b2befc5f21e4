(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7911],{25784:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let s=a(23464).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});s.interceptors.request.use(e=>e,e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let r=s},38796:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>ee});var s=a(95155),r=a(12115),n=a(50741),l=a(84355),i=a(51154);function o(e){let{className:t="",variant:a="default",...r}=e;return(0,s.jsx)("div",{className:"inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold ".concat({default:"bg-blue-500 text-neutral-100",secondary:"bg-gray-500 text-neutral-100",destructive:"bg-red-500 text-neutral-100",outline:"bg-transparent text-gray-700 border border-gray-300"}[a]," ").concat(t),...r})}function c(e){let{className:t="",...a}=e;return(0,s.jsx)("div",{className:"rounded-lg border border-gray-200 bg-neutral-100 shadow-sm ".concat(t),...a})}function d(e){let{className:t="",...a}=e;return(0,s.jsx)("div",{className:"flex flex-col space-y-1.5 p-6 ".concat(t),...a})}function u(e){let{className:t="",...a}=e;return(0,s.jsx)("h3",{className:"text-2xl font-semibold leading-none tracking-tight ".concat(t),...a})}function m(e){let{className:t="",...a}=e;return(0,s.jsx)("div",{className:"p-6 pt-0 ".concat(t),...a})}let x=(0,r.createContext)(void 0);function h(){let e=(0,r.useContext)(x);if(!e)throw Error("Tabs components must be used within a Tabs component");return e}function p(e){let{defaultValue:t,children:a,className:n=""}=e,[l,i]=(0,r.useState)(t);return(0,s.jsx)(x.Provider,{value:{activeTab:l,setActiveTab:i},children:(0,s.jsx)("div",{className:n,children:a})})}function b(e){let{children:t,className:a=""}=e;return(0,s.jsx)("div",{className:"inline-flex h-10 items-center justify-center rounded-md bg-gray-100 p-1 ".concat(a),children:t})}function f(e){let{value:t,children:a,className:r=""}=e,{activeTab:n,setActiveTab:l}=h(),i=n===t;return(0,s.jsx)("button",{className:"inline-flex items-center justify-center px-3 py-1.5 text-sm font-medium\n        ".concat(i?"bg-neutral-100 text-black shadow-sm":"text-gray-600","\n        ").concat(r),onClick:()=>l(t),children:a})}function g(e){let{value:t,children:a,className:r=""}=e,{activeTab:n}=h();return n!==t?null:(0,s.jsx)("div",{className:"mt-2 ".concat(r),children:a})}var j=a(69074),v=a(63008),y=a(53999),N=a(97168),w=a(42355),k=a(13052),S=a(84949),C=a(97972);function A(){let{previousMonth:e,nextMonth:t,goToMonth:a}=(0,S.cq)();return(0,s.jsxs)("div",{className:"space-x-1 flex items-center",children:[(0,s.jsx)("button",{type:"button",onClick:()=>e&&a(e),className:"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",children:(0,s.jsx)(w.A,{className:"h-4 w-4"})}),(0,s.jsx)("button",{type:"button",onClick:()=>t&&a(t),className:"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",children:(0,s.jsx)(k.A,{className:"h-4 w-4"})})]})}function L(e){let{className:t,classNames:a,showOutsideDays:r=!0,...n}=e;return(0,s.jsx)(C.h,{showOutsideDays:r,className:(0,y.cn)("p-4 bg-neutral-100 rounded-2xl shadow-xl max-w-md mx-auto border border-gray-100",t),components:{Nav:A},...n})}a(39303),L.displayName="Calendar";var D=a(49956);let R=D.bL,z=D.l9,E=r.forwardRef((e,t)=>{let{className:a,align:r="center",sideOffset:n=4,...l}=e;return(0,s.jsx)(D.ZL,{children:(0,s.jsx)(D.UC,{ref:t,align:r,sideOffset:n,className:(0,y.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...l})})});function P(e){let{value:t,onChange:a,className:r}=e;return(0,s.jsx)("div",{className:(0,y.cn)("grid gap-2",r),children:(0,s.jsxs)(R,{children:[(0,s.jsx)(z,{asChild:!0,children:(0,s.jsxs)(N.$,{id:"date",variant:"outline",className:(0,y.cn)("w-[300px] justify-start text-left font-normal",!t&&"text-muted-foreground"),children:[(0,s.jsx)(j.A,{className:"mr-2 h-4 w-4"}),(null==t?void 0:t.from)?t.to?(0,s.jsxs)(s.Fragment,{children:[(0,v.GP)(t.from,"LLL dd, y")," -"," ",(0,v.GP)(t.to,"LLL dd, y")]}):(0,v.GP)(t.from,"LLL dd, y"):(0,s.jsx)("span",{children:"Pick a date range"})]})}),(0,s.jsx)(E,{className:"w-auto p-0",align:"start",children:(0,s.jsx)(L,{initialFocus:!0,mode:"range",defaultMonth:null==t?void 0:t.from,selected:t,onSelect:a,numberOfMonths:2})})]})})}E.displayName=D.UC.displayName;var F=a(35695),B=a(25784),_=a(83540),T=a(8782),O=a(34e3),I=a(54811),M=a(94517),U=a(24026),K=a(3401),V=a(94754),q=a(96025),Q=a(16238),W=a(83394),G=a(56811);let Y=e=>{let{children:t,content:a,side:r="top",align:n="center",...l}=e;return(0,s.jsx)(G.Kq,{children:(0,s.jsxs)(G.bL,{delayDuration:0,children:[(0,s.jsx)(G.l9,{asChild:!0,children:t}),(0,s.jsx)(G.ZL,{children:(0,s.jsxs)(G.UC,{side:r,align:n,className:(0,y.cn)("z-50 rounded bg-black px-2 py-1 text-xs text-neutral-100 shadow-md animate-fade-in-up","data-[state=delayed-open]:data-[side=top]:animate-slide-in-from-bottom-2"),children:[a,(0,s.jsx)(G.i3,{className:"fill-black"})]})})]})})},$=["#0088FE","#00C49F","#FFBB28","#FF8042","#8884D8","#82CA9D","#FFC658","#FF6B6B"],Z=[{value:"verticalBar",label:"Vertical Bar",icon:n.A},{value:"pie",label:"Pie",icon:l.A},{value:"donut",label:"Donut",icon:l.A}],X=e=>{let{data:t,donut:a=!1}=e,r=t.labels.map((e,a)=>({name:e,value:t.values[a]}));return(0,s.jsx)("div",{className:"h-[400px] w-full",children:(0,s.jsx)(_.u,{children:(0,s.jsxs)(T.r,{children:[(0,s.jsx)(O.F,{data:r,dataKey:"value",nameKey:"name",cx:"50%",cy:"50%",outerRadius:150,innerRadius:80*!!a,fill:"#8884d8",label:e=>{let{name:t,percent:a}=e;return"".concat(t," (").concat((100*a).toFixed(0),"%)")},children:r.map((e,t)=>(0,s.jsx)(I.f,{fill:$[t%$.length]},"cell-".concat(t)))}),(0,s.jsx)(M.m,{formatter:e=>["".concat(e," responses"),"Count"]}),(0,s.jsx)(U.s,{})]})})})},H=e=>{let{data:t,layout:a="verticalBar",questionType:r}=e,n=t.labels.map((e,a)=>({name:e,value:t.values[a]})),l="table"===r?"horizontalBar":a,i="table"===r&&n.length>10?n.slice(0,10):n;return(0,s.jsx)("div",{className:"h-[400px] w-full",children:(0,s.jsx)(_.u,{children:(0,s.jsxs)(K.E,{data:i,layout:"horizontalBar"===l?"vertical":"horizontal",margin:{top:20,right:30,left:20,bottom:5},children:[(0,s.jsx)(V.d,{strokeDasharray:"3 3"}),(0,s.jsx)(q.W,{dataKey:"name",type:"horizontalBar"===l?"number":"category",tick:{fontSize:12}}),(0,s.jsx)(Q.h,{type:"horizontalBar"===l?"category":"number",tick:{fontSize:12},width:"table"===r?150:60}),(0,s.jsx)(M.m,{formatter:e=>["".concat(e," responses"),"Count"]}),(0,s.jsx)(U.s,{}),(0,s.jsx)(W.y,{dataKey:"value",fill:"#8884d8",children:i.map((e,t)=>(0,s.jsx)(I.f,{fill:$[t%$.length]},"cell-".concat(t)))})]})})})},J=e=>{let{report:t}=e,{question:a,type:n,answered:l,total:i,table:x,chartData:h,stats:j}=t,v=i>0?Math.round(l/i*100):0,[y,N]=(0,r.useState)("table"===n?"verticalBar":"pie"),w="selectone"===n||"selectmany"===n,k=()=>{if("table"===n&&!Array.isArray(x)&&x.structure){let{structure:e,data:t,cellValues:r,metadata:n}=x,l=e.columns.filter(e=>null===e.parentId),i=new Map;return r&&("object"!=typeof r||Array.isArray(r)?r instanceof Map&&(i=r):Object.entries(r).forEach(e=>{let[t,a]=e;i.set(t,a)})),t&&t.length>0&&t.forEach(e=>{if(e.rowId&&e.columnId){let t="".concat(e.rowId,"_").concat(e.columnId);e.cellValue&&i.set(t,e.cellValue)}}),0===i.size&&e.rows.forEach(t=>{e.columns.forEach(e=>{if(null===e.parentId){let a="".concat(t.id,"_").concat(e.id);i.set(a,"Test value for ".concat(t.name," - ").concat(e.name))}})}),(0,s.jsxs)("div",{className:"overflow-auto",children:[(0,s.jsxs)("div",{className:"mb-2 p-2 bg-blue-50 rounded-md",children:[(0,s.jsxs)("p",{className:"text-sm text-blue-700",children:[(0,s.jsx)("span",{className:"font-medium",children:"Matrix Question:"})," This table shows the submitted responses for each row and column."]}),n&&(0,s.jsxs)("div",{className:"mt-1 text-xs text-blue-600 flex items-center justify-between",children:[(0,s.jsx)("span",{children:n.hasRealData?"Showing data from ".concat(n.submissionsWithData," of ").concat(n.totalSubmissions," submissions"):"No submissions found for this table"}),(0,s.jsxs)("span",{children:["Last updated:"," ",new Date(n.lastUpdated).toLocaleString()]})]})]}),(0,s.jsxs)("table",{className:"w-full my-4 text-sm border-collapse shadow-sm",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b bg-gray-100",children:[(0,s.jsx)("th",{className:"pb-3 pt-3 text-left border p-2 bg-gray-50 font-semibold text-gray-700",children:a}),l.map(e=>(0,s.jsx)("th",{className:"pb-3 pt-3 text-center border p-2 bg-gray-50 font-semibold text-gray-700",children:e.name},e.id))]})}),(0,s.jsx)("tbody",{children:e.rows.map((e,t)=>(0,s.jsxs)("tr",{className:"border-b last:border-0 ".concat(t%2==0?"bg-white":"bg-gray-50"," hover:bg-blue-50 transition-colors duration-150"),children:[(0,s.jsx)("td",{className:"py-3 border p-3 font-medium text-gray-700",children:e.name}),l.map(t=>{if(t.children&&t.children.length>0)return(0,s.jsx)("td",{className:"py-3 border p-3 text-center",children:t.children.map(t=>{let a="".concat(e.id,"_").concat(t.id),n=r instanceof Map?r.get(a):"object"!=typeof r||Array.isArray(r)?i.get(a):r[a];return(0,s.jsxs)("div",{className:"mb-2 p-1 rounded hover:bg-blue-100",children:[(0,s.jsx)("span",{className:"font-medium text-xs text-gray-600 block mb-1",children:t.name}),(0,s.jsx)("span",{className:"text-gray-800",children:void 0!==n&&""!==n?n.includes(",")||n.includes("(")?(0,s.jsx)("span",{className:"text-xs",children:n.split(", ").map((e,t)=>(0,s.jsx)("span",{className:"inline-block mr-1 mb-1 px-1 py-0.5 rounded ".concat(e.includes("(")?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-700"),children:e},t))}):n:"-"})]},t.id)})},t.id);{let a="".concat(e.id,"_").concat(t.id),n=r instanceof Map?r.get(a):"object"!=typeof r||Array.isArray(r)?i.get(a):r[a];return(0,s.jsx)("td",{className:"py-3 border p-3 text-center ".concat(n&&"-"!==n?"text-gray-800 font-medium":"text-gray-400"),children:void 0!==n&&""!==n?n.includes(",")||n.includes("(")?(0,s.jsx)("span",{className:"text-xs",children:n.split(", ").map((e,t)=>(0,s.jsx)("span",{className:"inline-block mr-1 mb-1 px-1 py-0.5 rounded ".concat(e.includes("(")?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-700"),children:e},t))}):n:"-"},t.id)}})]},e.id))})]})]})}return(0,s.jsx)("div",{className:"overflow-auto",children:(0,s.jsxs)("table",{className:"w-full my-4 text-sm",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b",children:[(0,s.jsx)("th",{className:"pb-2 text-left",children:"Value"}),(0,s.jsx)("th",{className:"pb-2 text-right",children:"Count"}),(0,s.jsx)("th",{className:"pb-2 text-right",children:"Percentage"})]})}),(0,s.jsx)("tbody",{children:Array.isArray(x)&&x.map((e,t)=>(0,s.jsxs)("tr",{className:"border-b last:border-0",children:[(0,s.jsx)("td",{className:"py-2",children:e.value||"No Answer"}),(0,s.jsx)("td",{className:"py-2 text-right",children:e.frequency}),(0,s.jsxs)("td",{className:"py-2 text-right",children:[e.percentage,"%"]})]},t))})]})})};return(0,s.jsxs)(c,{className:"mb-6",children:[(0,s.jsx)(d,{children:(0,s.jsxs)("div",{className:"flex flex-col space-y-2 md:space-y-0 md:flex-row md:justify-between md:items-center",children:[(0,s.jsx)(u,{className:"text-base",children:a}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)(o,{variant:v>75?"default":v>50?"secondary":"outline",children:[l," of ",i," responses (",v,"%)"]}),(0,s.jsx)(o,{variant:"outline",children:n})]})]})}),(0,s.jsx)(m,{children:w?(0,s.jsxs)(p,{defaultValue:"table",children:[(0,s.jsxs)(b,{className:"grid w-full grid-cols-2",children:[(0,s.jsx)(f,{value:"table",children:"Table"}),(0,s.jsx)(f,{value:"chart",children:"Chart"})]}),(0,s.jsx)(g,{value:"table",children:k()}),(0,s.jsxs)(g,{value:"chart",children:[(0,s.jsx)("div",{className:"flex items-center space-x-2 mb-4",children:Z.map(e=>{let t=e.icon,a="verticalBar"===e.value?{transform:"rotate(90deg)"}:{};return(0,s.jsx)(Y,{content:e.label,side:"top",children:(0,s.jsx)("button",{onClick:()=>N(e.value),className:"p-2 rounded-full border transition-colors duration-150 ".concat(y===e.value?"bg-primary text-neutral-100 border-primary":"bg-muted text-muted-foreground border-transparent"),style:a,children:(0,s.jsx)(t,{size:20})})},e.value)})}),(()=>{switch(y){case"verticalBar":return(0,s.jsx)(H,{data:h,layout:"verticalBar",questionType:n});case"pie":default:return(0,s.jsx)(X,{data:h});case"donut":return(0,s.jsx)(X,{data:h,donut:!0})}})()]})]}):(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-sm font-medium mb-4",children:"Response Data"}),k()]})})]})};function ee(){let[e,t]=(0,r.useState)(!0),[a,n]=(0,r.useState)(null),[l,o]=(0,r.useState)(null),[x,h]=(0,r.useState)(),p=(0,F.usePathname)(),b=async(e,a)=>{var s,r,l,i,c,d;try{t(!0),n(null);let r=p.split("/"),l=r[r.indexOf("project")+1];if(!l)throw Error("Project ID not found in URL");let i=new URLSearchParams;e&&i.append("startDate",e.toISOString()),a&&i.append("endDate",a.toISOString());let c=await B.A.get("/projects/".concat(l,"/report?").concat(i.toString()));if(null==(s=c.data)?void 0:s.data)o(c.data.data);else throw Error("Invalid response format from server")}catch(e){console.error("Error fetching report data:",e),"ERR_NETWORK"===e.code?n("Unable to connect to the server. Please make sure the backend server is running."):(null==(r=e.response)?void 0:r.status)===401?n("You are not authorized to view this report. Please log in again."):(null==(l=e.response)?void 0:l.status)===403?n("You don't have permission to view this report."):(null==(i=e.response)?void 0:i.status)===404?n("Report not found. The project may have been deleted or you don't have access."):n((null==(d=e.response)||null==(c=d.data)?void 0:c.message)||e.message||"Failed to load report data. Please try again later.")}finally{t(!1)}};return((0,r.useEffect)(()=>{b()},[p]),e)?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-[calc(100vh-200px)]",children:[(0,s.jsx)(i.A,{className:"w-10 h-10 animate-spin text-primary"}),(0,s.jsx)("p",{className:"mt-4 text-lg",children:"Loading report data..."})]}):a||!l?(0,s.jsx)("div",{className:"p-4 md:p-8",children:(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Data Report"}),(0,s.jsxs)("div",{className:"p-8 mt-4 text-center border rounded-md border-gray-200 bg-gray-50",children:[(0,s.jsx)("p",{className:"text-lg text-red-500",children:a||"No data available"}),(0,s.jsx)("p",{className:"mt-2 text-gray-600",children:!a&&"Submit some form responses to generate a report."})]})]})}):(0,s.jsxs)("div",{className:"p-4 md:p-8",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("div",{className:"flex flex-col space-y-4 md:space-y-0 md:flex-row md:justify-between md:items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Data Report"}),(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:l.metadata.projectName})]}),(0,s.jsx)(P,{value:x,onChange:e=>{h(e),(null==e?void 0:e.from)&&(null==e?void 0:e.to)&&b(e.from,e.to)}})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-4 mt-6 md:grid-cols-3",children:[(0,s.jsxs)(c,{children:[(0,s.jsx)(d,{children:(0,s.jsx)(u,{className:"text-sm font-medium",children:"Total Submissions"})}),(0,s.jsx)(m,{children:(0,s.jsx)("p",{className:"text-2xl font-bold",children:l.summary.totalSubmissions})})]}),(0,s.jsxs)(c,{children:[(0,s.jsx)(d,{children:(0,s.jsx)(u,{className:"text-sm font-medium",children:"Total Questions"})}),(0,s.jsx)(m,{children:(0,s.jsx)("p",{className:"text-2xl font-bold",children:l.summary.totalQuestions})})]}),(0,s.jsxs)(c,{children:[(0,s.jsx)(d,{children:(0,s.jsx)(u,{className:"text-sm font-medium",children:"Response Rate"})}),(0,s.jsx)(m,{children:(0,s.jsxs)("p",{className:"text-2xl font-bold",children:[l.summary.averageResponseRate,"%"]})})]})]})]}),(0,s.jsx)("div",{children:l.data.map((e,t)=>(0,s.jsx)(J,{report:e},t))})]})}},53999:(e,t,a)=>{"use strict";a.d(t,{Y:()=>l,cn:()=>n});var s=a(52596),r=a(39688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"short";if(!e)return"";try{let a="string"==typeof e?new Date(e):e;if(isNaN(a.getTime()))return"";switch(t){case"short":return a.toLocaleDateString(void 0,{year:"numeric",month:"short",day:"numeric"});case"long":return a.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});case"full":return a.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",weekday:"long",hour:"2-digit",minute:"2-digit",second:"2-digit"});default:return a.toLocaleDateString()}}catch(t){return console.error("Error formatting date:",t),String(e)}}},89742:(e,t,a)=>{Promise.resolve().then(a.bind(a,38796))},97168:(e,t,a)=>{"use strict";a.d(t,{$:()=>o});var s=a(95155);a(12115);var r=a(99708),n=a(74466),l=a(53999);let i=(0,n.F)("inline-flex items-center justify-center gap-2 neutral-100space-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-neutral-100 shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:n,asChild:o=!1,...c}=e,d=o?r.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,l.cn)(i({variant:a,size:n,className:t})),...c})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8638,1445,4277,556,1467,4617,1198,8441,1684,7358],()=>t(89742)),_N_E=e.O()}]);