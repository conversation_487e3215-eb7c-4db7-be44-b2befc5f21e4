(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1053],{381:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},25784:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let r=s(23464).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let i=r},29350:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var r=s(97381),i=s(59362),a=s(25784),n=s(35695),l=s(12115),o=s(34540);let h=e=>{let t=(0,o.wA)(),s=(0,n.useRouter)(),h=(0,n.usePathname)(),{status:u,user:d,error:c}=(0,o.d4)(e=>e.auth),p=async()=>{try{t((0,r.Le)());let e=(await a.A.get("/users/me")).data;t((0,r.tQ)(e))}catch(a){if(t((0,r.x9)()),(0,i.F0)(a)){var e,n,l,o,u;if(console.error("Auth error:",null==(e=a.response)?void 0:e.status,null==(n=a.response)?void 0:n.data),(null==(l=a.response)?void 0:l.status)===401){if(h.startsWith("/form-submission"))return;s.push("/")}else t((0,r.jB)((null==(u=a.response)||null==(o=u.data)?void 0:o.message)||a.message))}else t((0,r.jB)(a instanceof Error?a.message:"An unknown error occurred."))}};return(0,l.useEffect)(()=>{(null==e?void 0:e.skipFetchUser)||p()},[null==e?void 0:e.skipFetchUser]),(0,l.useEffect)(()=>{let e=e=>{if("logout"===e.key&&"true"===e.newValue)if(t((0,r.x9)()),h.startsWith("/form-submission")){let e=h.split("/")[2];e?s.push("/form-submission/".concat(e,"/sign-in")):s.push("/")}else s.push("/")};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[t,s,h]),{status:u,user:d,error:c,isAuthenticated:"authenticated"===u,isLoading:"loading"===u,refreshAuthState:()=>{p()},signin:async(e,t,s)=>{try{await a.A.post("/users/login",e),await p(),null==t||t()}catch(e){if(e instanceof i.pe){var r,n;let t=null==(n=e.response)||null==(r=n.data)?void 0:r.errorType;null==s||s(t)}else null==s||s()}},logout:async()=>{try{await a.A.post("/users/logout"),localStorage.setItem("logout","true"),setTimeout(()=>localStorage.removeItem("logout"),100)}finally{if(t((0,r.x9)()),h.startsWith("/form-submission")){let e=h.split("/")[2];e?s.push("/form-submission/".concat(e,"/sign-in")):s.push("/")}else s.push("/")}}}}},35169:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},39716:(e,t,s)=>{"use strict";s.d(t,{F:()=>l});var r=s(95155),i=s(35695),a=s(6874),n=s.n(a);s(12115);let l=e=>{let{items:t}=e,s=(0,i.usePathname)(),a=e=>s.startsWith(e);return(0,r.jsx)("div",{className:"border-y border-neutral-400 rounded-md bg-primary-500 my-4 shadow-md",children:(0,r.jsx)("div",{className:"flex items-center",children:t.map(e=>e.disabled?(0,r.jsxs)("div",{className:"flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-gray-400 cursor-not-allowed",children:[e.icon,e.label]},e.route):(0,r.jsxs)(n(),{href:e.route,className:"flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-neutral-100 ".concat(a(e.route)?"border-neutral-100":"border-transparent hover:border-neutral-400"),children:[e.icon,e.label]},e.route))})})}},41050:(e,t,s)=>{"use strict";s.d(t,{A:()=>y});let r=e=>[...new Set(e)],i=(e,t)=>e.filter(e=>!t.includes(e)),a=(e,t)=>e.filter(e=>t.includes(e)),n=e=>"bigint"==typeof e||!Number.isNaN(Number(e))&&Math.floor(Number(e))===e,l=e=>"bigint"==typeof e||e>=0&&Number.isSafeInteger(e);function o(e,t){let s;if(0===t.length)return e;let r=[...e];for(let e=r.length-1,i=0,a=0;e>0;e--,i++){i%=t.length,a+=s=t[i].codePointAt(0);let n=(s+i+a)%e,l=r[e],o=r[n];r[n]=l,r[e]=o}return r}let h=(e,t)=>{let s=[],r=e;if("bigint"==typeof r){let e=BigInt(t.length);do s.unshift(t[Number(r%e)]),r/=e;while(r>BigInt(0))}else do s.unshift(t[r%t.length]),r=Math.floor(r/t.length);while(r>0);return s},u=(e,t)=>e.reduce((s,r)=>{let i=t.indexOf(r);if(-1===i)throw Error(`The provided ID (${e.join("")}) is invalid, as it contains characters that do not exist in the alphabet (${t.join("")})`);if("bigint"==typeof s)return s*BigInt(t.length)+BigInt(i);let a=s*t.length+i;return Number.isSafeInteger(a)?a:(b("Unable to decode the provided string, due to lack of support for BigInt numbers in the current environment"),BigInt(s)*BigInt(t.length)+BigInt(i))},0),d=/^\+?\d+$/,c=e=>{if(!d.test(e))return Number.NaN;let t=Number.parseInt(e,10);return Number.isSafeInteger(t)?t:(b("Unable to encode the provided BigInt string without loss of information due to lack of support for BigInt type in the current environment"),BigInt(e))},p=(e,t,s)=>Array.from({length:Math.ceil(e.length/t)},(r,i)=>s(e.slice(i*t,(i+1)*t))),g=e=>new RegExp(e.map(e=>m(e)).sort((e,t)=>t.length-e.length).join("|")),f=e=>RegExp(`^[${e.map(e=>m(e)).sort((e,t)=>t.length-e.length).join("")}]+$`),m=e=>e.replace(/[\s#$()*+,.?[\\\]^{|}-]/g,"\\$&"),b=(e="BigInt is not available in this environment")=>{if("function"!=typeof BigInt)throw TypeError(e)};class y{constructor(e="",t=0,s="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890",n="cfhistuCFHISTU"){let l,h;if(this.minLength=t,"number"!=typeof t)throw TypeError(`Hashids: Provided 'minLength' has to be a number (is ${typeof t})`);if("string"!=typeof e)throw TypeError(`Hashids: Provided 'salt' has to be a string (is ${typeof e})`);if("string"!=typeof s)throw TypeError(`Hashids: Provided alphabet has to be a string (is ${typeof s})`);let u=Array.from(e),d=Array.from(s),c=Array.from(n);this.salt=u;let p=r(d);if(p.length<16)throw Error(`Hashids: alphabet must contain at least 16 unique characters, provided: ${p.join("")}`);this.alphabet=i(p,c);let m=a(c,p);this.seps=o(m,u),(0===this.seps.length||this.alphabet.length/this.seps.length>3.5)&&(l=Math.ceil(this.alphabet.length/3.5))>this.seps.length&&(h=l-this.seps.length,this.seps.push(...this.alphabet.slice(0,h)),this.alphabet=this.alphabet.slice(h)),this.alphabet=o(this.alphabet,u);let b=Math.ceil(this.alphabet.length/12);this.alphabet.length<3?(this.guards=this.seps.slice(0,b),this.seps=this.seps.slice(b)):(this.guards=this.alphabet.slice(0,b),this.alphabet=this.alphabet.slice(b)),this.guardsRegExp=g(this.guards),this.sepsRegExp=g(this.seps),this.allowedCharsRegExp=f([...this.alphabet,...this.guards,...this.seps])}encode(e,...t){let s=Array.isArray(e)?e:[...null!=e?[e]:[],...t];return 0===s.length?"":(s.every(n)||(s=s.map(e=>"bigint"==typeof e||"number"==typeof e?e:c(String(e)))),s.every(l))?this._encode(s).join(""):""}decode(e){return e&&"string"==typeof e&&0!==e.length?this._decode(e):[]}encodeHex(e){let t=e;switch(typeof t){case"bigint":t=t.toString(16);break;case"string":if(!/^[\dA-Fa-f]+$/.test(t))return"";break;default:throw Error(`Hashids: The provided value is neither a string, nor a BigInt (got: ${typeof t})`)}let s=p(t,12,e=>Number.parseInt(`1${e}`,16));return this.encode(s)}decodeHex(e){return this.decode(e).map(e=>e.toString(16).slice(1)).join("")}isValidId(e){return this.allowedCharsRegExp.test(e)}_encode(e){let{alphabet:t}=this,s=e.reduce((e,t,s)=>e+("bigint"==typeof t?Number(t%BigInt(s+100)):t%(s+100)),0),r=[t[s%t.length]],i=[...r],{seps:a}=this,{guards:n}=this;if(e.forEach((s,n)=>{let l=i.concat(this.salt,t),u=h(s,t=o(t,l));if(r.push(...u),n+1<e.length){let e=u[0].codePointAt(0)+n,t="bigint"==typeof s?Number(s%BigInt(e)):s%e;r.push(a[t%a.length])}}),r.length<this.minLength){let e=(s+r[0].codePointAt(0))%n.length;if(r.unshift(n[e]),r.length<this.minLength){let e=(s+r[2].codePointAt(0))%n.length;r.push(n[e])}}let l=Math.floor(t.length/2);for(;r.length<this.minLength;){t=o(t,t),r.unshift(...t.slice(l)),r.push(...t.slice(0,l));let e=r.length-this.minLength;if(e>0){let t=e/2;r=r.slice(t,t+this.minLength)}}return r}_decode(e){if(!this.isValidId(e))throw Error(`The provided ID (${e}) is invalid, as it contains characters that do not exist in the alphabet (${this.guards.join("")}${this.seps.join("")}${this.alphabet.join("")})`);let t=e.split(this.guardsRegExp),s=+(3===t.length||2===t.length),r=t[s];if(0===r.length)return[];let i=r[Symbol.iterator]().next().value,a=r.slice(i.length).split(this.sepsRegExp),n=this.alphabet,l=[];for(let e of a){let t=[i,...this.salt,...n],s=o(n,t.slice(0,n.length));l.push(u(Array.from(e),s)),n=s}return this._encode(l).join("")!==e?[]:l}}},45072:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(95155),i=s(57799),a=s(49992),n=s(381),l=s(35695),o=s(39716);let h=()=>{let{hashedId:e}=(0,l.useParams)(),t=[{label:"Form Builder",icon:(0,r.jsx)(a.A,{size:16}),route:"/library/template/".concat(e,"/form-builder")},{label:"Settings",icon:(0,r.jsx)(n.A,{size:16}),route:"/library/template/".concat(e,"/settings")}];return(0,r.jsx)(o.F,{items:t})};var u=s(29350),d=s(94974),c=s(88570),p=s(19373),g=s(35169),f=s(6874),m=s.n(f),b=s(12115);let y=e=>{let{children:t}=e,[s,a]=(0,b.useState)(!1);(0,b.useEffect)(()=>{a(!0)},[]);let{hashedId:n}=(0,l.useParams)(),o=(0,c.D)(n),{user:f}=(0,u.A)(),{data:y,isLoading:v,isError:x}=(0,p.I)({queryKey:["templates",null==f?void 0:f.id,o],queryFn:()=>(0,d.J2)({templateId:o}),enabled:!!o&&!!(null==f?void 0:f.id)});return s?v?(0,r.jsx)(i.A,{}):n&&null!==o?x?(0,r.jsx)("p",{className:"text-red-500",children:"Failed to fetch template. Please try again"}):(0,r.jsxs)("div",{className:"section flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h1",{className:"heading-text capitalize",children:null==y?void 0:y.name}),(0,r.jsxs)(m(),{href:"/library",className:"flex items-center gap-2",children:[(0,r.jsx)(g.A,{size:16}),"Back to library"]})]}),(0,r.jsx)(h,{}),(0,r.jsx)("div",{className:"px-8",children:t})]}):(0,r.jsxs)("div",{className:"error-message",children:[(0,r.jsx)("h1",{className:"text-red-500",children:"Error: Invalid Template ID (hashedId)."}),(0,r.jsx)("p",{className:"text-neutral-700",children:"Please make sure the URL contains a valid project identifier."})]}):null}},49992:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("file-pen",[["path",{d:"M12.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v9.5",key:"1couwa"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M13.378 15.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1y4qbx"}]])},57799:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(95155);s(12115);let i=()=>(0,r.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,r.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},59362:(e,t,s)=>{"use strict";s.d(t,{F0:()=>d,pe:()=>i});let{Axios:r,AxiosError:i,CanceledError:a,isCancel:n,CancelToken:l,VERSION:o,all:h,Cancel:u,isAxiosError:d,spread:c,toFormData:p,AxiosHeaders:g,HttpStatusCode:f,formToJSON:m,getAdapter:b,mergeConfig:y}=s(23464).A},75558:(e,t,s)=>{Promise.resolve().then(s.bind(s,45072))},88570:(e,t,s)=>{"use strict";s.d(t,{D:()=>l,l:()=>n});var r=s(41050);let i=s(49509).env.SALT||"rushan-salt",a=new r.A(i,12),n=e=>a.encode(e),l=e=>{let t=a.decode(e)[0];return"bigint"==typeof t?t<Number.MAX_SAFE_INTEGER?Number(t):null:"number"==typeof t?t:null}},94974:(e,t,s)=>{"use strict";s.d(t,{I7:()=>l,J2:()=>i,QK:()=>n,Xu:()=>a,nh:()=>o});var r=s(25784);let i=async e=>{let{templateId:t}=e,{data:s}=await r.A.get("/libraries/".concat(t));return s.template},a=async e=>{let{data:t}=await r.A.post("/libraries",e);return t},n=async()=>{let{data:e}=await r.A.get("/libraries");return e.templates},l=async e=>{let{data:t}=await r.A.delete("/libraries/".concat(e));return t},o=async e=>{let{templateIds:t}=e;return null}},97381:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>o,Le:()=>n,jB:()=>l,tQ:()=>i,x9:()=>a});let r=(0,s(51990).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,t)=>{e.status="authenticated",e.user=t.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,t)=>{e.status="unauthenticated",e.error=t.payload,e.user=null}}}),{setAuthenticatedUser:i,setUnauthenticated:a,setAuthLoading:n,setAuthError:l}=r.actions,o=r.reducer}},e=>{var t=t=>e(e.s=t);e.O(0,[635,1445,6967,6903,6874,8441,1684,7358],()=>t(75558)),_N_E=e.O()}]);