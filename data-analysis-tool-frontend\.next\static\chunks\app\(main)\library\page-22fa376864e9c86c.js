(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9895],{19815:(e,t,a)=>{Promise.resolve().then(a.bind(a,91426))},26862:(e,t,a)=>{"use strict";a.d(t,{Sc:()=>h.S,dO:()=>m}),a(97168),a(89852),a(82714),a(99474);var r=a(95155),s=a(12115),l=a(38715),n=a(66474),i=a(47863),o=a(5196),d=a(53999);l.bL,l.YJ,l.WT,s.forwardRef((e,t)=>{let{className:a,children:s,...i}=e;return(0,r.jsxs)(l.l9,{ref:t,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm ring-offset-neutral-100 placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 dark:border-gray-700 dark:bg-gray-900 dark:ring-offset-gray-900 dark:placeholder:text-gray-500",a),...i,children:[s,(0,r.jsx)(l.In,{asChild:!0,children:(0,r.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})}).displayName=l.l9.displayName;let c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(l.PP,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,r.jsx)(i.A,{className:"h-4 w-4"})})});c.displayName=l.PP.displayName;let u=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(l.wn,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,r.jsx)(n.A,{className:"h-4 w-4"})})});u.displayName=l.wn.displayName,s.forwardRef((e,t)=>{let{className:a,children:s,position:n="popper",...i}=e;return(0,r.jsx)(l.ZL,{children:(0,r.jsxs)(l.UC,{ref:t,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border border-gray-200 bg-neutral-100 text-slate-700 shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:border-gray-700 dark:bg-gray-900 dark:text-slate-200","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:n,...i,children:[(0,r.jsx)(c,{}),(0,r.jsx)(l.LM,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,r.jsx)(u,{})]})})}).displayName=l.UC.displayName,s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(l.JU,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...s})}).displayName=l.JU.displayName,s.forwardRef((e,t)=>{let{className:a,children:s,...n}=e;return(0,r.jsxs)(l.q7,{ref:t,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-gray-100 focus:text-gray-900 data-[disabled]:pointer-events-none data-[disabled]:opacity-50 dark:focus:bg-gray-800 dark:focus:text-gray-50",a),...n,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(l.VF,{children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})}),(0,r.jsx)(l.p4,{children:s})]})}).displayName=l.q7.displayName,s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(l.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-gray-200 dark:bg-gray-700",a),...s})}).displayName=l.wv.displayName;var p=a(4884);let m=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(p.bL,{className:(0,d.cn)("peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 focus-visible:ring-offset-neutral-100 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 dark:focus-visible:ring-offset-gray-900",a),...s,ref:t,children:(0,r.jsx)(p.zi,{className:(0,d.cn)("pointer-events-none block h-5 w-5 rounded-full shadow-lg ring-0 transition-transform data-[state=checked]:bg-neutral-100 data-[state=unchecked]:bg-primary-500 data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});m.displayName=p.bL.displayName;var h=a(95139);a(55747)},29350:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var r=a(97381),s=a(59362),l=a(25784),n=a(35695),i=a(12115),o=a(34540);let d=e=>{let t=(0,o.wA)(),a=(0,n.useRouter)(),d=(0,n.usePathname)(),{status:c,user:u,error:p}=(0,o.d4)(e=>e.auth),m=async()=>{try{t((0,r.Le)());let e=(await l.A.get("/users/me")).data;t((0,r.tQ)(e))}catch(l){if(t((0,r.x9)()),(0,s.F0)(l)){var e,n,i,o,c;if(console.error("Auth error:",null==(e=l.response)?void 0:e.status,null==(n=l.response)?void 0:n.data),(null==(i=l.response)?void 0:i.status)===401){if(d.startsWith("/form-submission"))return;a.push("/")}else t((0,r.jB)((null==(c=l.response)||null==(o=c.data)?void 0:o.message)||l.message))}else t((0,r.jB)(l instanceof Error?l.message:"An unknown error occurred."))}};return(0,i.useEffect)(()=>{(null==e?void 0:e.skipFetchUser)||m()},[null==e?void 0:e.skipFetchUser]),(0,i.useEffect)(()=>{let e=e=>{if("logout"===e.key&&"true"===e.newValue)if(t((0,r.x9)()),d.startsWith("/form-submission")){let e=d.split("/")[2];e?a.push("/form-submission/".concat(e,"/sign-in")):a.push("/")}else a.push("/")};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[t,a,d]),{status:c,user:u,error:p,isAuthenticated:"authenticated"===c,isLoading:"loading"===c,refreshAuthState:()=>{m()},signin:async(e,t,a)=>{try{await l.A.post("/users/login",e),await m(),null==t||t()}catch(e){if(e instanceof s.pe){var r,n;let t=null==(n=e.response)||null==(r=n.data)?void 0:r.errorType;null==a||a(t)}else null==a||a()}},logout:async()=>{try{await l.A.post("/users/logout"),localStorage.setItem("logout","true"),setTimeout(()=>localStorage.removeItem("logout"),100)}finally{if(t((0,r.x9)()),d.startsWith("/form-submission")){let e=d.split("/")[2];e?a.push("/form-submission/".concat(e,"/sign-in")):a.push("/")}else a.push("/")}}}}},34947:(e,t,a)=>{"use strict";a.d(t,{Af:()=>i,K4:()=>l,ae:()=>p,dI:()=>u,ej:()=>n,ku:()=>d,sr:()=>c,ul:()=>o});var r=a(25784);let s=e=>{if("project"===e)return"/questions";if("template"===e)return"/template-questions";if("questionBlock"===e)return"/question-blocks";throw Error("Unsupported context type")},l=async e=>{let{projectId:t}=e,{data:a}=await r.A.get("/questions/".concat(t));return a.questions},n=async e=>{let{templateId:t}=e,{data:a}=await r.A.get("/template-questions/".concat(t));return a.questions},i=async e=>{var t,a,l,n,i,o;let{contextType:d,contextId:c,dataToSend:u,position:p}=e,m="questionBlock"===d?"".concat(s(d)):"".concat(s(d),"/").concat(c);if(!u.label||!u.inputType)throw Error("Label and inputType are required");let h=["selectone","selectmany"].includes(u.inputType),g=u.file instanceof File,y=Array.isArray(u.questionOptions)&&u.questionOptions.length>0;if(h&&!g&&!y)throw Error("Options are required for select input types");if(g){let e=new FormData;e.append("label",u.label),e.append("isRequired",u.isRequired?"true":"false"),e.append("inputType",u.inputType),u.hint&&e.append("hint",u.hint),u.placeholder&&e.append("placeholder",u.placeholder),e.append("position",String(p||1)),e.append("file",u.file);try{let{data:t}=await r.A.post(m,e,{headers:{"Content-Type":"multipart/form-data"}});return t}catch(e){throw console.error("Upload error details:",(null==(t=e.response)?void 0:t.data)||e.message),Error("Failed to upload question with file: ".concat((null==(l=e.response)||null==(a=l.data)?void 0:a.message)||e.message))}}try{let{data:e}=await r.A.post(m,{label:u.label,isRequired:u.isRequired,hint:u.hint,placeholder:u.placeholder,inputType:u.inputType,questionOptions:u.questionOptions,position:p||1});return e}catch(e){throw console.error("API error details:",(null==(n=e.response)?void 0:n.data)||e.message),Error("Failed to add question: ".concat((null==(o=e.response)||null==(i=o.data)?void 0:i.message)||e.message))}},o=async e=>{let{contextType:t,id:a,projectId:l}=e,{data:n}=await r.A.delete("".concat(s(t),"/").concat(a,"?projectId=").concat(l));return n},d=async e=>{let{id:t,contextType:a,contextId:l}=e,{data:n}=await r.A.post("".concat(s(a),"/duplicate/").concat(t,"?projectId=").concat(l),"questionBlock"===a?{}:"project"===a?{projectId:l}:{templateId:l});return n},c=async e=>{let{id:t,contextType:a,dataToSend:l,contextId:n}=e,{data:i}=await r.A.patch("".concat(s(a),"/").concat(t,"?projectId=").concat(n),l);return i},u=async()=>{try{return(await r.A.get("/question-blocks")).data.questions||[]}catch(e){throw console.error("Error fetching question block questions:",e),e}},p=async e=>{let{contextType:t,contextId:a,questionPositions:l}=e;if("project"!==t)throw Error("Question position updates are only supported for projects");let n="".concat(s(t),"/positions?projectId=").concat(a);try{let{data:e}=await r.A.patch(n,{questionPositions:l});return e}catch(e){var i,o,d,c,u,p;throw console.error("Update failed - Full error:",e),console.error("Update failed - Error details:",{status:null==(i=e.response)?void 0:i.status,statusText:null==(o=e.response)?void 0:o.statusText,data:null==(d=e.response)?void 0:d.data,message:e.message,config:{url:null==(c=e.config)?void 0:c.url,method:null==(u=e.config)?void 0:u.method,data:null==(p=e.config)?void 0:p.data}}),e}}},55747:(e,t,a)=>{"use strict";a.d(t,{C:()=>d,z:()=>o});var r=a(95155),s=a(12115),l=a(54059),n=a(9428),i=a(53999);let o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(l.bL,{className:(0,i.cn)("grid gap-2",a),...s,ref:t})});o.displayName=l.bL.displayName;let d=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(l.q7,{ref:t,className:(0,i.cn)("aspect-square h-4 w-4 rounded-full border text-gray-900 shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-gray-950 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus-visible:ring-gray-300",a),...s,children:(0,r.jsx)(l.C1,{className:"flex items-center justify-center",children:(0,r.jsx)(n.A,{className:"h-2.5 w-2.5 fill-current text-current"})})})});d.displayName=l.q7.displayName},57799:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(95155);a(12115);let s=()=>(0,r.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,r.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},59362:(e,t,a)=>{"use strict";a.d(t,{F0:()=>u,pe:()=>s});let{Axios:r,AxiosError:s,CanceledError:l,isCancel:n,CancelToken:i,VERSION:o,all:d,Cancel:c,isAxiosError:u,spread:p,toFormData:m,AxiosHeaders:h,HttpStatusCode:g,formToJSON:y,getAdapter:f,mergeConfig:x}=a(23464).A},63642:(e,t,a)=>{"use strict";a.d(t,{R:()=>l});var r=a(95155);a(12115);var s=a(13163);let l=e=>{let{showModal:t,onClose:a,onConfirm:l,title:n,description:i,confirmButtonText:o,cancelButtonText:d,confirmButtonClass:c,children:u}=e;return(0,r.jsxs)(s.A,{isOpen:t,onClose:a,className:"p-6 rounded-md max-w-xl",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:n}),(0,r.jsx)("div",{className:"text-neutral-700 mt-2",children:i}),u&&(0,r.jsx)("div",{className:"mt-6 space-y-4",children:u}),(0,r.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,r.jsx)("button",{className:"btn-outline",onClick:a,type:"button",children:d||"Cancel"}),(0,r.jsx)("button",{className:"font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ".concat(c),onClick:l,type:"button",children:o})]})]})}},74126:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])},82714:(e,t,a)=>{"use strict";a.d(t,{J:()=>i});var r=a(95155),s=a(12115),l=a(40968),n=a(53999);let i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(l.b,{ref:t,className:(0,n.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",a),...s})});i.displayName=l.b.displayName},91426:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>E});var r=a(95155),s=a(29350),l=a(94974),n=a(19373),i=a(26715),o=a(5041),d=a(12115),c=a(57799),u=a(63642),p=a(66163),m=a(66474),h=a(47863),g=a(74126),y=a(67133),f=a(48698),x=a(26862),b=a(88570),j=a(6874),v=a.n(j);let w=[{id:"select",header:e=>{let{table:t}=e;return(0,r.jsx)(x.Sc,{className:"w-6 h-6 data-[state=checked]:bg-primary-500 data-[state=checked]:text-neutral-500 rounded border border-neutral-100 data-[state=checked]:border-neutral-100 cursor-pointer",checked:t.getIsAllPageRowsSelected()||t.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:e=>t.toggleAllPageRowsSelected(!!e),"aria-label":"Select all"})},cell:e=>{let{row:t}=e;return(0,r.jsx)(x.Sc,{className:"w-6 h-6 bg-neutral-100 rounded border border-neutral-400 data-[state=checked]:bg-neutral-100 data-[state=checked]:text-primary-500 data-[state=checked]:border-primary-500 cursor-pointer",checked:t.getIsSelected(),onCheckedChange:e=>t.toggleSelected(!!e),"aria-label":"Select row"})},enableHiding:!1,enableSorting:!1},{accessorKey:"name",header:"Template Name",cell:e=>{let{row:t}=e,a=t.original.id,s=(0,b.l)(a);return(0,r.jsx)(v(),{href:"library/template/".concat(s,"/form-builder"),className:"cursor-pointer text-primary-500 hover:text-primary-600 hover:underline transition-all duration-300",children:t.getValue("name")})}},{accessorKey:"description",header:"Description"},{id:"owner",accessorFn:e=>{var t,a;return null!=(a=null==(t=e.user)?void 0:t.name)?a:"unknown"},header:"Owner",cell:e=>{let{getValue:t}=e;return t()}},{accessorKey:"sector",header:"Sector"},{accessorKey:"country",header:"Country"},{accessorKey:"updatedAt",header:"Last Modified"}],N="data-table-column-visibility",k=()=>{let{user:e}=(0,s.A)(),[t,a]=(0,d.useState)(!1),{data:x,isLoading:b,isError:j}=(0,n.I)({queryKey:["templates",null==e?void 0:e.id],queryFn:l.QK,enabled:!!(null==e?void 0:e.id)}),[v,k]=(0,d.useState)(""),[A,S]=(0,d.useState)({}),[C,q]=(0,d.useState)(null),[R,F]=(0,d.useState)(!1),[I,T]=(0,d.useState)({}),E=(0,i.jE)();(0,d.useEffect)(()=>{let e=localStorage.getItem(N);if(e)try{S(JSON.parse(e))}catch(e){console.error("Failed to parse saved column visibility",e)}},[]),(0,d.useEffect)(()=>{Object.keys(A).length>0&&localStorage.setItem(N,JSON.stringify(A))},[A]);let L=(0,o.n)({mutationFn:l.nh,onSuccess:()=>{E.invalidateQueries({queryKey:["templates",null==e?void 0:e.id]}),a(!1)},onError:()=>{}}),P=d.useMemo(()=>Object.keys(I).filter(e=>I[e]).map(e=>parseInt(e,10)),[I]);return b||!x?(0,r.jsx)(c.A,{}):j?(0,r.jsx)("p",{className:"text-red-500",children:"Error loading data"}):(0,r.jsxs)("div",{children:[(0,r.jsx)(u.R,{showModal:t,onClose:()=>a(!1),title:"Delete Templates",description:"Are you sure you want to delete these templates? This action cannot be undone.",confirmButtonText:"Delete",confirmButtonClass:"btn-danger",onConfirm:()=>L.mutate({templateIds:P})}),(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("h1",{className:"sub-heading-text",children:"Templates"}),(0,r.jsx)("div",{children:(0,r.jsx)("input",{type:"text",value:v,onChange:e=>k(e.target.value),placeholder:"Search templates...",className:"input-field text-sm"})}),C&&(0,r.jsxs)(y.rI,{open:R,onOpenChange:e=>F(e),children:[(0,r.jsx)(f.ty,{asChild:!0,children:(0,r.jsxs)("button",{className:"btn-outline text-sm text-neutral-700 border-neutral-400 font-normal",children:["Show/Hide Columns",R?(0,r.jsx)(m.A,{size:16}):(0,r.jsx)(h.A,{size:16})]})}),(0,r.jsx)(y.SQ,{align:"start",className:"border bg-neutral-100 border-neutral-200 shadow-md px-2",children:C.getAllColumns().filter(e=>e.getCanHide()).map(e=>{var t;return(0,r.jsx)(y.hO,{className:"capitalize cursor-pointer hover:bg-neutral-200",checked:null==(t=A[e.id])||t,onCheckedChange:t=>S(a=>({...a,[e.id]:t})),children:e.id},e.id)})})]}),(0,r.jsxs)("button",{type:"button",className:"ml-auto btn-danger text-sm",onClick:()=>{0!==P.length&&a(!0)},disabled:0===P.length,children:["Delete ",(0,r.jsx)(g.A,{size:16})]})]}),x.length>0?(0,r.jsx)(p.x,{columns:w,data:x,globalFilter:v,setGlobalFilter:k,onTableInit:e=>q(e),onRowSelectionChange:T,columnVisibility:A,setColumnVisibility:S}):(0,r.jsxs)("div",{className:"text-center py-16 space-y-4",children:[(0,r.jsx)("p",{className:"text-lg",children:"Let's get started by creating your first library question, block, template or collection. Click the New button to create it."}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Advanced users: You can also drag and drop XLSForms here and they will be uploaded and converted to library items."})]})]})]})},A=[{id:"sno",header:"S.N.",cell:e=>{let{row:t}=e;return t.index+1}},{accessorKey:"label",header:"Question"},{accessorKey:"inputType",header:"Input Type",cell:e=>{let{row:t}=e,a=t.getValue("inputType");return a.charAt(0).toUpperCase()+a.slice(1)}},{accessorKey:"questionOptions",header:"Options",cell:e=>{let{row:t}=e,a=t.original.inputType;if("selectone"===a||"selectmany"===a){let e=t.original.questionOptions||[];return(0,r.jsx)("div",{className:"space-y-1",children:e.map((e,t)=>(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsxs)("span",{className:"font-medium",children:["-",e.label]}),e.sublabel&&(0,r.jsxs)("span",{className:"text-neutral-500 ml-2",children:["(",e.sublabel,")"]})]},t))})}return null}},{accessorKey:"hint",header:"Hint"},{accessorKey:"isRequired",header:"Required",cell:e=>{let{row:t}=e;return t.getValue("isRequired")?"Yes":"No"}},{accessorKey:"updatedAt",header:"Last Modified",cell:e=>{let{row:t}=e;return new Date(t.getValue("updatedAt")).toLocaleDateString()}}];var S=a(36268),C=a(11032),q=a(88524);let R=e=>{let{columns:t,data:a,globalFilter:s,setGlobalFilter:l,onTableInit:n,columnVisibility:i,setColumnVisibility:o,onRowSelectionChange:c,rowSelection:u,onRowClick:p}=e,[m,h]=d.useState({pageIndex:0,pageSize:8}),[g,y]=d.useState([]),[f,x]=d.useState([]),[b,j]=d.useState({}),[v,w]=d.useState({}),N=void 0!==u?u:v,k=(0,S.N4)({data:a,columns:t,onPaginationChange:h,onColumnFiltersChange:y,onGlobalFilterChange:l,onColumnVisibilityChange:null!=o?o:j,onRowSelectionChange:e=>{let t="function"==typeof e?e(N):e;void 0===u&&w(t),c&&c(t)},onSortingChange:x,getCoreRowModel:(0,C.HT)(),getFilteredRowModel:(0,C.hM)(),getPaginationRowModel:(0,C.kW)(),getSortedRowModel:(0,C.h5)(),enableRowSelection:!0,enableSorting:!0,enableSortingRemoval:!0,state:{pagination:m,columnFilters:g,globalFilter:s,columnVisibility:null!=i?i:b,rowSelection:N,sorting:f}});return d.useEffect(()=>{n&&n(k)},[n,k]),d.useEffect(()=>{void 0!==u&&k.setRowSelection(u)},[u,k]),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"rounded-md border border-neutral-400 overflow-hidden",children:(0,r.jsxs)(q.XI,{className:"min-w-full",children:[(0,r.jsx)(q.A0,{className:"h-20",children:k.getHeaderGroups().map(e=>(0,r.jsx)(q.Hj,{className:"text-sm border-neutral-400",children:e.headers.map(e=>(0,r.jsxs)(q.nd,{className:"py-1 px-6 text-left bg-primary-500 text-neutral-100 font-semibold ".concat(0===e.index?"w-12 py-3 px-6":""),style:{cursor:e.column.getCanSort()?"pointer":"default"},children:[(0,r.jsx)("div",{onClick:e.column.getToggleSortingHandler(),children:(0,r.jsx)("div",{children:e.isPlaceholder?null:(0,S.Kv)(e.column.columnDef.header,e.getContext())})}),e.column.getCanFilter()&&(0,r.jsx)("input",{placeholder:"Search...",value:e.column.getFilterValue()||"",onChange:t=>e.column.setFilterValue(t.target.value),className:"input-field max-w-48 text-sm my-1 px-2 py-1 bg-neutral-100 text-neutral-700 font-light border-none rounded-md"})]},e.id))},e.id))}),(0,r.jsx)(q.BF,{children:k.getPaginationRowModel().rows.length?k.getPaginationRowModel().rows.map(e=>(0,r.jsx)(q.Hj,{"data-state":e.getIsSelected()&&"selected",className:"hover:bg-neutral-50 text-sm border-neutral-400",onClick:()=>null==p?void 0:p(e.original),children:e.getVisibleCells().map((e,t)=>(0,r.jsx)(q.nA,{className:"py-4 px-6 max-w-48  ".concat(0===t?"py-3 px-6":""," text-neutral-700 "),children:(0,S.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,r.jsx)(q.Hj,{children:(0,r.jsx)(q.nA,{colSpan:t.length,className:"h-24 text-center",children:"No results."})})})]})}),(0,r.jsx)("div",{className:"flex items-center justify-end space-x-2 py-4",children:a.length>m.pageSize&&(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-2 py-4",children:[(0,r.jsx)("button",{className:"btn-primary",onClick:()=>k.previousPage(),disabled:!k.getCanPreviousPage(),children:"Previous"}),(0,r.jsx)("button",{className:"btn-primary",onClick:()=>k.nextPage(),disabled:!k.getCanNextPage(),children:"Next"})]})})]})},F=e=>{let{questions:t}=e,[a,s]=(0,d.useState)("");return(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsx)(R,{columns:A,data:t,globalFilter:a,setGlobalFilter:s})})};var I=a(34947);let T=()=>{let{user:e}=(0,s.A)(),t=null==e?void 0:e.id,{data:a,isLoading:r,isError:l,error:i,refetch:o}=(0,n.I)({queryKey:["questionBlockQuestions",t],queryFn:()=>(0,I.dI)(),enabled:!!t,retry:1});return{questions:Array.isArray(a)?a:[],isLoading:r,isError:l,error:i,refetch:o,userId:t}};function E(){let{questions:e}=T();return(0,r.jsxs)("div",{className:"p-6 space-y-6 section gap-8",children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold",children:"My Library"}),(0,r.jsx)(k,{}),(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsx)("h1",{className:"sub-heading-text hover:text-neutral-700",children:(0,r.jsx)(v(),{href:"/library/question-block/form-builder",children:"Question Blocks"})}),e.length>0?(0,r.jsx)(F,{questions:e}):(0,r.jsxs)("div",{className:"text-center py-16 space-y-4",children:[(0,r.jsx)("p",{className:"text-lg",children:"Let's get started by creating your first library question, block, template or collection. Click the New button to create it."}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Advanced users: You can also drag and drop XLSForms here and they will be uploaded and converted to library items."})]})]})]})}},94974:(e,t,a)=>{"use strict";a.d(t,{I7:()=>i,J2:()=>s,QK:()=>n,Xu:()=>l,nh:()=>o});var r=a(25784);let s=async e=>{let{templateId:t}=e,{data:a}=await r.A.get("/libraries/".concat(t));return a.template},l=async e=>{let{data:t}=await r.A.post("/libraries",e);return t},n=async()=>{let{data:e}=await r.A.get("/libraries");return e.templates},i=async e=>{let{data:t}=await r.A.delete("/libraries/".concat(e));return t},o=async e=>{let{templateIds:t}=e;return null}},97381:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>o,Le:()=>n,jB:()=>i,tQ:()=>s,x9:()=>l});let r=(0,a(51990).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,t)=>{e.status="authenticated",e.user=t.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,t)=>{e.status="unauthenticated",e.error=t.payload,e.user=null}}}),{setAuthenticatedUser:s,setUnauthenticated:l,setAuthLoading:n,setAuthError:i}=r.actions,o=r.reducer},99474:(e,t,a)=>{"use strict";a.d(t,{T:()=>n});var r=a(95155),s=a(12115),l=a(53999);let n=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700 dark:bg-gray-900 dark:placeholder:text-gray-500",a),ref:t,...s})});n.displayName="Textarea"}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,635,1445,6967,6903,4601,4277,6874,556,3481,1467,6539,6268,919,4695,9660,8441,1684,7358],()=>t(19815)),_N_E=e.O()}]);