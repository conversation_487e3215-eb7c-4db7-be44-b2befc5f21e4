(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[389],{10951:(e,s,t)=>{Promise.resolve().then(t.bind(t,71888))},29350:(e,s,t)=>{"use strict";t.d(s,{A:()=>u});var r=t(97381),i=t(59362),n=t(25784),o=t(35695),a=t(12115),l=t(34540);let u=e=>{let s=(0,l.wA)(),t=(0,o.useRouter)(),u=(0,o.usePathname)(),{status:d,user:c,error:m}=(0,l.d4)(e=>e.auth),h=async()=>{try{s((0,r.Le)());let e=(await n.A.get("/users/me")).data;s((0,r.tQ)(e))}catch(n){if(s((0,r.x9)()),(0,i.F0)(n)){var e,o,a,l,d;if(console.error("Auth error:",null==(e=n.response)?void 0:e.status,null==(o=n.response)?void 0:o.data),(null==(a=n.response)?void 0:a.status)===401){if(u.startsWith("/form-submission"))return;t.push("/")}else s((0,r.jB)((null==(d=n.response)||null==(l=d.data)?void 0:l.message)||n.message))}else s((0,r.jB)(n instanceof Error?n.message:"An unknown error occurred."))}};return(0,a.useEffect)(()=>{(null==e?void 0:e.skipFetchUser)||h()},[null==e?void 0:e.skipFetchUser]),(0,a.useEffect)(()=>{let e=e=>{if("logout"===e.key&&"true"===e.newValue)if(s((0,r.x9)()),u.startsWith("/form-submission")){let e=u.split("/")[2];e?t.push("/form-submission/".concat(e,"/sign-in")):t.push("/")}else t.push("/")};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[s,t,u]),{status:d,user:c,error:m,isAuthenticated:"authenticated"===d,isLoading:"loading"===d,refreshAuthState:()=>{h()},signin:async(e,s,t)=>{try{await n.A.post("/users/login",e),await h(),null==s||s()}catch(e){if(e instanceof i.pe){var r,o;let s=null==(o=e.response)||null==(r=o.data)?void 0:r.errorType;null==t||t(s)}else null==t||t()}},logout:async()=>{try{await n.A.post("/users/logout"),localStorage.setItem("logout","true"),setTimeout(()=>localStorage.removeItem("logout"),100)}finally{if(s((0,r.x9)()),u.startsWith("/form-submission")){let e=u.split("/")[2];e?t.push("/form-submission/".concat(e,"/sign-in")):t.push("/")}else t.push("/")}}}}},59362:(e,s,t)=>{"use strict";t.d(s,{F0:()=>c,pe:()=>i});let{Axios:r,AxiosError:i,CanceledError:n,isCancel:o,CancelToken:a,VERSION:l,all:u,Cancel:d,isAxiosError:c,spread:m,toFormData:h,AxiosHeaders:p,HttpStatusCode:g,formToJSON:f,getAdapter:x,mergeConfig:b}=t(23464).A},71888:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var r=t(95155),i=t(59250),n=t(12115),o=t(19373),a=t(34947),l=t(57799),u=t(41232),d=t(29350);let c={viewForm:!0,editForm:!0,viewSubmissions:!0,addSubmissions:!0,deleteSubmissions:!0,editSubmissions:!0,manageProject:!0,validateSubmissions:!0};function m(){let[e,s]=(0,n.useState)(!1),{user:t}=(0,d.A)(),m=null==t?void 0:t.id,{data:h,isLoading:p,isError:g}=(0,o.I)({queryKey:["questionBlockQuestions",m],queryFn:()=>(0,a.dI)(),enabled:!!m,retry:1});if(!m)return(0,r.jsx)("div",{className:"p-6 text-center",children:(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto",children:[(0,r.jsx)("h1",{className:"text-xl font-semibold text-red-600 mb-2",children:"Authentication Required"}),(0,r.jsx)("p",{className:"text-neutral-700 mb-4",children:"You need to be logged in to access the question block form builder."}),(0,r.jsx)("a",{href:"/",className:"inline-block px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark",children:"Go to Login"})]})});if(p||!h)return(0,r.jsx)(l.A,{});if(g)return(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-red-500",children:"Error loading question block information"}),(0,r.jsx)("p",{className:"text-sm text-red-500 mt-2",children:"There was a problem fetching the questions. Please try refreshing the page."}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"If the problem persists, please check the browser console for more details."})]});let f=Array.isArray(h)?h:[];return(0,r.jsx)("div",{className:"p-6",children:e?(0,r.jsx)(i.V,{questions:f,questionGroups:[],contextType:"questionBlock",onClose:()=>s(!1)}):(0,r.jsx)(u.o,{setIsPreviewMode:s,questions:f,contextType:"questionBlock",contextId:m,permissions:c})})}},97381:(e,s,t)=>{"use strict";t.d(s,{Ay:()=>l,Le:()=>o,jB:()=>a,tQ:()=>i,x9:()=>n});let r=(0,t(51990).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,s)=>{e.status="authenticated",e.user=s.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,s)=>{e.status="unauthenticated",e.error=s.payload,e.user=null}}}),{setAuthenticatedUser:i,setUnauthenticated:n,setAuthLoading:o,setAuthError:a}=r.actions,l=r.reducer}},e=>{var s=s=>e(e.s=s);e.O(0,[3873,3524,635,1445,6967,6903,4601,2177,4277,556,3481,1467,6539,2050,919,7252,7248,8662,8441,1684,7358],()=>s(10951)),_N_E=e.O()}]);