(()=>{var e={};e.id=7767,e.ids=[7767],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6986:(e,t,r)=>{"use strict";r.d(t,{D:()=>o,l:()=>n});var s=r(53907);let i=process.env.SALT||"rushan-salt",a=new s.A(i,12),n=e=>a.encode(e),o=e=>{let t=a.decode(e)[0];return"bigint"==typeof t?t<Number.MAX_SAFE_INTEGER?Number(t):null:"number"==typeof t?t:null}},10125:(e,t,r)=>{"use strict";r.d(t,{Notification:()=>p});var s=r(60687),i=r(43210),a=r(54864),n=r(88920),o=r(57101),d=r(19150),l=r(14719),u=r(43649),c=r(93613);let p=()=>{let e=(0,a.wA)(),{message:t,type:r,visible:p}=(0,a.d4)(e=>e.notification);(0,i.useEffect)(()=>{if(p){let t=setTimeout(()=>{e((0,d._b)())},5e3);return()=>clearTimeout(t)}},[p,e]);let m="success"===r?(0,s.jsx)(l.A,{}):"warning"===r?(0,s.jsx)(u.A,{}):(0,s.jsx)(c.A,{});return(0,s.jsx)(n.N,{children:p&&(0,s.jsxs)(o.P.div,{className:`z-50 fixed top-0 right-0 m-4 px-4 py-2 rounded font-semibold w-auto max-w-xs flex items-center gap-2 cursor-pointer ${"success"===r?"bg-green-500 hover:bg-green-600":"warning"===r?"bg-yellow-500 hover:bg-yellow-600":"bg-red-500 hover:bg-red-600"} transition-colors duration-300`,onClick:()=>e((0,d._b)()),initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3,ease:"easeIn"},children:[(0,s.jsx)("span",{className:"text-2xl",children:m}),(0,s.jsx)("span",{className:"break-words neutral-100space-normal",children:t})]})})}},10271:(e,t,r)=>{"use strict";r.d(t,{ReactQueryProvider:()=>d});var s=r(60687),i=r(43210),a=r(39091),n=r(8693),o=r(9124);let d=({children:e})=>{let[t]=(0,i.useState)(()=>new a.E({defaultOptions:{queries:{staleTime:3e5,refetchOnWindowFocus:!1}}}));return(0,s.jsxs)(n.Ht,{client:t,children:[e,(0,s.jsx)(o.E,{initialIsOpen:!1})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12810:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let s=r(51060).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});s.interceptors.request.use(e=>e,e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let i=s},14902:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>l});var s=r(65239),i=r(48088),a=r(88170),n=r.n(a),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let l={children:["",{children:["edit-submission",{children:["[hashedId]",{children:["[submissionId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,95120)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\edit-submission\\[hashedId]\\[submissionId]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\edit-submission\\[hashedId]\\[submissionId]\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/edit-submission/[hashedId]/[submissionId]/page",pathname:"/edit-submission/[hashedId]/[submissionId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},14952:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},15616:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});var s=r(60687),i=r(43210),a=r(96241);let n=i.forwardRef(({className:e,...t},r)=>(0,s.jsx)("textarea",{className:(0,a.cn)("flex min-h-[80px] w-full rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700 dark:bg-gray-900 dark:placeholder:text-gray-500",e),ref:r,...t}));n.displayName="Textarea"},16319:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19150:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>n,Ds:()=>i,_b:()=>a});let s=(0,r(9317).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,t)=>{e.message=t.payload.message,e.type=t.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:i,hideNotification:a}=s.actions,n=s.reducer},21820:e=>{"use strict";e.exports=require("os")},26946:(e,t,r)=>{Promise.resolve().then(r.bind(r,10125)),Promise.resolve().then(r.bind(r,10271)),Promise.resolve().then(r.bind(r,49271))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35790:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>n,l:()=>a,yg:()=>i});let s=(0,r(9317).Z0)({name:"createLibraryItem",initialState:{visible:!1},reducers:{showCreateLibraryModal:e=>{e.visible=!0},hideCreateLibraryModal:e=>{e.visible=!1}}}),{showCreateLibraryModal:i,hideCreateLibraryModal:a}=s.actions,n=s.reducer},39390:(e,t,r)=>{"use strict";r.d(t,{J:()=>o});var s=r(60687),i=r(43210),a=r(78148),n=r(96241);let o=i.forwardRef(({className:e,...t},r)=>(0,s.jsx)(a.b,{ref:r,className:(0,n.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...t}));o.displayName=a.b.displayName},40347:(e,t,r)=>{"use strict";r.d(t,{C:()=>l,z:()=>d});var s=r(60687),i=r(43210),a=r(14555),n=r(65822),o=r(96241);let d=i.forwardRef(({className:e,...t},r)=>(0,s.jsx)(a.bL,{className:(0,o.cn)("grid gap-2",e),...t,ref:r}));d.displayName=a.bL.displayName;let l=i.forwardRef(({className:e,...t},r)=>(0,s.jsx)(a.q7,{ref:r,className:(0,o.cn)("aspect-square h-4 w-4 rounded-full border text-gray-900 shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-gray-950 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus-visible:ring-gray-300",e),...t,children:(0,s.jsx)(a.C1,{className:"flex items-center justify-center",children:(0,s.jsx)(n.A,{className:"h-2.5 w-2.5 fill-current text-current"})})}));l.displayName=a.q7.displayName},42895:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>d,Le:()=>n,jB:()=>o,tQ:()=>i,x9:()=>a});let s=(0,r(9317).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,t)=>{e.status="authenticated",e.user=t.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,t)=>{e.status="unauthenticated",e.error=t.payload,e.user=null}}}),{setAuthenticatedUser:i,setUnauthenticated:a,setAuthLoading:n,setAuthError:o}=s.actions,d=s.reducer},44395:(e,t,r)=>{"use strict";r.d(t,{Notification:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Notification() from the server but Notification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\components\\general\\Notification.tsx","Notification")},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},49271:(e,t,r)=>{"use strict";r.d(t,{ReduxProvider:()=>p});var s=r(60687),i=r(9317),a=r(19150),n=r(58432),o=r(42895),d=r(35790),l=r(89011);let u=(0,i.U1)({reducer:{notification:a.Ay,createProject:n.Ay,auth:o.Ay,createLibrary:d.Ay,createLibraryItem:l.Ay}});r(43210);var c=r(54864);let p=({children:e})=>(0,s.jsx)(c.Kq,{store:u,children:e})},50823:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},54481:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>O});var s=r(60687),i=r(16189),a=r(29494),n=r(12810),o=r(6986),d=r(75531),l=r(86429),u=r(43210),c=r(39390),p=r(15616),m=r(93437),h=r(40347),b=r(70334),y=r(78272),f=r(14952),x=r(54050),g=r(54864),v=r(19150),w=r(13784),j=r(80967),N=r(96),q=r(71845),I=r(24527),k=r(69396);function A({questions:e,submission:t,projectId:r,submissionId:i,onClose:o,onSave:d}){let l=(0,g.wA)(),[A,P]=(0,u.useState)({}),[O,S]=(0,u.useState)({}),[E,C]=(0,u.useState)({}),[R,T]=(0,u.useState)(!1),[$,M]=(0,u.useState)([]),[D,_]=(0,u.useState)([]),[L,G]=(0,u.useState)({}),[K,F]=(0,u.useState)(new Set);(0,u.useRef)(new Set),(0,u.useRef)(!1),(0,u.useRef)("");let{data:U=[]}=(0,a.I)({queryKey:["questionGroups",r],queryFn:()=>(0,N.pr)({projectId:r}),enabled:!!r}),{data:Q}=(0,a.I)({queryKey:["project",r],queryFn:()=>(0,q.kf)({projectId:r}),enabled:!!r}),z=(0,u.useMemo)(()=>U.reduce((t,r)=>(t[r.id]=e.filter(e=>e.questionGroupId===r.id),t),{}),[U,e]),J=(0,u.useMemo)(()=>e.filter(e=>null===e.questionGroupId||void 0===e.questionGroupId),[e]),Y=(0,u.useMemo)(()=>{let t=[];return U.forEach(r=>{let s=e.filter(e=>e.questionGroupId===r.id),i=s.length>0?Math.min(...s.map(e=>e.position)):r.order;t.push({type:"group",data:r,order:i,originalPosition:i})}),J.forEach(e=>{t.push({type:"question",data:e,order:e.position,originalPosition:e.position})}),t.sort((e,t)=>e.order===t.order?(e.originalPosition||e.order)-(t.originalPosition||t.order):e.order-t.order)},[U,J,e]),Z=(0,u.useCallback)(e=>{G(t=>({...t,[e]:!t[e]}))},[]),H=(0,u.useCallback)((e,t)=>{F(t=>new Set(t).add(e)),P(r=>({...r,[e]:t})),C(t=>({...t,[e]:""}))},[]),B=()=>{let e=(0,I.WK)($,A);return C(e),0===Object.keys(e).length},W=(0,x.n)({mutationFn:async s=>{let a=e.map(e=>{let a=s[e.id],n="selectmany"===e.inputType,o=t.answers.find(t=>t.question.id===e.id),d=!o?.id;if(n&&Array.isArray(a)){if(a.length>0){let t=[];e.questionOptions&&(t=a.map(t=>{let r=e.questionOptions.find(e=>e.label===t);return r?.id}).filter(e=>void 0!==e));let s={projectId:r,questionId:e.id,answerType:e.inputType,value:a.join(", "),questionOptionId:t,isOtherOption:!1,formSubmissionId:i};return d?s:{...s,id:o.id}}return null}{let t,s;if(void 0===(t="number"===e.inputType||"decimal"===e.inputType?a?Number(a):void 0:"date"===e.inputType||"dateandtime"===e.inputType?a||void 0:"table"===e.inputType?Array.isArray(a)&&a.length>0?JSON.stringify(a):void 0:a?String(a):void 0))return null;if("selectone"===e.inputType&&a&&e.questionOptions){let t=e.questionOptions.find(e=>e.label===a);s=t?.id}let n={projectId:r,questionId:e.id,answerType:e.inputType,value:t,questionOptionId:s,isOtherOption:!1,formSubmissionId:i};return d?n:{...n,id:o.id}}}).filter(e=>null!==e);if(0===a.length)throw Error("No valid answers with IDs to submit");let o=a.map(e=>e.id?{id:e.id,questionId:e.questionId,projectId:r,value:e.value,answerType:e.answerType,questionOptionId:e.questionOptionId,isOtherOption:e.isOtherOption||!1,formSubmissionId:e.formSubmissionId}:e.questionId?{questionId:e.questionId,projectId:r,value:e.value,answerType:e.answerType,questionOptionId:e.questionOptionId,isOtherOption:e.isOtherOption||!1,formSubmissionId:e.formSubmissionId}:null).filter(e=>null!==e);try{return await (0,j.GN)(o,r)}catch(s){console.error("Error with /answers/multiple endpoint:",s),s.response&&(console.error("Error response data:",JSON.stringify(s.response.data,null,2)),console.error("Error response status:",s.response.status),console.error("Error response headers:",s.response.headers));let e=[],t=[];for(let s of a)try{if(s.id){let{data:t}=await n.A.patch(`/answers/${s.id}?projectId=${r}`,{id:s.id,questionId:s.questionId,projectId:r,value:s.value,answerType:s.answerType,questionOptionId:s.questionOptionId,isOtherOption:s.isOtherOption||!1,formSubmissionId:s.formSubmissionId});e.push(t)}else if(s.questionId){let{data:t}=await n.A.post(`/answers?projectId=${r}`,{submissionId:s.formSubmissionId,questionId:s.questionId,value:s.value,answerType:s.answerType,questionOptionId:s.questionOptionId,isOtherOption:s.isOtherOption||!1});e.push(t)}}catch(r){let e=s.id||s.questionId;console.error(`Error handling answer ${e}:`,r),r.response&&console.error("Individual error response data:",JSON.stringify(r.response.data,null,2)),t.push(e)}if(t.length>0)throw Error(`Failed to update answers with IDs: ${t.join(", ")}`);if(e.length>0)return l((0,v.Ds)({message:"Submission updated successfully using individual updates. Consider checking the bulk update endpoint.",type:"warning"})),e;throw s}},onSuccess:()=>{l((0,v.Ds)({message:"Submission updated successfully. You can continue editing if needed.",type:"success"})),F(new Set),d()},onError:e=>{let t=e.response?.data?.message||e.response?.data?.error||e.message||"Failed to update submission. Please check your input and try again.";l((0,v.Ds)({message:t,type:"error"})),console.error("Update Error:",{message:t,status:e.response?.status,data:JSON.stringify(e.response?.data,null,2)})},onSettled:()=>{T(!1)}}),X=async e=>{e.preventDefault(),B()&&(T(!0),W.mutate(A))},V=t=>e.some(e=>e.questionOptions?.some(e=>e.nextQuestionId===t)),ee=e=>e.questionOptions?.some(e=>e.nextQuestionId)||!1,et=e=>{let t=V(e.id),r=ee(e);return(0,s.jsxs)("div",{className:`border rounded-md p-4 ${t?"border-primary-200 dark:border-primary-700 bg-primary-100 dark:bg-primary-900/20":"border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"}`,children:[(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(c.J,{className:"text-base font-medium",children:[e.label,e.isRequired&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),t&&(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200",children:[(0,s.jsx)(b.A,{className:"w-3 h-3 mr-1"}),"Follow-up"]}),r&&(0,s.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent-200 text-accent-700 dark:bg-accent-700/20 dark:text-accent-200",children:"Has conditions"})]}),e.hint&&(0,s.jsx)("p",{className:`text-sm mt-1 ${t?"text-primary-700 dark:text-primary-300":"text-muted-foreground"}`,children:e.hint}),E[e.id]&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:E[e.id]})]}),(0,s.jsx)("div",{className:"mt-2",children:er(e)})]},e.id)},er=e=>{let t=A[e.id]??("selectmany"===e.inputType?[]:"");switch(e.inputType){case"text":if(e.hint?.includes("multiline"))return(0,s.jsx)(p.T,{value:t,onChange:t=>H(e.id,t.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});return(0,s.jsx)("input",{className:"input-field w-full",value:t,onChange:t=>H(e.id,t.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"number":return(0,s.jsx)("input",{className:"input-field w-full",type:"number",value:t,onChange:t=>H(e.id,t.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"decimal":return(0,s.jsx)("input",{className:"input-field w-full",type:"number",step:"any",value:t,onChange:t=>H(e.id,t.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"selectone":return(0,s.jsx)(h.z,{value:t,onValueChange:t=>H(e.id,t),required:e.isRequired,children:(0,s.jsx)("div",{className:"space-y-2",children:e.questionOptions?.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(h.C,{value:e.label,id:`option-${e.id}`}),(0,s.jsx)(c.J,{htmlFor:`option-${e.id}`,className:"cursor-pointer",children:e.label})]},t))})});case"selectmany":return(0,s.jsx)("div",{className:"space-y-2",children:e.questionOptions?.map(r=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(m.S,{id:`option-${r.id}`,checked:(t||[]).includes(r.label),onCheckedChange:s=>{let i=t||[],a=s?[...i,r.label]:i.filter(e=>e!==r.label);H(e.id,a)}}),(0,s.jsx)(c.J,{htmlFor:`option-${r.id}`,className:"cursor-pointer",children:r.label})]},r.id))});case"date":return(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("input",{className:"input-field w-full",type:"date",value:t,onChange:t=>H(e.id,t.target.value),placeholder:e.hint||"Select date",required:e.isRequired})});case"dateandtime":return(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("input",{className:"input-field w-full",type:"time",value:t,onChange:t=>H(e.id,t.target.value),placeholder:e.hint||"Select time",required:e.isRequired})});case"table":return(0,s.jsx)(w.N,{questionId:e.id,value:t,onChange:t=>H(e.id,t),required:e.isRequired,tableLabel:e.label});default:return null}};return(0,s.jsxs)("div",{className:"w-full max-w-screen-lg bg-neutral-100 dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("h2",{className:"text-2xl font-semibold p-6 border-b border-gray-200 dark:border-gray-700",children:["Edit Submission",Q?.name?` for ${Q.name}`:""]}),(0,s.jsx)("form",{onSubmit:X,className:"p-6",children:(0,s.jsxs)("div",{className:"space-y-6",children:[0===e.length?(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"This form has no questions yet."})}):Y.map(e=>{if("group"===e.type){let t=e.data,r=z[t.id]||[],i=r.filter(e=>$.some(t=>t.id===e.id)),a=L[t.id];return 0===i.length?null:(0,s.jsxs)("div",{className:"border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800",children:[(0,s.jsx)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700",onClick:()=>Z(t.id),children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[a?(0,s.jsx)(y.A,{className:"h-5 w-5 text-gray-500"}):(0,s.jsx)(f.A,{className:"h-5 w-5 text-gray-500"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:t.title}),(0,s.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:["(",i.length," visible question",1!==i.length?"s":"",")"]})]})}),a&&(0,s.jsx)("div",{className:"p-4 space-y-4",children:D.filter(e=>r.some(t=>t.id===e.question.id)).map(e=>(0,s.jsx)(k.A,{questionGroup:e,renderQuestionInput:er,errors:E,className:""},e.question.id))})]},`group-${t.id}`)}{let t=e.data;if(!$.some(e=>e.id===t.id))return null;let r=D.find(e=>e.question.id===t.id);return r?(0,s.jsx)(k.A,{questionGroup:r,renderQuestionInput:er,errors:E,className:""},t.id):et(t)}}),e.length>0&&(0,s.jsxs)("div",{className:"mt-6 flex justify-end gap-4",children:[(0,s.jsx)("button",{className:"btn-primary bg-neutral-500 hover:bg-neutral-600",type:"button",onClick:o,disabled:R,children:"Cancel"}),(0,s.jsx)("button",{className:"btn-primary",type:"submit",disabled:R,children:R?"Saving...":"Save Changes"})]})]})})]})}let P=async(e,t)=>{let{data:r}=await n.A.get(`/form-submissions/${e}`),s=r.data.formSubmissions.find(e=>e.id===t);if(!s)throw Error("Submission not found");return s};function O(){let{hashedId:e,submissionId:t}=(0,i.useParams)(),r=(0,o.D)(e),n=Number(t);if(null===r||isNaN(n))return(0,s.jsx)("div",{children:"Error: Invalid project or submission ID."});let{data:u=[],isLoading:c,isError:p}=(0,a.I)({queryKey:["questions",r],queryFn:()=>(0,d.K4)({projectId:r}),enabled:!!r}),{data:m,isLoading:h,isError:b,refetch:y}=(0,a.I)({queryKey:["submission",r,n],queryFn:()=>P(r,n),enabled:!!r&&!!n});return c||h?(0,s.jsx)(l.A,{}):p||b||!u||!m?(0,s.jsx)("p",{className:"text-sm text-red-500",children:"Error loading submission or form. Please try again."}):(0,s.jsx)("div",{className:"min-h-screen w-full bg-neutral-100 dark:bg-gray-900 flex flex-col items-center p-4 md:p-6",children:(0,s.jsx)(A,{questions:u,submission:m,projectId:r,submissionId:n,onSave:()=>{window.opener&&window.opener.postMessage({type:"REFETCH_SUBMISSIONS"},"*"),y()}})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p,metadata:()=>c});var s=r(37413);r(82704);var i=r(7990),a=r.n(i),n=r(60866),o=r.n(n),d=r(77832),l=r(44395),u=r(60265);let c={title:"Data analysis tool",description:"A tool for data collection and analysis."};function p({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${a().className} ${o().className} antialiased`,children:(0,s.jsx)(d.ReduxProvider,{children:(0,s.jsxs)(u.ReactQueryProvider,{children:[(0,s.jsx)(l.Notification,{}),(0,s.jsx)("main",{className:"bg-neutral-200",children:e})]})})})})}},58432:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>n,Gl:()=>i,th:()=>a});let s=(0,r(9317).Z0)({name:"createProject",initialState:{visible:!1},reducers:{showCreateProjectModal:e=>{e.visible=!0},hideCreateProjectModal:e=>{e.visible=!1}}}),{showCreateProjectModal:i,hideCreateProjectModal:a}=s.actions,n=s.reducer},60265:(e,t,r)=>{"use strict";r.d(t,{ReactQueryProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ReactQueryProvider() from the server but ReactQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\providers\\ReactQueryProvider.tsx","ReactQueryProvider")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64702:(e,t,r)=>{Promise.resolve().then(r.bind(r,54481))},68988:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var s=r(60687);r(43210);var i=r(96241);function a({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-primary-500 focus-visible:ring-[1px]","focus-visible:outline-none","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},69430:(e,t,r)=>{Promise.resolve().then(r.bind(r,95120))},70334:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},71845:(e,t,r)=>{"use strict";r.d(t,{D_:()=>c,Im:()=>l,Oo:()=>p,c3:()=>a,kf:()=>i,lj:()=>h,or:()=>d,pf:()=>u,vj:()=>n,wI:()=>m,xx:()=>o});var s=r(12810);let i=async({projectId:e})=>{let{data:t}=await s.A.get(`/projects/${e}`);return t.project},a=async e=>{let{data:t}=await s.A.post("/projects/from-template",e);return t},n=async()=>{try{let{data:e}=await s.A.get("/projects");return e.projects}catch(e){throw console.error("Error fetching projects:",e),e}},o=async e=>{let{data:t}=await s.A.delete(`/projects/delete/${e}`);return t},d=async e=>{try{let{data:t}=await s.A.delete("/projects/delete-multiple",{data:{projectIds:e}});return t}catch(e){throw console.error("Error deleting multiple projects:",e),e}},l=async e=>{try{let{data:t}=await s.A.patch(`/projects/change-status/${e}`,{status:"archived"});return t}catch(e){throw console.error("Error archiving project:",e),e}},u=async(e,t=!1)=>{try{let{data:t}=await s.A.patch(`/projects/change-status/${e}`,{status:"deployed"});return t}catch(e){throw console.error("Error deploying project:",e),e}},c=async e=>{try{let{data:t}=await s.A.patch("/projects/update-many-status",{projectIds:e,status:"archived"});return t}catch(e){throw console.error("Error archiving multiple projects:",e),e}},p=async e=>{try{let{data:t}=await s.A.post("/users/check-email",{email:e});return t}catch(e){throw Error("object"==typeof e.response?.data?.message?JSON.stringify(e.response?.data?.message):e.response?.data?.message||e.message||"Failed to check user")}},m=async({projectId:e,email:t,permissions:r})=>{try{let i=await p(t);if(!i||!i.success)throw Error(i?.message||"User not found");let{data:a}=await s.A.post("/project-users",{userId:i.user.id,projectId:e,permission:r});return a}catch(e){throw console.error("Error adding user to project:",e),Error("object"==typeof e.response?.data?.message?JSON.stringify(e.response?.data?.message):e.response?.data?.message||e.message||"Failed to add user")}},h=async e=>{try{let{data:t}=await s.A.post("/answers/multiple",e);return t}catch(e){throw console.error("Error creating answer submission:",e),e}}},74075:e=>{"use strict";e.exports=require("zlib")},77832:(e,t,r)=>{"use strict";r.d(t,{ReduxProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ReduxProvider() from the server but ReduxProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\providers\\ReduxProvider.tsx","ReduxProvider")},79551:e=>{"use strict";e.exports=require("url")},80967:(e,t,r)=>{"use strict";r.d(t,{GN:()=>o,J6:()=>a,O8:()=>i,s4:()=>n});var s=r(12810);let i=async(e,t)=>{try{let{data:r}=await s.A.delete(`/form-submissions/${e}?projectId=${t}`);return r}catch(e){throw console.error("Error deleting form submission:",e),e}},a=async(e,t)=>{try{let r=e.map(e=>s.A.delete(`/form-submissions/${e}?projectId=${t}`));return(await Promise.all(r)).map(e=>e.data)}catch(e){throw console.error("Error deleting multiple form submissions:",e),e}},n=async(e,t)=>{try{if(!e.submissionId||!e.questionId)throw Error("submissionId and questionId are required");let r={...e};null===r.questionOptionId?delete r.questionOptionId:Array.isArray(r.questionOptionId)&&(r.questionOptionId=r.questionOptionId.filter(e=>null!=e),0===r.questionOptionId.length&&delete r.questionOptionId);let{data:i}=await s.A.patch(`/answers/${e.questionId}?projectId=${t}`,r);return i}catch(e){throw console.error("Error updating answer:",e),e}},o=async(e,t)=>{try{let{data:r}=await s.A.patch(`/answers/multiple?projectId=${t}`,e);return r}catch(e){throw console.error("Error updating multiple answers with endpoint:",e),e}}},81630:e=>{"use strict";e.exports=require("http")},82704:()=>{},83997:e=>{"use strict";e.exports=require("tty")},86429:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(60687);r(43210);let i=()=>(0,s.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,s.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},86778:(e,t,r)=>{Promise.resolve().then(r.bind(r,44395)),Promise.resolve().then(r.bind(r,60265)),Promise.resolve().then(r.bind(r,77832))},89011:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>n,dQ:()=>i,g7:()=>a});let s=(0,r(9317).Z0)({initialState:{visible:!1,option:""},name:"createLibraryItem",reducers:{showCreateLibraryItemModal:(e,t)=>{e.visible=!0,e.option=t.payload},hideCreateLibraryItemModal:e=>{e.visible=!1,e.option=""}}}),{showCreateLibraryItemModal:i,hideCreateLibraryItemModal:a}=s.actions,n=s.reducer},93437:(e,t,r)=>{"use strict";r.d(t,{S:()=>o});var s=r(60687);r(43210);var i=r(40211),a=r(13964),n=r(96241);function o({className:e,...t}){return(0,s.jsx)(i.bL,{"data-slot":"checkbox",className:(0,n.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(i.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(a.A,{className:"size-3.5"})})})}},94735:e=>{"use strict";e.exports=require("events")},95120:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\edit-submission\\\\[hashedId]\\\\[submissionId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\edit-submission\\[hashedId]\\[submissionId]\\page.tsx","default")},96241:(e,t,r)=>{"use strict";r.d(t,{Y:()=>n,cn:()=>a});var s=r(49384),i=r(82348);function a(...e){return(0,i.QP)((0,s.$)(e))}function n(e,t="short"){if(!e)return"";try{let r="string"==typeof e?new Date(e):e;if(isNaN(r.getTime()))return"";switch(t){case"short":return r.toLocaleDateString(void 0,{year:"numeric",month:"short",day:"numeric"});case"long":return r.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});case"full":return r.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",weekday:"long",hour:"2-digit",minute:"2-digit",second:"2-digit"});default:return r.toLocaleDateString()}}catch(t){return console.error("Error formatting date:",t),String(e)}}},96752:(e,t,r)=>{"use strict";r.d(t,{A0:()=>n,BF:()=>o,Hj:()=>d,XI:()=>a,nA:()=>u,nd:()=>l});var s=r(60687);r(43210);var i=r(96241);function a({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,i.cn)("w-full caption-bottom text-sm",e),...t})})}function n({className:e,...t}){return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,i.cn)("[&_tr]:border-b",e),...t})}function o({className:e,...t}){return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,i.cn)("[&_tr:last-child]:border-0",e),...t})}function d({className:e,...t}){return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,i.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function l({className:e,...t}){return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,i.cn)("text-foreground h-10 px-2 text-left align-middle font-medium neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function u({className:e,...t}){return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,i.cn)("p-2 align-middle neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7404,517,551,4072],()=>r(14902));module.exports=s})();