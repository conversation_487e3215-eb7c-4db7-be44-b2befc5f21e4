"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9660],{13163:(e,t,r)=>{r.d(t,{A:()=>l});var a=r(95155),n=r(60760),i=r(44518),o=r(95233),s=r(54416);r(12115);let l=e=>{let{children:t,className:r,isOpen:l,onClose:d,preventOutsideClick:c=!1}=e;return(0,a.jsx)(n.N,{children:l&&(0,a.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40 p-4 overflow-y-auto",onClick:e=>{c||d()},children:(0,a.jsxs)(i.P.div,{initial:{scale:.6,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.6,opacity:0},transition:{duration:.3,ease:o.am},className:"relative section flex flex-col max-w-4xl max-h-[90vh] overflow-y-auto ".concat(r),onClick:e=>e.stopPropagation(),children:[(0,a.jsx)(s.A,{onClick:d,className:"absolute self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300"}),t]})})})}},25784:(e,t,r)=>{r.d(t,{A:()=>n});let a=r(23464).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});a.interceptors.request.use(e=>e,e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let n=a},53999:(e,t,r)=>{r.d(t,{Y:()=>o,cn:()=>i});var a=r(52596),n=r(39688);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,a.$)(t))}function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"short";if(!e)return"";try{let r="string"==typeof e?new Date(e):e;if(isNaN(r.getTime()))return"";switch(t){case"short":return r.toLocaleDateString(void 0,{year:"numeric",month:"short",day:"numeric"});case"long":return r.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});case"full":return r.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",weekday:"long",hour:"2-digit",minute:"2-digit",second:"2-digit"});default:return r.toLocaleDateString()}}catch(t){return console.error("Error formatting date:",t),String(e)}}},66163:(e,t,r)=>{r.d(t,{x:()=>u});var a=r(95155),n=r(12115),i=r(36268),o=r(11032),s=r(88524),l=r(67133),d=r(97168),c=r(29911);let u=e=>{let{columns:t,data:r,globalFilter:u,setGlobalFilter:m,onTableInit:g,columnVisibility:x,setColumnVisibility:h,onRowSelectionChange:p,rowSelection:v,onRowClick:b}=e,[f,y]=n.useState({pageIndex:0,pageSize:8}),[j,w]=n.useState([]),[N,k]=n.useState([]),[C,S]=n.useState({}),[_,z]=n.useState({}),F=void 0!==v?v:_,P=(0,i.N4)({data:r,columns:t,onPaginationChange:y,onColumnFiltersChange:w,onGlobalFilterChange:m,onColumnVisibilityChange:null!=h?h:S,onRowSelectionChange:e=>{let t="function"==typeof e?e(F):e;void 0===v&&z(t),p&&p(t)},onSortingChange:k,getCoreRowModel:(0,o.HT)(),getFilteredRowModel:(0,o.hM)(),getPaginationRowModel:(0,o.kW)(),getSortedRowModel:(0,o.h5)(),enableRowSelection:!0,enableSorting:!0,enableSortingRemoval:!0,state:{pagination:f,columnFilters:j,globalFilter:u,columnVisibility:null!=x?x:C,rowSelection:F,sorting:N}});return n.useEffect(()=>{g&&g(P)},[g,P]),n.useEffect(()=>{void 0!==v&&P.setRowSelection(v)},[v,P]),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"rounded-md border border-neutral-400 overflow-hidden",children:(0,a.jsxs)(s.XI,{className:"min-w-full",children:[(0,a.jsx)(s.A0,{className:"h-20",children:P.getHeaderGroups().map(e=>(0,a.jsx)(s.Hj,{className:"text-sm border-neutral-400",children:e.headers.map(e=>(0,a.jsxs)(s.nd,{className:"py-1 px-6 text-left bg-primary-500 text-neutral-100 font-semibold ".concat(0===e.index?"w-12 py-3 px-6":""),style:{cursor:e.column.getCanSort()?"pointer":"default"},children:[(0,a.jsx)("div",{onClick:e.column.getToggleSortingHandler(),children:(0,a.jsx)("div",{children:e.isPlaceholder?null:(0,i.Kv)(e.column.columnDef.header,e.getContext())})}),"validation"===e.column.id?(0,a.jsxs)(l.rI,{children:[(0,a.jsx)(l.ty,{asChild:!0,children:(0,a.jsxs)(d.$,{variant:"outline",className:"h-8 my-1 text-neutral-700 cursor-pointer",children:["Filter",(0,a.jsx)(c.Vr3,{})]})}),(0,a.jsxs)(l.SQ,{className:"bg-neutral-100 border border-neutral-200 shadow-md cursor-pointer",children:[(0,a.jsx)(l._2,{className:"cursor-pointer hover:bg-neutral-300",onClick:()=>e.column.setFilterValue("Valid"),children:"Valid"}),(0,a.jsx)(l._2,{className:"cursor-pointer hover:bg-neutral-300",onClick:()=>e.column.setFilterValue("Not Valid"),children:"Not Valid"}),(0,a.jsx)(l._2,{className:"cursor-pointer hover:bg-neutral-300",onClick:()=>e.column.setFilterValue(""),children:"Clear Filter"})]})]}):e.column.getCanFilter()&&(0,a.jsx)("input",{placeholder:"Search...",value:e.column.getFilterValue()||"",onChange:t=>e.column.setFilterValue(t.target.value),className:"input-field max-w-48 text-sm my-1 px-2 py-1 bg-neutral-100 text-neutral-700 font-light border-none rounded-md"})]},e.id))},e.id))}),(0,a.jsx)(s.BF,{children:P.getPaginationRowModel().rows.length?P.getPaginationRowModel().rows.map(e=>(0,a.jsx)(s.Hj,{"data-state":e.getIsSelected()&&"selected",className:"hover:bg-neutral-50 text-sm border-neutral-400",onClick:()=>null==b?void 0:b(e.original),children:e.getVisibleCells().map((e,t)=>(0,a.jsx)(s.nA,{className:"py-4 px-6 max-w-48  ".concat(0===t?"py-3 px-6":""," text-neutral-700 "),children:(0,i.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,a.jsx)(s.Hj,{children:(0,a.jsx)(s.nA,{colSpan:t.length,className:"h-24 text-center",children:"No results."})})})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2 py-4",children:[(0,a.jsxs)("div",{className:"flex-1 text-sm text-muted-foreground",children:[P.getFilteredSelectedRowModel().rows.length," of"," ",P.getFilteredRowModel().rows.length," row(s) selected."]}),r.length>f.pageSize&&(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2 py-4",children:[(0,a.jsx)("button",{className:"btn-primary",onClick:()=>P.previousPage(),disabled:!P.getCanPreviousPage(),children:"Previous"}),(0,a.jsx)("button",{className:"btn-primary",onClick:()=>P.nextPage(),disabled:!P.getCanNextPage(),children:"Next"})]})]})]})}},67133:(e,t,r)=>{r.d(t,{SQ:()=>d,_2:()=>c,hO:()=>u,rI:()=>s,ty:()=>l});var a=r(95155);r(12115);var n=r(48698),i=r(5196),o=r(53999);function s(e){let{...t}=e;return(0,a.jsx)(n.bL,{"data-slot":"dropdown-menu",...t})}function l(e){let{...t}=e;return(0,a.jsx)(n.l9,{"data-slot":"dropdown-menu-trigger",...t})}function d(e){let{className:t,sideOffset:r=4,...i}=e;return(0,a.jsx)(n.ZL,{children:(0,a.jsx)(n.UC,{"data-slot":"dropdown-menu-content",sideOffset:r,className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...i})})}function c(e){let{className:t,inset:r,variant:i="default",...s}=e;return(0,a.jsx)(n.q7,{"data-slot":"dropdown-menu-item","data-inset":r,"data-variant":i,className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...s})}function u(e){let{className:t,children:r,checked:s,...l}=e;return(0,a.jsxs)(n.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),checked:s,...l,children:[(0,a.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(n.VF,{children:(0,a.jsx)(i.A,{className:"size-4"})})}),r]})}},88524:(e,t,r)=>{r.d(t,{A0:()=>o,BF:()=>s,Hj:()=>l,XI:()=>i,nA:()=>c,nd:()=>d});var a=r(95155);r(12115);var n=r(53999);function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,n.cn)("w-full caption-bottom text-sm",t),...r})})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,n.cn)("[&_tr]:border-b",t),...r})}function s(e){let{className:t,...r}=e;return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,n.cn)("[&_tr:last-child]:border-0",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,n.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,n.cn)("text-foreground h-10 px-2 text-left align-middle font-medium neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,n.cn)("p-2 align-middle neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...r})}},88570:(e,t,r)=>{r.d(t,{D:()=>s,l:()=>o});var a=r(41050);let n=r(49509).env.SALT||"rushan-salt",i=new a.A(n,12),o=e=>i.encode(e),s=e=>{let t=i.decode(e)[0];return"bigint"==typeof t?t<Number.MAX_SAFE_INTEGER?Number(t):null:"number"==typeof t?t:null}},89852:(e,t,r)=>{r.d(t,{p:()=>i});var a=r(95155);r(12115);var n=r(53999);function i(e){let{className:t,type:r,...i}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-primary-500 focus-visible:ring-[1px]","focus-visible:outline-none","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},95139:(e,t,r)=>{r.d(t,{S:()=>s});var a=r(95155);r(12115);var n=r(76981),i=r(5196),o=r(53999);function s(e){let{className:t,...r}=e;return(0,a.jsx)(n.bL,{"data-slot":"checkbox",className:(0,o.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...r,children:(0,a.jsx)(n.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,a.jsx)(i.A,{className:"size-3.5"})})})}},97168:(e,t,r)=>{r.d(t,{$:()=>l});var a=r(95155);r(12115);var n=r(99708),i=r(74466),o=r(53999);let s=(0,i.F)("inline-flex items-center justify-center gap-2 neutral-100space-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-neutral-100 shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:i,asChild:l=!1,...d}=e,c=l?n.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,o.cn)(s({variant:r,size:i,className:t})),...d})}}}]);