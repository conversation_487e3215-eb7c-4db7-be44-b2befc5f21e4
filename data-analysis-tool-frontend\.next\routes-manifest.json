{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/dashboard/[status]/not-available", "regex": "^/dashboard/([^/]+?)/not\\-available(?:/)?$", "routeKeys": {"nxtPstatus": "nxtPstatus"}, "namedRegex": "^/dashboard/(?<nxtPstatus>[^/]+?)/not\\-available(?:/)?$"}, {"page": "/edit-submission/[hashedId]/[submissionId]", "regex": "^/edit\\-submission/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPhashedId": "nxtPhashedId", "nxtPsubmissionId": "nxtPsubmissionId"}, "namedRegex": "^/edit\\-submission/(?<nxtPhashedId>[^/]+?)/(?<nxtPsubmissionId>[^/]+?)(?:/)?$"}, {"page": "/form-submission/[hashedId]", "regex": "^/form\\-submission/([^/]+?)(?:/)?$", "routeKeys": {"nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/form\\-submission/(?<nxtPhashedId>[^/]+?)(?:/)?$"}, {"page": "/form-submission/[hashedId]/sign-in", "regex": "^/form\\-submission/([^/]+?)/sign\\-in(?:/)?$", "routeKeys": {"nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/form\\-submission/(?<nxtPhashedId>[^/]+?)/sign\\-in(?:/)?$"}, {"page": "/library/template/[hashedId]/form-builder", "regex": "^/library/template/([^/]+?)/form\\-builder(?:/)?$", "routeKeys": {"nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/library/template/(?<nxtPhashedId>[^/]+?)/form\\-builder(?:/)?$"}, {"page": "/library/template/[hashedId]/settings", "regex": "^/library/template/([^/]+?)/settings(?:/)?$", "routeKeys": {"nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/library/template/(?<nxtPhashedId>[^/]+?)/settings(?:/)?$"}, {"page": "/project/[hashedId]/data", "regex": "^/project/([^/]+?)/data(?:/)?$", "routeKeys": {"nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/project/(?<nxtPhashedId>[^/]+?)/data(?:/)?$"}, {"page": "/project/[hashedId]/data/downloads", "regex": "^/project/([^/]+?)/data/downloads(?:/)?$", "routeKeys": {"nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/project/(?<nxtPhashedId>[^/]+?)/data/downloads(?:/)?$"}, {"page": "/project/[hashedId]/data/gallery", "regex": "^/project/([^/]+?)/data/gallery(?:/)?$", "routeKeys": {"nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/project/(?<nxtPhashedId>[^/]+?)/data/gallery(?:/)?$"}, {"page": "/project/[hashedId]/data/map", "regex": "^/project/([^/]+?)/data/map(?:/)?$", "routeKeys": {"nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/project/(?<nxtPhashedId>[^/]+?)/data/map(?:/)?$"}, {"page": "/project/[hashedId]/data/reports", "regex": "^/project/([^/]+?)/data/reports(?:/)?$", "routeKeys": {"nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/project/(?<nxtPhashedId>[^/]+?)/data/reports(?:/)?$"}, {"page": "/project/[hashedId]/data/table", "regex": "^/project/([^/]+?)/data/table(?:/)?$", "routeKeys": {"nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/project/(?<nxtPhashedId>[^/]+?)/data/table(?:/)?$"}, {"page": "/project/[hashedId]/form-builder", "regex": "^/project/([^/]+?)/form\\-builder(?:/)?$", "routeKeys": {"nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/project/(?<nxtPhashedId>[^/]+?)/form\\-builder(?:/)?$"}, {"page": "/project/[hashedId]/overview", "regex": "^/project/([^/]+?)/overview(?:/)?$", "routeKeys": {"nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/project/(?<nxtPhashedId>[^/]+?)/overview(?:/)?$"}, {"page": "/project/[hashedId]/settings", "regex": "^/project/([^/]+?)/settings(?:/)?$", "routeKeys": {"nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/project/(?<nxtPhashedId>[^/]+?)/settings(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/account/profile", "regex": "^/account/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/account/profile(?:/)?$"}, {"page": "/account/security", "regex": "^/account/security(?:/)?$", "routeKeys": {}, "namedRegex": "^/account/security(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/library", "regex": "^/library(?:/)?$", "routeKeys": {}, "namedRegex": "^/library(?:/)?$"}, {"page": "/library/asset", "regex": "^/library/asset(?:/)?$", "routeKeys": {}, "namedRegex": "^/library/asset(?:/)?$"}, {"page": "/library/not-available", "regex": "^/library/not\\-available(?:/)?$", "routeKeys": {}, "namedRegex": "^/library/not\\-available(?:/)?$"}, {"page": "/library/question-block/form-builder", "regex": "^/library/question\\-block/form\\-builder(?:/)?$", "routeKeys": {}, "namedRegex": "^/library/question\\-block/form\\-builder(?:/)?$"}, {"page": "/policy", "regex": "^/policy(?:/)?$", "routeKeys": {}, "namedRegex": "^/policy(?:/)?$"}, {"page": "/reset-password", "regex": "^/reset\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/reset\\-password(?:/)?$"}, {"page": "/reset-password/change-password", "regex": "^/reset\\-password/change\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/reset\\-password/change\\-password(?:/)?$"}, {"page": "/signup", "regex": "^/signup(?:/)?$", "routeKeys": {}, "namedRegex": "^/signup(?:/)?$"}, {"page": "/terms", "regex": "^/terms(?:/)?$", "routeKeys": {}, "namedRegex": "^/terms(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}