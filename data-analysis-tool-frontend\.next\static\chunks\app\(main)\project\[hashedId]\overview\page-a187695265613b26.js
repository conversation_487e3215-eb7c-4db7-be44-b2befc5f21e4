(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9756],{25784:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let a=t(23464).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});a.interceptors.request.use(e=>e,e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let r=a},29350:(e,s,t)=>{"use strict";t.d(s,{A:()=>c});var a=t(97381),r=t(59362),l=t(25784),i=t(35695),n=t(12115),o=t(34540);let c=e=>{let s=(0,o.wA)(),t=(0,i.useRouter)(),c=(0,i.usePathname)(),{status:d,user:u,error:p}=(0,o.d4)(e=>e.auth),m=async()=>{try{s((0,a.Le)());let e=(await l.A.get("/users/me")).data;s((0,a.tQ)(e))}catch(l){if(s((0,a.x9)()),(0,r.F0)(l)){var e,i,n,o,d;if(console.error("Auth error:",null==(e=l.response)?void 0:e.status,null==(i=l.response)?void 0:i.data),(null==(n=l.response)?void 0:n.status)===401){if(c.startsWith("/form-submission"))return;t.push("/")}else s((0,a.jB)((null==(d=l.response)||null==(o=d.data)?void 0:o.message)||l.message))}else s((0,a.jB)(l instanceof Error?l.message:"An unknown error occurred."))}};return(0,n.useEffect)(()=>{(null==e?void 0:e.skipFetchUser)||m()},[null==e?void 0:e.skipFetchUser]),(0,n.useEffect)(()=>{let e=e=>{if("logout"===e.key&&"true"===e.newValue)if(s((0,a.x9)()),c.startsWith("/form-submission")){let e=c.split("/")[2];e?t.push("/form-submission/".concat(e,"/sign-in")):t.push("/")}else t.push("/")};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[s,t,c]),{status:d,user:u,error:p,isAuthenticated:"authenticated"===d,isLoading:"loading"===d,refreshAuthState:()=>{m()},signin:async(e,s,t)=>{try{await l.A.post("/users/login",e),await m(),null==s||s()}catch(e){if(e instanceof r.pe){var a,i;let s=null==(i=e.response)||null==(a=i.data)?void 0:a.errorType;null==t||t(s)}else null==t||t()}},logout:async()=>{try{await l.A.post("/users/logout"),localStorage.setItem("logout","true"),setTimeout(()=>localStorage.removeItem("logout"),100)}finally{if(s((0,a.x9)()),c.startsWith("/form-submission")){let e=c.split("/")[2];e?t.push("/form-submission/".concat(e,"/sign-in")):t.push("/")}else t.push("/")}}}}},39953:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>A});var a=t(95155),r=t(85339),l=t(94788),i=t(71007),n=t(17576),o=t(81887),c=t(14186),d=t(69074),u=t(59964),p=t(29869),m=t(34869),h=t(12115),x=t(63008),j=t(57799),y=t(19373),f=t(77361),g=t(35695),v=t(88570),N=t(29350),b=t(34540),w=t(71402);let A=()=>{var e;let[s,t]=(0,h.useState)(!1);(0,h.useEffect)(()=>{t(!0)},[]);let{hashedId:A}=(0,g.useParams)(),E=(0,v.D)(A),{user:z}=(0,N.A)(),k=(0,b.wA)(),{data:D,isLoading:L,isError:S}=(0,y.I)({queryKey:["projects",null==z?void 0:z.id,E],queryFn:()=>(0,f.kf)({projectId:E}),enabled:!!E&&!!(null==z?void 0:z.id)});return s?L?(0,a.jsx)(j.A,{}):A&&null!==E?S?(0,a.jsx)("p",{className:"text-red-500",children:"Failed to fetch project. Please try again"}):(0,a.jsxs)("div",{className:"flex flex-col gap-8",children:[(0,a.jsxs)("section",{className:"flex flex-col gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-lg flex items-center gap-2 font-medium capitalize",children:[(0,a.jsx)(r.A,{size:18})," Description"]}),(0,a.jsxs)("span",{className:"rounded-full px-2 py-1 bg-accent-200 text-accent-700 font-medium text-sm flex items-center gap-2",children:[(0,a.jsx)("span",{className:"size-2 rounded-full bg-accent-700"}),null==D?void 0:D.status]})]}),(0,a.jsx)("p",{children:null==D?void 0:D.description})]}),(0,a.jsxs)("section",{className:"grid grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"rounded-md border-2 border-neutral-400 p-8 flex items-center justify-center flex-col gap-2",children:[(0,a.jsxs)("span",{className:"label-text capitalize",children:[(0,a.jsx)(l.A,{size:16}),"Questions"]}),(0,a.jsx)("span",{className:"text-lg font-medium",children:null==D||null==(e=D.questions)?void 0:e.length})]}),(0,a.jsxs)("div",{className:"rounded-md border-2 border-neutral-400 p-8 flex items-center justify-center flex-col gap-2",children:[(0,a.jsxs)("span",{className:"label-text capitalize",children:[(0,a.jsx)(i.A,{size:16}),"Owner"]}),(0,a.jsx)("span",{className:"text-lg font-medium",children:null==D?void 0:D.user.name})]}),(0,a.jsxs)("div",{className:"rounded-md border-2 border-neutral-400 p-8 flex items-center justify-center text-center flex-col gap-2",children:[(0,a.jsxs)("span",{className:"label-text capitalize",children:[(0,a.jsx)(n.A,{size:16}),"Sector"]}),(0,a.jsx)("span",{className:"text-lg font-medium w-full truncate",children:null==D?void 0:D.sector})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsx)("span",{className:"text-lg flex items-center gap-2 font-medium capitalize",children:"Collect Data (Online Form Submissions)"}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)("button",{className:"btn-primary",onClick:()=>{if(A){let e="".concat(window.location.origin,"/form-submission/").concat(A);navigator.clipboard.writeText(e).then(()=>{k((0,w.Ds)({message:"Link copied to clipboard!",type:"success"}))}).catch(e=>{console.error("Failed to copy: ",e),k((0,w.Ds)({message:"Failed to copy the link.",type:"error"}))})}else k((0,w.Ds)({message:"Invalid link. Try again.",type:"warning"}))},children:"Copy"}),(0,a.jsx)("button",{className:"btn-primary",onClick:()=>{A?window.open("/form-submission/".concat(A),"_blank"):console.error("hashedId is missing")},children:"Open"})]}),(0,a.jsxs)("span",{className:"text-lg flex items-center gap-2 font-medium capitalize",children:[(0,a.jsx)(o.A,{size:18}),"Timeline"]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("div",{className:"rounded-full p-2 bg-primary-200",children:(0,a.jsx)(c.A,{size:16,className:"text-primary-500"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"capitalize",children:"Last modified"}),(0,a.jsxs)("span",{className:"label-text",children:[(0,a.jsx)(d.A,{size:16}),(null==D?void 0:D.updatedAt)?(0,x.GP)(new Date(D.updatedAt),"MMMM d, yyyy h:mm a"):"N/A"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("div",{className:"rounded-full p-2 bg-primary-200",children:(0,a.jsx)(u.A,{size:16,className:"text-primary-500"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"capitalize",children:"Last deployed"}),(0,a.jsxs)("span",{className:"label-text",children:[(0,a.jsx)(d.A,{size:16}),(null==D?void 0:D.lastDeployedAt)?(0,x.GP)(new Date(D.lastDeployedAt),"MMMM d, yyyy h:mm a"):"Not deployed yet"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("div",{className:"rounded-full p-2 bg-primary-200",children:(0,a.jsx)(p.A,{size:16,className:"text-primary-500"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"capitalize",children:"Latest submission"}),(0,a.jsxs)("span",{className:"label-text",children:[(0,a.jsx)(d.A,{size:16}),(null==D?void 0:D.lastSubmissionAt)?(0,x.GP)(new Date(D.lastSubmissionAt),"MMMM d, yyyy h:mm a"):"No submissions yet"]})]})]})]})]}),(0,a.jsxs)("section",{className:"label-text cursor-default",children:[(0,a.jsx)(m.A,{size:16})," ",null==D?void 0:D.country]})]}):(0,a.jsxs)("div",{className:"error-message",children:[(0,a.jsx)("h1",{className:"text-red-500",children:"Error: Invalid Project ID (hashedId)."}),(0,a.jsx)("p",{className:"text-neutral-700",children:"Please make sure the URL contains a valid project identifier."})]}):null}},40084:(e,s,t)=>{Promise.resolve().then(t.bind(t,39953))},57799:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(95155);t(12115);let r=()=>(0,a.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,a.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},71402:(e,s,t)=>{"use strict";t.d(s,{Ay:()=>i,Ds:()=>r,_b:()=>l});let a=(0,t(51990).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,s)=>{e.message=s.payload.message,e.type=s.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:r,hideNotification:l}=a.actions,i=a.reducer},77361:(e,s,t)=>{"use strict";t.d(s,{D_:()=>u,Im:()=>c,Oo:()=>p,c3:()=>l,kf:()=>r,lj:()=>h,or:()=>o,pf:()=>d,vj:()=>i,wI:()=>m,xx:()=>n});var a=t(25784);let r=async e=>{let{projectId:s}=e,{data:t}=await a.A.get("/projects/".concat(s));return t.project},l=async e=>{let{data:s}=await a.A.post("/projects/from-template",e);return s},i=async()=>{try{let{data:e}=await a.A.get("/projects");return e.projects}catch(e){throw console.error("Error fetching projects:",e),e}},n=async e=>{let{data:s}=await a.A.delete("/projects/delete/".concat(e));return s},o=async e=>{try{let{data:s}=await a.A.delete("/projects/delete-multiple",{data:{projectIds:e}});return s}catch(e){throw console.error("Error deleting multiple projects:",e),e}},c=async e=>{try{let{data:s}=await a.A.patch("/projects/change-status/".concat(e),{status:"archived"});return s}catch(e){throw console.error("Error archiving project:",e),e}},d=async function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{let{data:s}=await a.A.patch("/projects/change-status/".concat(e),{status:"deployed"});return s}catch(e){throw console.error("Error deploying project:",e),e}},u=async e=>{try{let{data:s}=await a.A.patch("/projects/update-many-status",{projectIds:e,status:"archived"});return s}catch(e){throw console.error("Error archiving multiple projects:",e),e}},p=async e=>{try{let{data:s}=await a.A.post("/users/check-email",{email:e});return s}catch(e){var s,t,r,l,i,n;throw Error("object"==typeof(null==(t=e.response)||null==(s=t.data)?void 0:s.message)?JSON.stringify(null==(l=e.response)||null==(r=l.data)?void 0:r.message):(null==(n=e.response)||null==(i=n.data)?void 0:i.message)||e.message||"Failed to check user")}},m=async e=>{let{projectId:s,email:t,permissions:r}=e;try{let e=await p(t);if(!e||!e.success)throw Error((null==e?void 0:e.message)||"User not found");let{data:l}=await a.A.post("/project-users",{userId:e.user.id,projectId:s,permission:r});return l}catch(e){var l,i,n,o,c,d;throw console.error("Error adding user to project:",e),Error("object"==typeof(null==(i=e.response)||null==(l=i.data)?void 0:l.message)?JSON.stringify(null==(o=e.response)||null==(n=o.data)?void 0:n.message):(null==(d=e.response)||null==(c=d.data)?void 0:c.message)||e.message||"Failed to add user")}},h=async e=>{try{let{data:s}=await a.A.post("/answers/multiple",e);return s}catch(e){throw console.error("Error creating answer submission:",e),e}}},88570:(e,s,t)=>{"use strict";t.d(s,{D:()=>n,l:()=>i});var a=t(41050);let r=t(49509).env.SALT||"rushan-salt",l=new a.A(r,12),i=e=>l.encode(e),n=e=>{let s=l.decode(e)[0];return"bigint"==typeof s?s<Number.MAX_SAFE_INTEGER?Number(s):null:"number"==typeof s?s:null}},97381:(e,s,t)=>{"use strict";t.d(s,{Ay:()=>o,Le:()=>i,jB:()=>n,tQ:()=>r,x9:()=>l});let a=(0,t(51990).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,s)=>{e.status="authenticated",e.user=s.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,s)=>{e.status="unauthenticated",e.error=s.payload,e.user=null}}}),{setAuthenticatedUser:r,setUnauthenticated:l,setAuthLoading:i,setAuthError:n}=a.actions,o=a.reducer}},e=>{var s=s=>e(e.s=s);e.O(0,[635,1445,6967,6903,4617,473,8441,1684,7358],()=>s(40084)),_N_E=e.O()}]);