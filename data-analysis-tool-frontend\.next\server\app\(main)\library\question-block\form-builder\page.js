(()=>{var e={};e.id=389,e.ids=[389],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},17660:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\(main)\\\\library\\\\question-block\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\library\\question-block\\layout.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23784:(e,t,r)=>{Promise.resolve().then(r.bind(r,17660))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28714:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),o=r(17090),i=r(20174);let n=()=>{let e=[{label:"Form Builder",icon:(0,s.jsx)(o.A,{size:16}),route:"/library/question-block/form-builder"}];return(0,s.jsx)(i.F,{items:e})};var a=r(85814),l=r.n(a);r(43210);var d=r(28559);let u=({children:e})=>(0,s.jsxs)("div",{className:"section flex flex-col gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h1",{className:"heading-text capitalize",children:"Question Block"}),(0,s.jsxs)(l(),{href:"/library",className:"flex items-center gap-2",children:[(0,s.jsx)(d.A,{size:16}),"Back to library"]})]}),(0,s.jsx)(n,{}),(0,s.jsx)("div",{className:"px-8",children:e})]})},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36697:(e,t,r)=>{Promise.resolve().then(r.bind(r,48562))},44160:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(60687),o=r(38017),i=r(43210),n=r(29494),a=r(75531),l=r(86429),d=r(88678),u=r(21650);let c={viewForm:!0,editForm:!0,viewSubmissions:!0,addSubmissions:!0,deleteSubmissions:!0,editSubmissions:!0,manageProject:!0,validateSubmissions:!0};function p(){let[e,t]=(0,i.useState)(!1),{user:r}=(0,u.A)(),p=r?.id,{data:m,isLoading:b,isError:x}=(0,n.I)({queryKey:["questionBlockQuestions",p],queryFn:()=>(0,a.dI)(),enabled:!!p,retry:1});if(!p)return(0,s.jsx)("div",{className:"p-6 text-center",children:(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto",children:[(0,s.jsx)("h1",{className:"text-xl font-semibold text-red-600 mb-2",children:"Authentication Required"}),(0,s.jsx)("p",{className:"text-neutral-700 mb-4",children:"You need to be logged in to access the question block form builder."}),(0,s.jsx)("a",{href:"/",className:"inline-block px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark",children:"Go to Login"})]})});if(b||!m)return(0,s.jsx)(l.A,{});if(x)return(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-red-500",children:"Error loading question block information"}),(0,s.jsx)("p",{className:"text-sm text-red-500 mt-2",children:"There was a problem fetching the questions. Please try refreshing the page."}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"If the problem persists, please check the browser console for more details."})]});let h=Array.isArray(m)?m:[];return(0,s.jsx)("div",{className:"p-6",children:e?(0,s.jsx)(o.V,{questions:h,questionGroups:[],contextType:"questionBlock",onClose:()=>t(!1)}):(0,s.jsx)(d.o,{setIsPreviewMode:t,questions:h,contextType:"questionBlock",contextId:p,permissions:c})})}},48562:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\(main)\\\\library\\\\question-block\\\\form-builder\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\library\\question-block\\form-builder\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73145:(e,t,r)=>{Promise.resolve().then(r.bind(r,44160))},74075:e=>{"use strict";e.exports=require("zlib")},74388:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>d});var s=r(65239),o=r(48088),i=r(88170),n=r.n(i),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["(main)",{children:["library",{children:["question-block",{children:["form-builder",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,48562)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\library\\question-block\\form-builder\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,17660)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\library\\question-block\\layout.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,19559)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\library\\question-block\\form-builder\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(main)/library/question-block/form-builder/page",pathname:"/library/question-block/form-builder",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94280:(e,t,r)=>{Promise.resolve().then(r.bind(r,28714))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7404,517,7605,5814,551,8581,4921,6886,4292,5841,4677,4072,9751],()=>r(74388));module.exports=s})();