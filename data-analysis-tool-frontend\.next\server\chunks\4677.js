exports.id=4677,exports.ids=[4677],exports.modules={3984:(e,t,a)=>{"use strict";a.d(t,{I7:()=>l,J2:()=>s,QK:()=>i,Xu:()=>n,nh:()=>o});var r=a(12810);let s=async({templateId:e})=>{let{data:t}=await r.A.get(`/libraries/${e}`);return t.template},n=async e=>{let{data:t}=await r.A.post("/libraries",e);return t},i=async()=>{let{data:e}=await r.A.get("/libraries");return e.templates},l=async e=>{let{data:t}=await r.A.delete(`/libraries/${e}`);return t},o=async({templateIds:e})=>null},6986:(e,t,a)=>{"use strict";a.d(t,{D:()=>l,l:()=>i});var r=a(53907);let s=process.env.SALT||"rushan-salt",n=new r.A(s,12),i=e=>n.encode(e),l=e=>{let t=n.decode(e)[0];return"bigint"==typeof t?t<Number.MAX_SAFE_INTEGER?Number(t):null:"number"==typeof t?t:null}},15566:e=>{"use strict";e.exports=JSON.parse('["Afghanistan","Albania","Algeria","Andorra","Angola","Argentina","Armenia","Australia","Austria","Azerbaijan","Bahamas","Bahrain","Bangladesh","Barbados","Belarus","Belgium","Belize","Benin","Bhutan","Bolivia","Bosnia and Herzegovina","Botswana","Brazil","Brunei","Bulgaria","Burkina Faso","Burundi","Cabo Verde","Cambodia","Cameroon","Canada","Central African Republic","Chad","Chile","China","Colombia","Comoros","Congo (Congo-Brazzaville)","Costa Rica","Croatia","Cuba","Cyprus","Czech Republic","Denmark","Djibouti","Dominica","Dominican Republic","Ecuador","Egypt","El Salvador","Equatorial Guinea","Eritrea","Estonia","Eswatini","Ethiopia","Fiji","Finland","France","Gabon","Gambia","Georgia","Germany","Ghana","Greece","Grenada","Guatemala","Guinea","Guinea-Bissau","Guyana","Haiti","Honduras","Hungary","Iceland","India","Indonesia","Iran","Iraq","Ireland","Israel","Italy","Jamaica","Japan","Jordan","Kazakhstan","Kenya","Kiribati","Kuwait","Kyrgyzstan","Laos","Latvia","Lebanon","Lesotho","Liberia","Libya","Liechtenstein","Lithuania","Luxembourg","Madagascar","Malawi","Malaysia","Maldives","Mali","Malta","Marshall Islands","Mauritania","Mauritius","Mexico","Micronesia","Moldova","Monaco","Mongolia","Montenegro","Morocco","Mozambique","Myanmar","Namibia","Nauru","Nepal","Netherlands","New Zealand","Nicaragua","Niger","Nigeria","North Korea","North Macedonia","Norway","Oman","Pakistan","Palau","Palestine","Panama","Papua New Guinea","Paraguay","Peru","Philippines","Poland","Portugal","Qatar","Romania","Russia","Rwanda","Saint Kitts and Nevis","Saint Lucia","Saint Vincent and the Grenadines","Samoa","San Marino","Sao Tome and Principe","Saudi Arabia","Senegal","Serbia","Seychelles","Sierra Leone","Singapore","Slovakia","Slovenia","Solomon Islands","Somalia","South Africa","South Korea","South Sudan","Spain","Sri Lanka","Sudan","Suriname","Sweden","Switzerland","Syria","Taiwan","Tajikistan","Tanzania","Thailand","Timor-Leste","Togo","Tonga","Trinidad and Tobago","Tunisia","Turkey","Turkmenistan","Tuvalu","Uganda","Ukraine","United Arab Emirates","United Kingdom","United States","Uruguay","Uzbekistan","Vanuatu","Vatican City","Venezuela","Vietnam","Yemen","Zambia","Zimbabwe"]')},15616:(e,t,a)=>{"use strict";a.d(t,{T:()=>i});var r=a(60687),s=a(43210),n=a(96241);let i=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700 dark:bg-gray-900 dark:placeholder:text-gray-500",e),ref:a,...t}));i.displayName="Textarea"},19559:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\(main)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\(main)\\layout.tsx","default")},24934:(e,t,a)=>{"use strict";a.d(t,{$:()=>o});var r=a(60687);a(43210);var s=a(8730),n=a(24224),i=a(96241);let l=(0,n.F)("inline-flex items-center justify-center gap-2 neutral-100space-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-neutral-100 shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o({className:e,variant:t,size:a,asChild:n=!1,...o}){let c=n?s.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:t,size:a,className:e})),...o})}},32833:(e,t,a)=>{"use strict";a.d(t,{b:()=>r});let r={information_media:"Information / Media",econommic_social_development:"Economic & Social Development",security_police_peacekeeping:"Security / Police / Peacekeeping",disarmament_and_demobilization:"Disarmament & Demobilization",environment:"Environment",private_sector:"Private Sector",humanitarian_coordination_information_management:"Humanitarian - Coordination & Info Management",humanitarian_multiple_clusters:"Humanitarian - Multiple Clusters",humanitarian_camp_management_and_coordination:"Humanitarian - Camp Management & Coordination",humanitarian_early_recovery:"Humanitarian - Early Recovery",humanitarian_education:"Humanitarian - Education",humanitarian_emergency_shelter:"Humanitarian - Emergency Shelter",humanitarian_emergency_telecoms:"Humanitarian - Emergency Telecoms",humanitarian_food_security:"Humanitarian - Food Security",humanitarian_health:"Humanitarian - Health",humanitarian_logistics:"Humanitarian - Logistics",humanitarian_nutrition:"Humanitarian - Nutrition",humanitarian_protection:"Humanitarian - Protection",humanitarian_sanitation_water_and_hygiene:"Humanitarian - Sanitation / Water / Hygiene",other:"Other"}},38587:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(60687),s=a(88920),n=a(57101),i=a(74699),l=a(11860);a(43210);let o=({children:e,className:t,isOpen:a,onClose:o,preventOutsideClick:c=!1})=>(0,r.jsx)(s.N,{children:a&&(0,r.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40 p-4 overflow-y-auto",onClick:e=>{c||o()},children:(0,r.jsxs)(n.P.div,{initial:{scale:.6,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.6,opacity:0},transition:{duration:.3,ease:i.am},className:`relative section flex flex-col max-w-4xl max-h-[90vh] overflow-y-auto ${t}`,onClick:e=>e.stopPropagation(),children:[(0,r.jsx)(l.A,{onClick:o,className:"absolute self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300"}),e]})})})},39390:(e,t,a)=>{"use strict";a.d(t,{J:()=>l});var r=a(60687),s=a(43210),n=a(78148),i=a(96241);let l=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.b,{ref:a,className:(0,i.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...t}));l.displayName=n.b.displayName},40347:(e,t,a)=>{"use strict";a.d(t,{C:()=>c,z:()=>o});var r=a(60687),s=a(43210),n=a(14555),i=a(65822),l=a(96241);let o=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.bL,{className:(0,l.cn)("grid gap-2",e),...t,ref:a}));o.displayName=n.bL.displayName;let c=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.q7,{ref:a,className:(0,l.cn)("aspect-square h-4 w-4 rounded-full border text-gray-900 shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-gray-950 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus-visible:ring-gray-300",e),...t,children:(0,r.jsx)(n.C1,{className:"flex items-center justify-center",children:(0,r.jsx)(i.A,{className:"h-2.5 w-2.5 fill-current text-current"})})}));c.displayName=n.q7.displayName},40480:(e,t,a)=>{"use strict";a.d(t,{H:()=>r});let r=(e,t)=>{let a=Object.entries(t).find(([t,a])=>a===e);return a?a[0]:null}},43782:(e,t,a)=>{"use strict";a.d(t,{Sc:()=>x.S,dO:()=>p}),a(24934),a(68988),a(39390),a(15616);var r=a(60687),s=a(43210),n=a(97822),i=a(78272),l=a(3589),o=a(13964),c=a(96241);n.bL,n.YJ,n.WT,s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsxs)(n.l9,{ref:s,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm ring-offset-neutral-100 placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 dark:border-gray-700 dark:bg-gray-900 dark:ring-offset-gray-900 dark:placeholder:text-gray-500",e),...a,children:[t,(0,r.jsx)(n.In,{asChild:!0,children:(0,r.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]})).displayName=n.l9.displayName;let d=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.PP,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(l.A,{className:"h-4 w-4"})}));d.displayName=n.PP.displayName;let u=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.wn,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(i.A,{className:"h-4 w-4"})}));u.displayName=n.wn.displayName,s.forwardRef(({className:e,children:t,position:a="popper",...s},i)=>(0,r.jsx)(n.ZL,{children:(0,r.jsxs)(n.UC,{ref:i,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border border-gray-200 bg-neutral-100 text-slate-700 shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:border-gray-700 dark:bg-gray-900 dark:text-slate-200","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...s,children:[(0,r.jsx)(d,{}),(0,r.jsx)(n.LM,{className:(0,c.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,r.jsx)(u,{})]})})).displayName=n.UC.displayName,s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.JU,{ref:a,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=n.JU.displayName,s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsxs)(n.q7,{ref:s,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-gray-100 focus:text-gray-900 data-[disabled]:pointer-events-none data-[disabled]:opacity-50 dark:focus:bg-gray-800 dark:focus:text-gray-50",e),...a,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(n.VF,{children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})}),(0,r.jsx)(n.p4,{children:t})]})).displayName=n.q7.displayName,s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.wv,{ref:a,className:(0,c.cn)("-mx-1 my-1 h-px bg-gray-200 dark:bg-gray-700",e),...t})).displayName=n.wv.displayName;var m=a(90270);let p=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(m.bL,{className:(0,c.cn)("peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 focus-visible:ring-offset-neutral-100 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 dark:focus-visible:ring-offset-gray-900",e),...t,ref:a,children:(0,r.jsx)(m.zi,{className:(0,c.cn)("pointer-events-none block h-5 w-5 rounded-full shadow-lg ring-0 transition-transform data-[state=checked]:bg-neutral-100 data-[state=unchecked]:bg-primary-500 data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));p.displayName=m.bL.displayName;var x=a(93437);a(40347)},49187:(e,t,a)=>{Promise.resolve().then(a.bind(a,66043))},55629:(e,t,a)=>{"use strict";a.d(t,{SQ:()=>c,_2:()=>d,hO:()=>u,rI:()=>l,ty:()=>o});var r=a(60687);a(43210);var s=a(26312),n=a(13964),i=a(96241);function l({...e}){return(0,r.jsx)(s.bL,{"data-slot":"dropdown-menu",...e})}function o({...e}){return(0,r.jsx)(s.l9,{"data-slot":"dropdown-menu-trigger",...e})}function c({className:e,sideOffset:t=4,...a}){return(0,r.jsx)(s.ZL,{children:(0,r.jsx)(s.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...a})})}function d({className:e,inset:t,variant:a="default",...n}){return(0,r.jsx)(s.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":a,className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n})}function u({className:e,children:t,checked:a,...l}){return(0,r.jsxs)(s.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),checked:a,...l,children:[(0,r.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{children:(0,r.jsx)(n.A,{className:"size-4"})})}),t]})}},66043:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>et});var r=a(60687),s=a(43210),n=a(21650),i=a(11437),l=a(40083),o=a(85814),c=a.n(o),d=a(16189),u=a(79962);let m=({toggleSidebar:e,navbarRef:t})=>{let[a,o]=(0,s.useState)(!1),m=(0,s.useRef)(null);(0,d.useRouter)(),(0,s.useEffect)(()=>{let e=e=>{m.current&&!m.current.contains(e.target)&&o(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let{user:p,logout:x}=(0,n.A)();return(0,r.jsx)("header",{className:"bg-primary-800 p-4 sticky top-0 z-40",ref:t,children:(0,r.jsxs)("div",{className:" flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("button",{onClick:e,className:"laptop:hidden text-neutral-100 p-2 rounded hover:bg-primary-700 ",children:(0,r.jsx)(u.$4x,{size:24})}),(0,r.jsx)(c(),{href:"/dashboard",className:"text-neutral-100 text-2xl font-bold cursor-pointer hover:text-neutral-300 transition-all ease-in-out",children:"Data Analysis"})]}),(0,r.jsxs)("div",{className:"relative",ref:m,children:[(0,r.jsx)("button",{onClick:()=>o(!a),className:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-neutral-100 font-semibold cursor-pointer",children:p?.name[0].toUpperCase()}),a&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-neutral-100 rounded-md shadow-lg p-4 flex flex-col gap-2 ",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"size-10 bg-orange-500 rounded-full flex items-center justify-center text-neutral-100 text-xl font-semibold shrink-0",children:p?.name[0].toUpperCase()}),(0,r.jsxs)("div",{className:"flex flex-col min-w-0",children:[(0,r.jsx)("div",{className:"font-medium capitalize",children:p?.name}),(0,r.jsx)("div",{className:"text-sm text-neutral-700 truncate",children:p?.email})]})]}),(0,r.jsx)(c(),{href:"/account/profile",className:"btn-primary",onClick:()=>{o(!1)},children:"Profile"})]}),(0,r.jsx)("hr",{className:"border-neutral-400"}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)(c(),{onClick:()=>o(!1),href:"/terms",className:"text-sm text-neutral-700 hover:bg-neutral-700/10 px-4 py-1 rounded-md",children:"Terms of Service"}),(0,r.jsx)(c(),{onClick:()=>o(!1),href:"/policy",className:"block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-200",children:"Privacy Policy"})]}),(0,r.jsx)("hr",{className:"border-neutral-400"}),(0,r.jsxs)(c(),{href:"#",className:"flex items-center px-4 py-2 gap-2 text-sm rounded-md text-neutral-700 hover:bg-neutral-700/10 transition-all duration-300",children:[(0,r.jsx)(i.A,{size:16}),"Language"]}),(0,r.jsx)("hr",{className:"border-neutral-400"}),(0,r.jsxs)("button",{onClick:x,className:"flex items-center text-neutral-700 gap-2 px-4 py-2 hover:bg-neutral-700/10 rounded-md active:scale-95 transition-all duration-300 w-full cursor-pointer ",children:[(0,r.jsx)(l.A,{size:16}),"Logout"]})]})]})]})})};var p=a(44255),x=a(26273),h=a(54864),b=a(58432),g=a(35790),f=a(17257),j=a(20255),y=a(69587),v=a(29494),N=a(71845);let w=()=>{let{user:e}=(0,n.A)(),{data:t,isLoading:a,isError:r}=(0,v.I)({queryKey:["projects",e?.id],queryFn:N.vj,enabled:!!e?.id}),s=[],i=[],l=[];return!a&&t&&(s=t.filter(e=>"deployed"===e.status),i=t.filter(e=>"draft"===e.status),l=t.filter(e=>"archived"===e.status)),{navItems:[{id:1,icon:f.nko,label:"Deployed",count:s.length||0,href:"/dashboard/deployed",category:"project"},{id:2,icon:j.fzI,label:"Draft",count:i.length||0,href:"/dashboard/draft",category:"project"},{id:3,icon:y.Wlj,label:"Archived",count:l.length||0,href:"/dashboard/archived",category:"project"},{id:4,icon:x.rjU,label:"My Library",count:0,href:"/library",category:"library"},{id:5,icon:j.Blu,label:"Collections",count:0,href:"/library/#",category:"library"}],deployedProjects:s,draftStatusProjects:i,archivedProjects:l}};var k=a(6986);let C=({href:e,label:t,icon:a,count:n})=>{let i=(0,d.usePathname)(),l=(0,d.useRouter)(),{deployedProjects:o,draftStatusProjects:u,archivedProjects:m}=w(),[p,x]=(0,s.useState)(!1),h=e=>{let t=(0,k.l)(e);l.push(`/project/${t}/overview`)},b=e=>{let t=(0,k.l)(e);return`/project/${t}/overview`},g=(()=>{switch(t){case"Draft":return u;case"Deployed":return o;case"Archived":return m;default:return[]}})();return(0,r.jsxs)("li",{children:[(0,r.jsxs)(c(),{href:e,onClick:a=>{("Deployed"===t||"Draft"===t||"Archived"===t)&&(n>0?(a.preventDefault(),x(!p)):(a.preventDefault(),l.push(`${e}/not-available`)))},className:"flex items-center px-4 py-3 hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300 ease-in-out rounded-md text-neutral-800",children:[(0,r.jsx)(a,{className:"mr-2",size:20}),(0,r.jsx)("span",{className:"text-sm font-bold",children:t}),(0,r.jsx)("span",{className:"ml-auto bg-neutral-200 text-neutral-700 rounded-full px-2 py-0.5 text-xs",children:n})]}),p&&g.length>0&&(0,r.jsx)("div",{className:"ml-6 mt-1 space-y-1",children:g.map(e=>(0,r.jsx)("div",{onClick:()=>h(e.id),className:`flex items-center px-4 py-2 text-sm hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300 ease-in-out rounded-md cursor-pointer ${i===b(e.id)?"bg-primary-500 text-neutral-100":""}`,children:e.name},e.id))})]})},S=({isOpen:e,topOffset:t,onNewProject:a})=>{let s=(0,d.usePathname)(),n=(0,h.wA)(),{navItems:i,deployedProjects:l,draftStatusProjects:o,archivedProjects:u}=w(),m=s.includes("/library")?"library":"project",f=i.filter(e=>e.category===m);return(0,r.jsxs)("aside",{style:{top:`${t}px`,height:`calc(100vh - ${t}px)`},className:`
        ${e?"translate-x-0":"-translate-x-full"}
        fixed left-0 h-full w-64 shadow-xl bg-neutral-100 z-30
        transform transition-all duration-300 ease-in-out
        laptop:translate-x-0 laptop:static laptop:flex flex
      `,children:[(0,r.jsx)("div",{className:"w-1/5 bg-neutral-200 p-4",children:(0,r.jsxs)("div",{className:"flex flex-col gap-5 items-center",children:[(0,r.jsx)(c(),{href:"/dashboard","aria-label":"Projects",className:`p-2  hover:bg-primary-500 hover:rounded-full transition-all duration-300 ease-in-out cursor-pointer
              ${"project"===m?"bg-primary-500 text-neutral-100 rounded-full":"text-neutral-700 hover:bg-primary-500 hover:text-neutral-100"}
            `,children:(0,r.jsx)(p._zY,{size:20,title:"Projects"})}),(0,r.jsx)(c(),{href:"/library","aria-label":"Library",className:`p-2 hover:bg-primary-500 hover:rounded-full transition-all duration-300 ease-in-out cursor-pointer
              ${"library"===m?"bg-primary-500 text-neutral-100 rounded-full":"text-neutral-700 hover:bg-primary-500 hover:text-neutral-100"}
            `,children:(0,r.jsx)(x.rjU,{size:20,title:"Library"})})]})}),(0,r.jsxs)("div",{className:"w-4/5 bg-neutral-100 flex-1",children:[(0,r.jsx)("div",{className:"p-4",children:(0,r.jsx)("button",{onClick:()=>{"project"===m?a?a():n((0,b.Gl)()):n((0,g.yg)())},className:"btn-primary w-full",children:"project"===m?"NEW PROJECT":"NEW ITEM"})}),(0,r.jsx)("nav",{className:"mt-2",children:(0,r.jsx)("ul",{className:"space-y-1",children:f.map(e=>(0,r.jsx)(C,{href:e.href,label:e.label,icon:e.icon,count:e.count},e.id))})})]})]})};var A=a(38587),z=a(27605),E=a(68292),P=a(15566),_=a(32833),F=a(10022),L=a(57800),R=a(12810),M=a(8693),q=a(54050),D=a(19150),I=a(40480);let T=async({name:e,description:t,sector:a,country:r})=>{let{data:s}=await R.A.post("/projects",{name:e,description:t,sector:a,country:r});return s},$=({isOpen:e,onClose:t,onBack:a}={})=>{let n=(0,h.d4)(e=>e.createProject.visible),l=(0,h.wA)(),{register:o,formState:{isSubmitting:c,errors:u,isSubmitted:m},handleSubmit:p,setValue:x}=(0,z.mN)(),[g,f]=(0,s.useState)(null),[j,y]=(0,s.useState)(null);(0,s.useEffect)(()=>{o("country",{required:"Please select a country"}),o("sector",{required:"Please select a sector"})},[o]),(0,s.useEffect)(()=>{x("country",g,{shouldValidate:m}),x("sector",j,{shouldValidate:m})},[x,g,j]);let[v,N]=(0,s.useState)(!1),w=()=>{N(!0),setTimeout(()=>{l((0,b.th)())},300)},k=(0,d.useRouter)(),C=(0,M.jE)(),S=(0,q.n)({mutationFn:T,onSuccess:()=>{C.invalidateQueries({queryKey:["projects"],exact:!1}),w(),k.push("/dashboard"),l((0,D.Ds)({message:"Project has been created successfully.",type:"success"}))},onError:()=>{l((0,D.Ds)({message:"Failed to create project",type:"error"}))}}),R=async e=>{S.mutate({name:e.projectName,description:e.description,sector:e.sector,country:e.country})};return(0,r.jsxs)(A.A,{isOpen:n&&!v,onClose:w,className:"w-4/5 laptop:w-3/5 ",children:[(0,r.jsx)("h1",{className:"heading-text",children:"Create a new project"}),(0,r.jsx)("form",{className:"flex flex-col gap-8 max-h-[600px] overflow-y-auto p-4",onSubmit:p(R),children:(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"project-name",className:"label-text",children:[(0,r.jsx)(F.A,{size:16})," Project Name"]}),(0,r.jsx)("input",{...o("projectName",{required:"Project name is required."}),id:"project-name",type:"text",className:"input-field",placeholder:"Enter a project name"}),u.projectName&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${u.projectName.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"description",className:"label-text",children:"Description"}),(0,r.jsx)("textarea",{id:"description",...o("description",{required:"Please enter the project description"}),className:"input-field resize-none",cols:4,placeholder:"Enter the project description"}),u.description&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${u.description.message}`})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,r.jsx)(i.A,{size:16}),"Country"]}),(0,r.jsx)(E.l,{id:"country",options:P,value:g,onChange:f}),u.country&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${u.country.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,r.jsx)(L.A,{size:16})," Sector"]}),(0,r.jsx)(E.l,{id:"sector",options:Object.values(_.b),value:j&&_.b[j]?_.b[j]:"Select an option",onChange:e=>{y((0,I.H)(e,_.b))}}),u.sector&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${u.sector.message}`})]})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-3 mt-4",children:[a&&(0,r.jsx)("button",{type:"button",onClick:()=>{a&&a()},className:"btn-outline",children:"Back"}),(0,r.jsx)("button",{type:"submit",className:"btn-primary",children:c?(0,r.jsxs)("span",{className:"flex items-center gap-2",children:["Creating"," ",(0,r.jsx)("div",{className:"size-4 animate-spin border-x border-neutral-100 rounded-full"})]}):"Create Project"})]})]})})]})};var H=a(89011),V=a(3984);let B=({handleClose:e})=>{let t=(0,h.wA)(),{register:a,formState:{isSubmitting:l,errors:o,isSubmitted:c},handleSubmit:d,setValue:u}=(0,z.mN)(),[m,p]=(0,s.useState)(null),[x,b]=(0,s.useState)(null);(0,s.useEffect)(()=>{a("country",{required:"Please select a country"}),a("sector",{required:"Please select a sector"})},[a]),(0,s.useEffect)(()=>{u("country",m,{shouldValidate:c}),u("sector",x,{shouldValidate:c})},[u,m,x]);let g=(0,M.jE)(),{user:f}=(0,n.A)(),j=(0,q.n)({mutationFn:V.Xu,onSuccess:()=>{g.invalidateQueries({queryKey:["templates",f?.id]}),e(),t((0,D.Ds)({type:"success",message:"Template created successfully"}))},onError:()=>{t((0,D.Ds)({type:"error",message:"Failed to create template. Please try again"}))}}),y=async e=>{let t={name:e.name,description:e.description,sector:e.sector,country:e.country};j.mutate(t)};return(0,r.jsxs)("form",{className:"flex flex-col gap-8",onSubmit:d(y),children:[(0,r.jsx)("h1",{className:"heading-text capitalize",children:"Create new project template"}),(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"project-name",className:"label-text",children:[(0,r.jsx)(F.A,{size:16})," Template Name"]}),(0,r.jsx)("input",{...a("name",{required:"Template name is required."}),id:"project-name",type:"text",className:"input-field",placeholder:"Enter a project name"}),o.name&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${o.name.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"description",className:"label-text",children:"Description"}),(0,r.jsx)("textarea",{id:"description",...a("description",{required:"Please enter the template description"}),className:"input-field resize-none",cols:4,placeholder:"Enter the project description"}),o.description&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${o.description.message}`})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,r.jsx)(i.A,{size:16}),"Country"]}),(0,r.jsx)(E.l,{id:"country",options:P,value:m,onChange:p}),o.country&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${o.country.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,r.jsx)(L.A,{size:16})," Sector"]}),(0,r.jsx)(E.l,{id:"sector",options:Object.values(_.b),value:x&&_.b[x]?_.b[x]:"Select an option",onChange:e=>{b((0,I.H)(e,_.b))}}),o.sector&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${o.sector.message}`})]})]}),(0,r.jsx)("button",{type:"submit",className:"btn-primary self-end",children:l?(0,r.jsxs)("span",{className:"flex items-center gap-2",children:["Creating"," ",(0,r.jsx)("div",{className:"size-4 animate-spin border-x border-neutral-100 rounded-full"})]}):"Create Project"})]})]})},O=()=>{let{visible:e,option:t}=(0,h.d4)(e=>e.createLibraryItem),a=(0,h.wA)(),[n,i]=(0,s.useState)(!1),l=()=>{i(!0),setTimeout(()=>{a((0,H.g7)())},300)};return(0,r.jsx)(A.A,{isOpen:e&&!n,onClose:l,className:"w-3/5",children:(()=>{switch(t){case"question-block":return(0,r.jsx)("div",{children:"Question Block"});case"template":return(0,r.jsx)(B,{handleClose:l});case"upload":return(0,r.jsx)("div",{children:"Upload"});case"collection":return(0,r.jsx)("div",{children:"Collection"});default:return null}})()})};var K=a(90471),U=a(86429),G=a(93617),Q=a(43782);let J=({selectedRowId:e,setSelectedRowId:t})=>[{id:"select",header:"",cell:({row:a})=>{let s=a.original.id;return(0,r.jsx)(Q.Sc,{className:"w-6 h-6 bg-neutral-100 rounded border border-neutral-400 data-[state=checked]:bg-neutral-100 data-[state=checked]:text-primary-500 data-[state=checked]:border-primary-500 cursor-pointer",checked:s===e,onCheckedChange:e=>t(e?s:null),"aria-label":"Select row"})}},{accessorKey:"name",header:"Name"},{id:"owner",accessorFn:e=>e.user?.name??"unknown",header:"Owner",cell:({getValue:e})=>e()},{id:"questions",accessorFn:e=>e.libraryQuestions?.length.toString()??"0",header:"Questions",cell:({getValue:e})=>e()},{accessorKey:"updatedAt",header:"Last Modified"}],W=({showModal:e,closeModal:t,back:a,templateId:l})=>{let{register:o,formState:{isSubmitting:c,errors:u,isSubmitted:m},handleSubmit:p,setValue:x,reset:b}=(0,z.mN)(),g=(0,h.wA)(),f=(0,d.useRouter)(),[j,y]=(0,s.useState)(null),[w,k]=(0,s.useState)(null);(0,s.useEffect)(()=>{o("country",{required:"Please select a country"}),o("sector",{required:"Please select a sector"})},[o]),(0,s.useEffect)(()=>{x("country",j,{shouldValidate:m}),x("sector",w,{shouldValidate:m})},[x,j,w]);let{user:C,isLoading:S}=(0,n.A)(),{data:R,isLoading:T,isError:$}=(0,v.I)({queryKey:["templates",C?.id,l],queryFn:()=>(0,V.J2)({templateId:l}),enabled:!!C?.id});(0,s.useEffect)(()=>{R&&(b({projectName:R.name,description:R.description,sector:R.sector,country:R.country}),y(R.country),k(R.sector))},[R,b]);let H=(0,M.jE)(),B=(0,q.n)({mutationFn:N.c3,onSuccess:()=>{H.invalidateQueries({queryKey:["projects"],exact:!1}),t(),f.push("/dashboard"),g((0,D.Ds)({message:"Project has been created successfully.",type:"success"}))},onError:e=>{g((0,D.Ds)({message:"Failed to create project"+e.message,type:"error"}))}}),O=async e=>{let t={templateId:l,name:e.projectName,description:e.description,sector:e.sector,country:e.country};B.mutate(t)};return S||T||$?null:(0,r.jsx)(A.A,{isOpen:e,onClose:t,className:"w-3/6",children:(0,r.jsxs)("form",{className:"flex flex-col gap-8",onSubmit:p(O),children:[(0,r.jsx)("h1",{className:"heading-text",children:"Create a new project"}),(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"project-name",className:"label-text",children:[(0,r.jsx)(F.A,{size:16})," Project Name"]}),(0,r.jsx)("input",{...o("projectName",{required:"Project name is required."}),id:"project-name",type:"text",className:"input-field",placeholder:"Enter a project name"}),u.projectName&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${u.projectName.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"description",className:"label-text",children:"Description"}),(0,r.jsx)("textarea",{id:"description",...o("description",{required:"Please enter the project description"}),className:"input-field resize-none",cols:4,placeholder:"Enter the project description"}),u.description&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${u.description.message}`})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,r.jsx)(i.A,{size:16}),"Country"]}),(0,r.jsx)(E.l,{id:"country",options:P,value:j,onChange:y}),u.country&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${u.country.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,r.jsx)(L.A,{size:16})," Sector"]}),(0,r.jsx)(E.l,{id:"sector",options:Object.values(_.b),value:w&&_.b[w]?_.b[w]:"Select an option",onChange:e=>{k((0,I.H)(e,_.b))}}),u.sector&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${u.sector.message}`})]})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-3 mt-4",children:[(0,r.jsx)("button",{type:"button",onClick:a,className:"btn-outline",children:"Back"}),(0,r.jsx)("button",{type:"submit",className:"btn-primary",children:c?(0,r.jsxs)("span",{className:"flex items-center gap-2",children:["Creating"," ",(0,r.jsx)("div",{className:"size-4 animate-spin border-x border-neutral-100 rounded-full"})]}):"Create Project"})]})]})]})})},X=({isOpen:e,back:t,onClose:a})=>{let{user:i}=(0,n.A)(),{data:l,isLoading:o,isError:c}=(0,v.I)({queryFn:V.QK,queryKey:["templates",i?.id],enabled:!!i?.id}),[d,u]=(0,s.useState)(null),[m,p]=(0,s.useState)(!1),[x,h]=(0,s.useState)(!1),b=J({selectedRowId:d,setSelectedRowId:u}),g=e=>{h(!0),setTimeout(()=>{p(!1),u(null),"close"===e&&a(),h(!1)},300)};return l?o?(0,r.jsx)(U.A,{}):c?(0,r.jsx)("div",{children:"Error loading data, please try again"}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(A.A,{isOpen:e&&!m,onClose:()=>{u(null),a()},className:"bg-neutral-100 p-4 mobile:p-6 rounded-lg w-[95%] mobile:w-[85%] tablet:w-[75%] laptop:w-[60%] desktop:w-[50%] max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,r.jsx)("div",{className:"flex justify-between items-center",children:(0,r.jsx)("h2",{className:"text-xl font-semibold text-neutral-700",children:"Select a Template"})}),(0,r.jsx)(G.x,{data:l,columns:b}),(0,r.jsxs)("div",{className:"flex justify-end gap-3 mt-4",children:[(0,r.jsx)("button",{onClick:()=>{u(null),t()},className:"btn-outline",children:"Back"}),(0,r.jsxs)("button",{type:"button",disabled:!d,className:"btn-primary",onClick:()=>{d&&p(!0)},children:["Next",(0,r.jsx)(K.ZK4,{})]})]})]})}),(m||x)&&null!==d&&(0,r.jsx)(W,{showModal:m&&!x,closeModal:()=>g("close"),back:()=>g("back"),templateId:d})]}):null},Y=({isOpen:e,onClose:t})=>{let a=(0,h.wA)(),[n,i]=(0,s.useState)(!1),l=e=>{i(!1),"close"===e&&t()};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(A.A,{isOpen:e&&!n,onClose:t,className:"bg-neutral-100 p-4 mobile:p-6 rounded-lg w-[95%] mobile:w-[85%] tablet:w-[75%] laptop:w-[60%] desktop:w-[50%] max-w-3xl mx-auto",children:(0,r.jsxs)("div",{className:"flex flex-col gap-4 mobile:gap-6",children:[(0,r.jsx)("div",{className:"flex justify-between items-center",children:(0,r.jsx)("h2",{className:"text-lg mobile:text-xl font-semibold text-neutral-700",children:"Create project: Choose a source"})}),(0,r.jsx)("p",{className:"text-sm mobile:text-base text-neutral-600",children:"Choose one of the options below to continue. You will be prompted to enter name and other details in further steps."}),(0,r.jsxs)("div",{className:"grid grid-cols-1 mobile:grid-cols-2 gap-3 mobile:gap-4",children:[(0,r.jsxs)("div",{onClick:()=>{t(),a((0,b.Gl)())},className:"bg-neutral-200 rounded-lg p-4 mobile:p-6 tablet:p-8 flex flex-col items-center justify-center gap-2 mobile:gap-3 cursor-pointer hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300",children:[(0,r.jsx)(j.Yvo,{className:"w-5 h-5 mobile:w-6 mobile:h-6"}),(0,r.jsx)("span",{className:"text-sm mobile:text-base text-center",children:"Build from scratch"})]}),(0,r.jsxs)("div",{onClick:()=>{i(!0)},className:"bg-neutral-200 rounded-lg p-4 mobile:p-6 tablet:p-8 flex flex-col items-center justify-center gap-2 mobile:gap-3 cursor-pointer hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300",children:[(0,r.jsx)(x.S1H,{className:"w-5 h-5 mobile:w-6 mobile:h-6"}),(0,r.jsx)("span",{className:"text-sm mobile:text-base text-center",children:"Use a template"})]})]})]})}),(0,r.jsx)(X,{isOpen:n,onClose:()=>l("close"),back:()=>l("back")})]})};var Z=a(17019);let ee=()=>{let e=(0,h.wA)(),t=(0,h.d4)(e=>e.createLibrary.visible),[a,n]=(0,s.useState)(!1),i=()=>{n(!0),setTimeout(()=>{e((0,g.l)())},300)},l=(0,d.useRouter)(),o=[{id:"question-block",title:"Question Block",icon:p.Kt4,onClick:()=>{i(),l.push("/library/question-block/form-builder")}},{id:"template",title:"Template",icon:u.zFA,onClick:()=>{i(),e((0,H.dQ)("template"))}},{id:"upload",title:"Upload",icon:Z.B88,onClick:()=>{i()}},{id:"collection",title:"Collection",icon:y.M1W,onClick:()=>{i()}}];return(0,r.jsxs)(A.A,{isOpen:t&&!a,onClose:i,className:"p-6 rounded-md w-3/5",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700 mb-4",children:"Create Library Item"}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-4",children:o.map(e=>(0,r.jsxs)("button",{onClick:e.onClick,className:"flex flex-col gap-2 items-center justify-center p-6 bg-neutral-200 rounded-md hover:bg-primary-500 hover:text-neutral-100 cursor-pointer transition-all duration-300",children:[(0,r.jsx)(e.icon,{size:24,className:""}),(0,r.jsx)("span",{className:"",children:e.title})]},e.id))})]})};function et({children:e}){let[t,a]=(0,s.useState)(!1),[n,i]=(0,s.useState)(0),[l,o]=(0,s.useState)(!1),c=(0,s.useRef)(null),d=()=>a(!t),u=(0,h.d4)(e=>e.createProject.visible),p=(0,h.d4)(e=>e.createLibrary.visible),x=(0,h.d4)(e=>e.createLibraryItem.visible);return(0,r.jsxs)("div",{className:"min-h-screen flex flex-col",children:[u&&(0,r.jsx)($,{}),p&&(0,r.jsx)(ee,{}),x&&(0,r.jsx)(O,{}),(0,r.jsx)(Y,{isOpen:l,onClose:()=>o(!1)}),(0,r.jsx)(m,{toggleSidebar:d,isSidebarOpen:t,navbarRef:c}),(0,r.jsxs)("div",{className:"flex flex-1 overflow-hidden",children:[(0,r.jsx)(S,{isOpen:t,toggleSidebar:d,topOffset:n,onNewProject:()=>{o(!0)}}),(0,r.jsx)("main",{className:"flex-1 p-6 overflow-y-auto",style:{height:`calc(100vh - ${n}px)`},children:e})]})]})}},68292:(e,t,a)=>{"use strict";a.d(t,{l:()=>i});var r=a(60687),s=a(78272),n=a(43210);let i=({id:e,options:t,value:a,onChange:i})=>{let[l,o]=(0,n.useState)(!1),c=(0,n.useRef)(null),d=(0,n.useRef)([]),u=(0,n.useRef)(null);(0,n.useEffect)(()=>{let e=e=>{u.current&&!u.current.contains(e.target)&&o(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let m=e=>{if(!l)return;let a=e.key.toLowerCase();if(a.match(/[a-z]/)){let e=t.findIndex(e=>e.toLowerCase().startsWith(a));-1!==e&&d.current[e]&&d.current[e]?.scrollIntoView({behavior:"auto",block:"nearest"})}};return(0,n.useEffect)(()=>(document.addEventListener("keydown",m),()=>{document.removeEventListener("keydown",m)}),[l,t]),(0,r.jsxs)("div",{className:"relative",ref:u,children:[(0,r.jsxs)("button",{id:e,type:"button",className:"px-4 py-2 flex items-center justify-between rounded-md border border-neutral-400 focus:border-primary-500 duration-300 w-full text-left cursor-pointer",onClick:()=>{o(!l)},children:[(0,r.jsx)("span",{children:a||"Select an option"}),(0,r.jsx)(s.A,{})]}),l&&(0,r.jsx)("ul",{className:"absolute z-10 max-h-[180px] overflow-auto border border-neutral-400 rounded-md bg-neutral-100 w-full mt-1 flex flex-col",ref:c,children:t.map((e,t)=>(0,r.jsx)("li",{ref:e=>{d.current[t]=e},className:"cursor-pointer bg-neutral-100 hover:bg-neutral-200 px-4 py-2",onClick:()=>{i(e),o(!1)},children:e},t))})]})}},68988:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var r=a(60687);a(43210);var s=a(96241);function n({className:e,type:t,...a}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-primary-500 focus-visible:ring-[1px]","focus-visible:outline-none","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...a})}},71845:(e,t,a)=>{"use strict";a.d(t,{D_:()=>u,Im:()=>c,Oo:()=>m,c3:()=>n,kf:()=>s,lj:()=>x,or:()=>o,pf:()=>d,vj:()=>i,wI:()=>p,xx:()=>l});var r=a(12810);let s=async({projectId:e})=>{let{data:t}=await r.A.get(`/projects/${e}`);return t.project},n=async e=>{let{data:t}=await r.A.post("/projects/from-template",e);return t},i=async()=>{try{let{data:e}=await r.A.get("/projects");return e.projects}catch(e){throw console.error("Error fetching projects:",e),e}},l=async e=>{let{data:t}=await r.A.delete(`/projects/delete/${e}`);return t},o=async e=>{try{let{data:t}=await r.A.delete("/projects/delete-multiple",{data:{projectIds:e}});return t}catch(e){throw console.error("Error deleting multiple projects:",e),e}},c=async e=>{try{let{data:t}=await r.A.patch(`/projects/change-status/${e}`,{status:"archived"});return t}catch(e){throw console.error("Error archiving project:",e),e}},d=async(e,t=!1)=>{try{let{data:t}=await r.A.patch(`/projects/change-status/${e}`,{status:"deployed"});return t}catch(e){throw console.error("Error deploying project:",e),e}},u=async e=>{try{let{data:t}=await r.A.patch("/projects/update-many-status",{projectIds:e,status:"archived"});return t}catch(e){throw console.error("Error archiving multiple projects:",e),e}},m=async e=>{try{let{data:t}=await r.A.post("/users/check-email",{email:e});return t}catch(e){throw Error("object"==typeof e.response?.data?.message?JSON.stringify(e.response?.data?.message):e.response?.data?.message||e.message||"Failed to check user")}},p=async({projectId:e,email:t,permissions:a})=>{try{let s=await m(t);if(!s||!s.success)throw Error(s?.message||"User not found");let{data:n}=await r.A.post("/project-users",{userId:s.user.id,projectId:e,permission:a});return n}catch(e){throw console.error("Error adding user to project:",e),Error("object"==typeof e.response?.data?.message?JSON.stringify(e.response?.data?.message):e.response?.data?.message||e.message||"Failed to add user")}},x=async e=>{try{let{data:t}=await r.A.post("/answers/multiple",e);return t}catch(e){throw console.error("Error creating answer submission:",e),e}}},86429:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(60687);a(43210);let s=()=>(0,r.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,r.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},89515:(e,t,a)=>{Promise.resolve().then(a.bind(a,19559))},93437:(e,t,a)=>{"use strict";a.d(t,{S:()=>l});var r=a(60687);a(43210);var s=a(40211),n=a(13964),i=a(96241);function l({className:e,...t}){return(0,r.jsx)(s.bL,{"data-slot":"checkbox",className:(0,i.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,r.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,r.jsx)(n.A,{className:"size-3.5"})})})}},93617:(e,t,a)=>{"use strict";a.d(t,{x:()=>m});var r=a(60687),s=a(43210),n=a.n(s),i=a(56090),l=a(93772),o=a(96752),c=a(55629),d=a(24934),u=a(69587);let m=({columns:e,data:t,globalFilter:a,setGlobalFilter:s,onTableInit:m,columnVisibility:p,setColumnVisibility:x,onRowSelectionChange:h,rowSelection:b,onRowClick:g})=>{let[f,j]=n().useState({pageIndex:0,pageSize:8}),[y,v]=n().useState([]),[N,w]=n().useState([]),[k,C]=n().useState({}),[S,A]=n().useState({}),z=void 0!==b?b:S,E=(0,i.N4)({data:t,columns:e,onPaginationChange:j,onColumnFiltersChange:v,onGlobalFilterChange:s,onColumnVisibilityChange:x??C,onRowSelectionChange:e=>{let t="function"==typeof e?e(z):e;void 0===b&&A(t),h&&h(t)},onSortingChange:w,getCoreRowModel:(0,l.HT)(),getFilteredRowModel:(0,l.hM)(),getPaginationRowModel:(0,l.kW)(),getSortedRowModel:(0,l.h5)(),enableRowSelection:!0,enableSorting:!0,enableSortingRemoval:!0,state:{pagination:f,columnFilters:y,globalFilter:a,columnVisibility:p??k,rowSelection:z,sorting:N}});return n().useEffect(()=>{m&&m(E)},[m,E]),n().useEffect(()=>{void 0!==b&&E.setRowSelection(b)},[b,E]),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"rounded-md border border-neutral-400 overflow-hidden",children:(0,r.jsxs)(o.XI,{className:"min-w-full",children:[(0,r.jsx)(o.A0,{className:"h-20",children:E.getHeaderGroups().map(e=>(0,r.jsx)(o.Hj,{className:"text-sm border-neutral-400",children:e.headers.map(e=>(0,r.jsxs)(o.nd,{className:`py-1 px-6 text-left bg-primary-500 text-neutral-100 font-semibold ${0===e.index?"w-12 py-3 px-6":""}`,style:{cursor:e.column.getCanSort()?"pointer":"default"},children:[(0,r.jsx)("div",{onClick:e.column.getToggleSortingHandler(),children:(0,r.jsx)("div",{children:e.isPlaceholder?null:(0,i.Kv)(e.column.columnDef.header,e.getContext())})}),"validation"===e.column.id?(0,r.jsxs)(c.rI,{children:[(0,r.jsx)(c.ty,{asChild:!0,children:(0,r.jsxs)(d.$,{variant:"outline",className:"h-8 my-1 text-neutral-700 cursor-pointer",children:["Filter",(0,r.jsx)(u.Vr3,{})]})}),(0,r.jsxs)(c.SQ,{className:"bg-neutral-100 border border-neutral-200 shadow-md cursor-pointer",children:[(0,r.jsx)(c._2,{className:"cursor-pointer hover:bg-neutral-300",onClick:()=>e.column.setFilterValue("Valid"),children:"Valid"}),(0,r.jsx)(c._2,{className:"cursor-pointer hover:bg-neutral-300",onClick:()=>e.column.setFilterValue("Not Valid"),children:"Not Valid"}),(0,r.jsx)(c._2,{className:"cursor-pointer hover:bg-neutral-300",onClick:()=>e.column.setFilterValue(""),children:"Clear Filter"})]})]}):e.column.getCanFilter()&&(0,r.jsx)("input",{placeholder:"Search...",value:e.column.getFilterValue()||"",onChange:t=>e.column.setFilterValue(t.target.value),className:"input-field max-w-48 text-sm my-1 px-2 py-1 bg-neutral-100 text-neutral-700 font-light border-none rounded-md"})]},e.id))},e.id))}),(0,r.jsx)(o.BF,{children:E.getPaginationRowModel().rows.length?E.getPaginationRowModel().rows.map(e=>(0,r.jsx)(o.Hj,{"data-state":e.getIsSelected()&&"selected",className:"hover:bg-neutral-50 text-sm border-neutral-400",onClick:()=>g?.(e.original),children:e.getVisibleCells().map((e,t)=>(0,r.jsx)(o.nA,{className:`py-4 px-6 max-w-48  ${0===t?"py-3 px-6":""} text-neutral-700 `,children:(0,i.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,r.jsx)(o.Hj,{children:(0,r.jsx)(o.nA,{colSpan:e.length,className:"h-24 text-center",children:"No results."})})})]})}),(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-2 py-4",children:[(0,r.jsxs)("div",{className:"flex-1 text-sm text-muted-foreground",children:[E.getFilteredSelectedRowModel().rows.length," of"," ",E.getFilteredRowModel().rows.length," row(s) selected."]}),t.length>f.pageSize&&(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-2 py-4",children:[(0,r.jsx)("button",{className:"btn-primary",onClick:()=>E.previousPage(),disabled:!E.getCanPreviousPage(),children:"Previous"}),(0,r.jsx)("button",{className:"btn-primary",onClick:()=>E.nextPage(),disabled:!E.getCanNextPage(),children:"Next"})]})]})]})}},96241:(e,t,a)=>{"use strict";a.d(t,{Y:()=>i,cn:()=>n});var r=a(49384),s=a(82348);function n(...e){return(0,s.QP)((0,r.$)(e))}function i(e,t="short"){if(!e)return"";try{let a="string"==typeof e?new Date(e):e;if(isNaN(a.getTime()))return"";switch(t){case"short":return a.toLocaleDateString(void 0,{year:"numeric",month:"short",day:"numeric"});case"long":return a.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});case"full":return a.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",weekday:"long",hour:"2-digit",minute:"2-digit",second:"2-digit"});default:return a.toLocaleDateString()}}catch(t){return console.error("Error formatting date:",t),String(e)}}},96752:(e,t,a)=>{"use strict";a.d(t,{A0:()=>i,BF:()=>l,Hj:()=>o,XI:()=>n,nA:()=>d,nd:()=>c});var r=a(60687);a(43210);var s=a(96241);function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm",e),...t})})}function i({className:e,...t}){return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",e),...t})}function l({className:e,...t}){return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",e),...t})}function o({className:e,...t}){return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function c({className:e,...t}){return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function d({className:e,...t}){return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}}};