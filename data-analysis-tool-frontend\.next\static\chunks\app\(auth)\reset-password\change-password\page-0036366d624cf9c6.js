(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3553],{25784:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let t=a(23464).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});t.interceptors.request.use(e=>e,e=>Promise.reject(e)),t.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let r=t},35169:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,s,a)=>{"use strict";var t=a(18999);a.o(t,"useParams")&&a.d(s,{useParams:function(){return t.useParams}}),a.o(t,"usePathname")&&a.d(s,{usePathname:function(){return t.usePathname}}),a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(s,{useSearchParams:function(){return t.useSearchParams}})},48355:(e,s,a)=>{Promise.resolve().then(a.bind(a,86257))},71402:(e,s,a)=>{"use strict";a.d(s,{Ay:()=>n,Ds:()=>r,_b:()=>l});let t=(0,a(51990).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,s)=>{e.message=s.payload.message,e.type=s.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:r,hideNotification:l}=t.actions,n=t.reducer},78749:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},86257:(e,s,a)=>{"use strict";a.d(s,{ChangePasswordPage:()=>w});var t=a(95155),r=a(71402),l=a(25784),n=a(90232),o=a(78749),c=a(92657),i=a(35169),d=a(6874),m=a.n(d),p=a(35695),u=a(12115),h=a(62177),x=a(34540);let w=()=>{let{register:e,formState:{errors:s,isSubmitting:a},handleSubmit:d,getValues:w,watch:f}=(0,h.mN)(),y=f("password"),b=f("confirmPassword"),N=(0,p.useSearchParams)().get("token"),j=(0,p.useRouter)(),g=(0,x.wA)(),[P,k]=(0,u.useState)(!1),[v,A]=(0,u.useState)(!1),C=async e=>{try{await l.A.post("/users/resetpassword",{token:N,newPassword:e.password}),g((0,r.Ds)({message:"Password changed successfully",type:"success"})),j.push("/")}catch(e){console.error(e)}};return N?(0,t.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,t.jsxs)("div",{className:"section flex flex-col gap-8 w-11/12 mobile:w-4/5 tablet:w-lg",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,t.jsx)(n.A,{size:36}),(0,t.jsx)("h1",{className:"text-2xl tablet:text-3xl font-semibold text-center",children:"Create new password"}),(0,t.jsx)("p",{className:"text-neutral-700 text-center",children:"Your new password must be different from previously used passwords and meet the requirements below."})]}),(0,t.jsxs)("form",{className:"flex flex-col gap-4 ",onSubmit:d(C),noValidate:!0,children:[(0,t.jsxs)("div",{className:"label-input-group group",children:[(0,t.jsx)("label",{htmlFor:"password",className:"label-text",children:"New Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{...e("password",{required:"Please enter your password",validate:{minLength:e=>e.length>=8||"Password must be at least 8 characters",hasUppercase:e=>/[A-Z]/.test(e)||"Password must contain at least one uppercase letter",hasNumber:e=>/\d/.test(e)||"Password must contain at least one number",hasSymbol:e=>/[\W_]/.test(e)||"Password must contain at least one symbol"}}),id:"password",type:P?"text":"password",className:"input-field w-full pr-10",placeholder:"Enter a new password"}),y&&y.length>0&&(0,t.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>k(!P),children:[P?(0,t.jsx)(o.A,{className:"h-4 w-4"}):(0,t.jsx)(c.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{className:"sr-only",children:[P?"Hide":"Show"," password"]})]})]}),s.password&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:"".concat(s.password.message)})]}),(0,t.jsxs)("div",{className:"label-input-group group",children:[(0,t.jsx)("label",{htmlFor:"confirm-password",className:"label-text",children:"Confirm Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{...e("confirmPassword",{required:"Please confirm your password",validate:e=>e===w("password")||"Passwords do not match"}),id:"confirm-password",type:v?"text":"password",className:"input-field w-full pr-10",placeholder:"Confirm your password"}),b&&b.length>0&&(0,t.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>A(!v),children:[v?(0,t.jsx)(o.A,{className:"h-4 w-4"}):(0,t.jsx)(c.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{className:"sr-only",children:[v?"Hide":"Show"," password"]})]})]}),s.confirmPassword&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:"".concat(s.confirmPassword.message)})]}),(0,t.jsx)("button",{type:"submit",className:"btn-primary",children:a?(0,t.jsxs)("span",{className:"flex items-center gap-2",children:["Updating"," ",(0,t.jsx)("div",{className:"animate-spin border-x-2 border-neutral-100 rounded-full size-4"})]}):(0,t.jsx)("span",{className:"flex items-center gap-2",children:"Reset password"})})]}),(0,t.jsxs)(m(),{href:"/",className:"text-neutral-700 self-center flex items-center gap-2",children:[(0,t.jsx)(i.A,{size:16})," Back to signin page"]})]})}):(0,t.jsx)("p",{className:"text-red-500",children:"No password reset token found in search params"})}},90232:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("shield-check",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},92657:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[635,1445,2177,6874,8441,1684,7358],()=>s(48355)),_N_E=e.O()}]);