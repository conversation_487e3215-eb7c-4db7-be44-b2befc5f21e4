"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3465],{10786:(e,s,t)=>{t.d(s,{m:()=>w});var r=t(95155),a=t(12115),l=t(13163),n=t(14549),i=t(19373),o=t(77361),c=t(35695),d=t(88570),u=t(29350),m=t(57799),p=t(54416),h=t(26715),x=t(5041),g=t(34540),v=t(71402);let f=[{label:"View Form ",value:"viewForm"},{label:"Edit Form",value:"editForm"},{label:"View Submissions",value:"viewSubmissions"},{label:"Edit submissions",value:"editSubmissions"},{label:"Add submissions",value:"addSubmissions"},{label:"Delete Submissions",value:"deleteSubmissions"},{label:"Validate Submissions",value:"validateSubmissions"},{label:"Manage Project",value:"manageProject"}],j=e=>{let{onClose:s,projectId:t,onUserAdded:l}=e,[n,i]=(0,a.useState)(""),[c,d]=(0,a.useState)([]),[u,m]=(0,a.useState)(""),[j,b]=(0,a.useState)(!1),[y,w]=(0,a.useState)(null),N=(0,h.jE)(),A=(0,g.wA)(),S=(0,a.useRef)(null),E=e=>/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(e),k=e=>{d(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},C=(0,x.n)({mutationFn:o.Oo,onSuccess:()=>{w(!0),m("")},onError:e=>{var s,t;w(!1),m((null==(t=e.response)||null==(s=t.data)?void 0:s.message)||"User with this email does not exist")},onSettled:()=>{b(!1)}}),U=()=>n?E(n)?y?0===c.length?(m("At least one permission must be selected"),!1):(m(""),!0):(m("User with this email does not exist"),!1):(m("Please enter a valid email"),!1):(m("Email is required"),!1),F=(0,x.n)({mutationFn:()=>{let e=c.reduce((e,s)=>(e[s]=!0,e),{});return(0,o.wI)({projectId:t,email:n,permissions:e})},onSuccess:()=>{N.invalidateQueries({queryKey:["projectUsers",t]}),A((0,v.Ds)({message:"User added to project successfully",type:"success"})),l&&l(),s()},onError:e=>{var s,t;let r;r="string"==typeof e?e:e instanceof Error?e.message:(null==(t=e.response)||null==(s=t.data)?void 0:s.message)?"object"==typeof e.response.data.message?JSON.stringify(e.response.data.message):e.response.data.message:"Failed to add user",A((0,v.Ds)({message:r,type:"error"})),m(r)}});return(0,r.jsxs)("div",{className:"bg-neutral-100 p-6 rounded-md",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{className:"w-full border ".concat(u?"border-red-500":"border-neutral-300"," rounded px-3 py-2 mb-4 focus:outline-none focus:ring-2 focus:ring-primary-200 placeholder:text-neutral-500"),placeholder:"Email address",value:n,onChange:e=>{let s=e.target.value;if(i(s),w(null),m(""),s){if(!E(s))return void m("Please enter a valid email address");S.current&&clearTimeout(S.current),S.current=setTimeout(()=>{b(!0),C.mutate(s)},800)}}}),(0,r.jsx)("button",{className:"absolute right-2 top-2 text-neutral-700 hover:text-neutral-900",onClick:s,type:"button",children:(0,r.jsx)(p.A,{size:22})}),j&&(0,r.jsx)("p",{className:"text-neutral-500 text-sm mb-2",children:"Verifying email..."}),!0===y&&(0,r.jsx)("p",{className:"text-green-500 text-sm mb-2",children:"User found"}),u&&(0,r.jsx)("p",{className:"text-red-500 text-sm mb-2",children:u})]}),(0,r.jsx)("div",{className:"flex flex-col gap-2",children:f.map(e=>(0,r.jsx)("div",{className:"flex flex-col",children:(0,r.jsxs)("label",{className:"flex items-center gap-2",children:[(0,r.jsx)("input",{type:"checkbox",checked:c.includes(e.value),onChange:()=>k(e.value)}),e.label]})},e.value))}),(0,r.jsx)("button",{className:"mt-6 ".concat(F.isPending||j?"bg-neutral-400":"bg-blue-400 hover:bg-blue-500"," text-white px-6 py-2 rounded disabled:opacity-50"),disabled:F.isPending||j||!n||0===c.length||!y,onClick:()=>{if(!t)return void m("Project ID is required");U()&&F.mutate()},children:F.isPending?"Adding...":"Grant permissions"})]})};var b=t(25784),y=t(97168);let w=e=>{let{showModal:s,onClose:t,onShare:p,selectedProject:h}=e,{hashedId:x}=(0,c.useParams)(),{user:g}=(0,u.A)(),[v,f]=(0,a.useState)(!1),w=x?(0,d.D)(x):null,N=(null==h?void 0:h.id)||w,{data:A,isLoading:S}=(0,i.I)({queryKey:["project",N],queryFn:async()=>await (0,o.kf)({projectId:N}),enabled:!!N&&!!(null==g?void 0:g.id)}),[E,k]=(0,a.useState)([]),[C,U]=(0,a.useState)(!1);if((0,a.useEffect)(()=>{let e=async()=>{if(N){U(!0);try{let e=await b.A.get("/project-users/".concat(N));if(e.data&&e.data.data&&e.data.data.AllUser){let s=e.data.data.AllUser||[];k(s)}else console.warn("No users data in response:",e.data),k([])}catch(e){console.error("Error fetching project users:",e),k([])}finally{U(!1)}}};s&&N&&e()},[N,s]),S)return(0,r.jsx)(m.A,{});let F=A||h;if(!F)return(0,r.jsx)(l.A,{isOpen:s,onClose:t,className:"p-6 rounded-md",children:(0,r.jsxs)("div",{className:"text-center py-4",children:[(0,r.jsx)("p",{className:"text-red-500",children:"Project not found"}),(0,r.jsx)(y.$,{onClick:t,className:"mt-4",children:"Close"})]})});let P=e=>{if(!e)return"bg-gray-500";let s=["bg-green-500","bg-blue-500","bg-red-500","bg-purple-500","bg-yellow-500","bg-pink-500","bg-indigo-500","bg-orange-500"];return s[e.charCodeAt(0)%s.length]},I=e=>e?e.charAt(0).toUpperCase():"?";return(0,r.jsxs)(l.A,{isOpen:s,onClose:t,className:"p-6 rounded-md",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:"Sharing Project: ".concat(F.name||"")}),(0,r.jsxs)("div",{className:"w-2xl mt-4 p-4 max-h-[500px] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("div",{className:"text-xl font-semibold",children:"Who has access"}),(0,r.jsxs)("div",{className:"flex items-center border rounded-md px-3 py-1.5 cursor-pointer hover:bg-gray-50",onClick:()=>f(!0),children:[(0,r.jsx)(n._rf,{size:18,className:"mr-2"}),(0,r.jsx)("div",{className:"text-sm",children:"Add user"})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[F.user&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-10 h-10 rounded-full ".concat(P(F.user.name)," flex items-center justify-center text-neutral-100 font-medium mr-3"),children:I(F.user.name)}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium",children:F.user.name||F.user.email||"Unknown User"}),(0,r.jsx)("div",{className:"inline-block bg-gray-100 text-xs px-2 py-0.5 rounded",children:"Owner"})]})]}),C?(0,r.jsx)("div",{className:"py-2 text-center",children:(0,r.jsx)("div",{className:"inline-block w-6 h-6 rounded-full border-2 border-t-transparent border-primary-500 animate-spin"})}):E&&E.length>0?E.map((e,s)=>{let t=e.user&&e.user.name||e.user&&e.user.email||"User ".concat(e.userId);return(0,r.jsxs)("div",{className:"flex items-center mt-4",children:[(0,r.jsx)("div",{className:"w-10 h-10 rounded-full ".concat(P(t)," flex items-center justify-center text-neutral-100 font-medium mr-3"),children:I(t)}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium",children:t}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1 mt-1",children:e.permission&&Object.entries(e.permission).filter(e=>{let[s,t]=e;return!0===t}).map(e=>{let[s]=e;return(0,r.jsx)("div",{className:"inline-block bg-gray-100 text-xs px-2 py-0.5 rounded",children:"viewForm"===s?"View Form":"editForm"===s?"Edit Form":"viewSubmissions"===s?"View Submissions":"editSubmissions"===s?"Edit Submissions":"addSubmissions"===s?"Add Submissions":"deleteSubmissions"===s?"Delete Submissions":"validateSubmissions"===s?"Validate Submissions":"manageProject"===s?"Manage Project":s},s)})})]})]},s)}):null]}),v&&N&&(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsx)(j,{onClose:()=>f(!1),projectId:N,onUserAdded:()=>{(async()=>{U(!0);try{let e=await b.A.get("/project-users/".concat(N));if(e.data&&e.data.data&&e.data.data.AllUser){let s=e.data.data.AllUser||[];k(s)}else k([])}catch(e){console.error("Error fetching project users:",e),k([])}finally{U(!1)}})()}})}),(0,r.jsx)("div",{className:"mt-8 border-t pt-6",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:"Anonymous submissions"}),(0,r.jsx)("div",{className:"text-sm text-gray-500 mt-1",children:"Allow submissions without a username and password"})]}),(0,r.jsx)("div",{className:"w-12 h-6 bg-gray-200 rounded-full relative cursor-pointer",children:(0,r.jsx)("div",{className:"w-5 h-5 bg-neutral-100 rounded-full absolute top-0.5 left-0.5 shadow"})})]})}),(0,r.jsx)("div",{className:"mt-8",children:(0,r.jsx)("div",{className:"inline-block border rounded-md px-4 py-2 text-sm cursor-pointer hover:bg-gray-50",children:"Copy team from another project"})})]})]})}},29350:(e,s,t)=>{t.d(s,{A:()=>c});var r=t(97381),a=t(59362),l=t(25784),n=t(35695),i=t(12115),o=t(34540);let c=e=>{let s=(0,o.wA)(),t=(0,n.useRouter)(),c=(0,n.usePathname)(),{status:d,user:u,error:m}=(0,o.d4)(e=>e.auth),p=async()=>{try{s((0,r.Le)());let e=(await l.A.get("/users/me")).data;s((0,r.tQ)(e))}catch(l){if(s((0,r.x9)()),(0,a.F0)(l)){var e,n,i,o,d;if(console.error("Auth error:",null==(e=l.response)?void 0:e.status,null==(n=l.response)?void 0:n.data),(null==(i=l.response)?void 0:i.status)===401){if(c.startsWith("/form-submission"))return;t.push("/")}else s((0,r.jB)((null==(d=l.response)||null==(o=d.data)?void 0:o.message)||l.message))}else s((0,r.jB)(l instanceof Error?l.message:"An unknown error occurred."))}};return(0,i.useEffect)(()=>{(null==e?void 0:e.skipFetchUser)||p()},[null==e?void 0:e.skipFetchUser]),(0,i.useEffect)(()=>{let e=e=>{if("logout"===e.key&&"true"===e.newValue)if(s((0,r.x9)()),c.startsWith("/form-submission")){let e=c.split("/")[2];e?t.push("/form-submission/".concat(e,"/sign-in")):t.push("/")}else t.push("/")};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[s,t,c]),{status:d,user:u,error:m,isAuthenticated:"authenticated"===d,isLoading:"loading"===d,refreshAuthState:()=>{p()},signin:async(e,s,t)=>{try{await l.A.post("/users/login",e),await p(),null==s||s()}catch(e){if(e instanceof a.pe){var r,n;let s=null==(n=e.response)||null==(r=n.data)?void 0:r.errorType;null==t||t(s)}else null==t||t()}},logout:async()=>{try{await l.A.post("/users/logout"),localStorage.setItem("logout","true"),setTimeout(()=>localStorage.removeItem("logout"),100)}finally{if(s((0,r.x9)()),c.startsWith("/form-submission")){let e=c.split("/")[2];e?t.push("/form-submission/".concat(e,"/sign-in")):t.push("/")}else t.push("/")}}}}},57799:(e,s,t)=>{t.d(s,{A:()=>a});var r=t(95155);t(12115);let a=()=>(0,r.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,r.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},63642:(e,s,t)=>{t.d(s,{R:()=>l});var r=t(95155);t(12115);var a=t(13163);let l=e=>{let{showModal:s,onClose:t,onConfirm:l,title:n,description:i,confirmButtonText:o,cancelButtonText:c,confirmButtonClass:d,children:u}=e;return(0,r.jsxs)(a.A,{isOpen:s,onClose:t,className:"p-6 rounded-md max-w-xl",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:n}),(0,r.jsx)("div",{className:"text-neutral-700 mt-2",children:i}),u&&(0,r.jsx)("div",{className:"mt-6 space-y-4",children:u}),(0,r.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,r.jsx)("button",{className:"btn-outline",onClick:t,type:"button",children:c||"Cancel"}),(0,r.jsx)("button",{className:"font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ".concat(d),onClick:l,type:"button",children:o})]})]})}},71402:(e,s,t)=>{t.d(s,{Ay:()=>n,Ds:()=>a,_b:()=>l});let r=(0,t(51990).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,s)=>{e.message=s.payload.message,e.type=s.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:a,hideNotification:l}=r.actions,n=r.reducer},77361:(e,s,t)=>{t.d(s,{D_:()=>u,Im:()=>c,Oo:()=>m,c3:()=>l,kf:()=>a,lj:()=>h,or:()=>o,pf:()=>d,vj:()=>n,wI:()=>p,xx:()=>i});var r=t(25784);let a=async e=>{let{projectId:s}=e,{data:t}=await r.A.get("/projects/".concat(s));return t.project},l=async e=>{let{data:s}=await r.A.post("/projects/from-template",e);return s},n=async()=>{try{let{data:e}=await r.A.get("/projects");return e.projects}catch(e){throw console.error("Error fetching projects:",e),e}},i=async e=>{let{data:s}=await r.A.delete("/projects/delete/".concat(e));return s},o=async e=>{try{let{data:s}=await r.A.delete("/projects/delete-multiple",{data:{projectIds:e}});return s}catch(e){throw console.error("Error deleting multiple projects:",e),e}},c=async e=>{try{let{data:s}=await r.A.patch("/projects/change-status/".concat(e),{status:"archived"});return s}catch(e){throw console.error("Error archiving project:",e),e}},d=async function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{let{data:s}=await r.A.patch("/projects/change-status/".concat(e),{status:"deployed"});return s}catch(e){throw console.error("Error deploying project:",e),e}},u=async e=>{try{let{data:s}=await r.A.patch("/projects/update-many-status",{projectIds:e,status:"archived"});return s}catch(e){throw console.error("Error archiving multiple projects:",e),e}},m=async e=>{try{let{data:s}=await r.A.post("/users/check-email",{email:e});return s}catch(e){var s,t,a,l,n,i;throw Error("object"==typeof(null==(t=e.response)||null==(s=t.data)?void 0:s.message)?JSON.stringify(null==(l=e.response)||null==(a=l.data)?void 0:a.message):(null==(i=e.response)||null==(n=i.data)?void 0:n.message)||e.message||"Failed to check user")}},p=async e=>{let{projectId:s,email:t,permissions:a}=e;try{let e=await m(t);if(!e||!e.success)throw Error((null==e?void 0:e.message)||"User not found");let{data:l}=await r.A.post("/project-users",{userId:e.user.id,projectId:s,permission:a});return l}catch(e){var l,n,i,o,c,d;throw console.error("Error adding user to project:",e),Error("object"==typeof(null==(n=e.response)||null==(l=n.data)?void 0:l.message)?JSON.stringify(null==(o=e.response)||null==(i=o.data)?void 0:i.message):(null==(d=e.response)||null==(c=d.data)?void 0:c.message)||e.message||"Failed to add user")}},h=async e=>{try{let{data:s}=await r.A.post("/answers/multiple",e);return s}catch(e){throw console.error("Error creating answer submission:",e),e}}},97381:(e,s,t)=>{t.d(s,{Ay:()=>o,Le:()=>n,jB:()=>i,tQ:()=>a,x9:()=>l});let r=(0,t(51990).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,s)=>{e.status="authenticated",e.user=s.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,s)=>{e.status="unauthenticated",e.error=s.payload,e.user=null}}}),{setAuthenticatedUser:a,setUnauthenticated:l,setAuthLoading:n,setAuthError:i}=r.actions,o=r.reducer}}]);