(()=>{var e={};e.id=7893,e.ids=[7893],e.modules={1510:(e,r,t)=>{"use strict";t.d(r,{F0:()=>c,pe:()=>a});let{Axios:s,AxiosError:a,CanceledError:i,isCancel:n,CancelToken:o,VERSION:l,all:d,Cancel:u,isAxiosError:c,spread:p,toFormData:m,AxiosHeaders:h,HttpStatusCode:x,formToJSON:f,getAdapter:b,mergeConfig:g}=t(51060).A},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6986:(e,r,t)=>{"use strict";t.d(r,{D:()=>o,l:()=>n});var s=t(53907);let a=process.env.SALT||"rushan-salt",i=new s.A(a,12),n=e=>i.encode(e),o=e=>{let r=i.decode(e)[0];return"bigint"==typeof r?r<Number.MAX_SAFE_INTEGER?Number(r):null:"number"==typeof r?r:null}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14952:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},15616:(e,r,t)=>{"use strict";t.d(r,{T:()=>n});var s=t(60687),a=t(43210),i=t(96241);let n=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700 dark:bg-gray-900 dark:placeholder:text-gray-500",e),ref:t,...r}));n.displayName="Textarea"},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24746:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\form-submission\\\\[hashedId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\form-submission\\[hashedId]\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39390:(e,r,t)=>{"use strict";t.d(r,{J:()=>o});var s=t(60687),a=t(43210),i=t(78148),n=t(96241);let o=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(i.b,{ref:t,className:(0,n.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...r}));o.displayName=i.b.displayName},40347:(e,r,t)=>{"use strict";t.d(r,{C:()=>d,z:()=>l});var s=t(60687),a=t(43210),i=t(14555),n=t(65822),o=t(96241);let l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(i.bL,{className:(0,o.cn)("grid gap-2",e),...r,ref:t}));l.displayName=i.bL.displayName;let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(i.q7,{ref:t,className:(0,o.cn)("aspect-square h-4 w-4 rounded-full border text-gray-900 shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-gray-950 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus-visible:ring-gray-300",e),...r,children:(0,s.jsx)(i.C1,{className:"flex items-center justify-center",children:(0,s.jsx)(n.A,{className:"h-2.5 w-2.5 fill-current text-current"})})}));d.displayName=i.q7.displayName},51401:(e,r,t)=>{Promise.resolve().then(t.bind(t,24746))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58025:(e,r,t)=>{Promise.resolve().then(t.bind(t,93044))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68988:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var s=t(60687);t(43210);var a=t(96241);function i({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-primary-500 focus-visible:ring-[1px]","focus-visible:outline-none","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},70334:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},71845:(e,r,t)=>{"use strict";t.d(r,{D_:()=>c,Im:()=>d,Oo:()=>p,c3:()=>i,kf:()=>a,lj:()=>h,or:()=>l,pf:()=>u,vj:()=>n,wI:()=>m,xx:()=>o});var s=t(12810);let a=async({projectId:e})=>{let{data:r}=await s.A.get(`/projects/${e}`);return r.project},i=async e=>{let{data:r}=await s.A.post("/projects/from-template",e);return r},n=async()=>{try{let{data:e}=await s.A.get("/projects");return e.projects}catch(e){throw console.error("Error fetching projects:",e),e}},o=async e=>{let{data:r}=await s.A.delete(`/projects/delete/${e}`);return r},l=async e=>{try{let{data:r}=await s.A.delete("/projects/delete-multiple",{data:{projectIds:e}});return r}catch(e){throw console.error("Error deleting multiple projects:",e),e}},d=async e=>{try{let{data:r}=await s.A.patch(`/projects/change-status/${e}`,{status:"archived"});return r}catch(e){throw console.error("Error archiving project:",e),e}},u=async(e,r=!1)=>{try{let{data:r}=await s.A.patch(`/projects/change-status/${e}`,{status:"deployed"});return r}catch(e){throw console.error("Error deploying project:",e),e}},c=async e=>{try{let{data:r}=await s.A.patch("/projects/update-many-status",{projectIds:e,status:"archived"});return r}catch(e){throw console.error("Error archiving multiple projects:",e),e}},p=async e=>{try{let{data:r}=await s.A.post("/users/check-email",{email:e});return r}catch(e){throw Error("object"==typeof e.response?.data?.message?JSON.stringify(e.response?.data?.message):e.response?.data?.message||e.message||"Failed to check user")}},m=async({projectId:e,email:r,permissions:t})=>{try{let a=await p(r);if(!a||!a.success)throw Error(a?.message||"User not found");let{data:i}=await s.A.post("/project-users",{userId:a.user.id,projectId:e,permission:t});return i}catch(e){throw console.error("Error adding user to project:",e),Error("object"==typeof e.response?.data?.message?JSON.stringify(e.response?.data?.message):e.response?.data?.message||e.message||"Failed to add user")}},h=async e=>{try{let{data:r}=await s.A.post("/answers/multiple",e);return r}catch(e){throw console.error("Error creating answer submission:",e),e}}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86429:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(60687);t(43210);let a=()=>(0,s.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,s.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},93044:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>k});var s=t(60687),a=t(43210),i=t(29494),n=t(54050),o=t(16189),l=t(6986),d=t(75531),u=t(96),c=t(71845),p=t(86429),m=t(39390),h=t(15616),x=t(93437),f=t(40347),b=t(70334),g=t(78272),y=t(14952),v=t(54864),j=t(19150),w=t(13784);t(24527);var N=t(69396),q=t(21650);function k(){let e=(0,v.wA)();(0,o.useRouter)();let{hashedId:r}=(0,o.useParams)(),t=(0,l.D)(r),{status:k,isAuthenticated:A,isLoading:E}=(0,q.A)(),[I,S]=(0,a.useState)({}),[C,P]=(0,a.useState)({}),[O,_]=(0,a.useState)(!1),[T,R]=(0,a.useState)([]),[D,$]=(0,a.useState)([]),[F,G]=(0,a.useState)({}),{data:M,isLoading:K,isError:L}=(0,i.I)({queryKey:["questions",t],queryFn:()=>(0,d.K4)({projectId:t}),enabled:!!t}),{data:z=[]}=(0,i.I)({queryKey:["questionGroups",t],queryFn:()=>(0,u.pr)({projectId:t}),enabled:!!t}),{data:J}=(0,i.I)({queryKey:["project",t],queryFn:()=>(0,c.kf)({projectId:t}),enabled:!!t}),U=(0,a.useMemo)(()=>z.reduce((e,r)=>(e[r.id]=M?.filter(e=>e.questionGroupId===r.id)||[],e),{}),[z,M]),Q=(0,a.useMemo)(()=>M?.filter(e=>null===e.questionGroupId||void 0===e.questionGroupId)||[],[M]),Y=(0,a.useMemo)(()=>{let e=[];return z.forEach(r=>{let t=M?.filter(e=>e.questionGroupId===r.id)||[],s=t.length>0?Math.min(...t.map(e=>e.position)):r.order;e.push({type:"group",data:r,order:s,originalPosition:s})}),Q.forEach(r=>{e.push({type:"question",data:r,order:r.position,originalPosition:r.position})}),e.sort((e,r)=>e.order===r.order?(e.originalPosition||e.order)-(r.originalPosition||r.order):e.order-r.order)},[z,Q,M]),X=(0,a.useCallback)(e=>{G(r=>({...r,[e]:!r[e]}))},[]),H=(0,n.n)({mutationFn:async e=>{let r=M?.map(r=>{let s,a,i,n=e[r.id],o="selectmany"===r.inputType,l="selectone"===r.inputType;if(!o&&!l&&(null==n||""===n)||l&&(!n||""===n.trim()))return null;if(o&&Array.isArray(n)&&r.questionOptions){let e=n.map(e=>{let t=r.questionOptions.find(r=>r.label===e);return t?.id}).filter(e=>void 0!==e);s=e.length>0?e:[]}else if(l&&n&&r.questionOptions){let e=r.questionOptions.find(e=>e.label===n);if(void 0===(s=e?.id))return console.warn(`Could not find option ID for selectone question ${r.id} with value "${n}"`),null}if(null==(a=o?Array.isArray(n)?n.join(", "):"":"number"===r.inputType||"decimal"===r.inputType?n?Number(n):void 0:"date"===r.inputType||"dateandtime"===r.inputType?n||void 0:"table"===r.inputType?Array.isArray(n)&&n.length>0?JSON.stringify(n):void 0:n?String(n):void 0))return null;i=o?Array.isArray(s)?s:[]:l&&"number"==typeof s?s:void 0;let d={projectId:Number(t),questionId:r.id,answerType:String(r.inputType),value:a,isOtherOption:!1};return void 0!==i&&(d.questionOptionId=i),d}).filter(e=>null!==e)||[];if(0===r.length)throw Error("No valid answers to submit. Please fill out at least one field.");return await (0,c.lj)(r)},onSuccess:()=>{e((0,j.Ds)({message:"Form submitted successfully",type:"success"})),S({}),window.dispatchEvent(new Event("form-submitted")),localStorage.setItem("form_submitted",Date.now().toString())},onError:r=>{e((0,j.Ds)({message:"Failed to submit form. Please try again.",type:"error"})),console.error("Submission Error:",r)},onSettled:()=>{_(!1)}}),B=(0,a.useCallback)((e,r)=>{S(t=>({...t,[e]:r})),P(r=>({...r,[e]:""}))},[]),V=()=>{let e={};return T.forEach(r=>{if(r.isRequired){let t=I[r.id];("string"==typeof t&&!t.trim()||Array.isArray(t)&&0===t.length||null==t)&&(e[r.id]=`${r.label} is required`)}}),P(e),0===Object.keys(e).length},W=async e=>{e.preventDefault(),V()&&(_(!0),H.mutate(I))},Z=e=>!!M&&M.some(r=>r.questionOptions?.some(r=>r.nextQuestionId===e)),ee=e=>e.questionOptions?.some(e=>e.nextQuestionId)||!1,er=e=>{let r=I[e.id]??("selectmany"===e.inputType?[]:"");switch(e.inputType){case"text":if(e.hint?.includes("multiline"))return(0,s.jsx)(h.T,{value:r,onChange:r=>B(e.id,r.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});return(0,s.jsx)("input",{className:"input-field w-full",value:r,onChange:r=>B(e.id,r.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});case"number":return(0,s.jsx)("input",{className:"input-field w-full",type:"number",value:r,onChange:r=>B(e.id,r.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});case"decimal":return(0,s.jsx)("input",{className:"input-field w-full",type:"number",step:"any",value:r,onChange:r=>B(e.id,r.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});case"selectone":return(0,s.jsx)(f.z,{value:r,onValueChange:r=>B(e.id,r),required:e.isRequired,children:(0,s.jsx)("div",{className:"space-y-2",children:e.questionOptions?.map((e,r)=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(f.C,{value:e.label,id:`option-${e.id}`}),(0,s.jsx)(m.J,{htmlFor:`option-${e.id}`,className:"cursor-pointer",children:e.label}),e.sublabel&&(0,s.jsx)("p",{className:"text-sm text-neutral-700 ml-4",children:`(${e.sublabel})`})]},r))})});case"selectmany":return(0,s.jsx)("div",{className:"space-y-2",children:e.questionOptions?.map(t=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(x.S,{id:`option-${t.id}`,checked:(r||[]).includes(t.label),onCheckedChange:s=>{let a=r||[],i=s?[...a,t.label]:a.filter(e=>e!==t.label);B(e.id,i)}}),(0,s.jsx)(m.J,{htmlFor:`option-${t.id}`,className:"cursor-pointer",children:t.label})]},t.id))});case"date":return(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("input",{className:"input-field w-full",type:"date",value:r,onChange:r=>B(e.id,r.target.value),placeholder:e.placeholder||"Select date",required:e.isRequired})});case"dateandtime":return(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("input",{className:"input-field w-full",type:"time",value:r,onChange:r=>B(e.id,r.target.value),placeholder:e.placeholder||"Select time",required:e.isRequired})});case"table":return(0,s.jsx)(w.N,{questionId:e.id,value:r,onChange:r=>B(e.id,r),required:e.isRequired,tableLabel:e.label});default:return null}},et=e=>{let r=Z(e.id),t=ee(e);return(0,s.jsxs)("div",{className:`border rounded-md p-4 ${r?"border-primary-200 dark:border-primary-700 bg-primary-100 dark:bg-primary-900/20":"border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"}`,children:[(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(m.J,{className:"text-base font-medium",children:[e.label,e.isRequired&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),r&&(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-200 text-primary-800 dark:bg-primary-900 dark:text-primary-200",children:[(0,s.jsx)(b.A,{className:"w-3 h-3 mr-1"}),"Follow-up"]}),t&&(0,s.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent-200 text-accent-800 dark:bg-accent-700/20 dark:text-accent-200",children:"Has conditions"})]}),e.hint&&(0,s.jsx)("p",{className:`text-sm mt-1 ${r?"text-primary-700 dark:text-primary-300":"text-muted-foreground"}`,children:e.hint}),C[e.id]&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:C[e.id]})]}),(0,s.jsx)("div",{className:"mt-2",children:er(e)})]},e.id)};return E||K?(0,s.jsx)(p.A,{}):A?L||!M?(0,s.jsx)("p",{className:"text-sm text-red-500",children:"Error loading form. Please try again."}):(0,s.jsx)("div",{className:"min-h-screen w-full bg-neutral-100 dark:bg-gray-900 flex flex-col items-center p-4 md:p-6",children:(0,s.jsxs)("div",{className:"w-full max-w-screen-lg bg-neutral-100 dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("h2",{className:"text-2xl font-semibold p-6 border-b border-gray-200 dark:border-gray-700",children:["Form Submission ",J?.name?` for ${J.name}`:""]}),(0,s.jsx)("form",{onSubmit:W,className:"p-6",children:(0,s.jsxs)("div",{className:"space-y-6",children:[M&&0!==M.length?Y.map(e=>{if("group"===e.type){let r=e.data,t=U[r.id]||[],a=t.filter(e=>T.some(r=>r.id===e.id)),i=F[r.id];return 0===a.length?null:(0,s.jsxs)("div",{className:"border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800",children:[(0,s.jsx)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700",onClick:()=>X(r.id),children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[i?(0,s.jsx)(g.A,{className:"h-5 w-5 text-gray-500"}):(0,s.jsx)(y.A,{className:"h-5 w-5 text-gray-500"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:r.title}),(0,s.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:["(",a.length," visible question",1!==a.length?"s":"",")"]})]})}),i&&(0,s.jsx)("div",{className:"p-4 space-y-4",children:D.filter(e=>t.some(r=>r.id===e.question.id)).map(e=>(0,s.jsx)(N.A,{questionGroup:e,renderQuestionInput:er,errors:C,className:""},e.question.id))})]},`group-${r.id}`)}{let r=e.data;if(!T.some(e=>e.id===r.id))return null;let t=D.find(e=>e.question.id===r.id);return t?(0,s.jsx)(N.A,{questionGroup:t,renderQuestionInput:er,errors:C,className:""},r.id):et(r)}}):(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"This form has no questions yet."})}),M.length>0&&(0,s.jsx)("div",{className:"mt-6 flex justify-end",children:(0,s.jsx)("button",{className:"btn-primary",type:"submit",disabled:O,children:O?"Submitting...":"Submit Form"})}),0===M.length&&(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"This form has no questions yet."})}),M&&M.length>0&&0===T.length&&(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"No questions are currently visible. Please check your form configuration."})})]})})]})}):null}},93437:(e,r,t)=>{"use strict";t.d(r,{S:()=>o});var s=t(60687);t(43210);var a=t(40211),i=t(13964),n=t(96241);function o({className:e,...r}){return(0,s.jsx)(a.bL,{"data-slot":"checkbox",className:(0,n.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...r,children:(0,s.jsx)(a.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(i.A,{className:"size-3.5"})})})}},94735:e=>{"use strict";e.exports=require("events")},96241:(e,r,t)=>{"use strict";t.d(r,{Y:()=>n,cn:()=>i});var s=t(49384),a=t(82348);function i(...e){return(0,a.QP)((0,s.$)(e))}function n(e,r="short"){if(!e)return"";try{let t="string"==typeof e?new Date(e):e;if(isNaN(t.getTime()))return"";switch(r){case"short":return t.toLocaleDateString(void 0,{year:"numeric",month:"short",day:"numeric"});case"long":return t.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});case"full":return t.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",weekday:"long",hour:"2-digit",minute:"2-digit",second:"2-digit"});default:return t.toLocaleDateString()}}catch(r){return console.error("Error formatting date:",r),String(e)}}},96752:(e,r,t)=>{"use strict";t.d(r,{A0:()=>n,BF:()=>o,Hj:()=>l,XI:()=>i,nA:()=>u,nd:()=>d});var s=t(60687);t(43210);var a=t(96241);function i({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",e),...r})})}function n({className:e,...r}){return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",e),...r})}function o({className:e,...r}){return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",e),...r})}function l({className:e,...r}){return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...r})}function d({className:e,...r}){return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-foreground h-10 px-2 text-left align-middle font-medium neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...r})}function u({className:e,...r}){return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...r})}},99956:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>d});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["form-submission",{children:["[hashedId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,24746)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\form-submission\\[hashedId]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\form-submission\\[hashedId]\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/form-submission/[hashedId]/page",pathname:"/form-submission/[hashedId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,7404,517,551,5841,4072],()=>t(99956));module.exports=s})();