(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8662],{13163:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var l=s(95155),r=s(60760),n=s(44518),i=s(95233),a=s(54416);s(12115);let o=e=>{let{children:t,className:s,isOpen:o,onClose:d,preventOutsideClick:c=!1}=e;return(0,l.jsx)(r.N,{children:o&&(0,l.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40 p-4 overflow-y-auto",onClick:e=>{c||d()},children:(0,l.jsxs)(n.P.div,{initial:{scale:.6,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.6,opacity:0},transition:{duration:.3,ease:i.am},className:"relative section flex flex-col max-w-4xl max-h-[90vh] overflow-y-auto ".concat(s),onClick:e=>e.stopPropagation(),children:[(0,l.jsx)(a.A,{onClick:d,className:"absolute self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300"}),t]})})})}},26862:(e,t,s)=>{"use strict";s.d(t,{Sc:()=>x.S,dO:()=>p}),s(97168),s(89852),s(82714),s(99474);var l=s(95155),r=s(12115),n=s(38715),i=s(66474),a=s(47863),o=s(5196),d=s(53999);n.bL,n.YJ,n.WT,r.forwardRef((e,t)=>{let{className:s,children:r,...a}=e;return(0,l.jsxs)(n.l9,{ref:t,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm ring-offset-neutral-100 placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 dark:border-gray-700 dark:bg-gray-900 dark:ring-offset-gray-900 dark:placeholder:text-gray-500",s),...a,children:[r,(0,l.jsx)(n.In,{asChild:!0,children:(0,l.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]})}).displayName=n.l9.displayName;let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,l.jsx)(n.PP,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,l.jsx)(a.A,{className:"h-4 w-4"})})});c.displayName=n.PP.displayName;let u=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,l.jsx)(n.wn,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,l.jsx)(i.A,{className:"h-4 w-4"})})});u.displayName=n.wn.displayName,r.forwardRef((e,t)=>{let{className:s,children:r,position:i="popper",...a}=e;return(0,l.jsx)(n.ZL,{children:(0,l.jsxs)(n.UC,{ref:t,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border border-gray-200 bg-neutral-100 text-slate-700 shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:border-gray-700 dark:bg-gray-900 dark:text-slate-200","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:i,...a,children:[(0,l.jsx)(c,{}),(0,l.jsx)(n.LM,{className:(0,d.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,l.jsx)(u,{})]})})}).displayName=n.UC.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,l.jsx)(n.JU,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...r})}).displayName=n.JU.displayName,r.forwardRef((e,t)=>{let{className:s,children:r,...i}=e;return(0,l.jsxs)(n.q7,{ref:t,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-gray-100 focus:text-gray-900 data-[disabled]:pointer-events-none data-[disabled]:opacity-50 dark:focus:bg-gray-800 dark:focus:text-gray-50",s),...i,children:[(0,l.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,l.jsx)(n.VF,{children:(0,l.jsx)(o.A,{className:"h-4 w-4"})})}),(0,l.jsx)(n.p4,{children:r})]})}).displayName=n.q7.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,l.jsx)(n.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-gray-200 dark:bg-gray-700",s),...r})}).displayName=n.wv.displayName;var m=s(4884);let p=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,l.jsx)(m.bL,{className:(0,d.cn)("peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 focus-visible:ring-offset-neutral-100 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 dark:focus-visible:ring-offset-gray-900",s),...r,ref:t,children:(0,l.jsx)(m.zi,{className:(0,d.cn)("pointer-events-none block h-5 w-5 rounded-full shadow-lg ring-0 transition-transform data-[state=checked]:bg-neutral-100 data-[state=unchecked]:bg-primary-500 data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});p.displayName=m.bL.displayName;var x=s(95139);s(55747)},32383:()=>{},41232:(e,t,s)=>{"use strict";s.d(t,{o:()=>ep});var l=s(95155),r=s(12115),n=s(75143),i=s(50402),a=s(78266),o=s(48021),d=s(18084),c=s(74126),u=s(89917);let m=e=>{let{question:t,onEdit:s,onDelete:r,onDuplicate:n,isSelected:m=!1,onToggleSelect:p,selectionMode:x=!1}=e,{attributes:h,listeners:b,setNodeRef:f,transform:g,transition:v,isDragging:j}=(0,i.gl)({id:t.id,data:{type:"question",questionId:t.id,questionGroupId:"questionGroupId"in t?t.questionGroupId:null}}),y={transform:a.Ks.Transform.toString(g),transition:v,opacity:j?.5:1};return(0,l.jsx)("div",{ref:f,style:y,className:"border border-neutral-400 rounded-md bg-card shadow-sm",children:(0,l.jsxs)("div",{className:"flex items-center p-4",children:[x&&(0,l.jsx)("div",{className:"mr-2",children:(0,l.jsx)("input",{type:"checkbox",checked:m,onChange:()=>p&&p(),className:"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"})}),(0,l.jsx)("div",{...h,...b,className:"cursor-move mr-3 hover:text-primary",children:(0,l.jsx)(o.A,{className:"h-5 w-5"})}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("h3",{className:"text-xl font-semibold",children:t.label||(0,l.jsx)("span",{className:"text-muted-foreground italic",children:"Empty question"})}),t.hint&&(0,l.jsx)("p",{className:"text-sm sub-text mt-1",children:t.hint})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,l.jsx)("button",{onClick:e=>{e.stopPropagation(),n()},title:"Duplicate",className:"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors",children:(0,l.jsx)(d.A,{size:16})}),(0,l.jsx)("button",{onClick:e=>{e.stopPropagation(),r()},title:"Delete",className:"cursor-pointer p-2 rounded-full hover:bg-destructive/10 text-neutral-700 hover:text-destructive transition-colors",children:(0,l.jsx)(c.A,{size:16})}),(0,l.jsx)("button",{onClick:e=>{e.stopPropagation(),s()},title:"Edit",className:"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors",children:(0,l.jsx)(u.A,{size:16})})]})]})})};var p=s(66474),x=s(13052),h=s(84616),b=s(9343),f=s(13717);let g=e=>{let{id:t,title:s,questions:d,subGroups:u=[],parentGroupId:v,level:j=0,onEditGroup:y,onDeleteGroup:N,onAddQuestionToGroup:w,onCreateSubGroup:q,onEditQuestion:C,onDeleteQuestion:I,onDuplicateQuestion:k,onReorderQuestions:S,onMoveGroupToParent:E,onMoveQuestionBetweenGroups:A,isEditing:Q=!1,onStartEditing:T,onSaveGroupName:G,onCancelEditing:D,editingName:F="",onEditingNameChange:z,selectionMode:P=!1,isDragging:R=!1}=e,[O,M]=(0,r.useState)(!0),{attributes:L,listeners:K,setNodeRef:B,transform:U,transition:V,isDragging:H}=(0,i.gl)({id:"group-".concat(t),data:{type:"group",groupId:t,parentGroupId:v}}),{setNodeRef:$,isOver:_}=(0,n.zM)({id:"group-drop-".concat(t),data:{type:"group",groupId:t,accepts:["group","question"]}}),J={transform:a.Ks.Transform.toString(U),transition:V,opacity:H?.5:1},W=(0,n.FR)((0,n.MS)(n.AN,{activationConstraint:{distance:8}}),(0,n.MS)(n.uN));return(0,l.jsxs)("div",{ref:e=>{B(e),$(e)},style:{...J,marginLeft:"".concat(20*j,"px")},className:"border border-neutral-400 rounded-md bg-card shadow-sm mb-4 ".concat(_?"ring-2 ring-primary-500 ring-opacity-50":""," ").concat(H?"opacity-50":""),children:[(0,l.jsxs)("div",{className:"flex items-center p-4 bg-neutral-100 border-b border-neutral-300 rounded-t-md",children:[(0,l.jsx)("div",{...L,...K,className:"cursor-move mr-2 hover:text-primary-500",title:"Drag to reorder or nest group",children:(0,l.jsx)(o.A,{className:"h-5 w-5"})}),(0,l.jsx)("button",{onClick:()=>M(!O),className:"mr-2 text-neutral-700 hover:text-primary-500 transition-colors","aria-label":O?"Collapse group":"Expand group",children:O?(0,l.jsx)(p.A,{className:"h-5 w-5"}):(0,l.jsx)(x.A,{className:"h-5 w-5"})}),Q?(0,l.jsx)("div",{className:"flex-1 mr-4",children:(0,l.jsx)("input",{type:"text",value:F,onChange:e=>z&&z(e.target.value),className:"w-full p-2 border border-gray-300 rounded",autoFocus:!0,onKeyDown:e=>{"Enter"===e.key?G&&G(t):"Escape"===e.key&&D&&D()},placeholder:"Enter group name"})}):(0,l.jsx)("h3",{className:"flex-1 font-medium text-lg cursor-pointer hover:text-primary-500",onClick:()=>T&&T(t,s),title:"Click to edit group name",children:s}),(0,l.jsx)("div",{className:"flex items-center space-x-3",children:Q?(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)("button",{onClick:()=>D&&D(),title:"Cancel Editing",className:"cursor-pointer px-3 py-1 rounded btn-outline",children:"Cancel"}),(0,l.jsx)("button",{onClick:()=>G&&G(t),title:"Save Group Name",className:"cursor-pointer px-3 py-1 rounded btn-primary",children:"Save"})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("button",{onClick:()=>w(t),title:"Add Question to Group",className:"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors",children:(0,l.jsx)(h.A,{size:16})}),q&&(0,l.jsx)("button",{onClick:()=>q(t),title:"Create Sub Group",className:"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors",children:(0,l.jsx)(b.A,{size:16})}),(0,l.jsx)("button",{onClick:()=>T&&T(t,s),title:"Edit Group Name",className:"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors",children:(0,l.jsx)(f.A,{size:16})}),(0,l.jsx)("button",{onClick:()=>N(t),title:"Delete Group",className:"cursor-pointer p-2 rounded-full hover:bg-destructive/10 text-neutral-700 hover:text-destructive transition-colors",children:(0,l.jsx)(c.A,{size:16})})]})})]}),O&&(0,l.jsxs)("div",{className:"p-4 space-y-4",children:[u&&u.length>0&&(0,l.jsx)("div",{className:"space-y-4",children:u.sort((e,t)=>e.order-t.order).map(e=>(0,l.jsx)(g,{id:e.id,title:e.title,questions:e.question||[],subGroups:e.subGroups,parentGroupId:t,level:j+1,onEditGroup:y,onDeleteGroup:N,onAddQuestionToGroup:w,onCreateSubGroup:q,onEditQuestion:C,onDeleteQuestion:I,onDuplicateQuestion:k,onReorderQuestions:S,onMoveGroupToParent:E,onMoveQuestionBetweenGroups:A,isEditing:Q,onStartEditing:T,onSaveGroupName:G,onCancelEditing:D,editingName:F,onEditingNameChange:z,selectionMode:P},e.id))}),d.length>0?(0,l.jsx)(n.Mp,{sensors:W,collisionDetection:n.fp,onDragEnd:e=>{let{active:t,over:s}=e;if(!s||t.id===s.id)return;let l=t.data.current,r=s.data.current;if((null==l?void 0:l.type)==="question"&&S){let e=[...d].sort((e,t)=>e.position-t.position),l=e.findIndex(e=>e.id===t.id),r=e.findIndex(e=>e.id===s.id);if(-1===l||-1===r)return;S((0,i.be)(e,l,r).map((e,t)=>({id:Number(e.id),position:t+1})))}if((null==l?void 0:l.type)==="question"&&(null==r?void 0:r.type)==="group"&&A){let e=Number(t.id),s=l.questionGroupId,n=r.groupId;s!==n&&A(e,s,n)}if((null==l?void 0:l.type)==="group"&&(null==r?void 0:r.type)==="group"&&E){let e=l.groupId,t=r.groupId;e!==t&&E(e,t)}},children:(0,l.jsx)(i.gB,{items:d.map(e=>e.id),strategy:i._G,children:d.sort((e,t)=>e.position-t.position).map(e=>(0,l.jsx)("div",{className:"mb-4",children:(0,l.jsx)(m,{question:{...e},onEdit:()=>C(e),onDelete:()=>I(e),onDuplicate:()=>k(e),selectionMode:P,isSelected:!1,onToggleSelect:()=>{}})},e.id))})}):u&&0===u.length?(0,l.jsx)("div",{className:"text-center py-4 text-neutral-500",children:"No questions in this group. Click the + button to add questions."}):null]})]})};var v=s(92657),j=s(5040),y=s(49103),N=s(13163),w=s(62177),q=s(50408),C=s(64368);let I={text:"Text",number:"Number",decimal:"Decimal",selectone:"Select one",selectmany:"Select many",date:"Date",dateandtime:"Date and time",table:"Table"};Object.keys(I);var k=s(26862),S=s(26715),E=s(5041),A=s(34947),Q=s(19373);let T=e=>{let{contextType:t,contextId:s,value:r,onChange:n,currentQuestionId:i,placeholder:a="Select next question (optional)"}=e,{data:o=[],isLoading:d,error:c}=(0,Q.I)({queryKey:"project"===t?["questions",s]:"template"===t?["templateQuestions",s]:["questionBlockQuestions",s],queryFn:()=>"project"===t?(0,A.K4)({projectId:s}):"template"===t?(0,A.ej)({templateId:s}):"questionBlock"===t?(0,A.dI)():[],enabled:!!s}),u=o.filter(e=>e.id!==i),m=u.find(e=>e.id===r);return(0,l.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,l.jsxs)("select",{value:r||"",onChange:e=>{let t=e.target.value;n(t?parseInt(t):null)},className:"input-field text-sm",disabled:d,children:[(0,l.jsx)("option",{value:"",children:d?"Loading questions...":a}),u.map(e=>(0,l.jsx)("option",{value:e.id,children:e.label||"Question ".concat(e.id)},e.id))]}),m&&(0,l.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Type: ",m.inputType]})]})},G=e=>{var t;let{contextType:s,contextId:n,currentQuestionId:i,inputType:a}=e,{control:o,register:d,formState:{errors:u},setValue:m,watch:p}=(0,w.xW)(),{fields:x,append:b,remove:f}=(0,w.jz)({control:o,name:"questionOptions"});(0,r.useEffect)(()=>{0===x.length&&b({label:"",sublabel:"",code:"",nextQuestionId:null})},[x,b]);let g="selectone"===a||"selectmany"===a;return(0,l.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,l.jsx)("label",{className:"label-text",children:"Options"}),(0,l.jsxs)("div",{className:"flex flex-col gap-2",children:[x.map((e,t)=>(0,l.jsxs)("div",{className:"border  border-gray-400 rounded-lg p-3 space-y-2",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("input",{...d("questionOptions.".concat(t,".label")),placeholder:"Option ".concat(t+1),className:"input-field flex-1 min-w-[150px]"}),(0,l.jsx)("input",{...d("questionOptions.".concat(t,".sublabel")),placeholder:"Sub Options",className:"input-field flex-1 min-w-[150px]"}),(0,l.jsx)("input",{...d("questionOptions.".concat(t,".code")),placeholder:"Code",className:"w-28 input-field"}),(0,l.jsx)("button",{type:"button",onClick:()=>f(t),className:"p-2 rounded-full hover:bg-red-500/10 text-neutral-700 hover:text-red-500 transition-colors cursor-pointer duration-300",children:(0,l.jsx)(c.A,{size:16})})]}),g&&s&&n&&(0,l.jsxs)("div",{className:"ml-2",children:[(0,l.jsx)("label",{className:"text-xs text-gray-600 mb-1 block",children:"Next Question (when this option is selected):"}),(0,l.jsx)(T,{contextType:s,contextId:n,currentQuestionId:i,value:p("questionOptions.".concat(t,".nextQuestionId")),onChange:e=>{m("questionOptions.".concat(t,".nextQuestionId"),e)},placeholder:"No follow-up question"})]})]},t)),u.questionOptions&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:"".concat(null==(t=u.questionOptions.root)?void 0:t.message)}),(0,l.jsxs)("button",{type:"button",onClick:()=>b({label:"",sublabel:"",code:"",nextQuestionId:null}),className:"btn-outline mt-2 flex items-center justify-center gap-2",children:[(0,l.jsx)(h.A,{size:16}),"Add Option"]})]})]})};var D=s(90221);let F=e=>["selectone","selectmany"].includes(e);var z=s(55594);let P=Object.keys(I),R=z.z.object({label:z.z.string().min(1,"Question name is required"),inputType:z.z.enum(P),hint:z.z.string().optional(),placeholder:z.z.string().optional(),questionOptions:z.z.array(z.z.object({label:z.z.string(),sublabel:z.z.string().optional(),code:z.z.string(),nextQuestionId:z.z.number().optional().nullable()})).optional()}).superRefine((e,t)=>{F(e.inputType)});var O=s(57799);let M=()=>(0,l.jsx)("div",{className:"fixed top-0 left-0 h-screen w-screen bg-neutral-900/20 z-50 flex items-center justify-center",onClick:e=>{e.stopPropagation()},children:(0,l.jsx)(O.A,{})});var L=s(97168),K=s(89852),B=s(82714),U=s(78749),V=s(95749),H=s(88524),$=s(53999);function _(e){let{projectId:t,onTableCreated:s,isInModal:n=!1,isEditMode:i=!1,existingTableData:a}=e,{toast:o}=function(){let[e,t]=(0,r.useState)([]);return{toast:e=>{t(t=>[...t,e]),setTimeout(()=>{t(t=>t.filter(t=>t!==e))},3e3)},toasts:e}}(),[d,u]=(0,r.useState)((null==a?void 0:a.label)||""),[m,p]=(0,r.useState)(()=>{if(null==a?void 0:a.tableColumns){let e=[];return a.tableColumns.forEach(t=>{let s={id:"col-".concat(t.id),columnName:t.columnName,level:0,parentColumnId:void 0};e.push(s),t.childColumns&&t.childColumns.length>0&&(console.error("Processing ".concat(t.childColumns.length,' child columns for parent "').concat(t.columnName,'"')),t.childColumns.forEach(s=>{let l={id:"col-".concat(s.id),columnName:s.columnName,level:1,parentId:"col-".concat(t.id),parentColumnId:t.id};e.push(l)}))}),e.length>0?e:[{id:"col-1",columnName:"",level:0}]}return[{id:"col-1",columnName:"",level:0}]}),[x,b]=(0,r.useState)(()=>(null==a?void 0:a.tableRows)&&a.tableRows.length>0?[...a.tableRows].sort((e,t)=>e.id-t.id).map(e=>({id:e.id,rowsName:e.rowsName})):[{rowsName:"Row 1"}]),[f,g]=(0,r.useState)(!1),[j,y]=(0,r.useState)(!0),N=()=>"col-".concat(Date.now(),"-").concat(Math.floor(1e3*Math.random())),w=()=>{p([...m,{id:N(),columnName:"",level:0}])},q=e=>{let t=C(e);if(!t)return;if(t.level>0)return void o({title:"Cannot add child column",description:"Cannot create more than 2 levels of nested columns (parent → child → grandchild)",variant:"destructive"});if(m.filter(t=>t.parentId===e).length>=2)return void o({title:"Cannot add more child columns",description:'Parent column "'.concat(t.columnName||"Unnamed",'" cannot have more than 2 child columns'),variant:"destructive"});let s=e.match(/^col-(\d+)/),l=s?parseInt(s[1],10):void 0,r={id:N(),columnName:"",parentId:e,level:t.level+1,parentColumnId:l},n=[...m],i=I(e);if(-1===i||i===m.findIndex(t=>t.id===e)){let t=m.findIndex(t=>t.id===e);n.splice(t+1,0,r)}else n.splice(i+1,0,r);p(n)},C=e=>m.find(t=>t.id===e),I=e=>{let t=m.findIndex(t=>t.id===e);if(-1===t)return -1;let s=t,l=!1;for(let r=t+1;r<m.length;r++)if(m[r].parentId===e)s=r,l=!0;else if(k(m[r],e))s=r,l=!0;else break;return l?s:t},k=(e,t)=>{if(e.parentId===t)return!0;if(!e.parentId)return!1;let s=C(e.parentId);return!!s&&k(s,t)},S=()=>{let e=x.length+1;b([...x,{rowsName:"Row ".concat(e)}])},E=e=>{if(m.length<=1)return;let t=new Set([e]);m.forEach(s=>{k(s,e)&&t.add(s.id)});let s=m.filter(e=>!t.has(e.id));0===s.length&&s.push({id:N(),columnName:"",level:0}),p(s)},A=e=>{let t=[...x];t.splice(e,1),b(t)},Q=(e,t)=>{p(m.map(s=>s.id===e?{...s,columnName:t}:s))},T=(e,t)=>{let s=[...x];s[e]={...s[e],rowsName:t},b(s);let l=document.querySelectorAll(".row-input");l&&l[e]&&(t.trim()?l[e].classList.remove("border-red-500"):l[e].classList.add("border-red-500"))},G=()=>{var e;let t=i||(null==a?void 0:a.id),s=new Map;t&&(null==a?void 0:a.tableColumns)&&a.tableColumns.forEach(e=>{let t="col-".concat(e.id);s.set(t,e.id)});let l=new Map;m.forEach(e=>{if(e.parentId){var t;l.has(e.parentId)||l.set(e.parentId,[]),null==(t=l.get(e.parentId))||t.push(e.id)}}),t&&(null==a?void 0:a.tableColumns)&&m.forEach(e=>{if(e.columnName.trim()){let t=e.id.match(/^col-(\d+)/);if(t&&t[1]){let l=parseInt(t[1],10);a.tableColumns.find(e=>e.id===l)&&s.set(e.id,l)}}});let r=Math.max(...Array.from(s.values(),e=>e||0),...(null==a||null==(e=a.tableColumns)?void 0:e.map(e=>e.id))||[0],0)+1;m.forEach(e=>{e.columnName.trim()&&!s.has(e.id)&&s.set(e.id,r++)});let n=[],d=new Map,c=Math.max(...m.map(e=>e.level||0),0);for(let e=0;e<=c;e++)m.filter(t=>t.level===e&&t.columnName.trim()).forEach(e=>{let l=s.get(e.id),r={columnName:e.columnName.trim()};if(t&&l&&(r.id=l),e.parentId)if(t){m.find(t=>t.id===e.parentId);let t=s.get(e.parentId);t?r.parentColumnId=t:(console.warn('Could not find parent DB ID for column "'.concat(e.columnName,'" (parentId: ').concat(e.parentId,")")),o({title:"Warning",description:'Column "'.concat(e.columnName,'" had a missing parent reference and was converted to a top-level column.'),variant:"destructive"}))}else{let t=d.get(e.parentId);void 0!==t?r.parentColumnId=t+1:(console.warn('Could not find parent position for column "'.concat(e.columnName,'" (parentId: ').concat(e.parentId,")")),o({title:"Warning",description:'Column "'.concat(e.columnName,'" had a missing parent reference and was converted to a top-level column.'),variant:"destructive"}))}let i=n.length;n.push(r),d.set(e.id,i)});let u=n.filter(e=>{if(void 0===e.parentColumnId)return!1;if(t){if(e.parentColumnId<=0)return!0}else{if(e.parentColumnId<=0||e.parentColumnId>n.length)return!0;let t=n[e.parentColumnId-1];if(t&&void 0!==t.parentColumnId)return!0}return!1});return u.length>0&&(console.error("Found invalid parent column references:",u),u.forEach(e=>{e.parentColumnId=void 0}),o({title:"Warning",description:"Fixed ".concat(u.length," invalid column relationships. Some child columns were converted to top-level columns."),variant:"destructive"})),n},D=async e=>{if(e&&e.preventDefault(),!d.trim())return void o({title:"Error",description:"Please enter a table label",variant:"destructive"});let l=m.filter(e=>e.columnName.trim()),r=x.filter(e=>e.rowsName.trim()),n=document.querySelectorAll(".row-input");if(x.forEach((e,t)=>{n&&n[t]&&(e.rowsName.trim()?n[t].classList.remove("border-red-500"):n[t].classList.add("border-red-500"))}),0===l.length)return void o({title:"Error",description:"Please add at least one column with a name",variant:"destructive"});if(0===r.length)return void o({title:"Error",description:"Please add at least one row with a name",variant:"destructive"});g(!0);try{let e,l=G(),n=[...r].sort((e,t)=>e.id&&t.id?e.id-t.id:e.id?-1:t.id?1:r.indexOf(e)-r.indexOf(t)).map(e=>{let t={rowsName:e.rowsName.trim()};if(i||(null==a?void 0:a.id)){var s;e.id&&(null==a||null==(s=a.tableRows)?void 0:s.some(t=>t.id===e.id))&&(t.id=e.id)}return t});(null==a?void 0:a.id)?(e=await (0,V.am)(a.id,d.trim(),l,n),o({title:"Success",description:"Table question updated successfully"})):(e=await (0,V.ZR)(d.trim(),t,l,n),o({title:"Success",description:"Table question created successfully"}),u(""),p([{id:N(),columnName:"",level:0}]),b([])),s&&(null==e?void 0:e.id)&&s(e.id)}catch(t){var c,h;console.error("Error with table operation:",t);let e=(null==a?void 0:a.id)?"Failed to update table question":"Failed to create table question";(null==(h=t.response)||null==(c=h.data)?void 0:c.message)?e=t.response.data.message:t.message&&(e=t.message),o({title:"Error",description:e,variant:"destructive"})}finally{g(!1)}},F=r.useRef(null);return(0,r.useEffect)(()=>{if(n){let e=e=>{e.cancelable&&e.stopPropagation(),D()};F.current&&F.current.addEventListener("submitTable",e),document.addEventListener("submitTable",e);let t=document.querySelectorAll(".table-question-builder");return t.forEach(t=>{t.addEventListener("submitTable",e)}),()=>{F.current&&F.current.removeEventListener("submitTable",e),document.removeEventListener("submitTable",e),t.forEach(t=>{t.removeEventListener("submitTable",e)})}}},[n,D,m]),(0,l.jsxs)("div",{ref:F,className:"space-y-6 p-4 border border-gray-200 rounded-md w-full table-question-builder",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("h3",{className:"text-lg font-medium",children:i||(null==a?void 0:a.id)?"Edit Table Question":"Create Table Question"}),(0,l.jsx)(L.$,{type:"button",variant:"outline",size:"sm",onClick:()=>y(!j),className:"flex items-center gap-1",children:j?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(U.A,{className:"h-4 w-4"}),"Hide Preview"]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(v.A,{className:"h-4 w-4"}),"Show Preview"]})})]}),(0,l.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md text-sm text-blue-800 border border-blue-200",children:[(0,l.jsx)("p",{className:"font-medium mb-1",children:"Table Structure Guidelines:"}),(0,l.jsxs)("ul",{className:"list-disc pl-5 space-y-1",children:[(0,l.jsxs)("li",{children:["Create multiple ",(0,l.jsx)("span",{className:"font-medium",children:"parent columns"})," ",'using the "Add Top-Level Column" button']}),(0,l.jsxs)("li",{children:["Add up to 2 ",(0,l.jsx)("span",{className:"font-medium",children:"child columns"}),' under each parent using the "+" button']}),(0,l.jsx)("li",{children:"Child columns cannot have their own children (maximum 2 levels)"})]})]}),n?(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(B.J,{htmlFor:"table-label",children:"Table Label"}),(0,l.jsx)(K.p,{id:"table-label",value:d,onChange:e=>u(e.target.value),placeholder:"Enter table question label",required:!0})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 max-h-[60vh] overflow-y-auto",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(B.J,{children:"Columns"}),m.map(e=>(0,l.jsxs)("div",{className:(0,$.cn)("flex items-center gap-2 p-2 rounded-md",0===e.level?"bg-gray-50":"bg-white border-l-2 border-gray-300"),style:{marginLeft:"".concat(20*e.level,"px")},children:[(0,l.jsxs)("div",{className:"flex-1 flex items-center gap-2",children:[e.level>0&&(0,l.jsx)("div",{className:"w-4 h-4 flex items-center justify-center",children:(0,l.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full"})}),(0,l.jsx)(K.p,{value:e.columnName,onChange:t=>Q(e.id,t.target.value),placeholder:"".concat(0===e.level?"Parent":"Child"," Column"),className:(0,$.cn)("flex-1",0===e.level?"border-blue-200 bg-white":"border-dashed")}),0===e.level&&(0,l.jsx)("div",{className:"text-xs text-blue-500 font-medium",children:"Parent"}),e.level>0&&(0,l.jsx)("div",{className:"text-xs text-gray-500 font-medium",children:"Child"})]}),(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(L.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>q(e.id),title:e.level>0?"Child columns cannot have their own children":m.filter(t=>t.parentId===e.id).length>=2?"Maximum 2 child columns allowed":"Add child column",disabled:e.level>0||m.filter(t=>t.parentId===e.id).length>=2,children:(0,l.jsx)(h.A,{className:(0,$.cn)("h-4 w-4",(e.level>0||m.filter(t=>t.parentId===e.id).length>=2)&&"text-gray-300")})}),(0,l.jsx)(L.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>E(e.id),disabled:m.length<=1,title:"Remove column",children:(0,l.jsx)(c.A,{className:"h-4 w-4"})})]})]},e.id)),(0,l.jsxs)(L.$,{type:"button",variant:"outline",size:"sm",onClick:w,className:"mt-2",children:[(0,l.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Add Top-Level Column"]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(B.J,{children:"Rows"}),x.map((e,t)=>(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(K.p,{value:e.rowsName,onChange:e=>T(t,e.target.value),placeholder:"Row ".concat(t+1),className:"row-input ".concat(e.rowsName.trim()?"":"border-red-500")}),(0,l.jsx)(L.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>A(t),disabled:!1,children:(0,l.jsx)(c.A,{className:"h-4 w-4"})})]},t)),(0,l.jsxs)(L.$,{type:"button",variant:"outline",size:"sm",onClick:S,className:"mt-2",children:[(0,l.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Add Row"]})]})]}),j&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(B.J,{children:"Table Preview"}),(0,l.jsx)("div",{className:"border rounded-md p-4 overflow-x-auto max-h-[300px] overflow-y-auto",children:(0,l.jsxs)(H.XI,{children:[(0,l.jsx)(H.A0,{children:(()=>{let e=Math.max(...m.map(e=>e.level),0),t=[];t.push((0,l.jsx)(H.Hj,{children:m.filter(e=>0===e.level).map(e=>{let t=e=>{let s=m.filter(t=>t.parentId===e),l=[...s];return s.forEach(e=>{l=[...l,...t(e.id)]}),l},s=t(e.id),r=s.filter(e=>!m.some(t=>t.parentId===e.id)),n=s.length>0&&r.length||1;return(0,l.jsx)(H.nd,{colSpan:n,className:"text-center border-b",children:e.columnName||"Column"},e.id)})},"header-row-0"));for(let s=1;s<=e;s++)t.push((0,l.jsx)(H.Hj,{children:m.filter(e=>e.level===s-1).map(e=>{let t=m.filter(t=>t.parentId===e.id);return 0===t.length?(0,l.jsx)(H.nd,{className:"text-center border-b"},"empty-".concat(e.id)):t.map(e=>{let t=e=>{let s=m.filter(t=>t.parentId===e),l=[...s];return s.forEach(e=>{l=[...l,...t(e.id)]}),l},s=t(e.id),r=s.filter(e=>!m.some(t=>t.parentId===e.id)),n=s.length>0&&r.length||1;return(0,l.jsx)(H.nd,{colSpan:n,className:"text-center border-b",children:e.columnName||"Child Column"},e.id)})})},"header-row-".concat(s)));return t})()}),(0,l.jsx)(H.BF,{children:x.length>0?x.map((e,t)=>(0,l.jsx)(H.Hj,{children:m.filter(e=>{let t=m.some(t=>t.parentId===e.id);return 0===e.level&&!t||e.level>0}).map(e=>(0,l.jsx)(H.nA,{className:"bg-gray-50",children:(0,l.jsx)("div",{className:"h-8 flex items-center justify-center text-gray-400 text-xs",children:"Input field"})},e.id))},t)):(0,l.jsx)(H.Hj,{children:m.filter(e=>{let t=m.some(t=>t.parentId===e.id);return 0===e.level&&!t||e.level>0}).map(e=>(0,l.jsx)(H.nA,{className:"bg-gray-50",children:(0,l.jsx)("div",{className:"h-8 flex items-center justify-center text-gray-400 text-xs",children:"Input field"})},e.id))})})]})}),(0,l.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"This preview shows how the table will appear to users filling out the form."})]})]})]}):(0,l.jsxs)("form",{onSubmit:D,className:"space-y-4",children:[(0,l.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md text-sm text-blue-800 border border-blue-200 mb-4",children:[(0,l.jsx)("p",{className:"font-medium mb-1",children:"Table Structure Guidelines:"}),(0,l.jsxs)("ul",{className:"list-disc pl-5 space-y-1",children:[(0,l.jsxs)("li",{children:["Create multiple"," ",(0,l.jsx)("span",{className:"font-medium",children:"parent columns"}),' using the "Add Top-Level Column" button']}),(0,l.jsxs)("li",{children:["Add up to 2 ",(0,l.jsx)("span",{className:"font-medium",children:"child columns"})," ",'under each parent using the "+" button']}),(0,l.jsx)("li",{children:"Child columns cannot have their own children (maximum 2 levels)"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(B.J,{htmlFor:"table-label",children:"Table Label"}),(0,l.jsx)(K.p,{id:"table-label",value:d,onChange:e=>u(e.target.value),placeholder:"Enter table question label",required:!0})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(B.J,{children:"Columns"}),m.map(e=>(0,l.jsxs)("div",{className:(0,$.cn)("flex items-center gap-2 p-2 rounded-md",0===e.level?"bg-gray-50":"bg-white border-l-2 border-gray-300"),style:{marginLeft:"".concat(20*e.level,"px")},children:[(0,l.jsxs)("div",{className:"flex-1 flex items-center gap-2",children:[e.level>0&&(0,l.jsx)("div",{className:"w-4 h-4 flex items-center justify-center",children:(0,l.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full"})}),(0,l.jsx)(K.p,{value:e.columnName,onChange:t=>Q(e.id,t.target.value),placeholder:"".concat(0===e.level?"Parent":"Child"," Column"),className:(0,$.cn)("flex-1",0===e.level?"border-blue-200 bg-white":"border-dashed")}),0===e.level&&(0,l.jsx)("div",{className:"text-xs text-blue-500 font-medium",children:"Parent"}),e.level>0&&(0,l.jsx)("div",{className:"text-xs text-gray-500 font-medium",children:"Child"})]}),(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(L.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>q(e.id),title:e.level>0?"Child columns cannot have their own children":m.filter(t=>t.parentId===e.id).length>=2?"Maximum 2 child columns allowed":"Add child column",disabled:e.level>0||m.filter(t=>t.parentId===e.id).length>=2,children:(0,l.jsx)(h.A,{className:(0,$.cn)("h-4 w-4",(e.level>0||m.filter(t=>t.parentId===e.id).length>=2)&&"text-gray-300")})}),(0,l.jsx)(L.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>E(e.id),disabled:m.length<=1,title:"Remove column",children:(0,l.jsx)(c.A,{className:"h-4 w-4"})})]})]},e.id)),(0,l.jsxs)(L.$,{type:"button",variant:"outline",size:"sm",onClick:w,className:"mt-2",children:[(0,l.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Add Top-Level Column"]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(B.J,{children:"Rows"}),x.map((e,t)=>(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(K.p,{value:e.rowsName,onChange:e=>T(t,e.target.value),placeholder:"Row ".concat(t+1),className:"row-input ".concat(e.rowsName.trim()?"":"border-red-500")}),(0,l.jsx)(L.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>A(t),disabled:!1,children:(0,l.jsx)(c.A,{className:"h-4 w-4"})})]},t)),(0,l.jsxs)(L.$,{type:"button",variant:"outline",size:"sm",onClick:S,className:"mt-2",children:[(0,l.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Add Row"]})]})]}),j&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(B.J,{children:"Table Preview"}),(0,l.jsx)("div",{className:"border rounded-md p-4 overflow-x-auto",children:(0,l.jsxs)(H.XI,{children:[(0,l.jsx)(H.A0,{children:(()=>{let e=Math.max(...m.map(e=>e.level),0),t=[];t.push((0,l.jsx)(H.Hj,{children:m.filter(e=>0===e.level).map(e=>{let t=e=>{let s=m.filter(t=>t.parentId===e),l=[...s];return s.forEach(e=>{l=[...l,...t(e.id)]}),l},s=t(e.id),r=s.filter(e=>!m.some(t=>t.parentId===e.id)),n=s.length>0&&r.length||1;return(0,l.jsx)(H.nd,{colSpan:n,className:"text-center border-b",children:e.columnName||"Column"},e.id)})},"header-row-0"));for(let s=1;s<=e;s++)t.push((0,l.jsx)(H.Hj,{children:m.filter(e=>e.level===s-1).map(e=>{let t=m.filter(t=>t.parentId===e.id);return 0===t.length?(0,l.jsx)(H.nd,{className:"text-center border-b"},"empty-".concat(e.id)):t.map(e=>{let t=e=>{let s=m.filter(t=>t.parentId===e),l=[...s];return s.forEach(e=>{l=[...l,...t(e.id)]}),l},s=t(e.id),r=s.filter(e=>!m.some(t=>t.parentId===e.id)),n=s.length>0&&r.length||1;return(0,l.jsx)(H.nd,{colSpan:n,className:"text-center border-b",children:e.columnName||"Child Column"},e.id)})})},"header-row-".concat(s)));return t})()}),(0,l.jsx)(H.BF,{children:x.length>0?x.map((e,t)=>(0,l.jsx)(H.Hj,{children:m.filter(e=>{let t=m.some(t=>t.parentId===e.id);return 0===e.level&&!t||e.level>0}).map(e=>(0,l.jsx)(H.nA,{className:"bg-gray-50",children:(0,l.jsx)("div",{className:"h-8 flex items-center justify-center text-gray-400 text-xs",children:"Input field"})},e.id))},t)):(0,l.jsx)(H.Hj,{children:m.filter(e=>{let t=m.some(t=>t.parentId===e.id);return 0===e.level&&!t||e.level>0}).map(e=>(0,l.jsx)(H.nA,{className:"bg-gray-50",children:(0,l.jsx)("div",{className:"h-8 flex items-center justify-center text-gray-400 text-xs",children:"Input field"})},e.id))})})]})}),(0,l.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"This preview shows how the table will appear to users filling out the form."})]})]}),(0,l.jsx)("div",{className:"flex items-center justify-end space-x-4 mt-6",children:(0,l.jsx)(L.$,{type:"submit",disabled:f,className:"bg-primary-500 text-white hover:bg-primary-600",children:f?"Saving...":i||(null==a?void 0:a.id)?"Update":"Save"})})]})]})}var J=s(27859),W=s(3925);let Y=e=>new Promise(t=>{if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel"].includes(e.type))return void t({isValid:!1,error:"Please select a valid Excel file (.xlsx or .xls)"});if(e.size>5242880)return void t({isValid:!1,error:"File size must be less than 5MB"});let s=new FileReader;s.onload=e=>{try{var s,l,r;let n=new Uint8Array(null==(s=e.target)?void 0:s.result),i=W.LF(n,{type:"array"}),a=i.Sheets[i.SheetNames[0]],o=W.Wp.sheet_to_json(a,{header:1});if(o.length<2)return void t({isValid:!1,error:"Excel file is empty or has no valid data"});let d=o[0].map(e=>null==e?void 0:e.toString().trim());if(!(null==(l=d[0])?void 0:l.includes("label"))||!(null==(r=d[1])?void 0:r.includes("code")))return void t({isValid:!1,error:"Invalid Excel format: Missing required headers (Label, Code)"});let c=o.slice(1);if(0===c.length)return void t({isValid:!1,error:"Excel file contains no valid options"});for(let e=0;e<c.length;e++){let s=c[e];if(!s[0]||!s[1])return void t({isValid:!1,error:"Invalid data in row ".concat(e+2,": Label and Code are required")})}t({isValid:!0})}catch(e){t({isValid:!1,error:"Failed to parse Excel file"})}},s.onerror=()=>{t({isValid:!1,error:"Error reading Excel file"})},s.readAsArrayBuffer(e)}),X=e=>{let{file:t,onRemove:s,error:r}=e;return(0,l.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg ".concat(r?"bg-red-50 border border-red-200":"bg-green-50 border border-green-200"),children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[r?(0,l.jsx)(J.wew,{className:"text-red-500"}):(0,l.jsx)(J.qGT,{className:"text-green-500"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"text-sm font-medium",children:t.name}),(0,l.jsxs)("div",{className:"text-xs text-gray-500",children:[(t.size/1024).toFixed(1)," KB"]}),r&&(0,l.jsx)("div",{className:"text-xs text-red-600",children:r})]})]}),(0,l.jsx)("button",{type:"button",onClick:s,className:"text-red-500 hover:text-red-700 p-1",title:"Remove file",children:(0,l.jsx)(J.id1,{})})]})},Z=()=>{let e=new Blob(["Label,Code,Next Question ID\nOption 1,opt1,\nOption 2,opt2,\nOption 3,opt3,"],{type:"text/csv"}),t=window.URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download="question_options_template.csv",document.body.appendChild(s),s.click(),window.URL.revokeObjectURL(t),document.body.removeChild(s)},ee=e=>{let{isOpen:t,onConfirm:s,onCancel:r}=e;return t?(0,l.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,l.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full",children:[(0,l.jsx)("h2",{className:"text-lg text-neutral-700 font-semibold mb-4",children:"Unsaved Changes"}),(0,l.jsx)("p",{className:"mb-6 text-neutral-700",children:"You have unsaved changes. Are you sure you want to close this form?"}),(0,l.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,l.jsx)("button",{onClick:r,className:"btn-outline",children:"Cancel"}),(0,l.jsx)("button",{onClick:s,className:"btn-danger",children:"Discard Changes"})]})]})}):null},et=e=>{let{showModal:t,setShowModal:s,contextType:n,contextId:i,position:a}=e,o=(0,w.mN)({resolver:(0,D.u)(R),defaultValues:{label:"",inputType:"",hint:"",placeholder:"",questionOptions:[]}}),{register:d,formState:{errors:c,isSubmitted:u,isDirty:m},setValue:p,handleSubmit:x,reset:h,watch:b}=o,[f,g]=(0,r.useState)(!1),[v,j]=(0,r.useState)(null),[y,Q]=(0,r.useState)("form"),[T,z]=(0,r.useState)(""),[P,O]=(0,r.useState)(!1),[L,K]=(0,r.useState)(""),[B,U]=(0,r.useState)(!1),V=(0,r.useRef)(null),H=(0,S.jE)(),$="project"===n?["questions",i]:"template"===n?["templateQuestions",i]:["questionBlockQuestions",i],W=(0,E.n)({mutationFn:A.Af,onSuccess:()=>{H.invalidateQueries({queryKey:$}),ei()},onError:e=>{z(e.message||"Failed to add question"),U(!1)}});(0,r.useEffect)(()=>{d("inputType",{required:"Please select an input type"})},[d]),(0,r.useEffect)(()=>{p("inputType",L,{shouldValidate:u})},[L,p,u]),(0,r.useEffect)(()=>{t&&U(!1)},[t]);let et=async e=>{var t;let s=null==(t=e.target.files)?void 0:t[0];if(!s)return;let l=await Y(s);if(!l.isValid){z(l.error||"Invalid file"),j(null);return}z(""),j(s)},es=()=>{j(null),z(""),V.current&&(V.current.value="")},el=e=>{Q(e),"form"===e&&es()},er=()=>{let e=b("questionOptions");return m||!!b("label")||!!b("hint")||!!b("placeholder")||!!L||!!v||e&&Array.isArray(e)&&e.length>0},en=()=>{er()?g(!0):ei()},ei=()=>{h(),K(""),j(null),Q("form"),z(""),U(!1),g(!1),O(!1),s(!1)},ea=async e=>{if(B)return;if("table"===L){let e=document.querySelector(".table-question-builder");e?e.dispatchEvent(new CustomEvent("submitTable")):console.error("TableQuestionBuilder not found");return}if(F(L)){if("excel"===y){if(!v)return void z("Please select an Excel file")}else if(0===(e.questionOptions||[]).length)return void o.setError("questionOptions",{type:"custom",message:"At least one option is required for this input type"})}U(!0);let t=null!=v?v:void 0,s={label:e.label,isRequired:P,hint:e.hint,placeholder:e.placeholder,inputType:L,questionOptions:"form"===y?e.questionOptions:void 0,file:"excel"===y?t:void 0};W.mutate({contextType:n,contextId:i,dataToSend:s,position:a})},eo=F(L);return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)(N.A,{isOpen:t,onClose:en,className:"w-11/12 tablet:w-4/5 desktop:w-4/5 bg-neutral-100 rounded-lg p-6",children:[(0,l.jsx)("h1",{className:"heading-text capitalize mb-4",children:"Add question"}),W.isPending&&(0,l.jsx)(M,{}),(0,l.jsx)(w.Op,{...o,children:(0,l.jsxs)("form",{className:"space-y-4 max-h-[500px] overflow-y-auto p-4",onSubmit:x(ea),children:[(0,l.jsxs)("div",{className:"label-input-group group ",children:[(0,l.jsx)("input",{...d("label",{required:"Question name is required"}),className:"input-field",placeholder:"Enter the question"}),c.label&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:"".concat(c.label.message)})]}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"label-input-group group",children:[(0,l.jsx)("label",{htmlFor:"question-type",className:"label-text",children:"Input Type"}),(0,l.jsx)("div",{className:"mt-1",children:(0,l.jsx)(q.l,{id:"question-type",options:Object.values(I),value:L&&I[L]?I[L]:"Select an option",onChange:e=>{let t=(0,C.H)(e,I);K(null!=t?t:""),Q("form"),es()}})}),c.inputType&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:"".concat(c.inputType.message)})]}),(0,l.jsx)("div",{className:"flex items-end",children:(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(k.dO,{id:"required",checked:P,onCheckedChange:()=>O(e=>!e),className:"data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 border border-primary-500"}),(0,l.jsx)("label",{htmlFor:"required",className:"label-text",children:"Required"})]})})]}),(0,l.jsxs)("div",{className:"label-input-group group",children:[(0,l.jsx)("label",{htmlFor:"hint",className:"label-text",children:"Help text"}),(0,l.jsx)("textarea",{...d("hint"),id:"hint",placeholder:"Add a hint or instructions for this question",className:"input-field resize-none"})]}),(0,l.jsxs)("div",{className:"label-input-group group",children:[(0,l.jsx)("label",{htmlFor:"placeholder",className:"label-text",children:"Placeholder text"}),(0,l.jsx)("input",{...d("placeholder"),id:"placeholder",placeholder:"Add a placeholder for this question",className:"input-field"})]}),eo&&(0,l.jsx)("div",{className:"space-y-4",children:(0,l.jsxs)("div",{className:"label-input-group group",children:[(0,l.jsx)("label",{className:"label-text",children:"Question Options"}),(0,l.jsxs)("div",{className:"flex space-x-4 mb-4",children:[(0,l.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,l.jsx)("input",{type:"radio",value:"form",checked:"form"===y,onChange:e=>el(e.target.value),className:"text-primary-500"}),(0,l.jsx)("span",{children:"Manual Entry"})]}),(0,l.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,l.jsx)("input",{type:"radio",value:"excel",checked:"excel"===y,onChange:e=>el(e.target.value),className:"text-primary-500"}),(0,l.jsx)("span",{children:"Excel Upload"})]})]}),"excel"===y&&(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-4 hover:border-gray-400 transition-colors",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)(J.tAF,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,l.jsxs)("div",{className:"mt-2",children:[(0,l.jsxs)("label",{htmlFor:"excel-file",className:"cursor-pointer",children:[(0,l.jsx)("span",{className:"mt-2 block text-sm font-medium text-gray-900",children:"Upload Excel file with question options"}),(0,l.jsx)("span",{className:"mt-1 block text-xs text-gray-500",children:"Supported formats: .xlsx, .xls (max 5MB)"})]}),(0,l.jsx)("input",{ref:V,id:"excel-file",type:"file",accept:".xlsx,.xls",onChange:et,className:"sr-only"})]}),(0,l.jsxs)("div",{className:"mt-3 flex justify-center space-x-2",children:[(0,l.jsxs)("button",{type:"button",onClick:()=>{var e;return null==(e=V.current)?void 0:e.click()},className:"btn-outline inline-flex items-center",children:[(0,l.jsx)(J.bh6,{className:"mr-2"}),"Choose Excel File"]}),(0,l.jsxs)("button",{type:"button",onClick:Z,className:"btn-outline inline-flex items-center",title:"Download template",children:[(0,l.jsx)(J.Ah9,{className:"mr-2"}),"Download Template"]})]})]})}),v&&(0,l.jsx)(X,{file:v,onRemove:es,error:T}),T&&!v&&(0,l.jsx)("div",{className:"text-sm text-red-600 bg-red-50 p-2 rounded",children:T}),(0,l.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:[(0,l.jsx)("p",{className:"text-sm text-blue-800 font-medium mb-2",children:"Excel Format Requirements:"}),(0,l.jsxs)("ul",{className:"text-xs text-blue-700 space-y-1",children:[(0,l.jsxs)("li",{children:["• Column A: ",(0,l.jsx)("strong",{children:"Label"})," (required) - Display text for the option"]}),(0,l.jsxs)("li",{children:["• Column B: ",(0,l.jsx)("strong",{children:"Code"})," (required) - Unique identifier for the option"]}),(0,l.jsxs)("li",{children:["• Column C: ",(0,l.jsx)("strong",{children:"Next Question ID"})," ","(optional) - For conditional logic"]}),(0,l.jsx)("li",{children:"• First row should contain column headers"}),(0,l.jsx)("li",{children:"• Each subsequent row represents one option"})]})]})]}),"form"===y&&(0,l.jsx)(G,{contextType:n,contextId:i,inputType:L})]})}),"table"===L&&(0,l.jsx)("div",{className:"mt-4",children:(0,l.jsx)(_,{projectId:i,isInModal:!0,onTableCreated:e=>{-1!==e&&H.invalidateQueries({queryKey:$}),ei()}})}),(0,l.jsxs)("div",{className:"flex items-center justify-end space-x-4 pt-4",children:[(0,l.jsx)("button",{type:"button",onClick:en,className:"btn-outline",disabled:B,children:"Cancel"}),(0,l.jsx)("button",{type:"submit",className:"btn-primary flex items-center justify-center gap-2",onClick:e=>{if("table"===L){e.preventDefault();let t=document.querySelector(".table-question-builder");t&&t.dispatchEvent(new CustomEvent("submitTable"))}},disabled:B||"excel"===y&&(!v||!!T),children:B?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("span",{className:"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"}),"Saving..."]}):"Save"})]})]})})]}),(0,l.jsx)(ee,{isOpen:f,onConfirm:ei,onCancel:()=>g(!1)})]})};var es=s(71402),el=s(34540);let er=e=>{let{showModal:t,setShowModal:s,contextType:n,question:i,contextId:a}=e,o=(0,w.mN)({}),{register:d,formState:{errors:c,isSubmitted:u},setValue:m,handleSubmit:p,reset:x}=o,h=e=>"position"in e,b=(e,t)=>{var s;return null!=(s=i[e])?s:t},[f,g]=(0,r.useState)(!1),[v,j]=(0,r.useState)("");(0,r.useEffect)(()=>{let e=R.safeParse(i);if(e.success)x(e.data),j(e.data.inputType||""),e.data.questionOptions&&e.data.questionOptions.length>0&&m("questionOptions",e.data.questionOptions);else{console.warn("Schema parsing failed, using raw question data:",e.error),m("label",b("label","")),m("hint",b("hint","")),m("placeholder",b("placeholder","")),m("inputType",b("inputType","")),j(b("inputType",""));let t=b("questionOptions",[]);Array.isArray(t)&&t.length>0&&m("questionOptions",t)}g(b("isRequired",!1))},[i,x,m]),(0,r.useEffect)(()=>{d("inputType",{required:"Please select an input type"})},[d]),(0,r.useEffect)(()=>{m("inputType",v,{shouldValidate:u})},[v,m,u]);let y=(0,S.jE)(),q=(0,el.wA)(),C="project"===n?["questions"]:"template"===n?["templateQuestions"]:["questionBlockQuestions"],Q=()=>{g(!1),j(""),s(!1)},T=(0,E.n)({mutationFn:A.sr,onSuccess:()=>{y.invalidateQueries({queryKey:C,exact:!1}),q((0,es.Ds)({message:"Successfully updated question",type:"success"})),Q()},onError:()=>{q((0,es.Ds)({message:"Failed to update question",type:"error"}))}}),D=async e=>{if("table"===v){let e=document.querySelector(".table-question-builder");if(e)return void e.dispatchEvent(new CustomEvent("submitTable"))}let t={label:e.label,isRequired:f,hint:e.hint,placeholder:e.placeholder,inputType:v||b("inputType",""),questionOptions:e.questionOptions||[],...h(i)&&{position:i.position}};T.mutate({id:i.id,contextType:n,dataToSend:t,contextId:a})};return(0,l.jsxs)(N.A,{isOpen:t,onClose:Q,className:"w-11/12 tablet:w-4/5 desktop:w-3/5",children:[(0,l.jsx)("h1",{className:"heading-text capitalize mb-4",children:"Edit question"}),T.isPending&&(0,l.jsx)(M,{}),(0,l.jsx)(w.Op,{...o,children:(0,l.jsxs)("form",{className:"space-y-4 max-h-[500px] overflow-y-auto p-4",onSubmit:p(D),children:[(0,l.jsxs)("div",{className:"label-input-group group",children:[(0,l.jsx)("input",{...d("label",{required:"Question name is required"}),className:"input-field",placeholder:"Enter the question"}),c.label&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:"".concat(c.label.message)})]}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"label-input-group group",children:[(0,l.jsx)("label",{htmlFor:"question-type",className:"label-text",children:"Input Type"}),(0,l.jsx)("input",{id:"question-type",className:"input-field bg-gray-100 ",value:v&&I[v]?I[v]:"N/A",disabled:!0})]}),(0,l.jsx)("div",{className:"flex items-end",children:(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(k.dO,{id:"required",checked:f,onCheckedChange:()=>g(e=>!e),className:"data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 border border-primary-500"}),(0,l.jsx)("label",{htmlFor:"required",className:"label-text",children:"Required"})]})}),c.inputType&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:"".concat(c.inputType.message)})]}),(0,l.jsxs)("div",{className:"label-input-group group",children:[(0,l.jsx)("label",{htmlFor:"hint",className:"label-text",children:"Help text"}),(0,l.jsx)("textarea",{...d("hint"),id:"hint",placeholder:"Add a hint or instructions for this question",className:"input-field resize-none"})]}),(0,l.jsxs)("div",{className:"label-input-group group",children:[(0,l.jsx)("label",{htmlFor:"placeholder",className:"label-text",children:"Placeholder text"}),(0,l.jsx)("input",{...d("placeholder"),id:"placeholder",placeholder:"Add a placeholder for this question",className:"input-field"})]}),F(v)&&(0,l.jsx)(G,{contextType:n,contextId:a,currentQuestionId:i.id,inputType:v},"".concat(i.id,"-").concat(v)),(0,l.jsxs)("div",{className:"flex items-center justify-end space-x-4",children:[(0,l.jsx)("button",{type:"button",onClick:Q,className:"btn-outline",children:"Cancel"}),(0,l.jsx)("button",{onClick:p(D),className:"btn-primary",children:"Save Edit"})]})]})})]})},en=e=>{let{showModal:t,setShowModal:s,contextType:n,question:i,contextId:a}=e,o=(0,S.jE)(),d=(0,el.wA)(),{data:c,isLoading:u,error:m}=(0,Q.I)({queryKey:["tableQuestion",i.id],queryFn:async()=>{try{return await (0,V.q7)(i.id)}catch(e){throw console.error("Error fetching table data:",e),e}},enabled:t&&i.id>0&&"table"===i.inputType}),p=r.useMemo(()=>c?{id:c.id,label:c.label,tableColumns:c.tableColumns.map(e=>{var t;return{id:e.id,columnName:e.columnName,parentColumnId:e.parentColumnId,childColumns:(null==(t=e.childColumns)?void 0:t.map(t=>({id:t.id,columnName:t.columnName,parentColumnId:t.parentColumnId||e.id})))||[]}}),tableRows:c.tableRows.map(e=>({id:e.id,rowsName:e.rowsName}))}:null,[c]);(0,r.useEffect)(()=>{},[t,i]);let x="project"===n?["questions"]:"template"===n?["templateQuestions"]:["questionBlockQuestions"];m&&(console.error("Error fetching table data:",m),d((0,es.Ds)({message:"Failed to load table data",type:"error"})));let h=r.useRef(null);return(0,l.jsxs)(N.A,{isOpen:t,onClose:()=>{window.confirm("Are you sure you want to close? Any unsaved changes will be lost.")&&s(!1)},className:"w-11/12 tablet:w-4/5 desktop:w-3/5",preventOutsideClick:!0,children:[u&&(0,l.jsx)(M,{}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h1",{className:"heading-text capitalize mb-4",children:"Edit Table Question"}),p?(0,l.jsxs)("div",{ref:h,className:"table-question-builder-container",children:[(0,l.jsx)(_,{projectId:a,isInModal:!0,isEditMode:!0,existingTableData:p,onTableCreated:e=>{o.invalidateQueries({queryKey:x,exact:!1}),o.invalidateQueries({queryKey:["tableQuestion",i.id],exact:!0}),d((0,es.Ds)({message:"Successfully updated table question",type:"success"})),setTimeout(()=>{s(!1)},100)}}),(0,l.jsxs)("div",{className:"flex items-center justify-end space-x-4 mt-6",children:[(0,l.jsx)("button",{type:"button",onClick:()=>s(!1),className:"btn-outline",children:"Cancel"}),(0,l.jsx)("button",{type:"button",onClick:()=>{let e=null;if(h.current&&(e=h.current),!e){let t=document.querySelectorAll(".table-question-builder");t.length>0&&(e=t[0])}if(!e&&h.current){let t=h.current.querySelector(".table-question-builder");t&&(e=t)}if(e){let t=new CustomEvent("submitTable",{bubbles:!0,cancelable:!0,detail:{timestamp:Date.now()}});e.dispatchEvent(t)}else{console.error("Could not find any table builder element to dispatch event to");let e=document.querySelector("[class*='table']");if(e){let t=new CustomEvent("submitTable",{bubbles:!0,cancelable:!0,detail:{timestamp:Date.now(),isLastResort:!0}});e.dispatchEvent(t)}}},className:"btn-primary",children:"Save Changes"})]})]}):u?null:(0,l.jsx)("div",{className:"p-4 text-center",children:(0,l.jsx)("p",{className:"text-red-500",children:"Could not load table data. Please try again."})})]})]})};var ei=s(63642),ea=s(54416),eo=s(10150);let ed=e=>{let{showModal:t,setShowModal:s,contextType:n,contextId:i,existingGroup:a,questions:o,questionGroups:d=[]}=e,[c,u]=(0,r.useState)(""),[m,p]=(0,r.useState)([]),x=(0,el.wA)(),h=(0,S.jE)();(0,r.useEffect)(()=>{t&&(a?(u(a.title),p(o.filter(e=>e.questionGroupId===a.id).map(e=>e.id))):(u(""),p([])))},[t,a,o]);let b=(0,E.n)({mutationFn:eo.IF,onSuccess:e=>{h.invalidateQueries({queryKey:["questionGroups",i]}),h.invalidateQueries({queryKey:["questions",i]}),x((0,es.Ds)({message:"Question group created successfully",type:"success"})),s(!1)},onError:e=>{console.error("Error creating question group:",e),x((0,es.Ds)({message:"Failed to create question group",type:"error"}))}}),f=(0,E.n)({mutationFn:eo.lr,onSuccess:e=>{h.invalidateQueries({queryKey:["questionGroups",i]}),h.invalidateQueries({queryKey:["questions",i]}),x((0,es.Ds)({message:"Question group updated successfully",type:"success"})),s(!1)},onError:e=>{console.error("Error updating question group:",e),x((0,es.Ds)({message:"Failed to update question group",type:"error"}))}});return t?(0,l.jsx)("div",{className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40",children:(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-auto p-4",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center p-4 ",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold",children:a?"Edit Question Group":"Create Question Group"}),(0,l.jsx)("button",{onClick:()=>s(!1),className:"text-gray-500 hover:text-gray-700 cursor-pointer",children:(0,l.jsx)(ea.A,{size:20})})]}),(0,l.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),!c.trim())return void x((0,es.Ds)({message:"Group title is required",type:"error"}));a?f.mutate({id:a.id,title:c,order:a.order,selectedQuestionIds:m}):b.mutate({title:c,order:d.length+1,projectId:i,selectedQuestionIds:m})},className:"p-4",children:[(0,l.jsxs)("div",{className:"group label-input-group ",children:[(0,l.jsx)("label",{htmlFor:"title",children:"Group Title"}),(0,l.jsx)("input",{type:"text",id:"title",value:c,onChange:e=>u(e.target.value),className:" input-field w-full",placeholder:"Enter group title",required:!0})]}),(0,l.jsxs)("div",{className:"mt-8 label-input-group",children:[(0,l.jsx)("label",{children:"Select Questions for this Group"}),(0,l.jsx)("div",{className:"border border-neutral-300 rounded-md p-2 max-h-60 overflow-y-auto",children:o.length>0?o.map(e=>{let t=e.questionGroupId?d.find(t=>t.id===e.questionGroupId):null;return(0,l.jsxs)("div",{className:"flex gap-2 items-center mb-3 p-2 border-b border-neutral-300",children:[(0,l.jsx)("input",{type:"checkbox",id:"question-".concat(e.id),checked:m.includes(e.id),onChange:t=>{t.target.checked?p([...m,e.id]):p(m.filter(t=>t!==e.id))},className:"mr-2 cursor-pointer w-5 h-5"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"question-".concat(e.id),className:"text-sm",children:e.label}),t&&t.id!==((null==a?void 0:a.id)||-1)&&(0,l.jsxs)("div",{className:"text-xs text-neutral-700 mt-1",children:["Currently in group: ",(0,l.jsx)("span",{className:"font-medium text-amber-600",children:t.title}),(0,l.jsx)("span",{className:"ml-1 text-neutral-700",children:"(will be moved to this group)"})]})]})]},e.id)}):(0,l.jsx)("p",{className:"text-gray-500 text-sm p-2",children:"No available questions. Please add some questions first."})})]}),(0,l.jsxs)("div",{className:"flex justify-end space-x-2 mt-4",children:[(0,l.jsx)("button",{type:"button",onClick:()=>s(!1),className:"btn-outline",children:"Cancel"}),(0,l.jsx)("button",{type:"submit",className:"px-4 py-2 btn-primary",disabled:b.isPending||f.isPending,children:b.isPending||f.isPending?"Saving...":a?"Update Group":"Create Group"})]})]})]})}):null},ec=e=>{let{showModal:t,setShowModal:s,onConfirmDelete:r,onConfirmDeleteWithQuestions:n,isDeleting:i}=e;return t?(0,l.jsx)("div",{className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40",children:(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-lg w-full max-w-md",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center p-4 border-b",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold",children:"Delete Question Group"}),(0,l.jsx)("button",{onClick:()=>s(!1),className:"text-gray-500 hover:text-gray-700",disabled:i,children:(0,l.jsx)(ea.A,{size:20})})]}),(0,l.jsxs)("div",{className:"p-4",children:[(0,l.jsx)("p",{className:"mb-4",children:"How would you like to delete this question group?"}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("button",{onClick:r,className:"w-full px-4 py-2 bg-amber-500 text-white rounded-md hover:bg-amber-600",disabled:i,children:i?"Deleting...":"Delete Group Only (Keep Questions)"}),(0,l.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"This will remove the group but keep all questions. Questions will be available to add to other groups."}),(0,l.jsx)("button",{onClick:n,className:"w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600",disabled:i,children:i?"Deleting...":"Delete Group and All Questions"}),(0,l.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"This will permanently delete the group and all questions inside it."}),(0,l.jsx)("button",{onClick:()=>s(!1),className:"w-full px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",disabled:i,children:"Cancel"})]})]})]})}):null};var eu=s(47924);let em=e=>{let{isOpen:t,onClose:s,onAddQuestions:n}=e,[i,a]=(0,r.useState)(""),[o,d]=(0,r.useState)([]),[c,u]=(0,r.useState)(!0),{data:m,isLoading:p,isError:x}=(0,Q.I)({queryKey:["libraryQuestions"],queryFn:()=>(0,A.dI)(),enabled:t}),h=m?m.filter(e=>e.label.toLowerCase().includes(i.toLowerCase())):[],b=e=>{o.some(t=>t.id===e.id)?d(o.filter(t=>t.id!==e.id)):d([...o,e])};return((0,r.useEffect)(()=>{t||(d([]),a(""))},[t]),t)?(0,l.jsxs)("div",{className:"fixed inset-0 z-50 flex justify-end",children:[(0,l.jsx)("div",{className:"absolute inset-0 bg-neutral-900/50",onClick:s}),(0,l.jsxs)("div",{className:"relative w-full max-w-md bg-neutral-50 h-full overflow-auto shadow-xl",children:[(0,l.jsxs)("div",{className:"p-4 border-b border-neutral-200",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,l.jsx)("h2",{className:"text-xl font-bold",children:"Search Library"}),(0,l.jsx)("button",{onClick:s,className:"self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300",children:(0,l.jsx)(ea.A,{size:20})})]}),(0,l.jsxs)("div",{className:"relative mb-4",children:[(0,l.jsx)("input",{type:"text",placeholder:"Search...",className:"input-field w-full p-2 pl-10",value:i,onChange:e=>a(e.target.value)}),(0,l.jsx)(eu.A,{className:"absolute left-3 top-2.5 ",size:18})]}),(0,l.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,l.jsxs)("span",{children:[h.length," asset",1!==h.length?"s":""," found"]}),(0,l.jsxs)("label",{className:"flex items-center",children:[(0,l.jsx)("input",{type:"checkbox",checked:c,onChange:()=>u(!c),className:"mr-2"}),"expand details"]})]})]}),(0,l.jsx)("div",{className:"p-4",children:p?(0,l.jsx)("div",{className:"flex justify-center p-8",children:(0,l.jsx)(O.A,{})}):x?(0,l.jsx)("div",{className:"text-red-500 p-4 text-center",children:"Error loading library questions"}):0===h.length?(0,l.jsx)("div",{className:"text-neutral-700 p-4 text-center",children:"No questions found"}):(0,l.jsx)("div",{className:"space-y-2",children:h.map(e=>(0,l.jsx)("div",{className:"border border-neutral-500 rounded-md p-3",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("input",{type:"checkbox",checked:o.some(t=>t.id===e.id),onChange:()=>b(e),className:"mr-3 h-5 w-5 cursor-pointer"}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("div",{className:"font-medium",children:e.label}),c&&(0,l.jsxs)("div",{className:"text-sm text-neutral-700 mt-1",children:["Type: ",String(e.inputType),e.hint&&(0,l.jsxs)("div",{children:["Hint: ",e.hint]})]})]})]})},e.id))})}),(0,l.jsx)("div",{className:"border-t border-gray-200 p-4 sticky bottom-0 bg-neutral-50",children:(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("button",{onClick:s,className:"btn-outline",children:"Cancel"}),(0,l.jsxs)("button",{onClick:()=>{n(o),d([]),s()},disabled:0===o.length,className:"px-4 py-2 rounded-md ".concat(o.length>0?"btn-primary":"bg-gray-200 text-gray-500 pointer-events-none"),children:["Add Selected (",o.length,")"]})]})})]})]}):null},ep=e=>{let{setIsPreviewMode:t,questions:s,contextType:a,contextId:o,permissions:d}=e,c=(0,n.FR)((0,n.MS)(n.AN,{activationConstraint:{distance:8}}),(0,n.MS)(n.uN)),u=d.manageProject||d.editForm,[p,x]=(0,r.useState)(!1),[h,f]=(0,r.useState)(!1),[N,w]=(0,r.useState)(!1),[q,C]=(0,r.useState)(!1),[I,k]=(0,r.useState)(!1),[T,G]=(0,r.useState)(null),[D,F]=(0,r.useState)(!1),[z,P]=(0,r.useState)(!1),[R,O]=(0,r.useState)(!1),[L,K]=(0,r.useState)(!1),[B,U]=(0,r.useState)(null),[V,H]=(0,r.useState)([]),[$,_]=(0,r.useState)(null),[J,W]=(0,r.useState)(""),[Y,X]=(0,r.useState)(!1),[Z,ee]=(0,r.useState)(!1),ea=(0,el.wA)(),eu=(0,S.jE)(),ep="project"===a?["questions",o]:"template"===a?["templateQuestions",o]:["questionBlockQuestions",o],ex=["questionGroups",o],{data:eh=[],isLoading:eb}=(0,Q.I)({queryKey:ex,queryFn:()=>(0,eo.pr)({projectId:o}),enabled:"project"===a}),ef=(e=>{let t=new Map,l=[];return e.forEach(e=>{let l=s.filter(t=>t.questionGroupId===e.id).sort((e,t)=>e.position-t.position);t.set(e.id,{...e,question:l,subGroups:[]})}),e.forEach(e=>{let s=t.get(e.id);if(e.parentGroupId){let r=t.get(e.parentGroupId);r?(r.subGroups=r.subGroups||[],r.subGroups.push(s)):l.push(s)}else l.push(s)}),l.sort((e,t)=>e.order-t.order)})(eh),eg=s.filter(e=>!e.questionGroupId);r.useEffect(()=>{let e=s.some(e=>null!==e.questionGroupId&&void 0!==e.questionGroupId),t=0===eh.length;e&&t&&"project"===a&&eu.invalidateQueries({queryKey:ex})},[s,eh,a,eu,ex]);let ev=(0,E.n)({mutationFn:A.ul,onSuccess:()=>{eu.invalidateQueries({queryKey:ep}),ea((0,es.Ds)({message:"Question deleted successfully",type:"success"}))},onError:()=>{ea((0,es.Ds)({message:"Failed to delete question. Please try again",type:"error"}))},onSettled:()=>{F(!1)}}),ej=(0,E.n)({mutationFn:A.ku,onSuccess:()=>{eu.invalidateQueries({queryKey:ep}),ea((0,es.Ds)({message:"Question duplicated successfully",type:"success"}))},onError:()=>{ea((0,es.Ds)({message:"Failed to duplicate question. Please try again",type:"error"}))},onSettled:()=>{F(!1)}}),ey=(0,E.n)({mutationFn:eo.BU,onSuccess:(e,t)=>{let l=eh.find(e=>e.id===t.id);if(l&&s.filter(e=>e.questionGroupId===l.id).length>0){let e=s.map(e=>e.questionGroupId===l.id?{...e,questionGroupId:void 0}:e);eu.setQueryData(ep,e);let r=eh.filter(e=>e.id!==t.id);eu.setQueryData(ex,r)}eu.invalidateQueries({queryKey:ex}),eu.invalidateQueries({queryKey:ep}),ea((0,es.Ds)({message:"Question group deleted successfully",type:"success"})),K(!1),ee(!1)},onError:e=>{console.error("Error deleting question group:",e),ea((0,es.Ds)({message:"Failed to delete question group. Please try again",type:"error"})),ee(!1)}}),eN=(0,E.n)({mutationFn:eo.yb,onSuccess:(e,t)=>{let l=eh.find(e=>e.id===t.id);if(l){let e=eh.filter(e=>e.id!==t.id);eu.setQueryData(ex,e);let r=s.filter(e=>e.questionGroupId!==l.id);eu.setQueryData(ep,r)}eu.invalidateQueries({queryKey:ex}),eu.invalidateQueries({queryKey:ep}),ea((0,es.Ds)({message:"Question group and its questions deleted successfully",type:"success"})),K(!1),ee(!1)},onError:e=>{console.error("Error deleting question group and questions:",e),ea((0,es.Ds)({message:"Failed to delete question group and questions. Please try again",type:"error"})),ee(!1)}}),ew=(0,E.n)({mutationFn:A.ae,onSuccess:()=>{eu.invalidateQueries({queryKey:ep}),ea((0,es.Ds)({message:"Question order updated successfully",type:"success"}))},onError:e=>{var t,s,l;console.error("Failed to update question positions:",e),console.error("Error response:",null==(t=e.response)?void 0:t.data),ea((0,es.Ds)({message:"Failed to update question order: ".concat((null==(l=e.response)||null==(s=l.data)?void 0:s.message)||e.message||"Please try again"),type:"error"}))}}),eq=e=>{U(e),O(!0)},eC=e=>{U(e),K(!0)},eI=e=>{U(e),O(!0)},ek=e=>{H(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},eS=(0,E.n)({mutationFn:eo.IF,onSuccess:(e,t)=>{var l,r;let n=null==(r=e.data)||null==(l=r.questionGroup)?void 0:l.id;if(n&&t.selectedQuestionIds){let e=s.map(e=>{var s;return(null==(s=t.selectedQuestionIds)?void 0:s.includes(e.id))?{...e,questionGroupId:n}:e});eu.setQueryData(ep,e);let l={id:n,title:t.title,order:t.order,projectId:t.projectId,parentGroupId:t.parentGroupId,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),question:e.filter(e=>e.questionGroupId===n)};eu.setQueryData(ex,[...eh,l])}eu.invalidateQueries({queryKey:ex}),eu.invalidateQueries({queryKey:ep}),ea((0,es.Ds)({message:"Question group created successfully",type:"success"})),H([]),X(!1),ee(!1)},onError:e=>{console.error("Error creating question group:",e),ea((0,es.Ds)({message:"Failed to create question group",type:"error"})),ee(!1)}}),eE=(e,t)=>{_(e),W(t)},eA=()=>{_(null),W("")},eQ=(0,E.n)({mutationFn:eo.lr,onSuccess:()=>{eu.invalidateQueries({queryKey:ex}),ea((0,es.Ds)({message:"Group name updated successfully",type:"success"})),_(null),W(""),ee(!1)},onError:()=>{ea((0,es.Ds)({message:"Failed to update group name",type:"error"})),ee(!1)}}),eT=(0,E.n)({mutationFn:eo.YQ,onSuccess:()=>{eu.invalidateQueries({queryKey:ex}),ea((0,es.Ds)({message:"Group moved successfully",type:"success"}))},onError:()=>{ea((0,es.Ds)({message:"Failed to move group",type:"error"}))}}),eG=(0,E.n)({mutationFn:eo._U,onSuccess:()=>{eu.invalidateQueries({queryKey:ep}),eu.invalidateQueries({queryKey:ex}),ea((0,es.Ds)({message:"Question moved successfully",type:"success"}))},onError:()=>{ea((0,es.Ds)({message:"Failed to move question",type:"error"}))}}),eD=e=>{if(!J.trim())return void ea((0,es.Ds)({message:"Group name cannot be empty",type:"warning"}));ee(!0);let t=eh.find(t=>t.id===e);if(!t)return;let s=eh.map(t=>t.id===e?{...t,title:J}:t);eu.setQueryData(ex,s),eQ.mutate({id:e,title:J,order:t.order,parentGroupId:t.parentGroupId})},eF=e=>{let t=eh.length>0?Math.max(...eh.map(e=>e.order)):0;eS.mutate({title:"New Sub Group",order:t+1,projectId:o,parentGroupId:e,selectedQuestionIds:[]})},ez=(e,t)=>{eT.mutate({childGroupId:e,parentGroupId:t||0})},eP=(e,t,s)=>{eG.mutate({questionId:e,fromGroupId:t,toGroupId:s})};(0,E.n)({mutationFn:A.Af,onSuccess:()=>{eu.invalidateQueries({queryKey:ep})},onError:e=>{console.error("Error adding question:",e),ea((0,es.Ds)({message:"Failed to add a question. Please try again.",type:"error"}))}});let eR=async e=>{if(0!==e.length){k(!0);try{let t=s.length>0?Math.max(...s.map(e=>e.position)):0;for(let s=0;s<e.length;s++){let l=e[s],r={label:l.label,isRequired:l.isRequired,hint:l.hint||"",placeholder:l.placeholder||"",inputType:String(l.inputType),questionOptions:l.questionOptions||[]};await (0,A.Af)({contextType:a,contextId:o,dataToSend:r,position:t+s+1})}eu.invalidateQueries({queryKey:ep}),ea((0,es.Ds)({message:"".concat(e.length," question(s) added successfully"),type:"success"}))}catch(e){console.error("Error adding questions:",e),ea((0,es.Ds)({message:"Failed to add questions from library",type:"error"}))}finally{k(!1)}}};return(0,l.jsxs)("div",{className:"min-h-[60vh] relative",children:[(ev.isPending||ej.isPending||ey.isPending||eN.isPending||ew.isPending||I)&&(0,l.jsx)(M,{}),(0,l.jsx)(et,{showModal:p,setShowModal:x,contextType:a,contextId:o,position:s.length>0?Math.max(...s.map(e=>e.position))+1:1}),T&&"table"!==T.inputType&&(0,l.jsx)(er,{showModal:h,setShowModal:f,contextType:a,question:T,contextId:o}),T&&"table"===T.inputType&&(0,l.jsx)(en,{showModal:N,setShowModal:w,contextType:a,question:T,contextId:o}),(0,l.jsx)(ed,{showModal:z,setShowModal:P,contextType:a,contextId:o,questions:s,questionGroups:eh}),B&&(0,l.jsx)(ed,{showModal:R,setShowModal:O,contextType:a,contextId:o,existingGroup:eh.find(e=>e.id===B),questions:s,questionGroups:eh}),(0,l.jsx)(ei.R,{showModal:D,onClose:()=>F(!1),onConfirm:()=>{T&&T.id&&ev.mutate({contextType:a,id:null==T?void 0:T.id,projectId:o})},title:"Delete Question",description:"Are you sure you want to delete this question? This action cannot be undone.",confirmButtonText:"Delete",cancelButtonText:"Cancel",confirmButtonClass:"btn-danger"}),(0,l.jsx)(ec,{showModal:L,setShowModal:K,onConfirmDelete:()=>{B&&(ee(!0),ey.mutate({id:B}))},onConfirmDeleteWithQuestions:()=>{B&&(ee(!0),eN.mutate({id:B}))},isDeleting:ey.isPending||eN.isPending}),(0,l.jsx)(em,{isOpen:q,onClose:()=>C(!1),onAddQuestions:eR}),(0,l.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("h1",{className:"heading-text mr-4",children:"Form Builder"}),V.length>0?(0,l.jsxs)("button",{className:"btn-primary flex items-center gap-2",onClick:()=>{if(0===V.length)return void ea((0,es.Ds)({message:"Please select at least one question to create a group",type:"warning"}));ee(!0);let e=s.filter(e=>V.includes(e.id)),t=e.length>0?Math.min(...e.map(e=>e.position)):eh.length+1;eS.mutate({title:"New Group",order:t,projectId:o,selectedQuestionIds:V})},disabled:Z,children:[(0,l.jsx)(b.A,{size:16}),"Create Group (",V.length,")"]}):(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsxs)("button",{className:"btn-outline flex items-center gap-2",onClick:()=>{Y?(X(!1),H([])):X(!0)},children:[(0,l.jsx)(b.A,{size:16}),Y?"Cancel Selection":"Select Questions"]}),(0,l.jsxs)("button",{className:"btn-outline flex items-center gap-2",onClick:()=>P(!0),children:[(0,l.jsx)(b.A,{size:16}),"Create Empty Group"]})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("button",{className:"btn-outline p-2",onClick:()=>t(!0),title:"Preview Form",children:(0,l.jsx)(v.A,{size:16})}),(0,l.jsx)("button",{className:"btn-outline p-2",onClick:()=>C(!0),title:"Question Library",children:(0,l.jsx)(j.A,{size:16})})]})]}),(0,l.jsx)("div",{className:"section shadow-none border border-neutral-400",children:(0,l.jsx)(n.Mp,{sensors:c,collisionDetection:n.fp,onDragEnd:e=>{let{active:t,over:l}=e;if(!l||t.id===l.id||"project"!==a)return;let r=t.data.current,n=l.data.current;if((null==r?void 0:r.type)==="group"){let e=r.groupId;if((null==n?void 0:n.type)==="group"&&n.groupId!==e)return void ez(e,n.groupId);if(!n||"ungrouped"===n.type)return void ez(e,null)}if((null==r?void 0:r.type)==="question"){let e=Number(t.id),d=r.questionGroupId;if((null==n?void 0:n.type)==="group"){let t=n.groupId;if(d!==t)return void eP(e,d,t)}if((!n||"ungrouped"===n.type)&&d)return void eP(e,d,null);let c=s.find(e=>e.id===t.id),u=s.find(e=>e.id===l.id);if(c&&u&&c.questionGroupId===u.questionGroupId){let e=s.filter(e=>e.questionGroupId===c.questionGroupId).sort((e,t)=>e.position-t.position),r=e.findIndex(e=>e.id===t.id),n=e.findIndex(e=>e.id===l.id);if(-1!==r&&-1!==n&&r!==n){let t=(0,i.be)(e,r,n).map((e,t)=>({id:Number(e.id),position:t+1}));ew.mutate({contextType:a,contextId:o,questionPositions:t})}}}},children:(0,l.jsx)(i.gB,{items:[...ef.map(e=>"group-".concat(e.id)),...eg.map(e=>e.id)],strategy:i._G,children:(0,l.jsx)("div",{className:"space-y-4",children:0===s.length?(0,l.jsxs)("div",{className:"text-center py-16 px-4",children:[(0,l.jsx)("h3",{className:"heading-text text-muted-foreground",children:"No questions yet"}),(0,l.jsx)("p",{className:"mt-1 text-sm sub-text",children:"Get started by adding your first question"}),(0,l.jsx)("div",{className:"p-4 flex justify-center",children:(0,l.jsxs)("button",{onClick:()=>x(!0),className:"btn-primary",disabled:!u,children:[(0,l.jsx)(y.A,{size:16}),"Add First Question"]})})]}):(0,l.jsxs)(l.Fragment,{children:[ef.map(e=>(0,l.jsx)("div",{className:"mb-4",children:(0,l.jsx)(g,{id:e.id,title:e.title,questions:e.question||[],subGroups:e.subGroups,parentGroupId:e.parentGroupId,level:0,onEditGroup:eq,onDeleteGroup:eC,onAddQuestionToGroup:eI,onCreateSubGroup:eF,onEditQuestion:e=>{G(e),"table"===e.inputType?w(!0):f(!0)},onDeleteQuestion:e=>{G(e),F(!0)},onDuplicateQuestion:e=>{G(e),ej.mutate({id:e.id,contextType:a,contextId:o})},onReorderQuestions:e=>{ew.mutate({contextType:a,contextId:o,questionPositions:e})},onMoveGroupToParent:ez,onMoveQuestionBetweenGroups:eP,isEditing:$===e.id,onStartEditing:eE,onSaveGroupName:eD,onCancelEditing:eA,editingName:J,onEditingNameChange:W,selectionMode:Y})},"group-".concat(e.id))),eg.sort((e,t)=>e.position-t.position).map(e=>(0,l.jsx)("div",{className:"mb-4",children:(0,l.jsx)(m,{question:e,onEdit:()=>{G(e),"table"===e.inputType?w(!0):f(!0)},onDelete:()=>{G(e),F(!0)},onDuplicate:()=>{G(e),ej.mutate({id:e.id,contextType:a,contextId:o})},selectionMode:Y,isSelected:V.includes(e.id),onToggleSelect:()=>ek(e.id)})},"question-".concat(e.id)))]})})})})}),s.length>0&&(0,l.jsx)("div",{className:"sticky bottom-0 p-4 flex justify-center",children:(0,l.jsxs)("button",{className:"btn-primary  max-w-md flex items-center justify-center gap-2 ".concat(!u&&"text-gray-400 cursor-not-allowed"),onClick:()=>x(!0),disabled:!u,children:[(0,l.jsx)(y.A,{size:16}),"Add Question"]})})]})}},50408:(e,t,s)=>{"use strict";s.d(t,{l:()=>i});var l=s(95155),r=s(66474),n=s(12115);let i=e=>{let{id:t,options:s,value:i,onChange:a}=e,[o,d]=(0,n.useState)(!1),c=(0,n.useRef)(null),u=(0,n.useRef)([]),m=(0,n.useRef)(null);(0,n.useEffect)(()=>{let e=e=>{m.current&&!m.current.contains(e.target)&&d(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let p=e=>{if(!o)return;let t=e.key.toLowerCase();if(t.match(/[a-z]/)){let e=s.findIndex(e=>e.toLowerCase().startsWith(t));if(-1!==e&&u.current[e]){var l;null==(l=u.current[e])||l.scrollIntoView({behavior:"auto",block:"nearest"})}}};return(0,n.useEffect)(()=>(document.addEventListener("keydown",p),()=>{document.removeEventListener("keydown",p)}),[o,s]),(0,l.jsxs)("div",{className:"relative",ref:m,children:[(0,l.jsxs)("button",{id:t,type:"button",className:"px-4 py-2 flex items-center justify-between rounded-md border border-neutral-400 focus:border-primary-500 duration-300 w-full text-left cursor-pointer",onClick:()=>{d(!o)},children:[(0,l.jsx)("span",{children:i||"Select an option"}),(0,l.jsx)(r.A,{})]}),o&&(0,l.jsx)("ul",{className:"absolute z-10 max-h-[180px] overflow-auto border border-neutral-400 rounded-md bg-neutral-100 w-full mt-1 flex flex-col",ref:c,children:s.map((e,t)=>(0,l.jsx)("li",{ref:e=>{u.current[t]=e},className:"cursor-pointer bg-neutral-100 hover:bg-neutral-200 px-4 py-2",onClick:()=>{a(e),d(!1)},children:e},t))})]})}},59250:(e,t,s)=>{"use strict";s.d(t,{V:()=>j});var l=s(95155),r=s(12115),n=s(97168),i=s(82714),a=s(89852),o=s(99474),d=s(95139),c=s(55747),u=s(69074),m=s(14186),p=s(42355),x=s(54416),h=s(66474),b=s(13052),f=s(16112),g=s(3587),v=s(13388);function j(e){let{questions:t,questionGroups:s=[],contextType:j="project",onClose:y,hashedId:N}=e,[w,q]=(0,r.useState)({}),[C,I]=(0,r.useState)({}),[k,S]=(0,r.useState)([]),[E,A]=(0,r.useState)([]),[Q,T]=(0,r.useState)({});(0,r.useEffect)(()=>{let e={};t.forEach(t=>{e[t.id]="selectmany"===t.inputType?[]:""}),q(e)},[t]),(0,r.useEffect)(()=>{if(t){let e=(0,g.UL)(t,w);S(e),A((0,g.Tr)(t,w));let s=(0,g.OD)(w,e);Object.keys(s).length!==Object.keys(w).length&&q(s)}},[t,w]),(0,r.useEffect)(()=>{let e={};s.forEach(t=>{e[t.id]=!0}),T(e)},[s]);let G=s.reduce((e,s)=>(e[s.id]=t.filter(e=>e.questionGroupId===s.id),e),{}),D=t.filter(e=>null===e.questionGroupId||void 0===e.questionGroupId),F=r.useMemo(()=>{let e=[];return"project"===j&&s.forEach(s=>{let l=t.filter(e=>e.questionGroupId===s.id),r=l.length>0?Math.min(...l.map(e=>e.position)):s.order;e.push({type:"group",data:s,order:r,originalPosition:r})}),("project"===j?D:t).forEach(t=>{e.push({type:"question",data:t,order:t.position,originalPosition:t.position})}),e.sort((e,t)=>e.order===t.order?(e.originalPosition||e.order)-(t.originalPosition||t.order):e.order-t.order)},[s,D,t,j]),z=e=>{T(t=>({...t,[e]:!t[e]}))},P=(e,t)=>{q(s=>({...s,[e]:t})),I(t=>({...t,[e]:""}))},R=e=>{var t,s,r,n;let p=null!=(t=w[e.id])?t:"selectmany"===e.inputType?[]:"";switch(e.inputType){case"text":if(null==(s=e.hint)?void 0:s.includes("multiline"))return(0,l.jsx)(o.T,{value:p,onChange:t=>P(e.id,t.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});return(0,l.jsx)(a.p,{value:p,onChange:t=>P(e.id,t.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"number":return(0,l.jsx)(a.p,{type:"number",value:p,onChange:t=>P(e.id,t.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"decimal":return(0,l.jsx)(a.p,{type:"number",step:"any",value:p,onChange:t=>P(e.id,t.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"selectone":return(0,l.jsx)(c.z,{value:p,onValueChange:t=>P(e.id,t),required:e.isRequired,children:(0,l.jsx)("div",{className:"space-y-2",children:null==(r=e.questionOptions)?void 0:r.map((e,t)=>(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(c.C,{value:e.label,id:"option-".concat(e.id)}),(0,l.jsx)(i.J,{htmlFor:"option-".concat(e.id),className:"cursor-pointer",children:e.label}),e.sublabel&&(0,l.jsx)("p",{className:"text-sm text-neutral-700 ml-4",children:"(".concat(e.sublabel,")")})]},t))})});case"selectmany":return(0,l.jsx)("div",{className:"space-y-2",children:null==(n=e.questionOptions)?void 0:n.map(t=>(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(d.S,{className:"w-5 h-5 border border-neutral-500",id:"option-".concat(t.id),checked:(p||[]).includes(t.label),onCheckedChange:s=>{let l=p||[],r=s?[...l,t.label]:l.filter(e=>e!==t.label);P(e.id,r)}}),(0,l.jsxs)(i.J,{htmlFor:"option-".concat(t.id),className:"cursor-pointer",children:[t.label," ",t.sublabel]})]},t.id))});case"date":return(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(a.p,{type:"date",value:p,onChange:t=>P(e.id,t.target.value),placeholder:e.hint||"Select date",required:e.isRequired}),(0,l.jsx)(u.A,{className:"absolute top-3 right-3 h-4 w-4 text-muted-foreground pointer-events-none"})]});case"dateandtime":return(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(a.p,{type:"time",value:p,onChange:t=>P(e.id,t.target.value),placeholder:e.hint||"Select time",required:e.isRequired}),(0,l.jsx)(m.A,{className:"absolute top-3 right-3 h-4 w-4 text-muted-foreground pointer-events-none"})]});case"table":return(0,l.jsx)(f.N,{questionId:e.id,value:p,onChange:t=>P(e.id,t),required:e.isRequired,tableLabel:e.label});default:return null}};return(0,l.jsxs)("div",{className:"bg-neutral-100 dark:bg-neutral-800 rounded-md shadow-sm border border-neutral-500 dark:border-neutral-700",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700",children:[(0,l.jsx)(n.$,{variant:"ghost",size:"icon",onClick:y,children:(0,l.jsx)(p.A,{className:"h-5 w-5"})}),(0,l.jsx)("h2",{className:"text-lg font-semibold",children:"Form Preview"}),(0,l.jsx)(n.$,{className:"cursor-pointer hover:bg-neutral-200",variant:"ghost",size:"icon",onClick:y,children:(0,l.jsx)(x.A,{className:"h-5 w-5"})})]}),(0,l.jsx)("div",{className:"p-4 md:p-6",children:(0,l.jsxs)("div",{className:"space-y-6",children:[0===t.length?(0,l.jsx)("div",{className:"text-center py-12",children:(0,l.jsx)("p",{className:"text-muted-foreground",children:"This form has no questions yet."})}):F.map(e=>{if("group"===e.type){let t=e.data,s=G[t.id]||[],r=s.filter(e=>k.some(t=>t.id===e.id)),n=Q[t.id];return 0===r.length?null:(0,l.jsxs)("div",{className:"border border-neutral-500 dark:border-neutral-600 rounded-lg bg-neutral-100 dark:bg-neutral-800 overflow-hidden",children:[(0,l.jsx)("div",{className:"flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700 cursor-pointer hover:bg-neutral-200 dark:hover:bg-neutral-700",onClick:()=>z(t.id),children:(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[n?(0,l.jsx)(h.A,{className:"h-5 w-5 text-neutral-500"}):(0,l.jsx)(b.A,{className:"h-5 w-5 text-neutral-500"}),(0,l.jsx)("h3",{className:"text-lg font-semibold  dark:text-neutral-100",children:t.title}),(0,l.jsxs)("span",{className:"text-sm text-neutral-700 dark:text-neutral-400",children:["(",r.length," visible question",1!==r.length?"s":"",")"]})]})}),n&&(0,l.jsx)("div",{className:"p-4 space-y-4",children:E.filter(e=>s.some(t=>t.id===e.question.id)).map(e=>(0,l.jsx)(v.A,{questionGroup:e,renderQuestionInput:R,errors:C,className:""},e.question.id))})]},"group-".concat(t.id))}}),D.length>0&&(0,l.jsx)("div",{className:"space-y-4",children:E.filter(e=>D.some(t=>t.id===e.question.id)).map(e=>(0,l.jsx)(v.A,{questionGroup:e,renderQuestionInput:R,errors:C,className:""},e.question.id))}),"project"!==j&&(0,l.jsx)("div",{className:"space-y-4",children:E.map(e=>(0,l.jsx)(v.A,{questionGroup:e,renderQuestionInput:R,errors:C,className:""},e.question.id))}),0===t.length&&(0,l.jsx)("div",{className:"text-center py-12",children:(0,l.jsx)("p",{className:"text-muted-foreground",children:"This form has no questions yet."})}),t.length>0&&0===k.length&&(0,l.jsx)("div",{className:"text-center py-12",children:(0,l.jsx)("p",{className:"text-muted-foreground",children:"No questions are currently visible. Please check your form configuration."})})]})})]})}},63642:(e,t,s)=>{"use strict";s.d(t,{R:()=>n});var l=s(95155);s(12115);var r=s(13163);let n=e=>{let{showModal:t,onClose:s,onConfirm:n,title:i,description:a,confirmButtonText:o,cancelButtonText:d,confirmButtonClass:c,children:u}=e;return(0,l.jsxs)(r.A,{isOpen:t,onClose:s,className:"p-6 rounded-md max-w-xl",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:i}),(0,l.jsx)("div",{className:"text-neutral-700 mt-2",children:a}),u&&(0,l.jsx)("div",{className:"mt-6 space-y-4",children:u}),(0,l.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,l.jsx)("button",{className:"btn-outline",onClick:s,type:"button",children:d||"Cancel"}),(0,l.jsx)("button",{className:"font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ".concat(c),onClick:n,type:"button",children:o})]})]})}},64368:(e,t,s)=>{"use strict";s.d(t,{H:()=>l});let l=(e,t)=>{let s=Object.entries(t).find(t=>{let[s,l]=t;return l===e});return s?s[0]:null}},83686:()=>{},97168:(e,t,s)=>{"use strict";s.d(t,{$:()=>o});var l=s(95155);s(12115);var r=s(99708),n=s(74466),i=s(53999);let a=(0,n.F)("inline-flex items-center justify-center gap-2 neutral-100space-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-neutral-100 shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:s,size:n,asChild:o=!1,...d}=e,c=o?r.DX:"button";return(0,l.jsx)(c,{"data-slot":"button",className:(0,i.cn)(a({variant:s,size:n,className:t})),...d})}}}]);