"use strict";exports.id=9751,exports.ids=[9751],exports.modules={20174:(e,t,s)=>{s.d(t,{F:()=>a});var l=s(60687),i=s(16189),r=s(85814),n=s.n(r);s(43210);let a=({items:e})=>{let t=(0,i.usePathname)(),s=e=>t.startsWith(e);return(0,l.jsx)("div",{className:"border-y border-neutral-400 rounded-md bg-primary-500 my-4 shadow-md",children:(0,l.jsx)("div",{className:"flex items-center",children:e.map(e=>e.disabled?(0,l.jsxs)("div",{className:"flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-gray-400 cursor-not-allowed",children:[e.icon,e.label]},e.route):(0,l.jsxs)(n(),{href:e.route,className:`flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-neutral-100 ${s(e.route)?"border-neutral-100":"border-transparent hover:border-neutral-400"}`,children:[e.icon,e.label]},e.route))})})}},38017:(e,t,s)=>{s.d(t,{V:()=>v});var l=s(60687),i=s(43210),r=s.n(i),n=s(24934),a=s(39390),o=s(68988),d=s(15616),c=s(93437),u=s(40347),m=s(40228),p=s(48730),x=s(47033),h=s(11860),b=s(78272),g=s(14952),j=s(13784);s(24527);var f=s(69396);function v({questions:e,questionGroups:t=[],contextType:s="project",onClose:v,hashedId:y}){let[N,q]=(0,i.useState)({}),[w,C]=(0,i.useState)({}),[I,S]=(0,i.useState)([]),[E,k]=(0,i.useState)([]),[A,Q]=(0,i.useState)({}),G=t.reduce((t,s)=>(t[s.id]=e.filter(e=>e.questionGroupId===s.id),t),{}),T=e.filter(e=>null===e.questionGroupId||void 0===e.questionGroupId),D=r().useMemo(()=>{let l=[];return"project"===s&&t.forEach(t=>{let s=e.filter(e=>e.questionGroupId===t.id),i=s.length>0?Math.min(...s.map(e=>e.position)):t.order;l.push({type:"group",data:t,order:i,originalPosition:i})}),("project"===s?T:e).forEach(e=>{l.push({type:"question",data:e,order:e.position,originalPosition:e.position})}),l.sort((e,t)=>e.order===t.order?(e.originalPosition||e.order)-(t.originalPosition||t.order):e.order-t.order)},[t,T,e,s]),F=e=>{Q(t=>({...t,[e]:!t[e]}))},$=(e,t)=>{q(s=>({...s,[e]:t})),C(t=>({...t,[e]:""}))},z=e=>{let t=N[e.id]??("selectmany"===e.inputType?[]:"");switch(e.inputType){case"text":if(e.hint?.includes("multiline"))return(0,l.jsx)(d.T,{value:t,onChange:t=>$(e.id,t.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});return(0,l.jsx)(o.p,{value:t,onChange:t=>$(e.id,t.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"number":return(0,l.jsx)(o.p,{type:"number",value:t,onChange:t=>$(e.id,t.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"decimal":return(0,l.jsx)(o.p,{type:"number",step:"any",value:t,onChange:t=>$(e.id,t.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"selectone":return(0,l.jsx)(u.z,{value:t,onValueChange:t=>$(e.id,t),required:e.isRequired,children:(0,l.jsx)("div",{className:"space-y-2",children:e.questionOptions?.map((e,t)=>(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(u.C,{value:e.label,id:`option-${e.id}`}),(0,l.jsx)(a.J,{htmlFor:`option-${e.id}`,className:"cursor-pointer",children:e.label}),e.sublabel&&(0,l.jsx)("p",{className:"text-sm text-neutral-700 ml-4",children:`(${e.sublabel})`})]},t))})});case"selectmany":return(0,l.jsx)("div",{className:"space-y-2",children:e.questionOptions?.map(s=>(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(c.S,{className:"w-5 h-5 border border-neutral-500",id:`option-${s.id}`,checked:(t||[]).includes(s.label),onCheckedChange:l=>{let i=t||[],r=l?[...i,s.label]:i.filter(e=>e!==s.label);$(e.id,r)}}),(0,l.jsxs)(a.J,{htmlFor:`option-${s.id}`,className:"cursor-pointer",children:[s.label," ",s.sublabel]})]},s.id))});case"date":return(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(o.p,{type:"date",value:t,onChange:t=>$(e.id,t.target.value),placeholder:e.hint||"Select date",required:e.isRequired}),(0,l.jsx)(m.A,{className:"absolute top-3 right-3 h-4 w-4 text-muted-foreground pointer-events-none"})]});case"dateandtime":return(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(o.p,{type:"time",value:t,onChange:t=>$(e.id,t.target.value),placeholder:e.hint||"Select time",required:e.isRequired}),(0,l.jsx)(p.A,{className:"absolute top-3 right-3 h-4 w-4 text-muted-foreground pointer-events-none"})]});case"table":return(0,l.jsx)(j.N,{questionId:e.id,value:t,onChange:t=>$(e.id,t),required:e.isRequired,tableLabel:e.label});default:return null}};return(0,l.jsxs)("div",{className:"bg-neutral-100 dark:bg-neutral-800 rounded-md shadow-sm border border-neutral-500 dark:border-neutral-700",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700",children:[(0,l.jsx)(n.$,{variant:"ghost",size:"icon",onClick:v,children:(0,l.jsx)(x.A,{className:"h-5 w-5"})}),(0,l.jsx)("h2",{className:"text-lg font-semibold",children:"Form Preview"}),(0,l.jsx)(n.$,{className:"cursor-pointer hover:bg-neutral-200",variant:"ghost",size:"icon",onClick:v,children:(0,l.jsx)(h.A,{className:"h-5 w-5"})})]}),(0,l.jsx)("div",{className:"p-4 md:p-6",children:(0,l.jsxs)("div",{className:"space-y-6",children:[0===e.length?(0,l.jsx)("div",{className:"text-center py-12",children:(0,l.jsx)("p",{className:"text-muted-foreground",children:"This form has no questions yet."})}):D.map(e=>{if("group"===e.type){let t=e.data,s=G[t.id]||[],i=s.filter(e=>I.some(t=>t.id===e.id)),r=A[t.id];return 0===i.length?null:(0,l.jsxs)("div",{className:"border border-neutral-500 dark:border-neutral-600 rounded-lg bg-neutral-100 dark:bg-neutral-800 overflow-hidden",children:[(0,l.jsx)("div",{className:"flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700 cursor-pointer hover:bg-neutral-200 dark:hover:bg-neutral-700",onClick:()=>F(t.id),children:(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[r?(0,l.jsx)(b.A,{className:"h-5 w-5 text-neutral-500"}):(0,l.jsx)(g.A,{className:"h-5 w-5 text-neutral-500"}),(0,l.jsx)("h3",{className:"text-lg font-semibold  dark:text-neutral-100",children:t.title}),(0,l.jsxs)("span",{className:"text-sm text-neutral-700 dark:text-neutral-400",children:["(",i.length," visible question",1!==i.length?"s":"",")"]})]})}),r&&(0,l.jsx)("div",{className:"p-4 space-y-4",children:E.filter(e=>s.some(t=>t.id===e.question.id)).map(e=>(0,l.jsx)(f.A,{questionGroup:e,renderQuestionInput:z,errors:w,className:""},e.question.id))})]},`group-${t.id}`)}}),T.length>0&&(0,l.jsx)("div",{className:"space-y-4",children:E.filter(e=>T.some(t=>t.id===e.question.id)).map(e=>(0,l.jsx)(f.A,{questionGroup:e,renderQuestionInput:z,errors:w,className:""},e.question.id))}),"project"!==s&&(0,l.jsx)("div",{className:"space-y-4",children:E.map(e=>(0,l.jsx)(f.A,{questionGroup:e,renderQuestionInput:z,errors:w,className:""},e.question.id))}),0===e.length&&(0,l.jsx)("div",{className:"text-center py-12",children:(0,l.jsx)("p",{className:"text-muted-foreground",children:"This form has no questions yet."})}),e.length>0&&0===I.length&&(0,l.jsx)("div",{className:"text-center py-12",children:(0,l.jsx)("p",{className:"text-muted-foreground",children:"No questions are currently visible. Please check your form configuration."})})]})})]})}},73678:(e,t,s)=>{s.d(t,{R:()=>r});var l=s(60687);s(43210);var i=s(38587);let r=({showModal:e,onClose:t,onConfirm:s,title:r,description:n,confirmButtonText:a,cancelButtonText:o,confirmButtonClass:d,children:c})=>(0,l.jsxs)(i.A,{isOpen:e,onClose:t,className:"p-6 rounded-md max-w-xl",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:r}),(0,l.jsx)("div",{className:"text-neutral-700 mt-2",children:n}),c&&(0,l.jsx)("div",{className:"mt-6 space-y-4",children:c}),(0,l.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,l.jsx)("button",{className:"btn-outline",onClick:t,type:"button",children:o||"Cancel"}),(0,l.jsx)("button",{className:`font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ${d}`,onClick:s,type:"button",children:a})]})]})},88678:(e,t,s)=>{s.d(t,{o:()=>ex});var l=s(60687),i=s(43210),r=s.n(i),n=s(57601),a=s(51358),o=s(62478),d=s(81381),c=s(17804),u=s(96362),m=s(57175);let p=({question:e,onEdit:t,onDelete:s,onDuplicate:i,isSelected:r=!1,onToggleSelect:n,selectionMode:p=!1})=>{let{attributes:x,listeners:h,setNodeRef:b,transform:g,transition:j,isDragging:f}=(0,a.gl)({id:e.id,data:{type:"question",questionId:e.id,questionGroupId:"questionGroupId"in e?e.questionGroupId:null}}),v={transform:o.Ks.Transform.toString(g),transition:j,opacity:f?.5:1};return(0,l.jsx)("div",{ref:b,style:v,className:"border border-neutral-400 rounded-md bg-card shadow-sm",children:(0,l.jsxs)("div",{className:"flex items-center p-4",children:[p&&(0,l.jsx)("div",{className:"mr-2",children:(0,l.jsx)("input",{type:"checkbox",checked:r,onChange:()=>n&&n(),className:"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"})}),(0,l.jsx)("div",{...x,...h,className:"cursor-move mr-3 hover:text-primary",children:(0,l.jsx)(d.A,{className:"h-5 w-5"})}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("h3",{className:"text-xl font-semibold",children:e.label||(0,l.jsx)("span",{className:"text-muted-foreground italic",children:"Empty question"})}),e.hint&&(0,l.jsx)("p",{className:"text-sm sub-text mt-1",children:e.hint})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,l.jsx)("button",{onClick:e=>{e.stopPropagation(),i()},title:"Duplicate",className:"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors",children:(0,l.jsx)(c.A,{size:16})}),(0,l.jsx)("button",{onClick:e=>{e.stopPropagation(),s()},title:"Delete",className:"cursor-pointer p-2 rounded-full hover:bg-destructive/10 text-neutral-700 hover:text-destructive transition-colors",children:(0,l.jsx)(u.A,{size:16})}),(0,l.jsx)("button",{onClick:e=>{e.stopPropagation(),t()},title:"Edit",className:"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors",children:(0,l.jsx)(m.A,{size:16})})]})]})})};var x=s(78272),h=s(14952),b=s(96474),g=s(80675),j=s(63143);let f=({id:e,title:t,questions:s,subGroups:r=[],parentGroupId:c,level:m=0,onEditGroup:v,onDeleteGroup:y,onAddQuestionToGroup:N,onCreateSubGroup:q,onEditQuestion:w,onDeleteQuestion:C,onDuplicateQuestion:I,onReorderQuestions:S,onMoveGroupToParent:E,onMoveQuestionBetweenGroups:k,isEditing:A=!1,onStartEditing:Q,onSaveGroupName:G,onCancelEditing:T,editingName:D="",onEditingNameChange:F,selectionMode:$=!1,isDragging:z=!1})=>{let[P,O]=(0,i.useState)(!0),{attributes:M,listeners:R,setNodeRef:K,transform:L,transition:B,isDragging:V}=(0,a.gl)({id:`group-${e}`,data:{type:"group",groupId:e,parentGroupId:c}}),{setNodeRef:H,isOver:U}=(0,n.zM)({id:`group-drop-${e}`,data:{type:"group",groupId:e,accepts:["group","question"]}}),J={transform:o.Ks.Transform.toString(L),transition:B,opacity:V?.5:1},W=(0,n.FR)((0,n.MS)(n.AN,{activationConstraint:{distance:8}}),(0,n.MS)(n.uN));return(0,l.jsxs)("div",{ref:e=>{K(e),H(e)},style:{...J,marginLeft:`${20*m}px`},className:`border border-neutral-400 rounded-md bg-card shadow-sm mb-4 ${U?"ring-2 ring-primary-500 ring-opacity-50":""} ${V?"opacity-50":""}`,children:[(0,l.jsxs)("div",{className:"flex items-center p-4 bg-neutral-100 border-b border-neutral-300 rounded-t-md",children:[(0,l.jsx)("div",{...M,...R,className:"cursor-move mr-2 hover:text-primary-500",title:"Drag to reorder or nest group",children:(0,l.jsx)(d.A,{className:"h-5 w-5"})}),(0,l.jsx)("button",{onClick:()=>O(!P),className:"mr-2 text-neutral-700 hover:text-primary-500 transition-colors","aria-label":P?"Collapse group":"Expand group",children:P?(0,l.jsx)(x.A,{className:"h-5 w-5"}):(0,l.jsx)(h.A,{className:"h-5 w-5"})}),A?(0,l.jsx)("div",{className:"flex-1 mr-4",children:(0,l.jsx)("input",{type:"text",value:D,onChange:e=>F&&F(e.target.value),className:"w-full p-2 border border-gray-300 rounded",autoFocus:!0,onKeyDown:t=>{"Enter"===t.key?G&&G(e):"Escape"===t.key&&T&&T()},placeholder:"Enter group name"})}):(0,l.jsx)("h3",{className:"flex-1 font-medium text-lg cursor-pointer hover:text-primary-500",onClick:()=>Q&&Q(e,t),title:"Click to edit group name",children:t}),(0,l.jsx)("div",{className:"flex items-center space-x-3",children:A?(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)("button",{onClick:()=>T&&T(),title:"Cancel Editing",className:"cursor-pointer px-3 py-1 rounded btn-outline",children:"Cancel"}),(0,l.jsx)("button",{onClick:()=>G&&G(e),title:"Save Group Name",className:"cursor-pointer px-3 py-1 rounded btn-primary",children:"Save"})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("button",{onClick:()=>N(e),title:"Add Question to Group",className:"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors",children:(0,l.jsx)(b.A,{size:16})}),q&&(0,l.jsx)("button",{onClick:()=>q(e),title:"Create Sub Group",className:"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors",children:(0,l.jsx)(g.A,{size:16})}),(0,l.jsx)("button",{onClick:()=>Q&&Q(e,t),title:"Edit Group Name",className:"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors",children:(0,l.jsx)(j.A,{size:16})}),(0,l.jsx)("button",{onClick:()=>y(e),title:"Delete Group",className:"cursor-pointer p-2 rounded-full hover:bg-destructive/10 text-neutral-700 hover:text-destructive transition-colors",children:(0,l.jsx)(u.A,{size:16})})]})})]}),P&&(0,l.jsxs)("div",{className:"p-4 space-y-4",children:[r&&r.length>0&&(0,l.jsx)("div",{className:"space-y-4",children:r.sort((e,t)=>e.order-t.order).map(t=>(0,l.jsx)(f,{id:t.id,title:t.title,questions:t.question||[],subGroups:t.subGroups,parentGroupId:e,level:m+1,onEditGroup:v,onDeleteGroup:y,onAddQuestionToGroup:N,onCreateSubGroup:q,onEditQuestion:w,onDeleteQuestion:C,onDuplicateQuestion:I,onReorderQuestions:S,onMoveGroupToParent:E,onMoveQuestionBetweenGroups:k,isEditing:A,onStartEditing:Q,onSaveGroupName:G,onCancelEditing:T,editingName:D,onEditingNameChange:F,selectionMode:$},t.id))}),s.length>0?(0,l.jsx)(n.Mp,{sensors:W,collisionDetection:n.fp,onDragEnd:e=>{let{active:t,over:l}=e;if(!l||t.id===l.id)return;let i=t.data.current,r=l.data.current;if(i?.type==="question"&&S){let e=[...s].sort((e,t)=>e.position-t.position),i=e.findIndex(e=>e.id===t.id),r=e.findIndex(e=>e.id===l.id);if(-1===i||-1===r)return;S((0,a.be)(e,i,r).map((e,t)=>({id:Number(e.id),position:t+1})))}if(i?.type==="question"&&r?.type==="group"&&k){let e=Number(t.id),s=i.questionGroupId,l=r.groupId;s!==l&&k(e,s,l)}if(i?.type==="group"&&r?.type==="group"&&E){let e=i.groupId,t=r.groupId;e!==t&&E(e,t)}},children:(0,l.jsx)(a.gB,{items:s.map(e=>e.id),strategy:a._G,children:s.sort((e,t)=>e.position-t.position).map(e=>(0,l.jsx)("div",{className:"mb-4",children:(0,l.jsx)(p,{question:{...e},onEdit:()=>w(e),onDelete:()=>C(e),onDuplicate:()=>I(e),selectionMode:$,isSelected:!1,onToggleSelect:()=>{}})},e.id))})}):r&&0===r.length?(0,l.jsx)("div",{className:"text-center py-4 text-neutral-500",children:"No questions in this group. Click the + button to add questions."}):null]})]})};var v=s(13861),y=s(82080),N=s(1303),q=s(38587),w=s(27605),C=s(68292),I=s(40480);let S={text:"Text",number:"Number",decimal:"Decimal",selectone:"Select one",selectmany:"Select many",date:"Date",dateandtime:"Date and time",table:"Table"};Object.keys(S);var E=s(43782),k=s(8693),A=s(54050),Q=s(75531),G=s(29494);let T=({contextType:e,contextId:t,value:s,onChange:i,currentQuestionId:r,placeholder:n="Select next question (optional)"})=>{let{data:a=[],isLoading:o,error:d}=(0,G.I)({queryKey:"project"===e?["questions",t]:"template"===e?["templateQuestions",t]:["questionBlockQuestions",t],queryFn:()=>"project"===e?(0,Q.K4)({projectId:t}):"template"===e?(0,Q.ej)({templateId:t}):"questionBlock"===e?(0,Q.dI)():[],enabled:!!t}),c=a.filter(e=>e.id!==r),u=c.find(e=>e.id===s);return(0,l.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,l.jsxs)("select",{value:s||"",onChange:e=>{let t=e.target.value;i(t?parseInt(t):null)},className:"input-field text-sm",disabled:o,children:[(0,l.jsx)("option",{value:"",children:o?"Loading questions...":n}),c.map(e=>(0,l.jsx)("option",{value:e.id,children:e.label||`Question ${e.id}`},e.id))]}),u&&(0,l.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Type: ",u.inputType]})]})},D=({contextType:e,contextId:t,currentQuestionId:s,inputType:r})=>{let{control:n,register:a,formState:{errors:o},setValue:d,watch:c}=(0,w.xW)(),{fields:m,append:p,remove:x}=(0,w.jz)({control:n,name:"questionOptions"});(0,i.useEffect)(()=>{0===m.length&&p({label:"",sublabel:"",code:"",nextQuestionId:null})},[m,p]);let h="selectone"===r||"selectmany"===r;return(0,l.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,l.jsx)("label",{className:"label-text",children:"Options"}),(0,l.jsxs)("div",{className:"flex flex-col gap-2",children:[m.map((i,r)=>(0,l.jsxs)("div",{className:"border  border-gray-400 rounded-lg p-3 space-y-2",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("input",{...a(`questionOptions.${r}.label`),placeholder:`Option ${r+1}`,className:"input-field flex-1 min-w-[150px]"}),(0,l.jsx)("input",{...a(`questionOptions.${r}.sublabel`),placeholder:"Sub Options",className:"input-field flex-1 min-w-[150px]"}),(0,l.jsx)("input",{...a(`questionOptions.${r}.code`),placeholder:"Code",className:"w-28 input-field"}),(0,l.jsx)("button",{type:"button",onClick:()=>x(r),className:"p-2 rounded-full hover:bg-red-500/10 text-neutral-700 hover:text-red-500 transition-colors cursor-pointer duration-300",children:(0,l.jsx)(u.A,{size:16})})]}),h&&e&&t&&(0,l.jsxs)("div",{className:"ml-2",children:[(0,l.jsx)("label",{className:"text-xs text-gray-600 mb-1 block",children:"Next Question (when this option is selected):"}),(0,l.jsx)(T,{contextType:e,contextId:t,currentQuestionId:s,value:c(`questionOptions.${r}.nextQuestionId`),onChange:e=>{d(`questionOptions.${r}.nextQuestionId`,e)},placeholder:"No follow-up question"})]})]},r)),o.questionOptions&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:`${o.questionOptions.root?.message}`}),(0,l.jsxs)("button",{type:"button",onClick:()=>p({label:"",sublabel:"",code:"",nextQuestionId:null}),className:"btn-outline mt-2 flex items-center justify-center gap-2",children:[(0,l.jsx)(b.A,{size:16}),"Add Option"]})]})]})};var F=s(63442);let $=e=>["selectone","selectmany"].includes(e);var z=s(45880);let P=Object.keys(S),O=z.z.object({label:z.z.string().min(1,"Question name is required"),inputType:z.z.enum(P),hint:z.z.string().optional(),placeholder:z.z.string().optional(),questionOptions:z.z.array(z.z.object({label:z.z.string(),sublabel:z.z.string().optional(),code:z.z.string(),nextQuestionId:z.z.number().optional().nullable()})).optional()}).superRefine((e,t)=>{$(e.inputType)});var M=s(86429);let R=()=>(0,l.jsx)("div",{className:"fixed top-0 left-0 h-screen w-screen bg-neutral-900/20 z-50 flex items-center justify-center",onClick:e=>{e.stopPropagation()},children:(0,l.jsx)(M.A,{})});var K=s(24934),L=s(68988),B=s(39390),V=s(12597),H=s(15695),U=s(96752),J=s(96241);function W({projectId:e,onTableCreated:t,isInModal:s=!1,isEditMode:n=!1,existingTableData:a}){let{toast:o}=function(){let[e,t]=(0,i.useState)([]);return{toast:e=>{t(t=>[...t,e]),setTimeout(()=>{t(t=>t.filter(t=>t!==e))},3e3)},toasts:e}}(),[d,c]=(0,i.useState)(a?.label||""),[m,p]=(0,i.useState)(()=>{if(a?.tableColumns){let e=[];return a.tableColumns.forEach(t=>{let s={id:`col-${t.id}`,columnName:t.columnName,level:0,parentColumnId:void 0};e.push(s),t.childColumns&&t.childColumns.length>0&&(console.error(`Processing ${t.childColumns.length} child columns for parent "${t.columnName}"`),t.childColumns.forEach(s=>{let l={id:`col-${s.id}`,columnName:s.columnName,level:1,parentId:`col-${t.id}`,parentColumnId:t.id};e.push(l)}))}),e.length>0?e:[{id:"col-1",columnName:"",level:0}]}return[{id:"col-1",columnName:"",level:0}]}),[x,h]=(0,i.useState)(()=>a?.tableRows&&a.tableRows.length>0?[...a.tableRows].sort((e,t)=>e.id-t.id).map(e=>({id:e.id,rowsName:e.rowsName})):[{rowsName:"Row 1"}]),[g,j]=(0,i.useState)(!1),[f,y]=(0,i.useState)(!0),N=()=>`col-${Date.now()}-${Math.floor(1e3*Math.random())}`,q=()=>{p([...m,{id:N(),columnName:"",level:0}])},w=e=>{let t=C(e);if(!t)return;if(t.level>0)return void o({title:"Cannot add child column",description:"Cannot create more than 2 levels of nested columns (parent → child → grandchild)",variant:"destructive"});if(m.filter(t=>t.parentId===e).length>=2)return void o({title:"Cannot add more child columns",description:`Parent column "${t.columnName||"Unnamed"}" cannot have more than 2 child columns`,variant:"destructive"});let s=e.match(/^col-(\d+)/),l=s?parseInt(s[1],10):void 0,i={id:N(),columnName:"",parentId:e,level:t.level+1,parentColumnId:l},r=[...m],n=I(e);if(-1===n||n===m.findIndex(t=>t.id===e)){let t=m.findIndex(t=>t.id===e);r.splice(t+1,0,i)}else r.splice(n+1,0,i);p(r)},C=e=>m.find(t=>t.id===e),I=e=>{let t=m.findIndex(t=>t.id===e);if(-1===t)return -1;let s=t,l=!1;for(let i=t+1;i<m.length;i++)if(m[i].parentId===e)s=i,l=!0;else if(S(m[i],e))s=i,l=!0;else break;return l?s:t},S=(e,t)=>{if(e.parentId===t)return!0;if(!e.parentId)return!1;let s=C(e.parentId);return!!s&&S(s,t)},E=()=>{let e=x.length+1;h([...x,{rowsName:`Row ${e}`}])},k=e=>{if(m.length<=1)return;let t=new Set([e]);m.forEach(s=>{S(s,e)&&t.add(s.id)});let s=m.filter(e=>!t.has(e.id));0===s.length&&s.push({id:N(),columnName:"",level:0}),p(s)},A=e=>{let t=[...x];t.splice(e,1),h(t)},Q=(e,t)=>{p(m.map(s=>s.id===e?{...s,columnName:t}:s))},G=(e,t)=>{let s=[...x];s[e]={...s[e],rowsName:t},h(s);let l=document.querySelectorAll(".row-input");l&&l[e]&&(t.trim()?l[e].classList.remove("border-red-500"):l[e].classList.add("border-red-500"))},T=()=>{let e=n||a?.id,t=new Map;e&&a?.tableColumns&&a.tableColumns.forEach(e=>{let s=`col-${e.id}`;t.set(s,e.id)});let s=new Map;m.forEach(e=>{e.parentId&&(s.has(e.parentId)||s.set(e.parentId,[]),s.get(e.parentId)?.push(e.id))}),e&&a?.tableColumns&&m.forEach(e=>{if(e.columnName.trim()){let s=e.id.match(/^col-(\d+)/);if(s&&s[1]){let l=parseInt(s[1],10);a.tableColumns.find(e=>e.id===l)&&t.set(e.id,l)}}});let l=Math.max(...Array.from(t.values(),e=>e||0),...a?.tableColumns?.map(e=>e.id)||[0],0)+1;m.forEach(e=>{e.columnName.trim()&&!t.has(e.id)&&t.set(e.id,l++)});let i=[],r=new Map,d=Math.max(...m.map(e=>e.level||0),0);for(let s=0;s<=d;s++)m.filter(e=>e.level===s&&e.columnName.trim()).forEach(s=>{let l=t.get(s.id),n={columnName:s.columnName.trim()};if(e&&l&&(n.id=l),s.parentId)if(e){m.find(e=>e.id===s.parentId);let e=t.get(s.parentId);e?n.parentColumnId=e:(console.warn(`Could not find parent DB ID for column "${s.columnName}" (parentId: ${s.parentId})`),o({title:"Warning",description:`Column "${s.columnName}" had a missing parent reference and was converted to a top-level column.`,variant:"destructive"}))}else{let e=r.get(s.parentId);void 0!==e?n.parentColumnId=e+1:(console.warn(`Could not find parent position for column "${s.columnName}" (parentId: ${s.parentId})`),o({title:"Warning",description:`Column "${s.columnName}" had a missing parent reference and was converted to a top-level column.`,variant:"destructive"}))}let a=i.length;i.push(n),r.set(s.id,a)});let c=i.filter(t=>{if(void 0===t.parentColumnId)return!1;if(e){if(t.parentColumnId<=0)return!0}else{if(t.parentColumnId<=0||t.parentColumnId>i.length)return!0;let e=i[t.parentColumnId-1];if(e&&void 0!==e.parentColumnId)return!0}return!1});return c.length>0&&(console.error("Found invalid parent column references:",c),c.forEach(e=>{e.parentColumnId=void 0}),o({title:"Warning",description:`Fixed ${c.length} invalid column relationships. Some child columns were converted to top-level columns.`,variant:"destructive"})),i},D=async s=>{if(s&&s.preventDefault(),!d.trim())return void o({title:"Error",description:"Please enter a table label",variant:"destructive"});let l=m.filter(e=>e.columnName.trim()),i=x.filter(e=>e.rowsName.trim()),r=document.querySelectorAll(".row-input");if(x.forEach((e,t)=>{r&&r[t]&&(e.rowsName.trim()?r[t].classList.remove("border-red-500"):r[t].classList.add("border-red-500"))}),0===l.length)return void o({title:"Error",description:"Please add at least one column with a name",variant:"destructive"});if(0===i.length)return void o({title:"Error",description:"Please add at least one row with a name",variant:"destructive"});j(!0);try{let s,l=T(),r=[...i].sort((e,t)=>e.id&&t.id?e.id-t.id:e.id?-1:t.id?1:i.indexOf(e)-i.indexOf(t)).map(e=>{let t={rowsName:e.rowsName.trim()};return(n||a?.id)&&e.id&&a?.tableRows?.some(t=>t.id===e.id)&&(t.id=e.id),t});a?.id?(s=await (0,H.am)(a.id,d.trim(),l,r),o({title:"Success",description:"Table question updated successfully"})):(s=await (0,H.ZR)(d.trim(),e,l,r),o({title:"Success",description:"Table question created successfully"}),c(""),p([{id:N(),columnName:"",level:0}]),h([])),t&&s?.id&&t(s.id)}catch(t){console.error("Error with table operation:",t);let e=a?.id?"Failed to update table question":"Failed to create table question";t.response?.data?.message?e=t.response.data.message:t.message&&(e=t.message),o({title:"Error",description:e,variant:"destructive"})}finally{j(!1)}},F=r().useRef(null);return(0,l.jsxs)("div",{ref:F,className:"space-y-6 p-4 border border-gray-200 rounded-md w-full table-question-builder",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("h3",{className:"text-lg font-medium",children:n||a?.id?"Edit Table Question":"Create Table Question"}),(0,l.jsx)(K.$,{type:"button",variant:"outline",size:"sm",onClick:()=>y(!f),className:"flex items-center gap-1",children:f?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(V.A,{className:"h-4 w-4"}),"Hide Preview"]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(v.A,{className:"h-4 w-4"}),"Show Preview"]})})]}),(0,l.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md text-sm text-blue-800 border border-blue-200",children:[(0,l.jsx)("p",{className:"font-medium mb-1",children:"Table Structure Guidelines:"}),(0,l.jsxs)("ul",{className:"list-disc pl-5 space-y-1",children:[(0,l.jsxs)("li",{children:["Create multiple ",(0,l.jsx)("span",{className:"font-medium",children:"parent columns"})," ",'using the "Add Top-Level Column" button']}),(0,l.jsxs)("li",{children:["Add up to 2 ",(0,l.jsx)("span",{className:"font-medium",children:"child columns"}),' under each parent using the "+" button']}),(0,l.jsx)("li",{children:"Child columns cannot have their own children (maximum 2 levels)"})]})]}),s?(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(B.J,{htmlFor:"table-label",children:"Table Label"}),(0,l.jsx)(L.p,{id:"table-label",value:d,onChange:e=>c(e.target.value),placeholder:"Enter table question label",required:!0})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 max-h-[60vh] overflow-y-auto",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(B.J,{children:"Columns"}),m.map(e=>(0,l.jsxs)("div",{className:(0,J.cn)("flex items-center gap-2 p-2 rounded-md",0===e.level?"bg-gray-50":"bg-white border-l-2 border-gray-300"),style:{marginLeft:`${20*e.level}px`},children:[(0,l.jsxs)("div",{className:"flex-1 flex items-center gap-2",children:[e.level>0&&(0,l.jsx)("div",{className:"w-4 h-4 flex items-center justify-center",children:(0,l.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full"})}),(0,l.jsx)(L.p,{value:e.columnName,onChange:t=>Q(e.id,t.target.value),placeholder:`${0===e.level?"Parent":"Child"} Column`,className:(0,J.cn)("flex-1",0===e.level?"border-blue-200 bg-white":"border-dashed")}),0===e.level&&(0,l.jsx)("div",{className:"text-xs text-blue-500 font-medium",children:"Parent"}),e.level>0&&(0,l.jsx)("div",{className:"text-xs text-gray-500 font-medium",children:"Child"})]}),(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(K.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>w(e.id),title:e.level>0?"Child columns cannot have their own children":m.filter(t=>t.parentId===e.id).length>=2?"Maximum 2 child columns allowed":"Add child column",disabled:e.level>0||m.filter(t=>t.parentId===e.id).length>=2,children:(0,l.jsx)(b.A,{className:(0,J.cn)("h-4 w-4",(e.level>0||m.filter(t=>t.parentId===e.id).length>=2)&&"text-gray-300")})}),(0,l.jsx)(K.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>k(e.id),disabled:m.length<=1,title:"Remove column",children:(0,l.jsx)(u.A,{className:"h-4 w-4"})})]})]},e.id)),(0,l.jsxs)(K.$,{type:"button",variant:"outline",size:"sm",onClick:q,className:"mt-2",children:[(0,l.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Add Top-Level Column"]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(B.J,{children:"Rows"}),x.map((e,t)=>(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(L.p,{value:e.rowsName,onChange:e=>G(t,e.target.value),placeholder:`Row ${t+1}`,className:`row-input ${!e.rowsName.trim()?"border-red-500":""}`}),(0,l.jsx)(K.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>A(t),disabled:!1,children:(0,l.jsx)(u.A,{className:"h-4 w-4"})})]},t)),(0,l.jsxs)(K.$,{type:"button",variant:"outline",size:"sm",onClick:E,className:"mt-2",children:[(0,l.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Add Row"]})]})]}),f&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(B.J,{children:"Table Preview"}),(0,l.jsx)("div",{className:"border rounded-md p-4 overflow-x-auto max-h-[300px] overflow-y-auto",children:(0,l.jsxs)(U.XI,{children:[(0,l.jsx)(U.A0,{children:(()=>{let e=Math.max(...m.map(e=>e.level),0),t=[];t.push((0,l.jsx)(U.Hj,{children:m.filter(e=>0===e.level).map(e=>{let t=e=>{let s=m.filter(t=>t.parentId===e),l=[...s];return s.forEach(e=>{l=[...l,...t(e.id)]}),l},s=t(e.id),i=s.filter(e=>!m.some(t=>t.parentId===e.id)),r=s.length>0&&i.length||1;return(0,l.jsx)(U.nd,{colSpan:r,className:"text-center border-b",children:e.columnName||"Column"},e.id)})},"header-row-0"));for(let s=1;s<=e;s++)t.push((0,l.jsx)(U.Hj,{children:m.filter(e=>e.level===s-1).map(e=>{let t=m.filter(t=>t.parentId===e.id);return 0===t.length?(0,l.jsx)(U.nd,{className:"text-center border-b"},`empty-${e.id}`):t.map(e=>{let t=e=>{let s=m.filter(t=>t.parentId===e),l=[...s];return s.forEach(e=>{l=[...l,...t(e.id)]}),l},s=t(e.id),i=s.filter(e=>!m.some(t=>t.parentId===e.id)),r=s.length>0&&i.length||1;return(0,l.jsx)(U.nd,{colSpan:r,className:"text-center border-b",children:e.columnName||"Child Column"},e.id)})})},`header-row-${s}`));return t})()}),(0,l.jsx)(U.BF,{children:x.length>0?x.map((e,t)=>(0,l.jsx)(U.Hj,{children:m.filter(e=>{let t=m.some(t=>t.parentId===e.id);return 0===e.level&&!t||e.level>0}).map(e=>(0,l.jsx)(U.nA,{className:"bg-gray-50",children:(0,l.jsx)("div",{className:"h-8 flex items-center justify-center text-gray-400 text-xs",children:"Input field"})},e.id))},t)):(0,l.jsx)(U.Hj,{children:m.filter(e=>{let t=m.some(t=>t.parentId===e.id);return 0===e.level&&!t||e.level>0}).map(e=>(0,l.jsx)(U.nA,{className:"bg-gray-50",children:(0,l.jsx)("div",{className:"h-8 flex items-center justify-center text-gray-400 text-xs",children:"Input field"})},e.id))})})]})}),(0,l.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"This preview shows how the table will appear to users filling out the form."})]})]})]}):(0,l.jsxs)("form",{onSubmit:D,className:"space-y-4",children:[(0,l.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md text-sm text-blue-800 border border-blue-200 mb-4",children:[(0,l.jsx)("p",{className:"font-medium mb-1",children:"Table Structure Guidelines:"}),(0,l.jsxs)("ul",{className:"list-disc pl-5 space-y-1",children:[(0,l.jsxs)("li",{children:["Create multiple"," ",(0,l.jsx)("span",{className:"font-medium",children:"parent columns"}),' using the "Add Top-Level Column" button']}),(0,l.jsxs)("li",{children:["Add up to 2 ",(0,l.jsx)("span",{className:"font-medium",children:"child columns"})," ",'under each parent using the "+" button']}),(0,l.jsx)("li",{children:"Child columns cannot have their own children (maximum 2 levels)"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(B.J,{htmlFor:"table-label",children:"Table Label"}),(0,l.jsx)(L.p,{id:"table-label",value:d,onChange:e=>c(e.target.value),placeholder:"Enter table question label",required:!0})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(B.J,{children:"Columns"}),m.map(e=>(0,l.jsxs)("div",{className:(0,J.cn)("flex items-center gap-2 p-2 rounded-md",0===e.level?"bg-gray-50":"bg-white border-l-2 border-gray-300"),style:{marginLeft:`${20*e.level}px`},children:[(0,l.jsxs)("div",{className:"flex-1 flex items-center gap-2",children:[e.level>0&&(0,l.jsx)("div",{className:"w-4 h-4 flex items-center justify-center",children:(0,l.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full"})}),(0,l.jsx)(L.p,{value:e.columnName,onChange:t=>Q(e.id,t.target.value),placeholder:`${0===e.level?"Parent":"Child"} Column`,className:(0,J.cn)("flex-1",0===e.level?"border-blue-200 bg-white":"border-dashed")}),0===e.level&&(0,l.jsx)("div",{className:"text-xs text-blue-500 font-medium",children:"Parent"}),e.level>0&&(0,l.jsx)("div",{className:"text-xs text-gray-500 font-medium",children:"Child"})]}),(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(K.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>w(e.id),title:e.level>0?"Child columns cannot have their own children":m.filter(t=>t.parentId===e.id).length>=2?"Maximum 2 child columns allowed":"Add child column",disabled:e.level>0||m.filter(t=>t.parentId===e.id).length>=2,children:(0,l.jsx)(b.A,{className:(0,J.cn)("h-4 w-4",(e.level>0||m.filter(t=>t.parentId===e.id).length>=2)&&"text-gray-300")})}),(0,l.jsx)(K.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>k(e.id),disabled:m.length<=1,title:"Remove column",children:(0,l.jsx)(u.A,{className:"h-4 w-4"})})]})]},e.id)),(0,l.jsxs)(K.$,{type:"button",variant:"outline",size:"sm",onClick:q,className:"mt-2",children:[(0,l.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Add Top-Level Column"]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(B.J,{children:"Rows"}),x.map((e,t)=>(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(L.p,{value:e.rowsName,onChange:e=>G(t,e.target.value),placeholder:`Row ${t+1}`,className:`row-input ${!e.rowsName.trim()?"border-red-500":""}`}),(0,l.jsx)(K.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>A(t),disabled:!1,children:(0,l.jsx)(u.A,{className:"h-4 w-4"})})]},t)),(0,l.jsxs)(K.$,{type:"button",variant:"outline",size:"sm",onClick:E,className:"mt-2",children:[(0,l.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Add Row"]})]})]}),f&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(B.J,{children:"Table Preview"}),(0,l.jsx)("div",{className:"border rounded-md p-4 overflow-x-auto",children:(0,l.jsxs)(U.XI,{children:[(0,l.jsx)(U.A0,{children:(()=>{let e=Math.max(...m.map(e=>e.level),0),t=[];t.push((0,l.jsx)(U.Hj,{children:m.filter(e=>0===e.level).map(e=>{let t=e=>{let s=m.filter(t=>t.parentId===e),l=[...s];return s.forEach(e=>{l=[...l,...t(e.id)]}),l},s=t(e.id),i=s.filter(e=>!m.some(t=>t.parentId===e.id)),r=s.length>0&&i.length||1;return(0,l.jsx)(U.nd,{colSpan:r,className:"text-center border-b",children:e.columnName||"Column"},e.id)})},"header-row-0"));for(let s=1;s<=e;s++)t.push((0,l.jsx)(U.Hj,{children:m.filter(e=>e.level===s-1).map(e=>{let t=m.filter(t=>t.parentId===e.id);return 0===t.length?(0,l.jsx)(U.nd,{className:"text-center border-b"},`empty-${e.id}`):t.map(e=>{let t=e=>{let s=m.filter(t=>t.parentId===e),l=[...s];return s.forEach(e=>{l=[...l,...t(e.id)]}),l},s=t(e.id),i=s.filter(e=>!m.some(t=>t.parentId===e.id)),r=s.length>0&&i.length||1;return(0,l.jsx)(U.nd,{colSpan:r,className:"text-center border-b",children:e.columnName||"Child Column"},e.id)})})},`header-row-${s}`));return t})()}),(0,l.jsx)(U.BF,{children:x.length>0?x.map((e,t)=>(0,l.jsx)(U.Hj,{children:m.filter(e=>{let t=m.some(t=>t.parentId===e.id);return 0===e.level&&!t||e.level>0}).map(e=>(0,l.jsx)(U.nA,{className:"bg-gray-50",children:(0,l.jsx)("div",{className:"h-8 flex items-center justify-center text-gray-400 text-xs",children:"Input field"})},e.id))},t)):(0,l.jsx)(U.Hj,{children:m.filter(e=>{let t=m.some(t=>t.parentId===e.id);return 0===e.level&&!t||e.level>0}).map(e=>(0,l.jsx)(U.nA,{className:"bg-gray-50",children:(0,l.jsx)("div",{className:"h-8 flex items-center justify-center text-gray-400 text-xs",children:"Input field"})},e.id))})})]})}),(0,l.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"This preview shows how the table will appear to users filling out the form."})]})]}),(0,l.jsx)("div",{className:"flex items-center justify-end space-x-4 mt-6",children:(0,l.jsx)(K.$,{type:"submit",disabled:g,className:"bg-primary-500 text-white hover:bg-primary-600",children:g?"Saving...":n||a?.id?"Update":"Save"})})]})]})}var _=s(76847),Y=s(33103);let X=e=>new Promise(t=>{if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel"].includes(e.type))return void t({isValid:!1,error:"Please select a valid Excel file (.xlsx or .xls)"});if(e.size>5242880)return void t({isValid:!1,error:"File size must be less than 5MB"});let s=new FileReader;s.onload=e=>{try{let s=new Uint8Array(e.target?.result),l=Y.LF(s,{type:"array"}),i=l.Sheets[l.SheetNames[0]],r=Y.Wp.sheet_to_json(i,{header:1});if(r.length<2)return void t({isValid:!1,error:"Excel file is empty or has no valid data"});let n=r[0].map(e=>e?.toString().trim());if(!n[0]?.includes("label")||!n[1]?.includes("code"))return void t({isValid:!1,error:"Invalid Excel format: Missing required headers (Label, Code)"});let a=r.slice(1);if(0===a.length)return void t({isValid:!1,error:"Excel file contains no valid options"});for(let e=0;e<a.length;e++){let s=a[e];if(!s[0]||!s[1])return void t({isValid:!1,error:`Invalid data in row ${e+2}: Label and Code are required`})}t({isValid:!0})}catch(e){t({isValid:!1,error:"Failed to parse Excel file"})}},s.onerror=()=>{t({isValid:!1,error:"Error reading Excel file"})},s.readAsArrayBuffer(e)}),Z=({file:e,onRemove:t,error:s})=>(0,l.jsxs)("div",{className:`flex items-center justify-between p-3 rounded-lg ${s?"bg-red-50 border border-red-200":"bg-green-50 border border-green-200"}`,children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[s?(0,l.jsx)(_.wew,{className:"text-red-500"}):(0,l.jsx)(_.qGT,{className:"text-green-500"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"text-sm font-medium",children:e.name}),(0,l.jsxs)("div",{className:"text-xs text-gray-500",children:[(e.size/1024).toFixed(1)," KB"]}),s&&(0,l.jsx)("div",{className:"text-xs text-red-600",children:s})]})]}),(0,l.jsx)("button",{type:"button",onClick:t,className:"text-red-500 hover:text-red-700 p-1",title:"Remove file",children:(0,l.jsx)(_.id1,{})})]}),ee=()=>{let e=new Blob(["Label,Code,Next Question ID\nOption 1,opt1,\nOption 2,opt2,\nOption 3,opt3,"],{type:"text/csv"}),t=window.URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download="question_options_template.csv",document.body.appendChild(s),s.click(),window.URL.revokeObjectURL(t),document.body.removeChild(s)},et=({isOpen:e,onConfirm:t,onCancel:s})=>e?(0,l.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,l.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full",children:[(0,l.jsx)("h2",{className:"text-lg text-neutral-700 font-semibold mb-4",children:"Unsaved Changes"}),(0,l.jsx)("p",{className:"mb-6 text-neutral-700",children:"You have unsaved changes. Are you sure you want to close this form?"}),(0,l.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,l.jsx)("button",{onClick:s,className:"btn-outline",children:"Cancel"}),(0,l.jsx)("button",{onClick:t,className:"btn-danger",children:"Discard Changes"})]})]})}):null,es=({showModal:e,setShowModal:t,contextType:s,contextId:r,position:n})=>{let a=(0,w.mN)({resolver:(0,F.u)(O),defaultValues:{label:"",inputType:"",hint:"",placeholder:"",questionOptions:[]}}),{register:o,formState:{errors:d,isSubmitted:c,isDirty:u},setValue:m,handleSubmit:p,reset:x,watch:h}=a,[b,g]=(0,i.useState)(!1),[j,f]=(0,i.useState)(null),[v,y]=(0,i.useState)("form"),[N,G]=(0,i.useState)(""),[T,z]=(0,i.useState)(!1),[P,M]=(0,i.useState)(""),[K,L]=(0,i.useState)(!1),B=(0,i.useRef)(null),V=(0,k.jE)(),H="project"===s?["questions",r]:"template"===s?["templateQuestions",r]:["questionBlockQuestions",r],U=(0,A.n)({mutationFn:Q.Af,onSuccess:()=>{V.invalidateQueries({queryKey:H}),er()},onError:e=>{G(e.message||"Failed to add question"),L(!1)}});(0,i.useEffect)(()=>{o("inputType",{required:"Please select an input type"})},[o]),(0,i.useEffect)(()=>{m("inputType",P,{shouldValidate:c})},[P,m,c]),(0,i.useEffect)(()=>{e&&L(!1)},[e]);let J=async e=>{let t=e.target.files?.[0];if(!t)return;let s=await X(t);if(!s.isValid){G(s.error||"Invalid file"),f(null);return}G(""),f(t)},Y=()=>{f(null),G(""),B.current&&(B.current.value="")},es=e=>{y(e),"form"===e&&Y()},el=()=>{let e=h("questionOptions");return u||!!h("label")||!!h("hint")||!!h("placeholder")||!!P||!!j||e&&Array.isArray(e)&&e.length>0},ei=()=>{el()?g(!0):er()},er=()=>{x(),M(""),f(null),y("form"),G(""),L(!1),g(!1),z(!1),t(!1)},en=async e=>{if(K)return;if("table"===P){let e=document.querySelector(".table-question-builder");e?e.dispatchEvent(new CustomEvent("submitTable")):console.error("TableQuestionBuilder not found");return}if($(P)){if("excel"===v){if(!j)return void G("Please select an Excel file")}else if(0===(e.questionOptions||[]).length)return void a.setError("questionOptions",{type:"custom",message:"At least one option is required for this input type"})}L(!0);let t=j??void 0,l={label:e.label,isRequired:T,hint:e.hint,placeholder:e.placeholder,inputType:P,questionOptions:"form"===v?e.questionOptions:void 0,file:"excel"===v?t:void 0};U.mutate({contextType:s,contextId:r,dataToSend:l,position:n})},ea=$(P);return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)(q.A,{isOpen:e,onClose:ei,className:"w-11/12 tablet:w-4/5 desktop:w-4/5 bg-neutral-100 rounded-lg p-6",children:[(0,l.jsx)("h1",{className:"heading-text capitalize mb-4",children:"Add question"}),U.isPending&&(0,l.jsx)(R,{}),(0,l.jsx)(w.Op,{...a,children:(0,l.jsxs)("form",{className:"space-y-4 max-h-[500px] overflow-y-auto p-4",onSubmit:p(en),children:[(0,l.jsxs)("div",{className:"label-input-group group ",children:[(0,l.jsx)("input",{...o("label",{required:"Question name is required"}),className:"input-field",placeholder:"Enter the question"}),d.label&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:`${d.label.message}`})]}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"label-input-group group",children:[(0,l.jsx)("label",{htmlFor:"question-type",className:"label-text",children:"Input Type"}),(0,l.jsx)("div",{className:"mt-1",children:(0,l.jsx)(C.l,{id:"question-type",options:Object.values(S),value:P&&S[P]?S[P]:"Select an option",onChange:e=>{M((0,I.H)(e,S)??""),y("form"),Y()}})}),d.inputType&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:`${d.inputType.message}`})]}),(0,l.jsx)("div",{className:"flex items-end",children:(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(E.dO,{id:"required",checked:T,onCheckedChange:()=>z(e=>!e),className:"data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 border border-primary-500"}),(0,l.jsx)("label",{htmlFor:"required",className:"label-text",children:"Required"})]})})]}),(0,l.jsxs)("div",{className:"label-input-group group",children:[(0,l.jsx)("label",{htmlFor:"hint",className:"label-text",children:"Help text"}),(0,l.jsx)("textarea",{...o("hint"),id:"hint",placeholder:"Add a hint or instructions for this question",className:"input-field resize-none"})]}),(0,l.jsxs)("div",{className:"label-input-group group",children:[(0,l.jsx)("label",{htmlFor:"placeholder",className:"label-text",children:"Placeholder text"}),(0,l.jsx)("input",{...o("placeholder"),id:"placeholder",placeholder:"Add a placeholder for this question",className:"input-field"})]}),ea&&(0,l.jsx)("div",{className:"space-y-4",children:(0,l.jsxs)("div",{className:"label-input-group group",children:[(0,l.jsx)("label",{className:"label-text",children:"Question Options"}),(0,l.jsxs)("div",{className:"flex space-x-4 mb-4",children:[(0,l.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,l.jsx)("input",{type:"radio",value:"form",checked:"form"===v,onChange:e=>es(e.target.value),className:"text-primary-500"}),(0,l.jsx)("span",{children:"Manual Entry"})]}),(0,l.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,l.jsx)("input",{type:"radio",value:"excel",checked:"excel"===v,onChange:e=>es(e.target.value),className:"text-primary-500"}),(0,l.jsx)("span",{children:"Excel Upload"})]})]}),"excel"===v&&(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-4 hover:border-gray-400 transition-colors",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)(_.tAF,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,l.jsxs)("div",{className:"mt-2",children:[(0,l.jsxs)("label",{htmlFor:"excel-file",className:"cursor-pointer",children:[(0,l.jsx)("span",{className:"mt-2 block text-sm font-medium text-gray-900",children:"Upload Excel file with question options"}),(0,l.jsx)("span",{className:"mt-1 block text-xs text-gray-500",children:"Supported formats: .xlsx, .xls (max 5MB)"})]}),(0,l.jsx)("input",{ref:B,id:"excel-file",type:"file",accept:".xlsx,.xls",onChange:J,className:"sr-only"})]}),(0,l.jsxs)("div",{className:"mt-3 flex justify-center space-x-2",children:[(0,l.jsxs)("button",{type:"button",onClick:()=>B.current?.click(),className:"btn-outline inline-flex items-center",children:[(0,l.jsx)(_.bh6,{className:"mr-2"}),"Choose Excel File"]}),(0,l.jsxs)("button",{type:"button",onClick:ee,className:"btn-outline inline-flex items-center",title:"Download template",children:[(0,l.jsx)(_.Ah9,{className:"mr-2"}),"Download Template"]})]})]})}),j&&(0,l.jsx)(Z,{file:j,onRemove:Y,error:N}),N&&!j&&(0,l.jsx)("div",{className:"text-sm text-red-600 bg-red-50 p-2 rounded",children:N}),(0,l.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:[(0,l.jsx)("p",{className:"text-sm text-blue-800 font-medium mb-2",children:"Excel Format Requirements:"}),(0,l.jsxs)("ul",{className:"text-xs text-blue-700 space-y-1",children:[(0,l.jsxs)("li",{children:["• Column A: ",(0,l.jsx)("strong",{children:"Label"})," (required) - Display text for the option"]}),(0,l.jsxs)("li",{children:["• Column B: ",(0,l.jsx)("strong",{children:"Code"})," (required) - Unique identifier for the option"]}),(0,l.jsxs)("li",{children:["• Column C: ",(0,l.jsx)("strong",{children:"Next Question ID"})," ","(optional) - For conditional logic"]}),(0,l.jsx)("li",{children:"• First row should contain column headers"}),(0,l.jsx)("li",{children:"• Each subsequent row represents one option"})]})]})]}),"form"===v&&(0,l.jsx)(D,{contextType:s,contextId:r,inputType:P})]})}),"table"===P&&(0,l.jsx)("div",{className:"mt-4",children:(0,l.jsx)(W,{projectId:r,isInModal:!0,onTableCreated:e=>{-1!==e&&V.invalidateQueries({queryKey:H}),er()}})}),(0,l.jsxs)("div",{className:"flex items-center justify-end space-x-4 pt-4",children:[(0,l.jsx)("button",{type:"button",onClick:ei,className:"btn-outline",disabled:K,children:"Cancel"}),(0,l.jsx)("button",{type:"submit",className:"btn-primary flex items-center justify-center gap-2",onClick:e=>{if("table"===P){e.preventDefault();let t=document.querySelector(".table-question-builder");t&&t.dispatchEvent(new CustomEvent("submitTable"))}},disabled:K||"excel"===v&&(!j||!!N),children:K?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("span",{className:"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"}),"Saving..."]}):"Save"})]})]})})]}),(0,l.jsx)(et,{isOpen:b,onConfirm:er,onCancel:()=>g(!1)})]})};var el=s(19150),ei=s(54864);let er=({showModal:e,setShowModal:t,contextType:s,question:r,contextId:n})=>{let a=(0,w.mN)({}),{register:o,formState:{errors:d,isSubmitted:c},setValue:u,handleSubmit:m,reset:p}=a,x=e=>"position"in e,h=(e,t)=>r[e]??t,[b,g]=(0,i.useState)(!1),[j,f]=(0,i.useState)("");(0,i.useEffect)(()=>{let e=O.safeParse(r);if(e.success)p(e.data),f(e.data.inputType||""),e.data.questionOptions&&e.data.questionOptions.length>0&&u("questionOptions",e.data.questionOptions);else{console.warn("Schema parsing failed, using raw question data:",e.error),u("label",h("label","")),u("hint",h("hint","")),u("placeholder",h("placeholder","")),u("inputType",h("inputType","")),f(h("inputType",""));let t=h("questionOptions",[]);Array.isArray(t)&&t.length>0&&u("questionOptions",t)}g(h("isRequired",!1))},[r,p,u]),(0,i.useEffect)(()=>{o("inputType",{required:"Please select an input type"})},[o]),(0,i.useEffect)(()=>{u("inputType",j,{shouldValidate:c})},[j,u,c]);let v=(0,k.jE)(),y=(0,ei.wA)(),N="project"===s?["questions"]:"template"===s?["templateQuestions"]:["questionBlockQuestions"],C=()=>{g(!1),f(""),t(!1)},I=(0,A.n)({mutationFn:Q.sr,onSuccess:()=>{v.invalidateQueries({queryKey:N,exact:!1}),y((0,el.Ds)({message:"Successfully updated question",type:"success"})),C()},onError:()=>{y((0,el.Ds)({message:"Failed to update question",type:"error"}))}}),G=async e=>{if("table"===j){let e=document.querySelector(".table-question-builder");if(e)return void e.dispatchEvent(new CustomEvent("submitTable"))}let t={label:e.label,isRequired:b,hint:e.hint,placeholder:e.placeholder,inputType:j||h("inputType",""),questionOptions:e.questionOptions||[],...x(r)&&{position:r.position}};I.mutate({id:r.id,contextType:s,dataToSend:t,contextId:n})};return(0,l.jsxs)(q.A,{isOpen:e,onClose:C,className:"w-11/12 tablet:w-4/5 desktop:w-3/5",children:[(0,l.jsx)("h1",{className:"heading-text capitalize mb-4",children:"Edit question"}),I.isPending&&(0,l.jsx)(R,{}),(0,l.jsx)(w.Op,{...a,children:(0,l.jsxs)("form",{className:"space-y-4 max-h-[500px] overflow-y-auto p-4",onSubmit:m(G),children:[(0,l.jsxs)("div",{className:"label-input-group group",children:[(0,l.jsx)("input",{...o("label",{required:"Question name is required"}),className:"input-field",placeholder:"Enter the question"}),d.label&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:`${d.label.message}`})]}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"label-input-group group",children:[(0,l.jsx)("label",{htmlFor:"question-type",className:"label-text",children:"Input Type"}),(0,l.jsx)("input",{id:"question-type",className:"input-field bg-gray-100 ",value:j&&S[j]?S[j]:"N/A",disabled:!0})]}),(0,l.jsx)("div",{className:"flex items-end",children:(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(E.dO,{id:"required",checked:b,onCheckedChange:()=>g(e=>!e),className:"data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 border border-primary-500"}),(0,l.jsx)("label",{htmlFor:"required",className:"label-text",children:"Required"})]})}),d.inputType&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:`${d.inputType.message}`})]}),(0,l.jsxs)("div",{className:"label-input-group group",children:[(0,l.jsx)("label",{htmlFor:"hint",className:"label-text",children:"Help text"}),(0,l.jsx)("textarea",{...o("hint"),id:"hint",placeholder:"Add a hint or instructions for this question",className:"input-field resize-none"})]}),(0,l.jsxs)("div",{className:"label-input-group group",children:[(0,l.jsx)("label",{htmlFor:"placeholder",className:"label-text",children:"Placeholder text"}),(0,l.jsx)("input",{...o("placeholder"),id:"placeholder",placeholder:"Add a placeholder for this question",className:"input-field"})]}),$(j)&&(0,l.jsx)(D,{contextType:s,contextId:n,currentQuestionId:r.id,inputType:j},`${r.id}-${j}`),(0,l.jsxs)("div",{className:"flex items-center justify-end space-x-4",children:[(0,l.jsx)("button",{type:"button",onClick:C,className:"btn-outline",children:"Cancel"}),(0,l.jsx)("button",{onClick:m(G),className:"btn-primary",children:"Save Edit"})]})]})})]})},en=({showModal:e,setShowModal:t,contextType:s,question:n,contextId:a})=>{let o=(0,k.jE)(),d=(0,ei.wA)(),{data:c,isLoading:u,error:m}=(0,G.I)({queryKey:["tableQuestion",n.id],queryFn:async()=>{try{return await (0,H.q7)(n.id)}catch(e){throw console.error("Error fetching table data:",e),e}},enabled:e&&n.id>0&&"table"===n.inputType}),p=r().useMemo(()=>c?{id:c.id,label:c.label,tableColumns:c.tableColumns.map(e=>({id:e.id,columnName:e.columnName,parentColumnId:e.parentColumnId,childColumns:e.childColumns?.map(t=>({id:t.id,columnName:t.columnName,parentColumnId:t.parentColumnId||e.id}))||[]})),tableRows:c.tableRows.map(e=>({id:e.id,rowsName:e.rowsName}))}:null,[c]);(0,i.useEffect)(()=>{},[e,n]);let x="project"===s?["questions"]:"template"===s?["templateQuestions"]:["questionBlockQuestions"];m&&(console.error("Error fetching table data:",m),d((0,el.Ds)({message:"Failed to load table data",type:"error"})));let h=r().useRef(null);return(0,l.jsxs)(q.A,{isOpen:e,onClose:()=>{window.confirm("Are you sure you want to close? Any unsaved changes will be lost.")&&t(!1)},className:"w-11/12 tablet:w-4/5 desktop:w-3/5",preventOutsideClick:!0,children:[u&&(0,l.jsx)(R,{}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h1",{className:"heading-text capitalize mb-4",children:"Edit Table Question"}),p?(0,l.jsxs)("div",{ref:h,className:"table-question-builder-container",children:[(0,l.jsx)(W,{projectId:a,isInModal:!0,isEditMode:!0,existingTableData:p,onTableCreated:e=>{o.invalidateQueries({queryKey:x,exact:!1}),o.invalidateQueries({queryKey:["tableQuestion",n.id],exact:!0}),d((0,el.Ds)({message:"Successfully updated table question",type:"success"})),setTimeout(()=>{t(!1)},100)}}),(0,l.jsxs)("div",{className:"flex items-center justify-end space-x-4 mt-6",children:[(0,l.jsx)("button",{type:"button",onClick:()=>t(!1),className:"btn-outline",children:"Cancel"}),(0,l.jsx)("button",{type:"button",onClick:()=>{let e=null;if(h.current&&(e=h.current),!e){let t=document.querySelectorAll(".table-question-builder");t.length>0&&(e=t[0])}if(!e&&h.current){let t=h.current.querySelector(".table-question-builder");t&&(e=t)}if(e){let t=new CustomEvent("submitTable",{bubbles:!0,cancelable:!0,detail:{timestamp:Date.now()}});e.dispatchEvent(t)}else{console.error("Could not find any table builder element to dispatch event to");let e=document.querySelector("[class*='table']");if(e){let t=new CustomEvent("submitTable",{bubbles:!0,cancelable:!0,detail:{timestamp:Date.now(),isLastResort:!0}});e.dispatchEvent(t)}}},className:"btn-primary",children:"Save Changes"})]})]}):u?null:(0,l.jsx)("div",{className:"p-4 text-center",children:(0,l.jsx)("p",{className:"text-red-500",children:"Could not load table data. Please try again."})})]})]})};var ea=s(73678),eo=s(11860),ed=s(96);let ec=({showModal:e,setShowModal:t,contextType:s,contextId:r,existingGroup:n,questions:a,questionGroups:o=[]})=>{let[d,c]=(0,i.useState)(""),[u,m]=(0,i.useState)([]),p=(0,ei.wA)(),x=(0,k.jE)();(0,i.useEffect)(()=>{e&&(n?(c(n.title),m(a.filter(e=>e.questionGroupId===n.id).map(e=>e.id))):(c(""),m([])))},[e,n,a]);let h=(0,A.n)({mutationFn:ed.IF,onSuccess:e=>{x.invalidateQueries({queryKey:["questionGroups",r]}),x.invalidateQueries({queryKey:["questions",r]}),p((0,el.Ds)({message:"Question group created successfully",type:"success"})),t(!1)},onError:e=>{console.error("Error creating question group:",e),p((0,el.Ds)({message:"Failed to create question group",type:"error"}))}}),b=(0,A.n)({mutationFn:ed.lr,onSuccess:e=>{x.invalidateQueries({queryKey:["questionGroups",r]}),x.invalidateQueries({queryKey:["questions",r]}),p((0,el.Ds)({message:"Question group updated successfully",type:"success"})),t(!1)},onError:e=>{console.error("Error updating question group:",e),p((0,el.Ds)({message:"Failed to update question group",type:"error"}))}});return e?(0,l.jsx)("div",{className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40",children:(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-auto p-4",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center p-4 ",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold",children:n?"Edit Question Group":"Create Question Group"}),(0,l.jsx)("button",{onClick:()=>t(!1),className:"text-gray-500 hover:text-gray-700 cursor-pointer",children:(0,l.jsx)(eo.A,{size:20})})]}),(0,l.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),!d.trim())return void p((0,el.Ds)({message:"Group title is required",type:"error"}));n?b.mutate({id:n.id,title:d,order:n.order,selectedQuestionIds:u}):h.mutate({title:d,order:o.length+1,projectId:r,selectedQuestionIds:u})},className:"p-4",children:[(0,l.jsxs)("div",{className:"group label-input-group ",children:[(0,l.jsx)("label",{htmlFor:"title",children:"Group Title"}),(0,l.jsx)("input",{type:"text",id:"title",value:d,onChange:e=>c(e.target.value),className:" input-field w-full",placeholder:"Enter group title",required:!0})]}),(0,l.jsxs)("div",{className:"mt-8 label-input-group",children:[(0,l.jsx)("label",{children:"Select Questions for this Group"}),(0,l.jsx)("div",{className:"border border-neutral-300 rounded-md p-2 max-h-60 overflow-y-auto",children:a.length>0?a.map(e=>{let t=e.questionGroupId?o.find(t=>t.id===e.questionGroupId):null;return(0,l.jsxs)("div",{className:"flex gap-2 items-center mb-3 p-2 border-b border-neutral-300",children:[(0,l.jsx)("input",{type:"checkbox",id:`question-${e.id}`,checked:u.includes(e.id),onChange:t=>{t.target.checked?m([...u,e.id]):m(u.filter(t=>t!==e.id))},className:"mr-2 cursor-pointer w-5 h-5"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:`question-${e.id}`,className:"text-sm",children:e.label}),t&&t.id!==(n?.id||-1)&&(0,l.jsxs)("div",{className:"text-xs text-neutral-700 mt-1",children:["Currently in group: ",(0,l.jsx)("span",{className:"font-medium text-amber-600",children:t.title}),(0,l.jsx)("span",{className:"ml-1 text-neutral-700",children:"(will be moved to this group)"})]})]})]},e.id)}):(0,l.jsx)("p",{className:"text-gray-500 text-sm p-2",children:"No available questions. Please add some questions first."})})]}),(0,l.jsxs)("div",{className:"flex justify-end space-x-2 mt-4",children:[(0,l.jsx)("button",{type:"button",onClick:()=>t(!1),className:"btn-outline",children:"Cancel"}),(0,l.jsx)("button",{type:"submit",className:"px-4 py-2 btn-primary",disabled:h.isPending||b.isPending,children:h.isPending||b.isPending?"Saving...":n?"Update Group":"Create Group"})]})]})]})}):null},eu=({showModal:e,setShowModal:t,onConfirmDelete:s,onConfirmDeleteWithQuestions:i,isDeleting:r})=>e?(0,l.jsx)("div",{className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40",children:(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-lg w-full max-w-md",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center p-4 border-b",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold",children:"Delete Question Group"}),(0,l.jsx)("button",{onClick:()=>t(!1),className:"text-gray-500 hover:text-gray-700",disabled:r,children:(0,l.jsx)(eo.A,{size:20})})]}),(0,l.jsxs)("div",{className:"p-4",children:[(0,l.jsx)("p",{className:"mb-4",children:"How would you like to delete this question group?"}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("button",{onClick:s,className:"w-full px-4 py-2 bg-amber-500 text-white rounded-md hover:bg-amber-600",disabled:r,children:r?"Deleting...":"Delete Group Only (Keep Questions)"}),(0,l.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"This will remove the group but keep all questions. Questions will be available to add to other groups."}),(0,l.jsx)("button",{onClick:i,className:"w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600",disabled:r,children:r?"Deleting...":"Delete Group and All Questions"}),(0,l.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"This will permanently delete the group and all questions inside it."}),(0,l.jsx)("button",{onClick:()=>t(!1),className:"w-full px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",disabled:r,children:"Cancel"})]})]})]})}):null;var em=s(99270);let ep=({isOpen:e,onClose:t,onAddQuestions:s})=>{let[r,n]=(0,i.useState)(""),[a,o]=(0,i.useState)([]),[d,c]=(0,i.useState)(!0),{data:u,isLoading:m,isError:p}=(0,G.I)({queryKey:["libraryQuestions"],queryFn:()=>(0,Q.dI)(),enabled:e}),x=u?u.filter(e=>e.label.toLowerCase().includes(r.toLowerCase())):[],h=e=>{a.some(t=>t.id===e.id)?o(a.filter(t=>t.id!==e.id)):o([...a,e])};return((0,i.useEffect)(()=>{e||(o([]),n(""))},[e]),e)?(0,l.jsxs)("div",{className:"fixed inset-0 z-50 flex justify-end",children:[(0,l.jsx)("div",{className:"absolute inset-0 bg-neutral-900/50",onClick:t}),(0,l.jsxs)("div",{className:"relative w-full max-w-md bg-neutral-50 h-full overflow-auto shadow-xl",children:[(0,l.jsxs)("div",{className:"p-4 border-b border-neutral-200",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,l.jsx)("h2",{className:"text-xl font-bold",children:"Search Library"}),(0,l.jsx)("button",{onClick:t,className:"self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300",children:(0,l.jsx)(eo.A,{size:20})})]}),(0,l.jsxs)("div",{className:"relative mb-4",children:[(0,l.jsx)("input",{type:"text",placeholder:"Search...",className:"input-field w-full p-2 pl-10",value:r,onChange:e=>n(e.target.value)}),(0,l.jsx)(em.A,{className:"absolute left-3 top-2.5 ",size:18})]}),(0,l.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,l.jsxs)("span",{children:[x.length," asset",1!==x.length?"s":""," found"]}),(0,l.jsxs)("label",{className:"flex items-center",children:[(0,l.jsx)("input",{type:"checkbox",checked:d,onChange:()=>c(!d),className:"mr-2"}),"expand details"]})]})]}),(0,l.jsx)("div",{className:"p-4",children:m?(0,l.jsx)("div",{className:"flex justify-center p-8",children:(0,l.jsx)(M.A,{})}):p?(0,l.jsx)("div",{className:"text-red-500 p-4 text-center",children:"Error loading library questions"}):0===x.length?(0,l.jsx)("div",{className:"text-neutral-700 p-4 text-center",children:"No questions found"}):(0,l.jsx)("div",{className:"space-y-2",children:x.map(e=>(0,l.jsx)("div",{className:"border border-neutral-500 rounded-md p-3",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("input",{type:"checkbox",checked:a.some(t=>t.id===e.id),onChange:()=>h(e),className:"mr-3 h-5 w-5 cursor-pointer"}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("div",{className:"font-medium",children:e.label}),d&&(0,l.jsxs)("div",{className:"text-sm text-neutral-700 mt-1",children:["Type: ",String(e.inputType),e.hint&&(0,l.jsxs)("div",{children:["Hint: ",e.hint]})]})]})]})},e.id))})}),(0,l.jsx)("div",{className:"border-t border-gray-200 p-4 sticky bottom-0 bg-neutral-50",children:(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("button",{onClick:t,className:"btn-outline",children:"Cancel"}),(0,l.jsxs)("button",{onClick:()=>{s(a),o([]),t()},disabled:0===a.length,className:`px-4 py-2 rounded-md ${a.length>0?"btn-primary":"bg-gray-200 text-gray-500 pointer-events-none"}`,children:["Add Selected (",a.length,")"]})]})})]})]}):null},ex=({setIsPreviewMode:e,questions:t,contextType:s,contextId:o,permissions:d})=>{let c=(0,n.FR)((0,n.MS)(n.AN,{activationConstraint:{distance:8}}),(0,n.MS)(n.uN)),u=d.manageProject||d.editForm,[m,x]=(0,i.useState)(!1),[h,b]=(0,i.useState)(!1),[j,q]=(0,i.useState)(!1),[w,C]=(0,i.useState)(!1),[I,S]=(0,i.useState)(!1),[E,T]=(0,i.useState)(null),[D,F]=(0,i.useState)(!1),[$,z]=(0,i.useState)(!1),[P,O]=(0,i.useState)(!1),[M,K]=(0,i.useState)(!1),[L,B]=(0,i.useState)(null),[V,H]=(0,i.useState)([]),[U,J]=(0,i.useState)(null),[W,_]=(0,i.useState)(""),[Y,X]=(0,i.useState)(!1),[Z,ee]=(0,i.useState)(!1),et=(0,ei.wA)(),eo=(0,k.jE)(),em="project"===s?["questions",o]:"template"===s?["templateQuestions",o]:["questionBlockQuestions",o],ex=["questionGroups",o],{data:eh=[],isLoading:eb}=(0,G.I)({queryKey:ex,queryFn:()=>(0,ed.pr)({projectId:o}),enabled:"project"===s}),eg=(e=>{let s=new Map,l=[];return e.forEach(e=>{let l=t.filter(t=>t.questionGroupId===e.id).sort((e,t)=>e.position-t.position);s.set(e.id,{...e,question:l,subGroups:[]})}),e.forEach(e=>{let t=s.get(e.id);if(e.parentGroupId){let i=s.get(e.parentGroupId);i?(i.subGroups=i.subGroups||[],i.subGroups.push(t)):l.push(t)}else l.push(t)}),l.sort((e,t)=>e.order-t.order)})(eh),ej=t.filter(e=>!e.questionGroupId);r().useEffect(()=>{let e=t.some(e=>null!==e.questionGroupId&&void 0!==e.questionGroupId),l=0===eh.length;e&&l&&"project"===s&&eo.invalidateQueries({queryKey:ex})},[t,eh,s,eo,ex]);let ef=(0,A.n)({mutationFn:Q.ul,onSuccess:()=>{eo.invalidateQueries({queryKey:em}),et((0,el.Ds)({message:"Question deleted successfully",type:"success"}))},onError:()=>{et((0,el.Ds)({message:"Failed to delete question. Please try again",type:"error"}))},onSettled:()=>{F(!1)}}),ev=(0,A.n)({mutationFn:Q.ku,onSuccess:()=>{eo.invalidateQueries({queryKey:em}),et((0,el.Ds)({message:"Question duplicated successfully",type:"success"}))},onError:()=>{et((0,el.Ds)({message:"Failed to duplicate question. Please try again",type:"error"}))},onSettled:()=>{F(!1)}}),ey=(0,A.n)({mutationFn:ed.BU,onSuccess:(e,s)=>{let l=eh.find(e=>e.id===s.id);if(l&&t.filter(e=>e.questionGroupId===l.id).length>0){let e=t.map(e=>e.questionGroupId===l.id?{...e,questionGroupId:void 0}:e);eo.setQueryData(em,e);let i=eh.filter(e=>e.id!==s.id);eo.setQueryData(ex,i)}eo.invalidateQueries({queryKey:ex}),eo.invalidateQueries({queryKey:em}),et((0,el.Ds)({message:"Question group deleted successfully",type:"success"})),K(!1),ee(!1)},onError:e=>{console.error("Error deleting question group:",e),et((0,el.Ds)({message:"Failed to delete question group. Please try again",type:"error"})),ee(!1)}}),eN=(0,A.n)({mutationFn:ed.yb,onSuccess:(e,s)=>{let l=eh.find(e=>e.id===s.id);if(l){let e=eh.filter(e=>e.id!==s.id);eo.setQueryData(ex,e);let i=t.filter(e=>e.questionGroupId!==l.id);eo.setQueryData(em,i)}eo.invalidateQueries({queryKey:ex}),eo.invalidateQueries({queryKey:em}),et((0,el.Ds)({message:"Question group and its questions deleted successfully",type:"success"})),K(!1),ee(!1)},onError:e=>{console.error("Error deleting question group and questions:",e),et((0,el.Ds)({message:"Failed to delete question group and questions. Please try again",type:"error"})),ee(!1)}}),eq=(0,A.n)({mutationFn:Q.ae,onSuccess:()=>{eo.invalidateQueries({queryKey:em}),et((0,el.Ds)({message:"Question order updated successfully",type:"success"}))},onError:e=>{console.error("Failed to update question positions:",e),console.error("Error response:",e.response?.data),et((0,el.Ds)({message:`Failed to update question order: ${e.response?.data?.message||e.message||"Please try again"}`,type:"error"}))}}),ew=e=>{B(e),O(!0)},eC=e=>{B(e),K(!0)},eI=e=>{B(e),O(!0)},eS=e=>{H(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},eE=(0,A.n)({mutationFn:ed.IF,onSuccess:(e,s)=>{let l=e.data?.questionGroup?.id;if(l&&s.selectedQuestionIds){let e=t.map(e=>s.selectedQuestionIds?.includes(e.id)?{...e,questionGroupId:l}:e);eo.setQueryData(em,e);let i={id:l,title:s.title,order:s.order,projectId:s.projectId,parentGroupId:s.parentGroupId,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),question:e.filter(e=>e.questionGroupId===l)};eo.setQueryData(ex,[...eh,i])}eo.invalidateQueries({queryKey:ex}),eo.invalidateQueries({queryKey:em}),et((0,el.Ds)({message:"Question group created successfully",type:"success"})),H([]),X(!1),ee(!1)},onError:e=>{console.error("Error creating question group:",e),et((0,el.Ds)({message:"Failed to create question group",type:"error"})),ee(!1)}}),ek=(e,t)=>{J(e),_(t)},eA=()=>{J(null),_("")},eQ=(0,A.n)({mutationFn:ed.lr,onSuccess:()=>{eo.invalidateQueries({queryKey:ex}),et((0,el.Ds)({message:"Group name updated successfully",type:"success"})),J(null),_(""),ee(!1)},onError:()=>{et((0,el.Ds)({message:"Failed to update group name",type:"error"})),ee(!1)}}),eG=(0,A.n)({mutationFn:ed.YQ,onSuccess:()=>{eo.invalidateQueries({queryKey:ex}),et((0,el.Ds)({message:"Group moved successfully",type:"success"}))},onError:()=>{et((0,el.Ds)({message:"Failed to move group",type:"error"}))}}),eT=(0,A.n)({mutationFn:ed._U,onSuccess:()=>{eo.invalidateQueries({queryKey:em}),eo.invalidateQueries({queryKey:ex}),et((0,el.Ds)({message:"Question moved successfully",type:"success"}))},onError:()=>{et((0,el.Ds)({message:"Failed to move question",type:"error"}))}}),eD=e=>{if(!W.trim())return void et((0,el.Ds)({message:"Group name cannot be empty",type:"warning"}));ee(!0);let t=eh.find(t=>t.id===e);if(!t)return;let s=eh.map(t=>t.id===e?{...t,title:W}:t);eo.setQueryData(ex,s),eQ.mutate({id:e,title:W,order:t.order,parentGroupId:t.parentGroupId})},eF=e=>{let t=eh.length>0?Math.max(...eh.map(e=>e.order)):0;eE.mutate({title:"New Sub Group",order:t+1,projectId:o,parentGroupId:e,selectedQuestionIds:[]})},e$=(e,t)=>{eG.mutate({childGroupId:e,parentGroupId:t||0})},ez=(e,t,s)=>{eT.mutate({questionId:e,fromGroupId:t,toGroupId:s})};(0,A.n)({mutationFn:Q.Af,onSuccess:()=>{eo.invalidateQueries({queryKey:em})},onError:e=>{console.error("Error adding question:",e),et((0,el.Ds)({message:"Failed to add a question. Please try again.",type:"error"}))}});let eP=async e=>{if(0!==e.length){S(!0);try{let l=t.length>0?Math.max(...t.map(e=>e.position)):0;for(let t=0;t<e.length;t++){let i=e[t],r={label:i.label,isRequired:i.isRequired,hint:i.hint||"",placeholder:i.placeholder||"",inputType:String(i.inputType),questionOptions:i.questionOptions||[]};await (0,Q.Af)({contextType:s,contextId:o,dataToSend:r,position:l+t+1})}eo.invalidateQueries({queryKey:em}),et((0,el.Ds)({message:`${e.length} question(s) added successfully`,type:"success"}))}catch(e){console.error("Error adding questions:",e),et((0,el.Ds)({message:"Failed to add questions from library",type:"error"}))}finally{S(!1)}}};return(0,l.jsxs)("div",{className:"min-h-[60vh] relative",children:[(ef.isPending||ev.isPending||ey.isPending||eN.isPending||eq.isPending||I)&&(0,l.jsx)(R,{}),(0,l.jsx)(es,{showModal:m,setShowModal:x,contextType:s,contextId:o,position:t.length>0?Math.max(...t.map(e=>e.position))+1:1}),E&&"table"!==E.inputType&&(0,l.jsx)(er,{showModal:h,setShowModal:b,contextType:s,question:E,contextId:o}),E&&"table"===E.inputType&&(0,l.jsx)(en,{showModal:j,setShowModal:q,contextType:s,question:E,contextId:o}),(0,l.jsx)(ec,{showModal:$,setShowModal:z,contextType:s,contextId:o,questions:t,questionGroups:eh}),L&&(0,l.jsx)(ec,{showModal:P,setShowModal:O,contextType:s,contextId:o,existingGroup:eh.find(e=>e.id===L),questions:t,questionGroups:eh}),(0,l.jsx)(ea.R,{showModal:D,onClose:()=>F(!1),onConfirm:()=>{E&&E.id&&ef.mutate({contextType:s,id:E?.id,projectId:o})},title:"Delete Question",description:"Are you sure you want to delete this question? This action cannot be undone.",confirmButtonText:"Delete",cancelButtonText:"Cancel",confirmButtonClass:"btn-danger"}),(0,l.jsx)(eu,{showModal:M,setShowModal:K,onConfirmDelete:()=>{L&&(ee(!0),ey.mutate({id:L}))},onConfirmDeleteWithQuestions:()=>{L&&(ee(!0),eN.mutate({id:L}))},isDeleting:ey.isPending||eN.isPending}),(0,l.jsx)(ep,{isOpen:w,onClose:()=>C(!1),onAddQuestions:eP}),(0,l.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("h1",{className:"heading-text mr-4",children:"Form Builder"}),V.length>0?(0,l.jsxs)("button",{className:"btn-primary flex items-center gap-2",onClick:()=>{if(0===V.length)return void et((0,el.Ds)({message:"Please select at least one question to create a group",type:"warning"}));ee(!0);let e=t.filter(e=>V.includes(e.id)),s=e.length>0?Math.min(...e.map(e=>e.position)):eh.length+1;eE.mutate({title:"New Group",order:s,projectId:o,selectedQuestionIds:V})},disabled:Z,children:[(0,l.jsx)(g.A,{size:16}),"Create Group (",V.length,")"]}):(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsxs)("button",{className:"btn-outline flex items-center gap-2",onClick:()=>{Y?(X(!1),H([])):X(!0)},children:[(0,l.jsx)(g.A,{size:16}),Y?"Cancel Selection":"Select Questions"]}),(0,l.jsxs)("button",{className:"btn-outline flex items-center gap-2",onClick:()=>z(!0),children:[(0,l.jsx)(g.A,{size:16}),"Create Empty Group"]})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("button",{className:"btn-outline p-2",onClick:()=>e(!0),title:"Preview Form",children:(0,l.jsx)(v.A,{size:16})}),(0,l.jsx)("button",{className:"btn-outline p-2",onClick:()=>C(!0),title:"Question Library",children:(0,l.jsx)(y.A,{size:16})})]})]}),(0,l.jsx)("div",{className:"section shadow-none border border-neutral-400",children:(0,l.jsx)(n.Mp,{sensors:c,collisionDetection:n.fp,onDragEnd:e=>{let{active:l,over:i}=e;if(!i||l.id===i.id||"project"!==s)return;let r=l.data.current,n=i.data.current;if(r?.type==="group"){let e=r.groupId;if(n?.type==="group"&&n.groupId!==e)return void e$(e,n.groupId);if(!n||"ungrouped"===n.type)return void e$(e,null)}if(r?.type==="question"){let e=Number(l.id),d=r.questionGroupId;if(n?.type==="group"){let t=n.groupId;if(d!==t)return void ez(e,d,t)}if((!n||"ungrouped"===n.type)&&d)return void ez(e,d,null);let c=t.find(e=>e.id===l.id),u=t.find(e=>e.id===i.id);if(c&&u&&c.questionGroupId===u.questionGroupId){let e=t.filter(e=>e.questionGroupId===c.questionGroupId).sort((e,t)=>e.position-t.position),r=e.findIndex(e=>e.id===l.id),n=e.findIndex(e=>e.id===i.id);if(-1!==r&&-1!==n&&r!==n){let t=(0,a.be)(e,r,n).map((e,t)=>({id:Number(e.id),position:t+1}));eq.mutate({contextType:s,contextId:o,questionPositions:t})}}}},children:(0,l.jsx)(a.gB,{items:[...eg.map(e=>`group-${e.id}`),...ej.map(e=>e.id)],strategy:a._G,children:(0,l.jsx)("div",{className:"space-y-4",children:0===t.length?(0,l.jsxs)("div",{className:"text-center py-16 px-4",children:[(0,l.jsx)("h3",{className:"heading-text text-muted-foreground",children:"No questions yet"}),(0,l.jsx)("p",{className:"mt-1 text-sm sub-text",children:"Get started by adding your first question"}),(0,l.jsx)("div",{className:"p-4 flex justify-center",children:(0,l.jsxs)("button",{onClick:()=>x(!0),className:"btn-primary",disabled:!u,children:[(0,l.jsx)(N.A,{size:16}),"Add First Question"]})})]}):(0,l.jsxs)(l.Fragment,{children:[eg.map(e=>(0,l.jsx)("div",{className:"mb-4",children:(0,l.jsx)(f,{id:e.id,title:e.title,questions:e.question||[],subGroups:e.subGroups,parentGroupId:e.parentGroupId,level:0,onEditGroup:ew,onDeleteGroup:eC,onAddQuestionToGroup:eI,onCreateSubGroup:eF,onEditQuestion:e=>{T(e),"table"===e.inputType?q(!0):b(!0)},onDeleteQuestion:e=>{T(e),F(!0)},onDuplicateQuestion:e=>{T(e),ev.mutate({id:e.id,contextType:s,contextId:o})},onReorderQuestions:e=>{eq.mutate({contextType:s,contextId:o,questionPositions:e})},onMoveGroupToParent:e$,onMoveQuestionBetweenGroups:ez,isEditing:U===e.id,onStartEditing:ek,onSaveGroupName:eD,onCancelEditing:eA,editingName:W,onEditingNameChange:_,selectionMode:Y})},`group-${e.id}`)),ej.sort((e,t)=>e.position-t.position).map(e=>(0,l.jsx)("div",{className:"mb-4",children:(0,l.jsx)(p,{question:e,onEdit:()=>{T(e),"table"===e.inputType?q(!0):b(!0)},onDelete:()=>{T(e),F(!0)},onDuplicate:()=>{T(e),ev.mutate({id:e.id,contextType:s,contextId:o})},selectionMode:Y,isSelected:V.includes(e.id),onToggleSelect:()=>eS(e.id)})},`question-${e.id}`))]})})})})}),t.length>0&&(0,l.jsx)("div",{className:"sticky bottom-0 p-4 flex justify-center",children:(0,l.jsxs)("button",{className:`btn-primary  max-w-md flex items-center justify-center gap-2 ${!u&&"text-gray-400 cursor-not-allowed"}`,onClick:()=>x(!0),disabled:!u,children:[(0,l.jsx)(N.A,{size:16}),"Add Question"]})})]})}}};