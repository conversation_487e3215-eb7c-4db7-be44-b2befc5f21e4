(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5660],{7578:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>R});var l=r(95155),s=r(12115),a=r(29911),o=r(93347),i=r(89852),n=r(67133),c=r(97168),d=r(63642),u=r(10786),h=r(66163),m=r(14549),p=r(95139),g=r(6874),x=r.n(g),y=r(88570),j=r(53999),v=r(92657);let f=(e,t)=>{if(null==e)return"-";if("boolean"==typeof e)return e?"Yes":"No";if(e instanceof Date)return(0,j.Y)(e);if("date"===t&&"string"==typeof e)try{return(0,j.Y)(new Date(e))}catch(t){return e}return String(e)},b=[{id:"select",header:e=>{let{table:t}=e;return(0,l.jsx)(p.S,{className:"w-6 h-6 data-[state=checked]:bg-neutral-100 data-[state=checked]:text-primary-500 border-neutral-100 cursor-pointer",checked:t.getIsAllPageRowsSelected()||t.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:e=>t.toggleAllPageRowsSelected(!!e),"aria-label":"Select all"})},cell:e=>{let{row:t}=e,r=t.original.id,s=(0,y.l)(r);return(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(p.S,{className:"w-6 h-6 bg-neutral-100 border-neutral-400 data-[state=checked]:bg-neutral-100 data-[state=checked]:text-primary-500 cursor-pointer",checked:t.getIsSelected(),onCheckedChange:e=>t.toggleSelected(!!e),"aria-label":"Select row"}),(0,l.jsx)(x(),{title:"View project",href:"project/".concat(s,"/overview"),className:" hover:text-primary-500 transition-colors",children:(0,l.jsx)(v.A,{className:"w-5 h-5"})})]})},enableHiding:!1},{accessorKey:"name",header:e=>{let{column:t}=e;return(0,l.jsxs)("button",{className:"flex items-center text-left",onClick:()=>t.toggleSorting("asc"===t.getIsSorted()),children:["Project name",(0,l.jsx)(m.CBv,{className:"h-4 w-4"})]})},cell:e=>{let{row:t}=e,r=t.original.id,s=(0,y.l)(r);return(0,l.jsx)(x(),{href:"project/".concat(s,"/overview"),className:"cursor-pointer text-primary-500 hover:text-primary-600 hover:underline transition-all duration-300",children:t.getValue("name")})},enableSorting:!0,sortingFn:(e,t,r)=>{var l,s;let a=(null==(l=e.getValue(r))?void 0:l.toString().toLowerCase())||"",o=(null==(s=t.getValue(r))?void 0:s.toString().toLowerCase())||"";return a.localeCompare(o)}},{accessorKey:"description",header:"Description"},{accessorKey:"status",header:"Status"},{accessorKey:"updatedAt",header:"Date modified",cell:e=>{let{getValue:t}=e,r=t();return(0,l.jsx)("div",{className:"font-medium text-neutral-700",children:f(r,"date")||"Not recorded"})}},{accessorKey:"lastDeployedAt",header:"Date deployed"},{accessorKey:"sector",header:"Sector"},{accessorKey:"country",header:"Countries"},{accessorKey:"lastSubmittionAt",header:"Last submission at",sortingFn:"basic"}];var w=r(77361),S=r(26715),C=r(5041),N=r(34540),k=r(71402),A=r(29350);let D="data-table-column-visibility",E=e=>{let{data:t}=e,r=(0,S.jE)(),m=(0,N.wA)(),{user:p}=(0,A.A)(),[g,x]=(0,s.useState)(""),[y,j]=(0,s.useState)({}),[v,f]=(0,s.useState)(null),[E,F]=s.useState(!1),[P,K]=(0,s.useState)(!1),[I,R]=(0,s.useState)(null),[B,O]=(0,s.useState)(!1),[V,M]=(0,s.useState)(void 0),[_,T]=(0,s.useState)(!1),[L,Y]=(0,s.useState)(!1),[q,H]=(0,s.useState)(!1),J=(0,C.n)({mutationFn:e=>(0,w.D_)(e),onSuccess:(e,t)=>{let l=t.length;m((0,k.Ds)({message:1===l?"Project archived successfully":"".concat(l," projects archived successfully"),type:"success"})),r.invalidateQueries({queryKey:["projects",null==p?void 0:p.id]}),null==v||v.resetRowSelection(),K(!1)},onError:e=>{console.error("Error archiving projects:",e),m((0,k.Ds)({message:"Failed to archive projects. Please try again.",type:"error"})),K(!1)}}),Q=(0,C.n)({mutationFn:e=>(0,w.or)(e),onSuccess:(e,t)=>{let l=t.length;m((0,k.Ds)({message:1===l?"Project deleted successfully":"".concat(l," projects deleted successfully"),type:"success"})),r.invalidateQueries({queryKey:["projects",null==p?void 0:p.id]}),null==v||v.resetRowSelection(),K(!1)},onError:e=>{console.error("Error deleting projects:",e),m((0,k.Ds)({message:"Failed to delete projects. Please try again.",type:"error"})),K(!1)}});return(0,s.useEffect)(()=>{try{let e=localStorage.getItem(D);if(e){let t=JSON.parse(e);t&&"object"==typeof t&&!Array.isArray(t)?j(t):console.warn("Invalid format in localstorage for column visibility")}}catch(e){console.error("Error loading column visibility:",e)}},[]),(0,s.useEffect)(()=>{if(Object.keys(y).length>0)try{localStorage.setItem(D,JSON.stringify(y))}catch(e){console.error("Error saving column visibility:",e)}},[y]),(0,l.jsxs)("div",{className:"bg-neutral-100 rounded-md",children:[(0,l.jsxs)("div",{className:"px-6 py-2",children:[(0,l.jsxs)("div",{className:"flex flex-col laptop:flex-row justify-between items-center laptop:gap-5 mobile:p-4 laptop:p-2",children:[(0,l.jsxs)("div",{className:"flex flex-col items-center gap-2 laptop:gap-14 laptop:flex-row  text-neutral-700 laptop:p-4",children:[(0,l.jsx)("h2",{className:" font-semibold",children:"My Projects"}),(0,l.jsxs)("div",{className:"flex flex-col tablet:flex-row gap-4 items-center py-4",children:[(0,l.jsx)(i.p,{placeholder:"Search all columns...",value:g,onChange:e=>x(e.target.value)}),v&&(0,l.jsxs)(n.rI,{open:E,onOpenChange:e=>F(e),children:[(0,l.jsx)(n.ty,{asChild:!0,children:(0,l.jsxs)(c.$,{variant:"outline",className:"flex items-center gap-2 cursor-pointer",children:["Show/Hide Columns",E?(0,l.jsx)(a.Ucs,{className:"w-3 h-3"}):(0,l.jsx)(a.Vr3,{className:"w-3 h-3"})]})}),(0,l.jsx)(n.SQ,{align:"start",className:" border bg-neutral-100 border-neutral-200 shadow-md",children:v.getAllColumns().filter(e=>e.getCanHide()).map(e=>{var t;return(0,l.jsx)(n.hO,{className:"capitalize cursor-pointer hover:bg-neutral-200",checked:null==(t=y[e.id])||t,onCheckedChange:t=>j(r=>({...r,[e.id]:t})),children:e.id},e.id)})})]})]})]}),(0,l.jsxs)("div",{className:"flex gap-4 text-neutral-700",children:[(0,l.jsx)("div",{title:"Archive",className:"p-2 rounded-full transition-all duration-300 ease-in-out ".concat(q?"hover:bg-primary-500 hover:text-neutral-100 cursor-pointer":"opacity-50"),onClick:q?()=>{R({title:"Confirm Archive",description:"Are you sure you want to archive this item? You can restore it later if needed.",confirmButtonText:"Archive",confirmButtonClass:"btn-primary",onConfirm:()=>{let e=(null==v?void 0:v.getSelectedRowModel().rows.map(e=>e.original.id))||[];J.mutate(e)}}),K(!0)}:void 0,children:(0,l.jsx)(a.Wlj,{className:"h-4 w-4"})}),(0,l.jsx)("div",{title:"Share",className:"p-2 rounded-full transition-all duration-300 ease-in-out ".concat(L?"hover:bg-primary-500 hover:text-neutral-100 cursor-pointer":"opacity-50"),onClick:L?()=>{var e;let t=null==v||null==(e=v.getSelectedRowModel().rows[0])?void 0:e.original;t&&(M(t),O(!0))}:void 0,children:(0,l.jsx)(a.NPy,{className:"h-4 w-4"})}),(0,l.jsx)("div",{title:"Delete",className:"p-2 rounded-full transition-all duration-300 ease-in-out ".concat(_?"hover:bg-primary-500 hover:text-neutral-100 cursor-pointer":"opacity-50"),onClick:_?()=>{R({title:"Confirm Deletion",description:(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("p",{children:"Are you sure you want to delete this item? This action cannot be undone."}),(0,l.jsxs)("ul",{className:"list-disc list-inside flex flex-col gap-2 mt-2 text-neutral-700",children:[(0,l.jsx)("li",{children:"All data gathered for these projects will be deleted."}),(0,l.jsx)("li",{children:"Forms associated with these projects will be deleted."}),(0,l.jsx)("li",{children:"You will not be able to recover these projects after deletion."})]})]}),confirmButtonText:"Delete",confirmButtonClass:"bg-red-500 hover:bg-red-600 cursor-pointer",onConfirm:()=>{let e=(null==v?void 0:v.getSelectedRowModel().rows.map(e=>e.original.id))||[];Q.mutate(e)}}),K(!0)}:void 0,children:(0,l.jsx)(o.hJ0,{className:"h-4 w-4"})})]})]}),(0,l.jsx)("div",{className:" mx-auto",children:(0,l.jsx)(h.x,{columns:b,data:t,globalFilter:g,setGlobalFilter:x,onTableInit:e=>{f(e),Object.keys(y).length>0&&e.setColumnVisibility(y)},columnVisibility:y,setColumnVisibility:e=>{j(e)},onRowSelectionChange:e=>{let r=Object.keys(e),l=t.filter((e,t)=>r.includes(t.toString())),s=l.length>0&&l.every(e=>"deployed"===e.status.toLowerCase());T(r.length>0),Y(1===r.length),H(s)}})})]}),I&&(0,l.jsx)(d.R,{showModal:P,onClose:()=>K(!1),onConfirm:I.onConfirm,title:I.title,description:I.description,confirmButtonText:I.confirmButtonText,confirmButtonClass:I.confirmButtonClass}),(0,l.jsx)(u.m,{showModal:B,selectedProject:V,onClose:()=>O(!1),onShare:()=>{O(!1)}})]})};var F=r(25784),P=r(19373),K=r(57799);let I=async()=>{let{data:e}=await F.A.get("/projects");return e.projects};function R(){let{user:e}=(0,A.A)(),{data:t,isLoading:r,isError:s}=(0,P.I)({queryKey:["projects",null==e?void 0:e.id],queryFn:I,enabled:!!(null==e?void 0:e.id)});return r||!t?(0,l.jsx)(K.A,{}):s?(0,l.jsx)("p",{className:"text-red-500",children:"Error loading data"}):(0,l.jsx)(E,{data:t})}},59362:(e,t,r)=>{"use strict";r.d(t,{F0:()=>u,pe:()=>s});let{Axios:l,AxiosError:s,CanceledError:a,isCancel:o,CancelToken:i,VERSION:n,all:c,Cancel:d,isAxiosError:u,spread:h,toFormData:m,AxiosHeaders:p,HttpStatusCode:g,formToJSON:x,getAdapter:y,mergeConfig:j}=r(23464).A},88472:(e,t,r)=>{Promise.resolve().then(r.bind(r,7578))},92657:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});let l=(0,r(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[2150,6711,9204,635,1445,6967,6903,4601,4277,6874,556,3481,1467,6539,6268,4695,9660,3465,8441,1684,7358],()=>t(88472)),_N_E=e.O()}]);