{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/axios.ts"], "sourcesContent": ["import axios from \"axios\";\r\n\r\nconst axiosInstance = axios.create({\r\n  baseURL: process.env.NEXT_PUBLIC_API_URL || \"http://localhost:4000/api\",\r\n  headers: {\r\n    \"Content-Type\": \"application/json\",\r\n  },\r\n  withCredentials: true,\r\n});\r\n\r\n// Add request interceptor to handle auth token\r\naxiosInstance.interceptors.request.use(\r\n  (config) => {\r\n    // You can add auth token here if needed\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Add response interceptor to handle errors\r\naxiosInstance.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    if (error.code === \"ERR_NETWORK\") {\r\n      console.error(\r\n        \"Network error - Please check if the backend server is running\"\r\n      );\r\n    }\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default axiosInstance;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,gBAAgB,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACjC,SAAS,iEAAmC;IAC5C,SAAS;QACP,gBAAgB;IAClB;IACA,iBAAiB;AACnB;AAEA,+CAA+C;AAC/C,cAAc,YAAY,CAAC,OAAO,CAAC,GAAG,CACpC,CAAC;IACC,wCAAwC;IACxC,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,4CAA4C;AAC5C,cAAc,YAAY,CAAC,QAAQ,CAAC,GAAG,CACrC,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,IAAI,KAAK,eAAe;QAChC,QAAQ,KAAK,CACX;IAEJ;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/hooks/useAuth.tsx"], "sourcesContent": ["import {\r\n  setAuthenticatedUser,\r\n  setAuthError,\r\n  setAuthLoading,\r\n  setUnauthenticated,\r\n} from \"@/redux/slices/authSlice\";\r\nimport { RootState } from \"@/redux/store\";\r\nimport { UserSession } from \"@/types/authTypes\";\r\nimport { AxiosError, isAxiosError } from \"axios\";\r\nimport axios from \"@/lib/axios\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useEffect } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\n\r\nconst useAuth = (options?: { skipFetchUser?: boolean }) => {\r\n  const dispatch = useDispatch();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const { status, user, error } = useSelector((state: RootState) => state.auth);\r\n\r\n  const fetchUserData = async () => {\r\n    try {\r\n      dispatch(setAuthLoading());\r\n      const response = await axios.get(`/users/me`);\r\n      const userData: UserSession = response.data;\r\n      dispatch(setAuthenticatedUser(userData));\r\n    } catch (error) {\r\n      // Handle errors, especially 401 Unauthorized\r\n      dispatch(setUnauthenticated());\r\n\r\n      if (isAxiosError(error)) {\r\n        console.error(\r\n          \"Auth error:\",\r\n          error.response?.status,\r\n          error.response?.data\r\n        );\r\n\r\n        // If error is 401 Unauthorized (including expired token)\r\n        if (error.response?.status === 401) {\r\n          // Check if we're on a form-test route\r\n          if (pathname.startsWith(\"/form-submission\")) {\r\n            // Don't redirect for form-test routes, let the component handle it\r\n            return;\r\n          }\r\n          router.push(\"/\");\r\n        } else {\r\n          // For other errors\r\n          dispatch(\r\n            setAuthError(error.response?.data?.message || error.message)\r\n          );\r\n        }\r\n      } else {\r\n        dispatch(\r\n          setAuthError(\r\n            error instanceof Error\r\n              ? error.message\r\n              : \"An unknown error occurred.\"\r\n          )\r\n        );\r\n      }\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (!options?.skipFetchUser) {\r\n      fetchUserData();\r\n    }\r\n  }, [options?.skipFetchUser]);\r\n\r\n  // Add event listener for storage changes to handle logout across tabs\r\n  useEffect(() => {\r\n    const handleStorageChange = (e: StorageEvent) => {\r\n      if (e.key === \"logout\" && e.newValue === \"true\") {\r\n        dispatch(setUnauthenticated());\r\n        // Check if we're on a form-test route\r\n        if (pathname.startsWith(\"/form-submission\")) {\r\n          // For form-test routes, redirect to the sign-in page of the same form\r\n          const hashedId = pathname.split(\"/\")[2]; // Extract hashedId from path\r\n          if (hashedId) {\r\n            router.push(`/form-submission/${hashedId}/sign-in`);\r\n          } else {\r\n            router.push(\"/\");\r\n          }\r\n        } else {\r\n          router.push(\"/\");\r\n        }\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"storage\", handleStorageChange);\r\n\r\n    return () => {\r\n      window.removeEventListener(\"storage\", handleStorageChange);\r\n    };\r\n  }, [dispatch, router, pathname]);\r\n\r\n  const refreshAuthState = () => {\r\n    fetchUserData();\r\n  };\r\n\r\n  const signin = async (\r\n    data: { email: string; password: string },\r\n    onSuccess?: () => void,\r\n    onError?: (errorType?: string) => void\r\n  ) => {\r\n    try {\r\n      await axios.post(`/users/login`, data);\r\n      await fetchUserData();\r\n      onSuccess?.();\r\n    } catch (error) {\r\n      if (error instanceof AxiosError) {\r\n        const errorType = error.response?.data?.errorType;\r\n        onError?.(errorType);\r\n      } else {\r\n        onError?.();\r\n      }\r\n    }\r\n  };\r\n\r\n  const logout = async () => {\r\n    try {\r\n      await axios.post(`/users/logout`);\r\n      // Notify other tabs about logout\r\n      localStorage.setItem(\"logout\", \"true\");\r\n      // Remove the flag immediately to ensure future logout events still trigger\r\n      setTimeout(() => localStorage.removeItem(\"logout\"), 100);\r\n    } finally {\r\n      dispatch(setUnauthenticated());\r\n      // Check if we're on a form-test route\r\n      if (pathname.startsWith(\"/form-submission\")) {\r\n        // For form-test routes, redirect to the sign-in page of the same form\r\n        const hashedId = pathname.split(\"/\")[2]; // Extract hashedId from path\r\n        if (hashedId) {\r\n          router.push(`/form-submission/${hashedId}/sign-in`);\r\n        } else {\r\n          router.push(\"/\");\r\n        }\r\n      } else {\r\n        router.push(\"/\");\r\n      }\r\n    }\r\n  };\r\n\r\n  return {\r\n    status,\r\n    user,\r\n    error,\r\n    isAuthenticated: status === \"authenticated\",\r\n    isLoading: status === \"loading\",\r\n    refreshAuthState,\r\n    signin,\r\n    logout,\r\n  };\r\n};\r\n\r\nexport { useAuth };\r\n"], "names": [], "mappings": ";;;AAAA;AAQA;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,UAAU,CAAC;IACf,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAqB,MAAM,IAAI;IAE5E,MAAM,gBAAgB;QACpB,IAAI;YACF,SAAS,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD;YACtB,MAAM,WAAW,MAAM,4GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC;YAC5C,MAAM,WAAwB,SAAS,IAAI;YAC3C,SAAS,CAAA,GAAA,6HAAA,CAAA,uBAAoB,AAAD,EAAE;QAChC,EAAE,OAAO,OAAO;YACd,6CAA6C;YAC7C,SAAS,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD;YAE1B,IAAI,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;gBACvB,QAAQ,KAAK,CACX,eACA,MAAM,QAAQ,EAAE,QAChB,MAAM,QAAQ,EAAE;gBAGlB,yDAAyD;gBACzD,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;oBAClC,sCAAsC;oBACtC,IAAI,SAAS,UAAU,CAAC,qBAAqB;wBAC3C,mEAAmE;wBACnE;oBACF;oBACA,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,mBAAmB;oBACnB,SACE,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO;gBAE/D;YACF,OAAO;gBACL,SACE,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EACT,iBAAiB,QACb,MAAM,OAAO,GACb;YAGV;QACF;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS,eAAe;YAC3B;QACF;IACF,GAAG;QAAC,SAAS;KAAc;IAE3B,sEAAsE;IACtE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB,CAAC;YAC3B,IAAI,EAAE,GAAG,KAAK,YAAY,EAAE,QAAQ,KAAK,QAAQ;gBAC/C,SAAS,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD;gBAC1B,sCAAsC;gBACtC,IAAI,SAAS,UAAU,CAAC,qBAAqB;oBAC3C,sEAAsE;oBACtE,MAAM,WAAW,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,6BAA6B;oBACtE,IAAI,UAAU;wBACZ,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,SAAS,QAAQ,CAAC;oBACpD,OAAO;wBACL,OAAO,IAAI,CAAC;oBACd;gBACF,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QAEnC,OAAO;YACL,OAAO,mBAAmB,CAAC,WAAW;QACxC;IACF,GAAG;QAAC;QAAU;QAAQ;KAAS;IAE/B,MAAM,mBAAmB;QACvB;IACF;IAEA,MAAM,SAAS,OACb,MACA,WACA;QAEA,IAAI;YACF,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,EAAE;YACjC,MAAM;YACN;QACF,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,8IAAA,CAAA,aAAU,EAAE;gBAC/B,MAAM,YAAY,MAAM,QAAQ,EAAE,MAAM;gBACxC,UAAU;YACZ,OAAO;gBACL;YACF;QACF;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;YAChC,iCAAiC;YACjC,aAAa,OAAO,CAAC,UAAU;YAC/B,2EAA2E;YAC3E,WAAW,IAAM,aAAa,UAAU,CAAC,WAAW;QACtD,SAAU;YACR,SAAS,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD;YAC1B,sCAAsC;YACtC,IAAI,SAAS,UAAU,CAAC,qBAAqB;gBAC3C,sEAAsE;gBACtE,MAAM,WAAW,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,6BAA6B;gBACtE,IAAI,UAAU;oBACZ,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,SAAS,QAAQ,CAAC;gBACpD,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA,iBAAiB,WAAW;QAC5B,WAAW,WAAW;QACtB;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/root/Navbar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useAuth } from \"@/hooks/useAuth\";\r\nimport axios from \"@/lib/axios\";\r\nimport { log } from \"console\";\r\nimport { Globe, LogOut } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport React, { useEffect, useRef, useState } from \"react\";\r\nimport { TbLayoutDashboardFilled } from \"react-icons/tb\";\r\n\r\ntype NavbarProps = {\r\n  toggleSidebar: () => void;\r\n  isSidebarOpen: boolean;\r\n  navbarRef: React.RefObject<HTMLElement | null>;\r\n};\r\n\r\nconst Navbar: React.FC<NavbarProps> = ({ toggleSidebar, navbarRef }) => {\r\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false);\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      if (\r\n        dropdownRef.current &&\r\n        !dropdownRef.current.contains(event.target as Node)\r\n      ) {\r\n        setIsDropdownOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => document.removeEventListener(\"mousedown\", handleClickOutside);\r\n  }, []);\r\n\r\n  const { user, logout } = useAuth();\r\n\r\n  return (\r\n    <header className=\"bg-primary-800 p-4 sticky top-0 z-40\" ref={navbarRef}>\r\n      <div className=\" flex items-center justify-between\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <button\r\n            onClick={toggleSidebar}\r\n            className=\"laptop:hidden text-neutral-100 p-2 rounded hover:bg-primary-700 \"\r\n          >\r\n            <TbLayoutDashboardFilled size={24} />\r\n          </button>\r\n\r\n          <Link\r\n            href={\"/dashboard\"}\r\n            className=\"text-neutral-100 text-2xl font-bold cursor-pointer hover:text-neutral-300 transition-all ease-in-out\"\r\n          >\r\n            Data Analysis\r\n          </Link>\r\n        </div>\r\n\r\n        <div className=\"relative\" ref={dropdownRef}>\r\n          <button\r\n            onClick={() => setIsDropdownOpen(!isDropdownOpen)}\r\n            className=\"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-neutral-100 font-semibold cursor-pointer\"\r\n          >\r\n            {user?.name[0].toUpperCase()}\r\n          </button>\r\n          {isDropdownOpen && (\r\n            <div className=\"absolute right-0 mt-2 w-64 bg-neutral-100 rounded-md shadow-lg p-4 flex flex-col gap-2 \">\r\n              <div className=\"flex flex-col gap-4\">\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <div className=\"size-10 bg-orange-500 rounded-full flex items-center justify-center text-neutral-100 text-xl font-semibold shrink-0\">\r\n                    {user?.name[0].toUpperCase()}\r\n                  </div>\r\n                  <div className=\"flex flex-col min-w-0\">\r\n                    <div className=\"font-medium capitalize\">{user?.name}</div>\r\n                    <div className=\"text-sm text-neutral-700 truncate\">\r\n                      {user?.email}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <Link\r\n                  href=\"/account/profile\"\r\n                  className=\"btn-primary\"\r\n                  onClick={() => {\r\n                    setIsDropdownOpen(false);\r\n                  }}\r\n                >\r\n                  Profile\r\n                </Link>\r\n              </div>\r\n              <hr className=\"border-neutral-400\" />\r\n\r\n              <div className=\"flex flex-col\">\r\n                <Link\r\n                  onClick={() => setIsDropdownOpen(false)}\r\n                  href={\"/terms\"}\r\n                  className=\"text-sm text-neutral-700 hover:bg-neutral-700/10 px-4 py-1 rounded-md\"\r\n                >\r\n                  Terms of Service\r\n                </Link>\r\n                <Link\r\n                  onClick={() => setIsDropdownOpen(false)}\r\n                  href={\"/policy\"}\r\n                  className=\"block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-200\"\r\n                >\r\n                  Privacy Policy\r\n                </Link>\r\n              </div>\r\n              <hr className=\"border-neutral-400\" />\r\n              <Link\r\n                href=\"#\"\r\n                className=\"flex items-center px-4 py-2 gap-2 text-sm rounded-md text-neutral-700 hover:bg-neutral-700/10 transition-all duration-300\"\r\n              >\r\n                <Globe size={16} />\r\n                Language\r\n              </Link>\r\n              <hr className=\"border-neutral-400\" />\r\n\r\n              <button\r\n                onClick={logout}\r\n                className=\"flex items-center text-neutral-700 gap-2 px-4 py-2 hover:bg-neutral-700/10 rounded-md active:scale-95 transition-all duration-300 w-full cursor-pointer \"\r\n              >\r\n                <LogOut size={16} />\r\n                Logout\r\n              </button>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Navbar;\r\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AAAA;AACA;AACA;AACA;AACA;AATA;;;;;;;;AAiBA,MAAM,SAAgC,CAAC,EAAE,aAAa,EAAE,SAAS,EAAE;IACjE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IACE,YAAY,OAAO,IACnB,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAC1C;gBACA,kBAAkB;YACpB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iHAAA,CAAA,UAAO,AAAD;IAE/B,qBACE,8OAAC;QAAO,WAAU;QAAuC,KAAK;kBAC5D,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,8IAAA,CAAA,0BAAuB;gCAAC,MAAM;;;;;;;;;;;sCAGjC,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAM;4BACN,WAAU;sCACX;;;;;;;;;;;;8BAKH,8OAAC;oBAAI,WAAU;oBAAW,KAAK;;sCAC7B,8OAAC;4BACC,SAAS,IAAM,kBAAkB,CAAC;4BAClC,WAAU;sCAET,MAAM,IAAI,CAAC,EAAE,CAAC;;;;;;wBAEhB,gCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,MAAM,IAAI,CAAC,EAAE,CAAC;;;;;;8DAEjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAA0B,MAAM;;;;;;sEAC/C,8OAAC;4DAAI,WAAU;sEACZ,MAAM;;;;;;;;;;;;;;;;;;sDAKb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,SAAS;gDACP,kBAAkB;4CACpB;sDACD;;;;;;;;;;;;8CAIH,8OAAC;oCAAG,WAAU;;;;;;8CAEd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,SAAS,IAAM,kBAAkB;4CACjC,MAAM;4CACN,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,SAAS,IAAM,kBAAkB;4CACjC,MAAM;4CACN,WAAU;sDACX;;;;;;;;;;;;8CAIH,8OAAC;oCAAG,WAAU;;;;;;8CACd,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC,oMAAA,CAAA,QAAK;4CAAC,MAAM;;;;;;wCAAM;;;;;;;8CAGrB,8OAAC;oCAAG,WAAU;;;;;;8CAEd,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,SAAM;4CAAC,MAAM;;;;;;wCAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpC;uCAEe", "debugId": null}}, {"offset": {"line": 577, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/api/projects.ts"], "sourcesContent": ["import axios from \"@/lib/axios\";\r\nimport { Project } from \"@/types\";\r\n\r\nconst fetchProjectById = async ({ projectId }: { projectId: number }) => {\r\n  const { data } = await axios.get(`/projects/${projectId}`);\r\n  return data.project;\r\n};\r\n\r\nconst createProjectFromTemplate = async (dataToSend: {\r\n  templateId: number;\r\n  name: string;\r\n  description: string;\r\n  sector: string;\r\n  country: string;\r\n}) => {\r\n  const { data } = await axios.post(`/projects/from-template`, dataToSend);\r\n  return data;\r\n};\r\n\r\n//Fetch all projects for the current user\r\nconst fetchProjects = async (): Promise<Project[]> => {\r\n  try {\r\n    const { data } = await axios.get(`/projects`);\r\n    return data.projects;\r\n  } catch (error) {\r\n    console.error(\"Error fetching projects:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Delete project\r\nconst deleteProject = async (projectId: number) => {\r\n  const { data } = await axios.delete(`/projects/delete/${projectId}`);\r\n  return data;\r\n};\r\n\r\n// Delete multiple projects\r\nconst deleteMultipleProjects = async (projectIds: number[]) => {\r\n  try {\r\n    const { data } = await axios.delete(`/projects/delete-multiple`, {\r\n      data: { projectIds },\r\n    });\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting multiple projects:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n//Archive project\r\nconst archiveProject = async (projectId: number) => {\r\n  try {\r\n    const { data } = await axios.patch(`/projects/change-status/${projectId}`, {\r\n      status: \"archived\",\r\n    });\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error archiving project:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n//Deploy project\r\nconst deployProject = async (\r\n  projectId: number,\r\n  isUnarchive: boolean = false\r\n) => {\r\n  try {\r\n    const { data } = await axios.patch(`/projects/change-status/${projectId}`, {\r\n      status: \"deployed\",\r\n    });\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deploying project:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Archive multiple projects\r\nconst archiveMultipleProjects = async (projectIds: number[]) => {\r\n  try {\r\n    const { data } = await axios.patch(`/projects/update-many-status`, {\r\n      projectIds,\r\n      status: \"archived\",\r\n    });\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error archiving multiple projects:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Check if user exists by email\r\nconst checkUserExists = async (email: string) => {\r\n  try {\r\n    const { data } = await axios.post(`/users/check-email`, { email });\r\n    return data;\r\n  } catch (error: any) {\r\n    // Format error message consistently\r\n    const errorMessage =\r\n      typeof error.response?.data?.message === \"object\"\r\n        ? JSON.stringify(error.response?.data?.message)\r\n        : error.response?.data?.message ||\r\n          error.message ||\r\n          \"Failed to check user\";\r\n\r\n    throw new Error(errorMessage);\r\n  }\r\n};\r\n\r\n// Add user to project by email\r\nconst addProjectUser = async ({\r\n  projectId,\r\n  email,\r\n  permissions,\r\n}: {\r\n  projectId: number;\r\n  email: string;\r\n  permissions: Record<string, boolean>;\r\n}) => {\r\n  try {\r\n    // First check if the user exists\r\n    const userData = await checkUserExists(email);\r\n\r\n    if (!userData || !userData.success) {\r\n      throw new Error(userData?.message || \"User not found\");\r\n    }\r\n\r\n    // Now use the user ID to add them to the project\r\n    const { data } = await axios.post(`/project-users`, {\r\n      userId: userData.user.id,\r\n      projectId,\r\n      permission: permissions,\r\n    });\r\n\r\n    return data;\r\n  } catch (error: any) {\r\n    console.error(\"Error adding user to project:\", error);\r\n    // Format error message as a string\r\n    const errorMessage =\r\n      typeof error.response?.data?.message === \"object\"\r\n        ? JSON.stringify(error.response?.data?.message)\r\n        : error.response?.data?.message ||\r\n          error.message ||\r\n          \"Failed to add user\";\r\n\r\n    throw new Error(errorMessage);\r\n  }\r\n};\r\n\r\n// Fetch all users for a specific project\r\nconst fetchProjectUsers = async (projectId: number) => {\r\n  try {\r\n    const { data } = await axios.get(`/project-users/${projectId}`);\r\n    return data.data.AllUser;\r\n  } catch (error: any) {\r\n    console.error(\"Error fetching project users:\", error);\r\n    const errorMessage =\r\n      typeof error.response?.data?.message === \"object\"\r\n        ? JSON.stringify(error.response?.data?.message)\r\n        : error.response?.data?.message ||\r\n          error.message ||\r\n          \"Failed to fetch project users\";\r\n\r\n    throw new Error(errorMessage);\r\n  }\r\n};\r\n\r\n// Create answer submission\r\nconst createAnswerSubmission = async (\r\n  answers: {\r\n    projectId: number;\r\n    questionId: number;\r\n    answerType: string;\r\n    value?: string | number | boolean;\r\n    imageUrl?: string;\r\n    questionOptionId?: number | number[];\r\n    isOtherOption?: boolean;\r\n  }[]\r\n) => {\r\n  try {\r\n    const { data } = await axios.post(`/answers/multiple`, answers);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error creating answer submission:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Update answer submission\r\nconst updateAnswerSubmission = async (\r\n  projectId: number,\r\n  submissionId: number,\r\n  answers: {\r\n    projectId: number;\r\n    questionId: number;\r\n    answerType: string;\r\n    value?: string | number | boolean;\r\n    imageUrl?: string;\r\n    questionOptionId?: number | number[];\r\n    isOtherOption?: boolean;\r\n  }[]\r\n) => {\r\n  try {\r\n    const { data } = await axios.patch(`/form-submissions/${projectId}/${submissionId}`, { answers });\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error updating answer submission:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n\r\n\r\n\r\nexport {\r\n  fetchProjectById,\r\n  fetchProjects,\r\n  deleteProject,\r\n  deleteMultipleProjects,\r\n  archiveMultipleProjects,\r\n  createProjectFromTemplate,\r\n  archiveProject,\r\n  deployProject,\r\n  addProjectUser,\r\n  checkUserExists,\r\n  fetchProjectUsers,\r\n  createAnswerSubmission,\r\n  updateAnswerSubmission,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;AAGA,MAAM,mBAAmB,OAAO,EAAE,SAAS,EAAyB;IAClE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW;IACzD,OAAO,KAAK,OAAO;AACrB;AAEA,MAAM,4BAA4B,OAAO;IAOvC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,uBAAuB,CAAC,EAAE;IAC7D,OAAO;AACT;AAEA,yCAAyC;AACzC,MAAM,gBAAgB;IACpB,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC;QAC5C,OAAO,KAAK,QAAQ;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEA,iBAAiB;AACjB,MAAM,gBAAgB,OAAO;IAC3B,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,iBAAiB,EAAE,WAAW;IACnE,OAAO;AACT;AAEA,2BAA2B;AAC3B,MAAM,yBAAyB,OAAO;IACpC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,yBAAyB,CAAC,EAAE;YAC/D,MAAM;gBAAE;YAAW;QACrB;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM;IACR;AACF;AAEA,iBAAiB;AACjB,MAAM,iBAAiB,OAAO;IAC5B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,wBAAwB,EAAE,WAAW,EAAE;YACzE,QAAQ;QACV;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEA,gBAAgB;AAChB,MAAM,gBAAgB,OACpB,WACA,cAAuB,KAAK;IAE5B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,wBAAwB,EAAE,WAAW,EAAE;YACzE,QAAQ;QACV;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEA,4BAA4B;AAC5B,MAAM,0BAA0B,OAAO;IACrC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAE;YACjE;YACA,QAAQ;QACV;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,MAAM;IACR;AACF;AAEA,gCAAgC;AAChC,MAAM,kBAAkB,OAAO;IAC7B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,EAAE;YAAE;QAAM;QAChE,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,oCAAoC;QACpC,MAAM,eACJ,OAAO,MAAM,QAAQ,EAAE,MAAM,YAAY,WACrC,KAAK,SAAS,CAAC,MAAM,QAAQ,EAAE,MAAM,WACrC,MAAM,QAAQ,EAAE,MAAM,WACtB,MAAM,OAAO,IACb;QAEN,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,+BAA+B;AAC/B,MAAM,iBAAiB,OAAO,EAC5B,SAAS,EACT,KAAK,EACL,WAAW,EAKZ;IACC,IAAI;QACF,iCAAiC;QACjC,MAAM,WAAW,MAAM,gBAAgB;QAEvC,IAAI,CAAC,YAAY,CAAC,SAAS,OAAO,EAAE;YAClC,MAAM,IAAI,MAAM,UAAU,WAAW;QACvC;QAEA,iDAAiD;QACjD,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE;YAClD,QAAQ,SAAS,IAAI,CAAC,EAAE;YACxB;YACA,YAAY;QACd;QAEA,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,mCAAmC;QACnC,MAAM,eACJ,OAAO,MAAM,QAAQ,EAAE,MAAM,YAAY,WACrC,KAAK,SAAS,CAAC,MAAM,QAAQ,EAAE,MAAM,WACrC,MAAM,QAAQ,EAAE,MAAM,WACtB,MAAM,OAAO,IACb;QAEN,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,yCAAyC;AACzC,MAAM,oBAAoB,OAAO;IAC/B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,WAAW;QAC9D,OAAO,KAAK,IAAI,CAAC,OAAO;IAC1B,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM,eACJ,OAAO,MAAM,QAAQ,EAAE,MAAM,YAAY,WACrC,KAAK,SAAS,CAAC,MAAM,QAAQ,EAAE,MAAM,WACrC,MAAM,QAAQ,EAAE,MAAM,WACtB,MAAM,OAAO,IACb;QAEN,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,2BAA2B;AAC3B,MAAM,yBAAyB,OAC7B;IAUA,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC,EAAE;QACvD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM;IACR;AACF;AAEA,2BAA2B;AAC3B,MAAM,yBAAyB,OAC7B,WACA,cACA;IAUA,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,kBAAkB,EAAE,UAAU,CAAC,EAAE,cAAc,EAAE;YAAE;QAAQ;QAC/F,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 743, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/data/SidebarItems.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { BsRocketTakeoffFill } from \"react-icons/bs\";\r\nimport { MdEditDocument } from \"react-icons/md\";\r\nimport { FaArchive } from \"react-icons/fa\";\r\nimport { LuLibrary } from \"react-icons/lu\";\r\nimport { MdFileCopy } from \"react-icons/md\";\r\nimport axios from \"axios\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport { Project } from \"@/types\";\r\nimport { fetchProjects } from \"@/lib/api/projects\";\r\nimport { useAuth } from \"@/hooks/useAuth\";\r\n\r\ntype SubItem = {\r\n  id: number;\r\n  label: string;\r\n  href: string;\r\n};\r\n\r\ntype NavItem = {\r\n  id: number;\r\n  icon: React.ElementType;\r\n  label: string;\r\n  count: number;\r\n  href: string;\r\n  category: \"project\" | \"library\";\r\n  subItems?: SubItem[];\r\n};\r\n\r\nconst useNavItems = () => {\r\n  const { user } = useAuth();\r\n\r\n  const {\r\n    data: projectsData,\r\n    isLoading: projectsLoading,\r\n    isError: projectsError,\r\n  } = useQuery<Project[]>({\r\n    queryKey: [\"projects\", user?.id],\r\n    queryFn: fetchProjects,\r\n    enabled: !!user?.id,\r\n  });\r\n\r\n  // Initialize separate arrays for each status\r\n  let deployedProjects: Project[] = [];\r\n  let draftStatusProjects: Project[] = [];\r\n  let archivedProjects: Project[] = [];\r\n\r\n  if (!projectsLoading && projectsData) {\r\n    deployedProjects = projectsData.filter((projectData) => projectData.status === \"deployed\");\r\n    draftStatusProjects = projectsData.filter((projectData) => projectData.status === \"draft\");\r\n    archivedProjects = projectsData.filter((projectData) => projectData.status === \"archived\");\r\n  }\r\n\r\n  const navItems: NavItem[] = [\r\n    {\r\n      id: 1,\r\n      icon: BsRocketTakeoffFill,\r\n      label: \"Deployed\",\r\n      count: deployedProjects.length || 0,\r\n      href: \"/dashboard/deployed\",\r\n      category: \"project\",\r\n    },\r\n    {\r\n      id: 2,\r\n      icon: MdEditDocument,\r\n      label: \"Draft\",\r\n      count: draftStatusProjects.length || 0,\r\n      href: \"/dashboard/draft\",\r\n      category: \"project\",\r\n    },\r\n    {\r\n      id: 3,\r\n      icon: FaArchive,\r\n      label: \"Archived\",\r\n      count: archivedProjects.length || 0,\r\n      href: \"/dashboard/archived\",\r\n      category: \"project\",\r\n    },\r\n    {\r\n      id: 4,\r\n      icon: LuLibrary,\r\n      label: \"My Library\",\r\n      count: 0,\r\n      href: \"/library\",\r\n      category: \"library\",\r\n    },\r\n    {\r\n      id: 5,\r\n      icon: MdFileCopy,\r\n      label: \"Collections\",\r\n      count: 0,\r\n      href: \"/library/#\",\r\n      category: \"library\",\r\n    },\r\n  ];\r\n\r\n  return { navItems, deployedProjects, draftStatusProjects, archivedProjects };\r\n};\r\n\r\nexport default useNavItems;\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AAGA;AAEA;AACA;AAXA;;;;;;;;;AA6BA,MAAM,cAAc;IAClB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,iHAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,EACJ,MAAM,YAAY,EAClB,WAAW,eAAe,EAC1B,SAAS,aAAa,EACvB,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAa;QACtB,UAAU;YAAC;YAAY,MAAM;SAAG;QAChC,SAAS,sHAAA,CAAA,gBAAa;QACtB,SAAS,CAAC,CAAC,MAAM;IACnB;IAEA,6CAA6C;IAC7C,IAAI,mBAA8B,EAAE;IACpC,IAAI,sBAAiC,EAAE;IACvC,IAAI,mBAA8B,EAAE;IAEpC,IAAI,CAAC,mBAAmB,cAAc;QACpC,mBAAmB,aAAa,MAAM,CAAC,CAAC,cAAgB,YAAY,MAAM,KAAK;QAC/E,sBAAsB,aAAa,MAAM,CAAC,CAAC,cAAgB,YAAY,MAAM,KAAK;QAClF,mBAAmB,aAAa,MAAM,CAAC,CAAC,cAAgB,YAAY,MAAM,KAAK;IACjF;IAEA,MAAM,WAAsB;QAC1B;YACE,IAAI;YACJ,MAAM,8IAAA,CAAA,sBAAmB;YACzB,OAAO;YACP,OAAO,iBAAiB,MAAM,IAAI;YAClC,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM,8IAAA,CAAA,iBAAc;YACpB,OAAO;YACP,OAAO,oBAAoB,MAAM,IAAI;YACrC,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM,8IAAA,CAAA,YAAS;YACf,OAAO;YACP,OAAO,iBAAiB,MAAM,IAAI;YAClC,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM,8IAAA,CAAA,YAAS;YACf,OAAO;YACP,OAAO;YACP,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM,8IAAA,CAAA,aAAU;YAChB,OAAO;YACP,OAAO;YACP,MAAM;YACN,UAAU;QACZ;KACD;IAED,OAAO;QAAE;QAAU;QAAkB;QAAqB;IAAiB;AAC7E;uCAEe", "debugId": null}}, {"offset": {"line": 837, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/encodeDecode.ts"], "sourcesContent": ["import Hashids from \"hashids\";\r\n\r\nconst salt = process.env.SALT || \"rushan-salt\";\r\n\r\nconst hashids = new Hashids(salt, 12);\r\n\r\nconst encode = (id: number) => {\r\n  return hashids.encode(id);\r\n};\r\n\r\nconst decode = (hash: string) => {\r\n  const decodedNumberLike = hashids.decode(hash)[0];\r\n  const decoded =\r\n    typeof decodedNumberLike === \"bigint\"\r\n      ? decodedNumberLike < Number.MAX_SAFE_INTEGER\r\n        ? Number(decodedNumberLike)\r\n        : null\r\n      : typeof decodedNumberLike === \"number\"\r\n      ? decodedNumberLike\r\n      : null;\r\n  return decoded;\r\n};\r\n\r\nexport { encode, decode };\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,OAAO,QAAQ,GAAG,CAAC,IAAI,IAAI;AAEjC,MAAM,UAAU,IAAI,yIAAA,CAAA,UAAO,CAAC,MAAM;AAElC,MAAM,SAAS,CAAC;IACd,OAAO,QAAQ,MAAM,CAAC;AACxB;AAEA,MAAM,SAAS,CAAC;IACd,MAAM,oBAAoB,QAAQ,MAAM,CAAC,KAAK,CAAC,EAAE;IACjD,MAAM,UACJ,OAAO,sBAAsB,WACzB,oBAAoB,OAAO,gBAAgB,GACzC,OAAO,qBACP,OACF,OAAO,sBAAsB,WAC7B,oBACA;IACN,OAAO;AACT", "debugId": null}}, {"offset": {"line": 860, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/root/Sidebar.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { RiFileList3Fill } from \"react-icons/ri\";\r\nimport { LuLibrary } from \"react-icons/lu\";\r\nimport Link from \"next/link\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { showCreateProjectModal } from \"@/redux/slices/createProjectSlice\";\r\nimport { showCreateLibraryModal } from \"@/redux/slices/createLibrarySlice\";\r\nimport useNavItems from \"../data/SidebarItems\";\r\nimport Spinner from \"../general/Spinner\";\r\nimport { encode } from \"@/lib/encodeDecode\";\r\nimport { Project } from \"@/types\";\r\n\r\ninterface SidebarProps {\r\n  isOpen: boolean;\r\n  toggleSidebar: () => void;\r\n  topOffset: number;\r\n  style?: React.CSSProperties;\r\n  onNewProject?: () => void;\r\n}\r\n\r\ninterface SidebarItemProps {\r\n  href: string;\r\n  label: string;\r\n  icon: React.ElementType;\r\n  count: number;\r\n}\r\n\r\nconst SidebarItem: React.FC<SidebarItemProps> = ({\r\n  href,\r\n  label,\r\n  icon: Icon,\r\n  count,\r\n}) => {\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const { deployedProjects, draftStatusProjects, archivedProjects } =\r\n    useNavItems();\r\n  const [isOpen, setIsOpen] = useState(false);\r\n\r\n  const handleClick = (e: React.MouseEvent) => {\r\n    if (label === \"Deployed\" || label === \"Draft\" || label === \"Archived\") {\r\n      if (count > 0) {\r\n        e.preventDefault();\r\n        setIsOpen(!isOpen);\r\n      } else {\r\n        e.preventDefault();\r\n        router.push(`${href}/not-available`);\r\n      }\r\n    }\r\n    // For library categories\r\n    // else if (label === \"My Library\" || label === \"Collections\") {\r\n    //   if (count === 0) {\r\n    //     e.preventDefault();\r\n    //     // Navigate to the \"not available\" page for library\r\n    //     router.push(/library/not-available);\r\n    //   }\r\n    // }\r\n  };\r\n\r\n  const handleProjectClick = (projectId: number) => {\r\n    const encryptedProjectId = encode(projectId);\r\n    router.push(`/project/${encryptedProjectId}/overview`);\r\n  };\r\n\r\n  const getProjects = (): Project[] => {\r\n    switch (label) {\r\n      case \"Draft\":\r\n        return draftStatusProjects;\r\n      case \"Deployed\":\r\n        return deployedProjects;\r\n      case \"Archived\":\r\n        return archivedProjects;\r\n      default:\r\n        return [];\r\n    }\r\n  };\r\n\r\n  const getProjectPath = (projectId: number) => {\r\n    const encryptedProjectId = encode(projectId);\r\n    return `/project/${encryptedProjectId}/overview`;\r\n  };\r\n\r\n  const currentProjects = getProjects();\r\n\r\n  return (\r\n    <li>\r\n      <Link\r\n        href={href}\r\n        onClick={handleClick}\r\n        className=\"flex items-center px-4 py-3 hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300 ease-in-out rounded-md text-neutral-800\"\r\n      >\r\n        <Icon className=\"mr-2\" size={20} />\r\n        <span className=\"text-sm font-bold\">{label}</span>\r\n        <span className=\"ml-auto bg-neutral-200 text-neutral-700 rounded-full px-2 py-0.5 text-xs\">\r\n          {count}\r\n        </span>\r\n      </Link>\r\n\r\n      {isOpen && currentProjects.length > 0 && (\r\n        <div className=\"ml-6 mt-1 space-y-1\">\r\n          {currentProjects.map((project) => (\r\n            <div\r\n              key={project.id}\r\n              onClick={() => handleProjectClick(project.id)}\r\n              className={`flex items-center px-4 py-2 text-sm hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300 ease-in-out rounded-md cursor-pointer ${\r\n                pathname === getProjectPath(project.id)\r\n                  ? \"bg-primary-500 text-neutral-100\"\r\n                  : \"\"\r\n              }`}\r\n            >\r\n              {project.name}\r\n            </div>\r\n          ))}\r\n        </div>\r\n      )}\r\n    </li>\r\n  );\r\n};\r\n\r\nconst Sidebar: React.FC<SidebarProps> = ({\r\n  isOpen,\r\n  topOffset,\r\n  onNewProject,\r\n}) => {\r\n  const pathname = usePathname();\r\n  const dispatch = useDispatch();\r\n  const { navItems, deployedProjects, draftStatusProjects, archivedProjects } =\r\n    useNavItems();\r\n\r\n  const activeCategory = pathname.includes(\"/library\") ? \"library\" : \"project\";\r\n\r\n  const handleNewButtonClick = () => {\r\n    if (activeCategory === \"project\") {\r\n      if (onNewProject) {\r\n        onNewProject();\r\n      } else {\r\n        // If no callback provided, use Redux (fallback for compatibility)\r\n        dispatch(showCreateProjectModal());\r\n      }\r\n    } else {\r\n      dispatch(showCreateLibraryModal());\r\n    }\r\n  };\r\n\r\n  const filteredNavItems = navItems.filter(\r\n    (item) => item.category === activeCategory\r\n  );\r\n\r\n  return (\r\n    <aside\r\n      style={{ top: `${topOffset}px`, height: `calc(100vh - ${topOffset}px)` }}\r\n      className={`\r\n        ${isOpen ? \"translate-x-0\" : \"-translate-x-full\"}\r\n        fixed left-0 h-full w-64 shadow-xl bg-neutral-100 z-30\r\n        transform transition-all duration-300 ease-in-out\r\n        laptop:translate-x-0 laptop:static laptop:flex flex\r\n      `}\r\n    >\r\n      {/* Left Icon Column */}\r\n      <div className=\"w-1/5 bg-neutral-200 p-4\">\r\n        <div className=\"flex flex-col gap-5 items-center\">\r\n          <Link\r\n            href=\"/dashboard\"\r\n            aria-label=\"Projects\"\r\n            className={`p-2  hover:bg-primary-500 hover:rounded-full transition-all duration-300 ease-in-out cursor-pointer\r\n              ${\r\n                activeCategory === \"project\"\r\n                  ? \"bg-primary-500 text-neutral-100 rounded-full\"\r\n                  : \"text-neutral-700 hover:bg-primary-500 hover:text-neutral-100\"\r\n              }\r\n            `}\r\n          >\r\n            <RiFileList3Fill size={20} title=\"Projects\" />\r\n          </Link>\r\n          <Link\r\n            href=\"/library\"\r\n            aria-label=\"Library\"\r\n            className={`p-2 hover:bg-primary-500 hover:rounded-full transition-all duration-300 ease-in-out cursor-pointer\r\n              ${\r\n                activeCategory === \"library\"\r\n                  ? \"bg-primary-500 text-neutral-100 rounded-full\"\r\n                  : \"text-neutral-700 hover:bg-primary-500 hover:text-neutral-100\"\r\n              }\r\n            `}\r\n          >\r\n            <LuLibrary size={20} title=\"Library\" />\r\n          </Link>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Right Navigation Area */}\r\n      <div className=\"w-4/5 bg-neutral-100 flex-1\">\r\n        <div className=\"p-4\">\r\n          <button onClick={handleNewButtonClick} className=\"btn-primary w-full\">\r\n            {activeCategory === \"project\" ? \"NEW PROJECT\" : \"NEW ITEM\"}\r\n          </button>\r\n        </div>\r\n\r\n        <nav className=\"mt-2\">\r\n          {/* {isLoading ? (\r\n            <div className=\"flex justify-center p-4\">\r\n              <Spinner />\r\n            </div>\r\n          ) : ( */}\r\n          <ul className=\"space-y-1\">\r\n            {filteredNavItems.map((item) => (\r\n              <SidebarItem\r\n                key={item.id}\r\n                href={item.href}\r\n                label={item.label}\r\n                icon={item.icon}\r\n                count={item.count}\r\n              />\r\n            ))}\r\n          </ul>\r\n\r\n          {/* )} */}\r\n        </nav>\r\n      </div>\r\n    </aside>\r\n  );\r\n};\r\n\r\nexport default Sidebar;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;AAkBA,MAAM,cAA0C,CAAC,EAC/C,IAAI,EACJ,KAAK,EACL,MAAM,IAAI,EACV,KAAK,EACN;IACC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,GAC/D,CAAA,GAAA,kIAAA,CAAA,UAAW,AAAD;IACZ,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,cAAc,CAAC;QACnB,IAAI,UAAU,cAAc,UAAU,WAAW,UAAU,YAAY;YACrE,IAAI,QAAQ,GAAG;gBACb,EAAE,cAAc;gBAChB,UAAU,CAAC;YACb,OAAO;gBACL,EAAE,cAAc;gBAChB,OAAO,IAAI,CAAC,GAAG,KAAK,cAAc,CAAC;YACrC;QACF;IACA,yBAAyB;IACzB,gEAAgE;IAChE,uBAAuB;IACvB,0BAA0B;IAC1B,0DAA0D;IAC1D,2CAA2C;IAC3C,MAAM;IACN,IAAI;IACN;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,qBAAqB,CAAA,GAAA,mHAAA,CAAA,SAAM,AAAD,EAAE;QAClC,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,mBAAmB,SAAS,CAAC;IACvD;IAEA,MAAM,cAAc;QAClB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO,EAAE;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,qBAAqB,CAAA,GAAA,mHAAA,CAAA,SAAM,AAAD,EAAE;QAClC,OAAO,CAAC,SAAS,EAAE,mBAAmB,SAAS,CAAC;IAClD;IAEA,MAAM,kBAAkB;IAExB,qBACE,8OAAC;;0BACC,8OAAC,4JAAA,CAAA,UAAI;gBACH,MAAM;gBACN,SAAS;gBACT,WAAU;;kCAEV,8OAAC;wBAAK,WAAU;wBAAO,MAAM;;;;;;kCAC7B,8OAAC;wBAAK,WAAU;kCAAqB;;;;;;kCACrC,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;YAIJ,UAAU,gBAAgB,MAAM,GAAG,mBAClC,8OAAC;gBAAI,WAAU;0BACZ,gBAAgB,GAAG,CAAC,CAAC,wBACpB,8OAAC;wBAEC,SAAS,IAAM,mBAAmB,QAAQ,EAAE;wBAC5C,WAAW,CAAC,kJAAkJ,EAC5J,aAAa,eAAe,QAAQ,EAAE,IAClC,oCACA,IACJ;kCAED,QAAQ,IAAI;uBARR,QAAQ,EAAE;;;;;;;;;;;;;;;;AAe7B;AAEA,MAAM,UAAkC,CAAC,EACvC,MAAM,EACN,SAAS,EACT,YAAY,EACb;IACC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,QAAQ,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,GACzE,CAAA,GAAA,kIAAA,CAAA,UAAW,AAAD;IAEZ,MAAM,iBAAiB,SAAS,QAAQ,CAAC,cAAc,YAAY;IAEnE,MAAM,uBAAuB;QAC3B,IAAI,mBAAmB,WAAW;YAChC,IAAI,cAAc;gBAChB;YACF,OAAO;gBACL,kEAAkE;gBAClE,SAAS,CAAA,GAAA,qIAAA,CAAA,yBAAsB,AAAD;YAChC;QACF,OAAO;YACL,SAAS,CAAA,GAAA,qIAAA,CAAA,yBAAsB,AAAD;QAChC;IACF;IAEA,MAAM,mBAAmB,SAAS,MAAM,CACtC,CAAC,OAAS,KAAK,QAAQ,KAAK;IAG9B,qBACE,8OAAC;QACC,OAAO;YAAE,KAAK,GAAG,UAAU,EAAE,CAAC;YAAE,QAAQ,CAAC,aAAa,EAAE,UAAU,GAAG,CAAC;QAAC;QACvE,WAAW,CAAC;QACV,EAAE,SAAS,kBAAkB,oBAAoB;;;;MAInD,CAAC;;0BAGD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,cAAW;4BACX,WAAW,CAAC;cACV,EACE,mBAAmB,YACf,iDACA,+DACL;YACH,CAAC;sCAED,cAAA,8OAAC,8IAAA,CAAA,kBAAe;gCAAC,MAAM;gCAAI,OAAM;;;;;;;;;;;sCAEnC,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,cAAW;4BACX,WAAW,CAAC;cACV,EACE,mBAAmB,YACf,iDACA,+DACL;YACH,CAAC;sCAED,cAAA,8OAAC,8IAAA,CAAA,YAAS;gCAAC,MAAM;gCAAI,OAAM;;;;;;;;;;;;;;;;;;;;;;0BAMjC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAO,SAAS;4BAAsB,WAAU;sCAC9C,mBAAmB,YAAY,gBAAgB;;;;;;;;;;;kCAIpD,8OAAC;wBAAI,WAAU;kCAMb,cAAA,8OAAC;4BAAG,WAAU;sCACX,iBAAiB,GAAG,CAAC,CAAC,qBACrB,8OAAC;oCAEC,MAAM,KAAK,IAAI;oCACf,OAAO,KAAK,KAAK;oCACjB,MAAM,KAAK,IAAI;oCACf,OAAO,KAAK,KAAK;mCAJZ,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAc5B;uCAEe", "debugId": null}}, {"offset": {"line": 1137, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/Modal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { AnimatePresence, motion, easeInOut } from \"framer-motion\";\r\nimport { X } from \"lucide-react\";\r\nimport React from \"react\";\r\n\r\nconst Modal = ({\r\n  children,\r\n  className,\r\n  isOpen,\r\n  onClose,\r\n  preventOutsideClick = false,\r\n}: {\r\n  children: React.ReactNode;\r\n  className?: string;\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  preventOutsideClick?: boolean;\r\n}) => {\r\n  // Handle backdrop click with confirmation if needed\r\n  const handleBackdropClick = (e: React.MouseEvent) => {\r\n    if (preventOutsideClick) {\r\n      // Do nothing, prevent closing\r\n      return;\r\n    } else {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {isOpen && (\r\n        <motion.div\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          exit={{ opacity: 0 }}\r\n          className=\"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40 p-4 overflow-y-auto\"\r\n          onClick={handleBackdropClick} // Handle backdrop click\r\n        >\r\n          <motion.div\r\n            initial={{ scale: 0.6, opacity: 0 }}\r\n            animate={{ scale: 1, opacity: 1 }}\r\n            exit={{ scale: 0.6, opacity: 0 }}\r\n            transition={{ duration: 0.3, ease: easeInOut }}\r\n            className={`relative section flex flex-col max-w-4xl max-h-[90vh] overflow-y-auto ${className}`}\r\n            onClick={(e) => e.stopPropagation()} // Prevent clicks inside the modal from closing it\r\n          >\r\n            <X\r\n              onClick={onClose}\r\n              className=\"absolute self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300\"\r\n            />\r\n            {children}\r\n          </motion.div>\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  );\r\n};\r\n\r\nexport default Modal;\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AAHA;;;;AAMA,MAAM,QAAQ,CAAC,EACb,QAAQ,EACR,SAAS,EACT,MAAM,EACN,OAAO,EACP,sBAAsB,KAAK,EAO5B;IACC,oDAAoD;IACpD,MAAM,sBAAsB,CAAC;QAC3B,IAAI,qBAAqB;YACvB,8BAA8B;YAC9B;QACF,OAAO;YACL;QACF;IACF;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,WAAU;YACV,SAAS;sBAET,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAClC,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,MAAM;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAC/B,YAAY;oBAAE,UAAU;oBAAK,MAAM,iKAAA,CAAA,YAAS;gBAAC;gBAC7C,WAAW,CAAC,sEAAsE,EAAE,WAAW;gBAC/F,SAAS,CAAC,IAAM,EAAE,eAAe;;kCAEjC,8OAAC,4LAAA,CAAA,IAAC;wBACA,SAAS;wBACT,WAAU;;;;;;oBAEX;;;;;;;;;;;;;;;;;AAMb;uCAEe", "debugId": null}}, {"offset": {"line": 1225, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/general/Select.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ChevronDown } from \"lucide-react\";\r\nimport React, { useEffect, useRef, useState } from \"react\";\r\n\r\nconst Select = ({\r\n  id,\r\n  options,\r\n  value,\r\n  onChange,\r\n}: {\r\n  id: string;\r\n  options: string[];\r\n  value: string | null;\r\n  onChange: (value: string) => void;\r\n}) => {\r\n  const [isOpen, setIsOpen] = useState<boolean>(false);\r\n  const listRef = useRef<HTMLUListElement>(null);\r\n  const optionRefs = useRef<(HTMLLIElement | null)[]>([]);\r\n  const containerRef = useRef<HTMLDivElement>(null);\r\n\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      if (\r\n        containerRef.current &&\r\n        !containerRef.current.contains(event.target as Node)\r\n      ) {\r\n        setIsOpen(false);\r\n      }\r\n    };\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => document.removeEventListener(\"mousedown\", handleClickOutside);\r\n  }, []);\r\n\r\n  const handleKeyPress = (event: KeyboardEvent) => {\r\n    if (!isOpen) return;\r\n\r\n    const pressedKey = event.key.toLowerCase();\r\n\r\n    if (pressedKey.match(/[a-z]/)) {\r\n      // Find the first option that starts with pressed key\r\n      const index = options.findIndex((option) =>\r\n        option.toLowerCase().startsWith(pressedKey)\r\n      );\r\n\r\n      if (index !== -1 && optionRefs.current[index]) {\r\n        optionRefs.current[index]?.scrollIntoView({\r\n          behavior: \"auto\",\r\n          block: \"nearest\",\r\n        });\r\n      }\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    document.addEventListener(\"keydown\", handleKeyPress);\r\n    return () => {\r\n      document.removeEventListener(\"keydown\", handleKeyPress);\r\n    };\r\n  }, [isOpen, options]);\r\n\r\n  return (\r\n    <div className=\"relative\" ref={containerRef}>\r\n      <button\r\n        id={id}\r\n        type=\"button\"\r\n        className=\"px-4 py-2 flex items-center justify-between rounded-md border border-neutral-400 focus:border-primary-500 duration-300 w-full text-left cursor-pointer\"\r\n        onClick={() => {\r\n          setIsOpen(!isOpen);\r\n        }}\r\n      >\r\n        <span>{value || \"Select an option\"}</span>\r\n        <ChevronDown />\r\n      </button>\r\n      {isOpen && (\r\n        <ul\r\n          className=\"absolute z-10 max-h-[180px] overflow-auto border border-neutral-400 rounded-md bg-neutral-100 w-full mt-1 flex flex-col\"\r\n          ref={listRef}\r\n        >\r\n          {options.map((option, index) => (\r\n            <li\r\n              key={index}\r\n              ref={(el) => {\r\n                optionRefs.current[index] = el;\r\n              }}\r\n              className=\"cursor-pointer bg-neutral-100 hover:bg-neutral-200 px-4 py-2\"\r\n              onClick={() => {\r\n                onChange(option);\r\n                setIsOpen(false);\r\n              }}\r\n            >\r\n              {option}\r\n            </li>\r\n          ))}\r\n        </ul>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { Select };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,SAAS,CAAC,EACd,EAAE,EACF,OAAO,EACP,KAAK,EACL,QAAQ,EAMT;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC9C,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IACzC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA4B,EAAE;IACtD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IACE,aAAa,OAAO,IACpB,CAAC,aAAa,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAC3C;gBACA,UAAU;YACZ;QACF;QACA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,QAAQ;QAEb,MAAM,aAAa,MAAM,GAAG,CAAC,WAAW;QAExC,IAAI,WAAW,KAAK,CAAC,UAAU;YAC7B,qDAAqD;YACrD,MAAM,QAAQ,QAAQ,SAAS,CAAC,CAAC,SAC/B,OAAO,WAAW,GAAG,UAAU,CAAC;YAGlC,IAAI,UAAU,CAAC,KAAK,WAAW,OAAO,CAAC,MAAM,EAAE;gBAC7C,WAAW,OAAO,CAAC,MAAM,EAAE,eAAe;oBACxC,UAAU;oBACV,OAAO;gBACT;YACF;QACF;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;QAC1C;IACF,GAAG;QAAC;QAAQ;KAAQ;IAEpB,qBACE,8OAAC;QAAI,WAAU;QAAW,KAAK;;0BAC7B,8OAAC;gBACC,IAAI;gBACJ,MAAK;gBACL,WAAU;gBACV,SAAS;oBACP,UAAU,CAAC;gBACb;;kCAEA,8OAAC;kCAAM,SAAS;;;;;;kCAChB,8OAAC,oNAAA,CAAA,cAAW;;;;;;;;;;;YAEb,wBACC,8OAAC;gBACC,WAAU;gBACV,KAAK;0BAEJ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;wBAEC,KAAK,CAAC;4BACJ,WAAW,OAAO,CAAC,MAAM,GAAG;wBAC9B;wBACA,WAAU;wBACV,SAAS;4BACP,SAAS;4BACT,UAAU;wBACZ;kCAEC;uBAVI;;;;;;;;;;;;;;;;AAiBnB", "debugId": null}}, {"offset": {"line": 1344, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/constants/sectors.ts"], "sourcesContent": ["export const SectorLabelMap: Record<string, string> = {\r\n  information_media: \"Information / Media\",\r\n  econommic_social_development: \"Economic & Social Development\",\r\n  security_police_peacekeeping: \"Security / Police / Peacekeeping\",\r\n  disarmament_and_demobilization: \"Disarmament & Demobilization\",\r\n  environment: \"Environment\",\r\n  private_sector: \"Private Sector\",\r\n  humanitarian_coordination_information_management:\r\n    \"Humanitarian - Coordination & Info Management\",\r\n  humanitarian_multiple_clusters: \"Humanitarian - Multiple Clusters\",\r\n  humanitarian_camp_management_and_coordination:\r\n    \"Humanitarian - Camp Management & Coordination\",\r\n  humanitarian_early_recovery: \"Humanitarian - Early Recovery\",\r\n  humanitarian_education: \"Humanitarian - Education\",\r\n  humanitarian_emergency_shelter: \"Humanitarian - Emergency Shelter\",\r\n  humanitarian_emergency_telecoms: \"Humanitarian - Emergency Telecoms\",\r\n  humanitarian_food_security: \"Humanitarian - Food Security\",\r\n  humanitarian_health: \"Humanitarian - Health\",\r\n  humanitarian_logistics: \"Humanitarian - Logistics\",\r\n  humanitarian_nutrition: \"Humanitarian - Nutrition\",\r\n  humanitarian_protection: \"Humanitarian - Protection\",\r\n  humanitarian_sanitation_water_and_hygiene:\r\n    \"Humanitarian - Sanitation / Water / Hygiene\",\r\n  other: \"Other\",\r\n};\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,iBAAyC;IACpD,mBAAmB;IACnB,8BAA8B;IAC9B,8BAA8B;IAC9B,gCAAgC;IAChC,aAAa;IACb,gBAAgB;IAChB,kDACE;IACF,gCAAgC;IAChC,+CACE;IACF,6BAA6B;IAC7B,wBAAwB;IACxB,gCAAgC;IAChC,iCAAiC;IACjC,4BAA4B;IAC5B,qBAAqB;IACrB,wBAAwB;IACxB,wBAAwB;IACxB,yBAAyB;IACzB,2CACE;IACF,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1375, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/labelToKey.ts"], "sourcesContent": ["export const labelToKey = (label: string, labelMap: Record<string, string>) => {\r\n    const entry = Object.entries(labelMap).find(([_, value]) => value === label);\r\n    return entry ? entry[0] : null;\r\n  };\r\n  "], "names": [], "mappings": ";;;AAAO,MAAM,aAAa,CAAC,OAAe;IACtC,MAAM,QAAQ,OAAO,OAAO,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,GAAK,UAAU;IACtE,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;AAC5B", "debugId": null}}, {"offset": {"line": 1388, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/CreateProjectModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState } from \"react\";\r\nimport Modal from \"./Modal\";\r\nimport { FieldValues, useForm } from \"react-hook-form\";\r\nimport { Select } from \"../general/Select\";\r\nimport countries from \"@/constants/countryNames.json\";\r\nimport { SectorLabelMap } from \"@/constants/sectors\";\r\nimport { Briefcase, FileText, Globe } from \"lucide-react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { RootState } from \"@/redux/store\";\r\nimport { hideCreateProjectModal } from \"@/redux/slices/createProjectSlice\";\r\nimport axios from \"@/lib/axios\";\r\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useAuth } from \"@/hooks/useAuth\";\r\nimport { labelToKey } from \"@/lib/labelToKey\";\r\n\r\nconst createProject = async ({\r\n  name,\r\n  description,\r\n  sector,\r\n  country,\r\n}: {\r\n  name: string;\r\n  description: string;\r\n  sector: string;\r\n  country: string;\r\n}) => {\r\n  const { data } = await axios.post(`/projects`, {\r\n    name,\r\n    description,\r\n    sector,\r\n    country,\r\n  });\r\n  return data;\r\n};\r\n\r\ninterface CreateProjectModalProps {\r\n  onBack?: () => void; // Optional back handler\r\n  isOpen?: boolean;\r\n  onClose?: () => void;\r\n}\r\n\r\nconst CreateProjectModal = ({\r\n  isOpen,\r\n  onClose,\r\n  onBack,\r\n}: CreateProjectModalProps = {}) => {\r\n  const showModal = useSelector(\r\n    (state: RootState) => state.createProject.visible\r\n  );\r\n\r\n  const dispatch = useDispatch();\r\n\r\n  const {\r\n    register,\r\n    formState: { isSubmitting, errors, isSubmitted },\r\n    handleSubmit,\r\n    setValue,\r\n  } = useForm();\r\n\r\n  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);\r\n  const [selectedSector, setSelectedSector] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    register(\"country\", { required: \"Please select a country\" });\r\n    register(\"sector\", { required: \"Please select a sector\" });\r\n  }, [register]);\r\n\r\n  useEffect(() => {\r\n    setValue(\"country\", selectedCountry, { shouldValidate: isSubmitted });\r\n    setValue(\"sector\", selectedSector, { shouldValidate: isSubmitted });\r\n  }, [setValue, selectedCountry, selectedSector]);\r\n\r\n  // without this closing animation won't work\r\n  const [isClosing, setIsClosing] = useState(false);\r\n  const handleClose = () => {\r\n    setIsClosing(true);\r\n    setTimeout(() => {\r\n      dispatch(hideCreateProjectModal());\r\n    }, 300);\r\n    // match the time with animation duration\r\n  };\r\n\r\n  const handleBack = () => {\r\n    if (onBack) {\r\n      // Just call onBack without closing the modal\r\n      onBack();\r\n    }\r\n  };\r\n\r\n  const router = useRouter();\r\n  // for posting data\r\n  const queryClient = useQueryClient();\r\n\r\n  const projectMutation = useMutation({\r\n    mutationFn: createProject,\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"projects\"], exact: false });\r\n      handleClose();\r\n      router.push(\"/dashboard\");\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Project has been created successfully.\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n    },\r\n    onError: () => {\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Failed to create project\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n  });\r\n\r\n  const onSubmit = async (data: FieldValues) => {\r\n    projectMutation.mutate({\r\n      name: data.projectName,\r\n      description: data.description,\r\n      sector: data.sector,\r\n      country: data.country,\r\n    });\r\n  };\r\n\r\n  return (\r\n    <Modal\r\n      isOpen={showModal && !isClosing}\r\n      onClose={handleClose}\r\n      className=\"w-4/5 laptop:w-3/5 \"\r\n    >\r\n      <h1 className=\"heading-text\">Create a new project</h1>\r\n\r\n      <form\r\n        className=\"flex flex-col gap-8 max-h-[600px] overflow-y-auto p-4\"\r\n        onSubmit={handleSubmit(onSubmit)}\r\n      >\r\n        <div className=\"flex flex-col gap-4\">\r\n          {/* Project Name */}\r\n          <div className=\"label-input-group group\">\r\n            <label htmlFor=\"project-name\" className=\"label-text\">\r\n              <FileText size={16} /> Project Name\r\n            </label>\r\n            <input\r\n              {...register(\"projectName\", {\r\n                required: \"Project name is required.\",\r\n              })}\r\n              id=\"project-name\"\r\n              type=\"text\"\r\n              className=\"input-field\"\r\n              placeholder=\"Enter a project name\"\r\n            />\r\n            {errors.projectName && (\r\n              <p className=\"text-red-500 text-sm\">{`${errors.projectName.message}`}</p>\r\n            )}\r\n          </div>\r\n          {/* Project Description */}\r\n          <div className=\"label-input-group group\">\r\n            <label htmlFor=\"description\" className=\"label-text\">\r\n              Description\r\n            </label>\r\n            <textarea\r\n              id=\"description\"\r\n              {...register(\"description\", {\r\n                required: \"Please enter the project description\",\r\n              })}\r\n              className=\"input-field resize-none\"\r\n              cols={4}\r\n              placeholder=\"Enter the project description\"\r\n            />\r\n            {errors.description && (\r\n              <p className=\"text-red-500 text-sm\">{`${errors.description.message}`}</p>\r\n            )}\r\n          </div>\r\n          {/* Country and Sector */}\r\n          <div className=\"grid grid-cols-2 gap-4\">\r\n            <div className=\"label-input-group group\">\r\n              <label htmlFor=\"country\" className=\"label-text\">\r\n                <Globe size={16} />\r\n                Country\r\n              </label>\r\n              <Select\r\n                id={`country`}\r\n                options={countries}\r\n                value={selectedCountry}\r\n                onChange={setSelectedCountry}\r\n              />\r\n              {errors.country && (\r\n                <p className=\"text-red-500 text-sm\">{`${errors.country.message}`}</p>\r\n              )}\r\n            </div>\r\n            <div className=\"label-input-group group\">\r\n              <label htmlFor=\"sector\" className=\"label-text\">\r\n                <Briefcase size={16} /> Sector\r\n              </label>\r\n              <Select\r\n                id={`sector`}\r\n                options={Object.values(SectorLabelMap)} // Display labels\r\n                value={\r\n                  selectedSector && SectorLabelMap[selectedSector]\r\n                    ? SectorLabelMap[selectedSector]\r\n                    : \"Select an option\"\r\n                }\r\n                onChange={(label) => {\r\n                  const selectedKey = labelToKey(label, SectorLabelMap);\r\n                  setSelectedSector(selectedKey); // Set the enum key for storage\r\n                }}\r\n              />\r\n              {errors.sector && (\r\n                <p className=\"text-red-500 text-sm\">{`${errors.sector.message}`}</p>\r\n              )}\r\n            </div>\r\n          </div>\r\n          <div className=\"flex justify-end gap-3 mt-4\">\r\n            {onBack && (\r\n              <button\r\n                type=\"button\"\r\n                onClick={handleBack}\r\n                className=\"btn-outline\"\r\n              >\r\n                Back\r\n              </button>\r\n            )}\r\n            <button type=\"submit\" className=\"btn-primary\">\r\n              {isSubmitting ? (\r\n                <span className=\"flex items-center gap-2\">\r\n                  Creating{\" \"}\r\n                  <div className=\"size-4 animate-spin border-x border-neutral-100 rounded-full\"></div>\r\n                </span>\r\n              ) : (\r\n                \"Create Project\"\r\n              )}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </form>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport { CreateProjectModal };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AAEA;AAjBA;;;;;;;;;;;;;;;;AAmBA,MAAM,gBAAgB,OAAO,EAC3B,IAAI,EACJ,WAAW,EACX,MAAM,EACN,OAAO,EAMR;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,EAAE;QAC7C;QACA;QACA;QACA;IACF;IACA,OAAO;AACT;AAQA,MAAM,qBAAqB,CAAC,EAC1B,MAAM,EACN,OAAO,EACP,MAAM,EACkB,GAAG,CAAC,CAAC;IAC7B,MAAM,YAAY,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAC1B,CAAC,QAAqB,MAAM,aAAa,CAAC,OAAO;IAGnD,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,EACJ,QAAQ,EACR,WAAW,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,EAChD,YAAY,EACZ,QAAQ,EACT,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD;IAEV,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,WAAW;YAAE,UAAU;QAA0B;QAC1D,SAAS,UAAU;YAAE,UAAU;QAAyB;IAC1D,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,WAAW,iBAAiB;YAAE,gBAAgB;QAAY;QACnE,SAAS,UAAU,gBAAgB;YAAE,gBAAgB;QAAY;IACnE,GAAG;QAAC;QAAU;QAAiB;KAAe;IAE9C,4CAA4C;IAC5C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,cAAc;QAClB,aAAa;QACb,WAAW;YACT,SAAS,CAAA,GAAA,qIAAA,CAAA,yBAAsB,AAAD;QAChC,GAAG;IACH,yCAAyC;IAC3C;IAEA,MAAM,aAAa;QACjB,IAAI,QAAQ;YACV,6CAA6C;YAC7C;QACF;IACF;IAEA,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,mBAAmB;IACnB,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,kBAAkB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QAClC,YAAY;QACZ,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;gBAAE,OAAO;YAAM;YACrE;YACA,OAAO,IAAI,CAAC;YACZ,SACE,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS;gBACT,MAAM;YACR;QAEJ;QACA,SAAS;YACP,SACE,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS;gBACT,MAAM;YACR;QAEJ;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,gBAAgB,MAAM,CAAC;YACrB,MAAM,KAAK,WAAW;YACtB,aAAa,KAAK,WAAW;YAC7B,QAAQ,KAAK,MAAM;YACnB,SAAS,KAAK,OAAO;QACvB;IACF;IAEA,qBACE,8OAAC,8HAAA,CAAA,UAAK;QACJ,QAAQ,aAAa,CAAC;QACtB,SAAS;QACT,WAAU;;0BAEV,8OAAC;gBAAG,WAAU;0BAAe;;;;;;0BAE7B,8OAAC;gBACC,WAAU;gBACV,UAAU,aAAa;0BAEvB,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,SAAQ;oCAAe,WAAU;;sDACtC,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;wCAAM;;;;;;;8CAExB,8OAAC;oCACE,GAAG,SAAS,eAAe;wCAC1B,UAAU;oCACZ,EAAE;oCACF,IAAG;oCACH,MAAK;oCACL,WAAU;oCACV,aAAY;;;;;;gCAEb,OAAO,WAAW,kBACjB,8OAAC;oCAAE,WAAU;8CAAwB,GAAG,OAAO,WAAW,CAAC,OAAO,EAAE;;;;;;;;;;;;sCAIxE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,SAAQ;oCAAc,WAAU;8CAAa;;;;;;8CAGpD,8OAAC;oCACC,IAAG;oCACF,GAAG,SAAS,eAAe;wCAC1B,UAAU;oCACZ,EAAE;oCACF,WAAU;oCACV,MAAM;oCACN,aAAY;;;;;;gCAEb,OAAO,WAAW,kBACjB,8OAAC;oCAAE,WAAU;8CAAwB,GAAG,OAAO,WAAW,CAAC,OAAO,EAAE;;;;;;;;;;;;sCAIxE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,SAAQ;4CAAU,WAAU;;8DACjC,8OAAC,oMAAA,CAAA,QAAK;oDAAC,MAAM;;;;;;gDAAM;;;;;;;sDAGrB,8OAAC,gIAAA,CAAA,SAAM;4CACL,IAAI,CAAC,OAAO,CAAC;4CACb,SAAS,iGAAA,CAAA,UAAS;4CAClB,OAAO;4CACP,UAAU;;;;;;wCAEX,OAAO,OAAO,kBACb,8OAAC;4CAAE,WAAU;sDAAwB,GAAG,OAAO,OAAO,CAAC,OAAO,EAAE;;;;;;;;;;;;8CAGpE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,SAAQ;4CAAS,WAAU;;8DAChC,8OAAC,4MAAA,CAAA,YAAS;oDAAC,MAAM;;;;;;gDAAM;;;;;;;sDAEzB,8OAAC,gIAAA,CAAA,SAAM;4CACL,IAAI,CAAC,MAAM,CAAC;4CACZ,SAAS,OAAO,MAAM,CAAC,oHAAA,CAAA,iBAAc;4CACrC,OACE,kBAAkB,oHAAA,CAAA,iBAAc,CAAC,eAAe,GAC5C,oHAAA,CAAA,iBAAc,CAAC,eAAe,GAC9B;4CAEN,UAAU,CAAC;gDACT,MAAM,cAAc,CAAA,GAAA,iHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,oHAAA,CAAA,iBAAc;gDACpD,kBAAkB,cAAc,+BAA+B;4CACjE;;;;;;wCAED,OAAO,MAAM,kBACZ,8OAAC;4CAAE,WAAU;sDAAwB,GAAG,OAAO,MAAM,CAAC,OAAO,EAAE;;;;;;;;;;;;;;;;;;sCAIrE,8OAAC;4BAAI,WAAU;;gCACZ,wBACC,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;8CAIH,8OAAC;oCAAO,MAAK;oCAAS,WAAU;8CAC7B,6BACC,8OAAC;wCAAK,WAAU;;4CAA0B;4CAC/B;0DACT,8OAAC;gDAAI,WAAU;;;;;;;;;;;+CAGjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}, {"offset": {"line": 1789, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/api/templates.ts"], "sourcesContent": ["import axios from \"@/lib/axios\";\r\n\r\nconst fetchTemplateById = async ({ templateId }: { templateId: number }) => {\r\n  const { data } = await axios.get(`/libraries/${templateId}`);\r\n  return data.template;\r\n};\r\n\r\nconst createTemplate = async (dataToSend: {\r\n  name: string;\r\n  description: string;\r\n  sector: string;\r\n  country: string;\r\n}) => {\r\n  const { data } = await axios.post(`/libraries`, dataToSend);\r\n  return data;\r\n};\r\n\r\nconst fetchTemplates = async () => {\r\n  const { data } = await axios.get(`/libraries`);\r\n  return data.templates;\r\n};\r\n\r\nconst deleteTemplate = async (templateId: number) => {\r\n  const { data } = await axios.delete(`/libraries/${templateId}`);\r\n  return data;\r\n};\r\n\r\nconst deleteTemplates = async ({ templateIds }: { templateIds: number[] }) => {\r\n  return null;\r\n};\r\n\r\nexport {\r\n  createTemplate,\r\n  fetchTemplateById,\r\n  fetchTemplates,\r\n  deleteTemplates,\r\n  deleteTemplate,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEA,MAAM,oBAAoB,OAAO,EAAE,UAAU,EAA0B;IACrE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,YAAY;IAC3D,OAAO,KAAK,QAAQ;AACtB;AAEA,MAAM,iBAAiB,OAAO;IAM5B,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,EAAE;IAChD,OAAO;AACT;AAEA,MAAM,iBAAiB;IACrB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC;IAC7C,OAAO,KAAK,SAAS;AACvB;AAEA,MAAM,iBAAiB,OAAO;IAC5B,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,4GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,YAAY;IAC9D,OAAO;AACT;AAEA,MAAM,kBAAkB,OAAO,EAAE,WAAW,EAA6B;IACvE,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1824, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/library/CreateLibraryTemplate.tsx"], "sourcesContent": ["import { createTemplate } from \"@/lib/api/templates\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { Briefcase, FileText, Globe } from \"lucide-react\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { FieldValues, useForm } from \"react-hook-form\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { Select } from \"../general/Select\";\r\nimport countries from \"@/constants/countryNames.json\";\r\nimport { SectorLabelMap } from \"@/constants/sectors\";\r\nimport { labelToKey } from \"@/lib/labelToKey\";\r\nimport { useAuth } from \"@/hooks/useAuth\";\r\n\r\nconst CreateLibraryTemplate = ({\r\n  handleClose,\r\n}: {\r\n  handleClose: () => void;\r\n}) => {\r\n  const dispatch = useDispatch();\r\n\r\n  const {\r\n    register,\r\n    formState: { isSubmitting, errors, isSubmitted },\r\n    handleSubmit,\r\n    setValue,\r\n  } = useForm();\r\n\r\n  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);\r\n  const [selectedSector, setSelectedSector] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    register(\"country\", { required: \"Please select a country\" });\r\n    register(\"sector\", { required: \"Please select a sector\" });\r\n  }, [register]);\r\n\r\n  useEffect(() => {\r\n    setValue(\"country\", selectedCountry, { shouldValidate: isSubmitted });\r\n    setValue(\"sector\", selectedSector, { shouldValidate: isSubmitted });\r\n  }, [setValue, selectedCountry, selectedSector]);\r\n\r\n  const queryClient = useQueryClient();\r\n\r\n  const { user } = useAuth();\r\n\r\n  const templateMutation = useMutation({\r\n    mutationFn: createTemplate,\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"templates\", user?.id] });\r\n      handleClose();\r\n      dispatch(\r\n        showNotification({\r\n          type: \"success\",\r\n          message: \"Template created successfully\",\r\n        })\r\n      );\r\n    },\r\n    onError: () => {\r\n      dispatch(\r\n        showNotification({\r\n          type: \"error\",\r\n          message: \"Failed to create template. Please try again\",\r\n        })\r\n      );\r\n    },\r\n  });\r\n\r\n  const onSubmit = async (data: FieldValues) => {\r\n    const dataToSend = {\r\n      name: data.name,\r\n      description: data.description,\r\n      sector: data.sector,\r\n      country: data.country,\r\n    };\r\n\r\n    templateMutation.mutate(dataToSend);\r\n  };\r\n\r\n  return (\r\n    <form className=\"flex flex-col gap-8\" onSubmit={handleSubmit(onSubmit)}>\r\n      <h1 className=\"heading-text capitalize\">Create new project template</h1>\r\n      <div className=\"flex flex-col gap-4\">\r\n        {/* Project Name */}\r\n        <div className=\"label-input-group group\">\r\n          <label htmlFor=\"project-name\" className=\"label-text\">\r\n            <FileText size={16} /> Template Name\r\n          </label>\r\n          <input\r\n            {...register(\"name\", {\r\n              required: \"Template name is required.\",\r\n            })}\r\n            id=\"project-name\"\r\n            type=\"text\"\r\n            className=\"input-field\"\r\n            placeholder=\"Enter a project name\"\r\n          />\r\n          {errors.name && (\r\n            <p className=\"text-red-500 text-sm\">{`${errors.name.message}`}</p>\r\n          )}\r\n        </div>\r\n        {/* Project Description */}\r\n        <div className=\"label-input-group group\">\r\n          <label htmlFor=\"description\" className=\"label-text\">\r\n            Description\r\n          </label>\r\n          <textarea\r\n            id=\"description\"\r\n            {...register(\"description\", {\r\n              required: \"Please enter the template description\",\r\n            })}\r\n            className=\"input-field resize-none\"\r\n            cols={4}\r\n            placeholder=\"Enter the project description\"\r\n          />\r\n          {errors.description && (\r\n            <p className=\"text-red-500 text-sm\">{`${errors.description.message}`}</p>\r\n          )}\r\n        </div>\r\n        {/* Country and Sector */}\r\n        <div className=\"grid grid-cols-2 gap-4\">\r\n          <div className=\"label-input-group group\">\r\n            <label htmlFor=\"country\" className=\"label-text\">\r\n              <Globe size={16} />\r\n              Country\r\n            </label>\r\n            <Select\r\n              id={`country`}\r\n              options={countries}\r\n              value={selectedCountry}\r\n              onChange={setSelectedCountry}\r\n            />\r\n            {errors.country && (\r\n              <p className=\"text-red-500 text-sm\">{`${errors.country.message}`}</p>\r\n            )}\r\n          </div>\r\n          <div className=\"label-input-group group\">\r\n            <label htmlFor=\"sector\" className=\"label-text\">\r\n              <Briefcase size={16} /> Sector\r\n            </label>\r\n            <Select\r\n              id={`sector`}\r\n              options={Object.values(SectorLabelMap)} // Display labels\r\n              value={\r\n                selectedSector && SectorLabelMap[selectedSector]\r\n                  ? SectorLabelMap[selectedSector]\r\n                  : \"Select an option\"\r\n              }\r\n              onChange={(label) => {\r\n                const selectedKey = labelToKey(label, SectorLabelMap);\r\n                setSelectedSector(selectedKey); // Set the enum key for storage\r\n              }}\r\n            />\r\n            {errors.sector && (\r\n              <p className=\"text-red-500 text-sm\">{`${errors.sector.message}`}</p>\r\n            )}\r\n          </div>\r\n        </div>\r\n        <button type=\"submit\" className=\"btn-primary self-end\">\r\n          {isSubmitting ? (\r\n            <span className=\"flex items-center gap-2\">\r\n              Creating{\" \"}\r\n              <div className=\"size-4 animate-spin border-x border-neutral-100 rounded-full\"></div>\r\n            </span>\r\n          ) : (\r\n            \"Create Project\"\r\n          )}\r\n        </button>\r\n      </div>\r\n    </form>\r\n  );\r\n};\r\n\r\nexport { CreateLibraryTemplate };\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AAEA,MAAM,wBAAwB,CAAC,EAC7B,WAAW,EAGZ;IACC,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,EACJ,QAAQ,EACR,WAAW,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,EAChD,YAAY,EACZ,QAAQ,EACT,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD;IAEV,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,WAAW;YAAE,UAAU;QAA0B;QAC1D,SAAS,UAAU;YAAE,UAAU;QAAyB;IAC1D,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,WAAW,iBAAiB;YAAE,gBAAgB;QAAY;QACnE,SAAS,UAAU,gBAAgB;YAAE,gBAAgB;QAAY;IACnE,GAAG;QAAC;QAAU;QAAiB;KAAe;IAE9C,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,iHAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,mBAAmB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACnC,YAAY,uHAAA,CAAA,iBAAc;QAC1B,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAa,MAAM;iBAAG;YAAC;YAClE;YACA,SACE,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,MAAM;gBACN,SAAS;YACX;QAEJ;QACA,SAAS;YACP,SACE,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,MAAM;gBACN,SAAS;YACX;QAEJ;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,MAAM,aAAa;YACjB,MAAM,KAAK,IAAI;YACf,aAAa,KAAK,WAAW;YAC7B,QAAQ,KAAK,MAAM;YACnB,SAAS,KAAK,OAAO;QACvB;QAEA,iBAAiB,MAAM,CAAC;IAC1B;IAEA,qBACE,8OAAC;QAAK,WAAU;QAAsB,UAAU,aAAa;;0BAC3D,8OAAC;gBAAG,WAAU;0BAA0B;;;;;;0BACxC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,SAAQ;gCAAe,WAAU;;kDACtC,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;oCAAM;;;;;;;0CAExB,8OAAC;gCACE,GAAG,SAAS,QAAQ;oCACnB,UAAU;gCACZ,EAAE;gCACF,IAAG;gCACH,MAAK;gCACL,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,IAAI,kBACV,8OAAC;gCAAE,WAAU;0CAAwB,GAAG,OAAO,IAAI,CAAC,OAAO,EAAE;;;;;;;;;;;;kCAIjE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,SAAQ;gCAAc,WAAU;0CAAa;;;;;;0CAGpD,8OAAC;gCACC,IAAG;gCACF,GAAG,SAAS,eAAe;oCAC1B,UAAU;gCACZ,EAAE;gCACF,WAAU;gCACV,MAAM;gCACN,aAAY;;;;;;4BAEb,OAAO,WAAW,kBACjB,8OAAC;gCAAE,WAAU;0CAAwB,GAAG,OAAO,WAAW,CAAC,OAAO,EAAE;;;;;;;;;;;;kCAIxE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,SAAQ;wCAAU,WAAU;;0DACjC,8OAAC,oMAAA,CAAA,QAAK;gDAAC,MAAM;;;;;;4CAAM;;;;;;;kDAGrB,8OAAC,gIAAA,CAAA,SAAM;wCACL,IAAI,CAAC,OAAO,CAAC;wCACb,SAAS,iGAAA,CAAA,UAAS;wCAClB,OAAO;wCACP,UAAU;;;;;;oCAEX,OAAO,OAAO,kBACb,8OAAC;wCAAE,WAAU;kDAAwB,GAAG,OAAO,OAAO,CAAC,OAAO,EAAE;;;;;;;;;;;;0CAGpE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,SAAQ;wCAAS,WAAU;;0DAChC,8OAAC,4MAAA,CAAA,YAAS;gDAAC,MAAM;;;;;;4CAAM;;;;;;;kDAEzB,8OAAC,gIAAA,CAAA,SAAM;wCACL,IAAI,CAAC,MAAM,CAAC;wCACZ,SAAS,OAAO,MAAM,CAAC,oHAAA,CAAA,iBAAc;wCACrC,OACE,kBAAkB,oHAAA,CAAA,iBAAc,CAAC,eAAe,GAC5C,oHAAA,CAAA,iBAAc,CAAC,eAAe,GAC9B;wCAEN,UAAU,CAAC;4CACT,MAAM,cAAc,CAAA,GAAA,iHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,oHAAA,CAAA,iBAAc;4CACpD,kBAAkB,cAAc,+BAA+B;wCACjE;;;;;;oCAED,OAAO,MAAM,kBACZ,8OAAC;wCAAE,WAAU;kDAAwB,GAAG,OAAO,MAAM,CAAC,OAAO,EAAE;;;;;;;;;;;;;;;;;;kCAIrE,8OAAC;wBAAO,MAAK;wBAAS,WAAU;kCAC7B,6BACC,8OAAC;4BAAK,WAAU;;gCAA0B;gCAC/B;8CACT,8OAAC;oCAAI,WAAU;;;;;;;;;;;mCAGjB;;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}, {"offset": {"line": 2166, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/CreateLibraryItemModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\nimport Modal from \"./Modal\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { RootState } from \"@/redux/store\";\r\nimport { hideCreateLibraryItemModal } from \"@/redux/slices/createLibraryItemSlice\";\r\nimport { CreateLibraryTemplate } from \"../library/CreateLibraryTemplate\";\r\n\r\nconst CreateLibraryItemModal = () => {\r\n  const { visible, option } = useSelector(\r\n    (state: RootState) => state.createLibraryItem\r\n  );\r\n\r\n  const dispatch = useDispatch();\r\n  // without this closing animation won't work\r\n  const [isClosing, setIsClosing] = useState(false);\r\n  const handleClose = () => {\r\n    setIsClosing(true);\r\n    setTimeout(() => {\r\n      dispatch(hideCreateLibraryItemModal());\r\n    }, 300);\r\n    // match the time with animation duration\r\n  };\r\n\r\n  const content = () => {\r\n    switch (option) {\r\n      case \"question-block\":\r\n        return <div>Question Block</div>;\r\n      case \"template\":\r\n        return <CreateLibraryTemplate handleClose={handleClose} />;\r\n      case \"upload\":\r\n        return <div>Upload</div>;\r\n      case \"collection\":\r\n        return <div>Collection</div>;\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Modal\r\n      isOpen={visible && !isClosing}\r\n      onClose={handleClose}\r\n      className=\"w-3/5\"\r\n    >\r\n      {content()}\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport { CreateLibraryItemModal };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAPA;;;;;;;AASA,MAAM,yBAAyB;IAC7B,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EACpC,CAAC,QAAqB,MAAM,iBAAiB;IAG/C,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAC3B,4CAA4C;IAC5C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,cAAc;QAClB,aAAa;QACb,WAAW;YACT,SAAS,CAAA,GAAA,yIAAA,CAAA,6BAA0B,AAAD;QACpC,GAAG;IACH,yCAAyC;IAC3C;IAEA,MAAM,UAAU;QACd,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC;8BAAI;;;;;;YACd,KAAK;gBACH,qBAAO,8OAAC,+IAAA,CAAA,wBAAqB;oBAAC,aAAa;;;;;;YAC7C,KAAK;gBACH,qBAAO,8OAAC;8BAAI;;;;;;YACd,KAAK;gBACH,qBAAO,8OAAC;8BAAI;;;;;;YACd;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,8HAAA,CAAA,UAAK;QACJ,QAAQ,WAAW,CAAC;QACpB,SAAS;QACT,WAAU;kBAET;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 2250, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/general/Spinner.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nconst Spinner = () => {\r\n  return (\r\n    <div className=\"w-full flex items-center justify-center\">\r\n      <div className=\"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16\"></div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Spinner;\r\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,UAAU;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;uCAEe", "debugId": null}}, {"offset": {"line": 2278, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\n/**\r\n * Format a date into a readable string format\r\n * @param date - Date object or string to format\r\n * @param format - Optional format type ('short', 'long', or 'full')\r\n * @returns Formatted date string\r\n */\r\nexport function formatDate(\r\n  date: Date | string,\r\n  format: \"short\" | \"long\" | \"full\" = \"short\"\r\n): string {\r\n  if (!date) return \"\";\r\n\r\n  try {\r\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\r\n\r\n    // Return empty string if invalid date\r\n    if (isNaN(dateObj.getTime())) return \"\";\r\n\r\n    switch (format) {\r\n      case \"short\":\r\n        return dateObj.toLocaleDateString(undefined, {\r\n          year: \"numeric\",\r\n          month: \"short\",\r\n          day: \"numeric\",\r\n        });\r\n\r\n      case \"long\":\r\n        return dateObj.toLocaleDateString(undefined, {\r\n          year: \"numeric\",\r\n          month: \"long\",\r\n          day: \"numeric\",\r\n          hour: \"2-digit\",\r\n          minute: \"2-digit\",\r\n        });\r\n\r\n      case \"full\":\r\n        return dateObj.toLocaleDateString(undefined, {\r\n          year: \"numeric\",\r\n          month: \"long\",\r\n          day: \"numeric\",\r\n          weekday: \"long\",\r\n          hour: \"2-digit\",\r\n          minute: \"2-digit\",\r\n          second: \"2-digit\",\r\n        });\r\n\r\n      default:\r\n        return dateObj.toLocaleDateString();\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error formatting date:\", error);\r\n    return String(date);\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAQO,SAAS,WACd,IAAmB,EACnB,SAAoC,OAAO;IAE3C,IAAI,CAAC,MAAM,OAAO;IAElB,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;QAE5D,sCAAsC;QACtC,IAAI,MAAM,QAAQ,OAAO,KAAK,OAAO;QAErC,OAAQ;YACN,KAAK;gBACH,OAAO,QAAQ,kBAAkB,CAAC,WAAW;oBAC3C,MAAM;oBACN,OAAO;oBACP,KAAK;gBACP;YAEF,KAAK;gBACH,OAAO,QAAQ,kBAAkB,CAAC,WAAW;oBAC3C,MAAM;oBACN,OAAO;oBACP,KAAK;oBACL,MAAM;oBACN,QAAQ;gBACV;YAEF,KAAK;gBACH,OAAO,QAAQ,kBAAkB,CAAC,WAAW;oBAC3C,MAAM;oBACN,OAAO;oBACP,KAAK;oBACL,SAAS;oBACT,MAAM;oBACN,QAAQ;oBACR,QAAQ;gBACV;YAEF;gBACE,OAAO,QAAQ,kBAAkB;QACrC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,OAAO;IAChB;AACF", "debugId": null}}, {"offset": {"line": 2334, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ui/table.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"table-container\"\r\n      className=\"relative w-full overflow-x-auto\"\r\n    >\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn(\"w-full caption-bottom text-sm\", className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\r\n  return (\r\n    <thead\r\n      data-slot=\"table-header\"\r\n      className={cn(\"[&_tr]:border-b\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn(\r\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        \"text-foreground h-10 px-2 text-left align-middle font-medium neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        \"p-2 align-middle neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"caption\">) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gHACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2452, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2714, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 neutral-100space-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-neutral-100 shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,qcACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2771, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/tables/GeneralTable.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport {\r\n  ColumnDef,\r\n  VisibilityState,\r\n  ColumnFiltersState,\r\n  flexRender,\r\n  getCoreRowModel,\r\n  getFilteredRowModel,\r\n  getPaginationRowModel,\r\n  getSortedRowModel,\r\n  useReactTable,\r\n  OnChangeFn,\r\n  SortingState,\r\n  RowSelectionState,\r\n} from \"@tanstack/react-table\";\r\n\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { FaChevronDown } from \"react-icons/fa\";\r\n\r\ninterface GeneralTableProps<TData, TValue> {\r\n  columns: ColumnDef<TData, TValue>[];\r\n  data: TData[];\r\n  globalFilter?: string;\r\n  setGlobalFilter?: (filterValue: string) => void;\r\n  onTableInit?: (table: import(\"@tanstack/react-table\").Table<TData>) => void;\r\n  columnVisibility?: VisibilityState;\r\n  setColumnVisibility?: (state: VisibilityState) => void;\r\n  onRowSelectionChange?: (rowSelection: RowSelectionState) => void;\r\n  rowSelection?: RowSelectionState;\r\n  onRowClick?: (row: TData) => void;\r\n}\r\n\r\nconst GeneralTable = <TData, TValue>({\r\n  columns,\r\n  data,\r\n  globalFilter,\r\n  setGlobalFilter,\r\n  onTableInit,\r\n  columnVisibility: externalColumnVisibility,\r\n  setColumnVisibility: setExternalColumnVisibility,\r\n  onRowSelectionChange,\r\n  rowSelection: externalRowSelection,\r\n  onRowClick,\r\n}: GeneralTableProps<TData, TValue>) => {\r\n  const [pagination, setPagination] = React.useState({\r\n    pageIndex: 0,\r\n    pageSize: 8,\r\n  });\r\n\r\n  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(\r\n    []\r\n  );\r\n  const [sorting, setSorting] = React.useState<SortingState>([]);\r\n\r\n  const [internalColumnVisibility, setInternalColumnVisibility] =\r\n    React.useState<VisibilityState>({});\r\n\r\n  const effectiveColumnVisibility =\r\n    externalColumnVisibility ?? internalColumnVisibility;\r\n  const setEffectiveColumnVisibility =\r\n    setExternalColumnVisibility ?? setInternalColumnVisibility;\r\n\r\n  const [internalRowSelection, setInternalRowSelection] = React.useState<RowSelectionState>({});\r\n  \r\n  // Use external row selection if provided, otherwise use internal\r\n  const effectiveRowSelection = externalRowSelection !== undefined \r\n    ? externalRowSelection \r\n    : internalRowSelection;\r\n\r\n  const table = useReactTable({\r\n    data,\r\n    columns,\r\n    onPaginationChange: setPagination,\r\n    onColumnFiltersChange: setColumnFilters,\r\n    onGlobalFilterChange: setGlobalFilter,\r\n    onColumnVisibilityChange:\r\n      setEffectiveColumnVisibility as OnChangeFn<VisibilityState>,\r\n    onRowSelectionChange: (updater) => {\r\n      const newRowSelection =\r\n        typeof updater === \"function\" ? updater(effectiveRowSelection) : updater;\r\n      \r\n      // Only update internal state if we're not using external state\r\n      if (externalRowSelection === undefined) {\r\n        setInternalRowSelection(newRowSelection);\r\n      }\r\n      \r\n      if (onRowSelectionChange) {\r\n        onRowSelectionChange(newRowSelection);\r\n      }\r\n    },\r\n    onSortingChange: setSorting,\r\n\r\n    getCoreRowModel: getCoreRowModel(),\r\n    getFilteredRowModel: getFilteredRowModel(), // Ensure filtering is applied to the entire dataset\r\n    getPaginationRowModel: getPaginationRowModel(),\r\n    getSortedRowModel: getSortedRowModel(),\r\n    enableRowSelection: true,\r\n    enableSorting: true,\r\n    enableSortingRemoval: true,\r\n\r\n    state: {\r\n      pagination,\r\n      columnFilters,\r\n      globalFilter,\r\n      columnVisibility: effectiveColumnVisibility,\r\n      rowSelection: effectiveRowSelection,\r\n      sorting,\r\n    },\r\n  });\r\n\r\n  React.useEffect(() => {\r\n    if (onTableInit) {\r\n      onTableInit(table);\r\n    }\r\n  }, [onTableInit, table]);\r\n\r\n  // Effect to handle external row selection changes\r\n  React.useEffect(() => {\r\n    if (externalRowSelection !== undefined) {\r\n      // If we receive new external selection data, reset the table's row selection state\r\n      table.setRowSelection(externalRowSelection);\r\n    }\r\n  }, [externalRowSelection, table]);\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"rounded-md border border-neutral-400 overflow-hidden\">\r\n        <Table className=\"min-w-full\">\r\n          <TableHeader className=\"h-20\">\r\n            {table.getHeaderGroups().map((headerGroup) => (\r\n              <TableRow\r\n                key={headerGroup.id}\r\n                className=\"text-sm border-neutral-400\"\r\n              >\r\n                {headerGroup.headers.map((header) => (\r\n                  <TableHead\r\n                    key={header.id}\r\n                    className={`py-1 px-6 text-left bg-primary-500 text-neutral-100 font-semibold ${\r\n                      header.index === 0 ? \"w-12 py-3 px-6\" : \"\"\r\n                    }`}\r\n                    style={{\r\n                      cursor: header.column.getCanSort()\r\n                        ? \"pointer\"\r\n                        : \"default\",\r\n                    }}\r\n                  >\r\n                    <div onClick={header.column.getToggleSortingHandler()}>\r\n                      <div>\r\n                        {header.isPlaceholder\r\n                          ? null\r\n                          : flexRender(\r\n                              header.column.columnDef.header,\r\n                              header.getContext()\r\n                            )}\r\n                      </div>\r\n                    </div>\r\n                    {header.column.id === \"validation\" ? (\r\n                      <DropdownMenu>\r\n                        <DropdownMenuTrigger asChild>\r\n                          <Button\r\n                            variant=\"outline\"\r\n                            className=\"h-8 my-1 text-neutral-700 cursor-pointer\"\r\n                          >\r\n                            Filter\r\n                            <FaChevronDown />\r\n                          </Button>\r\n                        </DropdownMenuTrigger>\r\n                        <DropdownMenuContent className=\"bg-neutral-100 border border-neutral-200 shadow-md cursor-pointer\">\r\n                          <DropdownMenuItem\r\n                            className=\"cursor-pointer hover:bg-neutral-300\"\r\n                            onClick={() =>\r\n                              header.column.setFilterValue(\"Valid\")\r\n                            }\r\n                          >\r\n                            Valid\r\n                          </DropdownMenuItem>\r\n                          <DropdownMenuItem\r\n                            className=\"cursor-pointer hover:bg-neutral-300\"\r\n                            onClick={() =>\r\n                              header.column.setFilterValue(\"Not Valid\")\r\n                            }\r\n                          >\r\n                            Not Valid\r\n                          </DropdownMenuItem>\r\n                          <DropdownMenuItem\r\n                            className=\"cursor-pointer hover:bg-neutral-300\"\r\n                            onClick={() => header.column.setFilterValue(\"\")}\r\n                          >\r\n                            Clear Filter\r\n                          </DropdownMenuItem>\r\n                        </DropdownMenuContent>\r\n                      </DropdownMenu>\r\n                    ) : (\r\n                      header.column.getCanFilter() && (\r\n                        <input\r\n                          placeholder=\"Search...\"\r\n                          value={\r\n                            (header.column.getFilterValue() as string) || \"\"\r\n                          }\r\n                          onChange={(e) =>\r\n                            header.column.setFilterValue(e.target.value)\r\n                          }\r\n                          className=\"input-field max-w-48 text-sm my-1 px-2 py-1 bg-neutral-100 text-neutral-700 font-light border-none rounded-md\"\r\n                        />\r\n                      )\r\n                    )}\r\n                  </TableHead>\r\n                ))}\r\n              </TableRow>\r\n            ))}\r\n          </TableHeader>\r\n          <TableBody>\r\n            {table.getPaginationRowModel().rows.length ? (\r\n              table.getPaginationRowModel().rows.map((row) => (\r\n                <TableRow\r\n                  key={row.id}\r\n                  data-state={row.getIsSelected() && \"selected\"}\r\n                  className=\"hover:bg-neutral-50 text-sm border-neutral-400\"\r\n                  onClick={() => onRowClick?.(row.original)}\r\n                >\r\n                  {row.getVisibleCells().map((cell, index) => {\r\n                    return (\r\n                      <TableCell\r\n                        key={cell.id}\r\n                        className={`py-4 px-6 max-w-48  ${\r\n                          index === 0 ? \"py-3 px-6\" : \"\"\r\n                        } text-neutral-700 `}\r\n                      >\r\n                        {flexRender(\r\n                          cell.column.columnDef.cell,\r\n                          cell.getContext()\r\n                        )}\r\n                      </TableCell>\r\n                    );\r\n                  })}\r\n                </TableRow>\r\n              ))\r\n            ) : (\r\n              <TableRow>\r\n                <TableCell\r\n                  colSpan={columns.length}\r\n                  className=\"h-24 text-center\"\r\n                >\r\n                  No results.\r\n                </TableCell>\r\n              </TableRow>\r\n            )}\r\n          </TableBody>\r\n        </Table>\r\n      </div>\r\n\r\n      <div className=\"flex items-center justify-end space-x-2 py-4\">\r\n        <div className=\"flex-1 text-sm text-muted-foreground\">\r\n          {table.getFilteredSelectedRowModel().rows.length} of{\" \"}\r\n          {table.getFilteredRowModel().rows.length} row(s) selected.\r\n        </div>\r\n\r\n        {data.length > pagination.pageSize && (\r\n          <div className=\"flex items-center justify-end space-x-2 py-4\">\r\n            <button\r\n              className=\"btn-primary\"\r\n              onClick={() => table.previousPage()}\r\n              disabled={!table.getCanPreviousPage()}\r\n            >\r\n              Previous\r\n            </button>\r\n            <button\r\n              className=\"btn-primary\"\r\n              onClick={() => table.nextPage()}\r\n              disabled={!table.getCanNextPage()}\r\n            >\r\n              Next\r\n            </button>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { GeneralTable };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAeA;AAQA;AAMA;AACA;AAjCA;;;;;;;;AAgDA,MAAM,eAAe,CAAgB,EACnC,OAAO,EACP,IAAI,EACJ,YAAY,EACZ,eAAe,EACf,WAAW,EACX,kBAAkB,wBAAwB,EAC1C,qBAAqB,2BAA2B,EAChD,oBAAoB,EACpB,cAAc,oBAAoB,EAClC,UAAU,EACuB;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;QACjD,WAAW;QACX,UAAU;IACZ;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CACtD,EAAE;IAEJ,MAAM,CAAC,SAAS,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAe,EAAE;IAE7D,MAAM,CAAC,0BAA0B,4BAA4B,GAC3D,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAkB,CAAC;IAEnC,MAAM,4BACJ,4BAA4B;IAC9B,MAAM,+BACJ,+BAA+B;IAEjC,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAoB,CAAC;IAE3F,iEAAiE;IACjE,MAAM,wBAAwB,yBAAyB,YACnD,uBACA;IAEJ,MAAM,QAAQ,CAAA,GAAA,sLAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B;QACA;QACA,oBAAoB;QACpB,uBAAuB;QACvB,sBAAsB;QACtB,0BACE;QACF,sBAAsB,CAAC;YACrB,MAAM,kBACJ,OAAO,YAAY,aAAa,QAAQ,yBAAyB;YAEnE,+DAA+D;YAC/D,IAAI,yBAAyB,WAAW;gBACtC,wBAAwB;YAC1B;YAEA,IAAI,sBAAsB;gBACxB,qBAAqB;YACvB;QACF;QACA,iBAAiB;QAEjB,iBAAiB,CAAA,GAAA,qKAAA,CAAA,kBAAe,AAAD;QAC/B,qBAAqB,CAAA,GAAA,qKAAA,CAAA,sBAAmB,AAAD;QACvC,uBAAuB,CAAA,GAAA,qKAAA,CAAA,wBAAqB,AAAD;QAC3C,mBAAmB,CAAA,GAAA,qKAAA,CAAA,oBAAiB,AAAD;QACnC,oBAAoB;QACpB,eAAe;QACf,sBAAsB;QAEtB,OAAO;YACL;YACA;YACA;YACA,kBAAkB;YAClB,cAAc;YACd;QACF;IACF;IAEA,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,aAAa;YACf,YAAY;QACd;IACF,GAAG;QAAC;QAAa;KAAM;IAEvB,kDAAkD;IAClD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,yBAAyB,WAAW;YACtC,mFAAmF;YACnF,MAAM,eAAe,CAAC;QACxB;IACF,GAAG;QAAC;QAAsB;KAAM;IAEhC,qBACE,8OAAC;;0BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0HAAA,CAAA,QAAK;oBAAC,WAAU;;sCACf,8OAAC,0HAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAC5B,8OAAC,0HAAA,CAAA,WAAQ;oCAEP,WAAU;8CAET,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,uBACxB,8OAAC,0HAAA,CAAA,YAAS;4CAER,WAAW,CAAC,kEAAkE,EAC5E,OAAO,KAAK,KAAK,IAAI,mBAAmB,IACxC;4CACF,OAAO;gDACL,QAAQ,OAAO,MAAM,CAAC,UAAU,KAC5B,YACA;4CACN;;8DAEA,8OAAC;oDAAI,SAAS,OAAO,MAAM,CAAC,uBAAuB;8DACjD,cAAA,8OAAC;kEACE,OAAO,aAAa,GACjB,OACA,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EACP,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;;;;;;;;;;;gDAI1B,OAAO,MAAM,CAAC,EAAE,KAAK,6BACpB,8OAAC,qIAAA,CAAA,eAAY;;sEACX,8OAAC,qIAAA,CAAA,sBAAmB;4DAAC,OAAO;sEAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,WAAU;;oEACX;kFAEC,8OAAC,8IAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;sEAGlB,8OAAC,qIAAA,CAAA,sBAAmB;4DAAC,WAAU;;8EAC7B,8OAAC,qIAAA,CAAA,mBAAgB;oEACf,WAAU;oEACV,SAAS,IACP,OAAO,MAAM,CAAC,cAAc,CAAC;8EAEhC;;;;;;8EAGD,8OAAC,qIAAA,CAAA,mBAAgB;oEACf,WAAU;oEACV,SAAS,IACP,OAAO,MAAM,CAAC,cAAc,CAAC;8EAEhC;;;;;;8EAGD,8OAAC,qIAAA,CAAA,mBAAgB;oEACf,WAAU;oEACV,SAAS,IAAM,OAAO,MAAM,CAAC,cAAc,CAAC;8EAC7C;;;;;;;;;;;;;;;;;2DAML,OAAO,MAAM,CAAC,YAAY,oBACxB,8OAAC;oDACC,aAAY;oDACZ,OACE,AAAC,OAAO,MAAM,CAAC,cAAc,MAAiB;oDAEhD,UAAU,CAAC,IACT,OAAO,MAAM,CAAC,cAAc,CAAC,EAAE,MAAM,CAAC,KAAK;oDAE7C,WAAU;;;;;;;2CAlEX,OAAO,EAAE;;;;;mCALb,YAAY,EAAE;;;;;;;;;;sCAgFzB,8OAAC,0HAAA,CAAA,YAAS;sCACP,MAAM,qBAAqB,GAAG,IAAI,CAAC,MAAM,GACxC,MAAM,qBAAqB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,oBACtC,8OAAC,0HAAA,CAAA,WAAQ;oCAEP,cAAY,IAAI,aAAa,MAAM;oCACnC,WAAU;oCACV,SAAS,IAAM,aAAa,IAAI,QAAQ;8CAEvC,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,MAAM;wCAChC,qBACE,8OAAC,0HAAA,CAAA,YAAS;4CAER,WAAW,CAAC,oBAAoB,EAC9B,UAAU,IAAI,cAAc,GAC7B,kBAAkB,CAAC;sDAEnB,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EACR,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;2CAPZ,KAAK,EAAE;;;;;oCAWlB;mCAnBK,IAAI,EAAE;;;;0DAuBf,8OAAC,0HAAA,CAAA,WAAQ;0CACP,cAAA,8OAAC,0HAAA,CAAA,YAAS;oCACR,SAAS,QAAQ,MAAM;oCACvB,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASX,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BACZ,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM;4BAAC;4BAAI;4BACpD,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;4BAAC;;;;;;;oBAG1C,KAAK,MAAM,GAAG,WAAW,QAAQ,kBAChC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,SAAS,IAAM,MAAM,YAAY;gCACjC,UAAU,CAAC,MAAM,kBAAkB;0CACpC;;;;;;0CAGD,8OAAC;gCACC,WAAU;gCACV,SAAS,IAAM,MAAM,QAAQ;gCAC7B,UAAU,CAAC,MAAM,cAAc;0CAChC;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 3094, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-primary-500 focus-visible:ring-[1px]\",\r\n        \"focus-visible:outline-none\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mcACA,qFACA,8BACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3120, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label } "], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3149, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nexport interface TextareaProps\r\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\r\n\r\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\r\n  ({ className, ...props }, ref) => {\r\n    return (\r\n      <textarea\r\n        className={cn(\r\n          \"flex min-h-[80px] w-full rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700 dark:bg-gray-900 dark:placeholder:text-gray-500\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nTextarea.displayName = \"Textarea\";\r\n\r\nexport { Textarea };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mUACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3177, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ui/select.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\";\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Select = SelectPrimitive.Root;\r\n\r\nconst SelectGroup = SelectPrimitive.Group;\r\n\r\nconst SelectValue = SelectPrimitive.Value;\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-10 w-full items-center justify-between rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm ring-offset-neutral-100 placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 dark:border-gray-700 dark:bg-gray-900 dark:ring-offset-gray-900 dark:placeholder:text-gray-500\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n));\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName;\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n));\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName;\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n));\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName;\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border border-gray-200 bg-neutral-100 text-slate-700 shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:border-gray-700 dark:bg-gray-900 dark:text-slate-200\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n));\r\nSelectContent.displayName = SelectPrimitive.Content.displayName;\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName;\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-gray-100 focus:text-gray-900 data-[disabled]:pointer-events-none data-[disabled]:opacity-50 dark:focus:bg-gray-800 dark:focus:text-gray-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n));\r\nSelectItem.displayName = SelectPrimitive.Item.displayName;\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-gray-200 dark:bg-gray-700\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName;\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4ZACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4gBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qQACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3369, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ui/switch.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Switch = React.forwardRef<\r\n  React.ElementRef<typeof SwitchPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SwitchPrimitives.Root\r\n    className={cn(\r\n      \"peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 focus-visible:ring-offset-neutral-100 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 dark:focus-visible:ring-offset-gray-900\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  >\r\n    <SwitchPrimitives.Thumb\r\n      className={cn(\r\n        \"pointer-events-none block h-5 w-5 rounded-full shadow-lg ring-0 transition-transform data-[state=checked]:bg-neutral-100 data-[state=unchecked]:bg-primary-500 data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\r\n      )}\r\n    />\r\n  </SwitchPrimitives.Root>\r\n));\r\nSwitch.displayName = SwitchPrimitives.Root.displayName;\r\n\r\nexport { Switch };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,8OAAC,kKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3405, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\r\nimport { CheckIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Checkbox({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\r\n  return (\r\n    <CheckboxPrimitive.Root\r\n      data-slot=\"checkbox\"\r\n      className={cn(\r\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <CheckboxPrimitive.Indicator\r\n        data-slot=\"checkbox-indicator\"\r\n        className=\"flex items-center justify-center text-current transition-none\"\r\n      >\r\n        <CheckIcon className=\"size-3.5\" />\r\n      </CheckboxPrimitive.Indicator>\r\n    </CheckboxPrimitive.Root>\r\n  )\r\n}\r\n\r\nexport { Checkbox }\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,8OAAC,wMAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B", "debugId": null}}, {"offset": {"line": 3450, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ui/radio-group.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\"\r\nimport { Circle } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst RadioGroup = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Root\r\n      className={cn(\"grid gap-2\", className)}\r\n      {...props}\r\n      ref={ref}\r\n    />\r\n  )\r\n})\r\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName\r\n\r\nconst RadioGroupItem = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Item\r\n      ref={ref}\r\n      className={cn(\r\n        \"aspect-square h-4 w-4 rounded-full border text-gray-900 shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-gray-950 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus-visible:ring-gray-300\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\r\n        <Circle className=\"h-2.5 w-2.5 fill-current text-current\" />\r\n      </RadioGroupPrimitive.Indicator>\r\n    </RadioGroupPrimitive.Item>\r\n  )\r\n})\r\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName\r\n\r\nexport { RadioGroup, RadioGroupItem } "], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;QAC3B,GAAG,KAAK;QACT,KAAK;;;;;;AAGX;AACA,WAAW,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yOACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,YAA6B;YAAC,WAAU;sBACvC,cAAA,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI1B;AACA,eAAe,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3510, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ui/index.ts"], "sourcesContent": ["export { Button } from './button';\nexport { Input } from './input';\nexport { Label } from './label';\nexport { Textarea } from './textarea';\nexport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './select';\nexport { Switch } from './switch';\nexport { Checkbox } from './checkbox';\nexport { RadioGroup, RadioGroupItem } from './radio-group'; "], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 3549, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/tables/columns/TemplateListColumnsForProjectCreation.tsx"], "sourcesContent": ["import { Checkbox } from \"@/components/ui\";\r\nimport { Template } from \"@/types\";\r\nimport { ColumnDef } from \"@tanstack/react-table\";\r\n\r\ntype Props = {\r\n  selectedRowId: number | null;\r\n  setSelectedRowId: (id: number | null) => void;\r\n};\r\n\r\nconst getTemplateListColumnsForProjectCreation = ({\r\n  selectedRowId,\r\n  setSelectedRowId,\r\n}: Props): ColumnDef<Template>[] => [\r\n  {\r\n    id: \"select\",\r\n    header: \"\",\r\n    cell: ({ row }) => {\r\n      const rowId = row.original.id;\r\n      const isSelected = rowId === selectedRowId;\r\n\r\n      return (\r\n        <Checkbox\r\n          className=\"w-6 h-6 bg-neutral-100 rounded border border-neutral-400 data-[state=checked]:bg-neutral-100 data-[state=checked]:text-primary-500 data-[state=checked]:border-primary-500 cursor-pointer\"\r\n          checked={isSelected}\r\n          onCheckedChange={(value) => setSelectedRowId(value ? rowId : null)}\r\n          aria-label=\"Select row\"\r\n        />\r\n      );\r\n    },\r\n  },\r\n  {\r\n    accessorKey: \"name\",\r\n    header: \"Name\",\r\n  },\r\n  {\r\n    id: \"owner\",\r\n    accessorFn: (row) => row.user?.name ?? \"unknown\",\r\n    header: \"Owner\",\r\n    cell: ({ getValue }) => getValue(),\r\n  },\r\n  {\r\n    id: \"questions\",\r\n    accessorFn: (row) => row.libraryQuestions?.length.toString() ?? \"0\",\r\n    header: \"Questions\",\r\n    cell: ({ getValue }) => getValue(),\r\n  },\r\n  {\r\n    accessorKey: \"updatedAt\",\r\n    header: \"Last Modified\",\r\n  },\r\n];\r\n\r\nexport { getTemplateListColumnsForProjectCreation };\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AASA,MAAM,2CAA2C,CAAC,EAChD,aAAa,EACb,gBAAgB,EACV,GAA4B;QAClC;YACE,IAAI;YACJ,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,QAAQ,IAAI,QAAQ,CAAC,EAAE;gBAC7B,MAAM,aAAa,UAAU;gBAE7B,qBACE,8OAAC,6HAAA,CAAA,WAAQ;oBACP,WAAU;oBACV,SAAS;oBACT,iBAAiB,CAAC,QAAU,iBAAiB,QAAQ,QAAQ;oBAC7D,cAAW;;;;;;YAGjB;QACF;QACA;YACE,aAAa;YACb,QAAQ;QACV;QACA;YACE,IAAI;YACJ,YAAY,CAAC,MAAQ,IAAI,IAAI,EAAE,QAAQ;YACvC,QAAQ;YACR,MAAM,CAAC,EAAE,QAAQ,EAAE,GAAK;QAC1B;QACA;YACE,IAAI;YACJ,YAAY,CAAC,MAAQ,IAAI,gBAAgB,EAAE,OAAO,cAAc;YAChE,QAAQ;YACR,MAAM,CAAC,EAAE,QAAQ,EAAE,GAAK;QAC1B;QACA;YACE,aAAa;YACb,QAAQ;QACV;KACD", "debugId": null}}, {"offset": {"line": 3604, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/CreateProjectFromTemplateModal.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport Modal from \"./Modal\";\r\nimport { FieldValues, useForm } from \"react-hook-form\";\r\nimport { Select } from \"../general/Select\";\r\nimport countries from \"@/constants/countryNames.json\";\r\nimport { SectorLabelMap } from \"@/constants/sectors\";\r\nimport { Briefcase, FileText, Globe } from \"lucide-react\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { labelToKey } from \"@/lib/labelToKey\";\r\nimport { createProjectFromTemplate } from \"@/lib/api/projects\";\r\nimport { useAuth } from \"@/hooks/useAuth\";\r\nimport { Template } from \"@/types\";\r\nimport { fetchTemplateById } from \"@/lib/api/templates\";\r\n\r\nconst CreateProjectFromTemplateModal = ({\r\n  showModal,\r\n  closeModal,\r\n  back,\r\n  templateId,\r\n}: {\r\n  showModal: boolean;\r\n  closeModal: () => void;\r\n  back: () => void;\r\n  templateId: number;\r\n}) => {\r\n  const {\r\n    register,\r\n    formState: { isSubmitting, errors, isSubmitted },\r\n    handleSubmit,\r\n    setValue,\r\n    reset,\r\n  } = useForm();\r\n\r\n  const dispatch = useDispatch();\r\n  const router = useRouter();\r\n\r\n  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);\r\n  const [selectedSector, setSelectedSector] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    register(\"country\", { required: \"Please select a country\" });\r\n    register(\"sector\", { required: \"Please select a sector\" });\r\n  }, [register]);\r\n\r\n  useEffect(() => {\r\n    setValue(\"country\", selectedCountry, { shouldValidate: isSubmitted });\r\n    setValue(\"sector\", selectedSector, { shouldValidate: isSubmitted });\r\n  }, [setValue, selectedCountry, selectedSector]);\r\n\r\n  const { user, isLoading: sessionLoading } = useAuth();\r\n\r\n  const {\r\n    data: templateData,\r\n    isLoading: templateLoading,\r\n    isError: templateError,\r\n  } = useQuery<Template>({\r\n    queryKey: [\"templates\", user?.id, templateId],\r\n    queryFn: () => fetchTemplateById({ templateId }),\r\n    enabled: !!user?.id,\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (templateData) {\r\n      reset({\r\n        projectName: templateData.name,\r\n        description: templateData.description,\r\n        sector: templateData.sector,\r\n        country: templateData.country,\r\n      });\r\n      setSelectedCountry(templateData.country);\r\n      setSelectedSector(templateData.sector);\r\n    }\r\n  }, [templateData, reset]);\r\n\r\n  const queryClient = useQueryClient();\r\n\r\n  const projectMutation = useMutation({\r\n    mutationFn: createProjectFromTemplate,\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"projects\"], exact: false });\r\n      closeModal();\r\n      router.push(\"/dashboard\");\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Project has been created successfully.\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n    },\r\n    onError: (error) => {\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Failed to create project\" + error.message,\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n  });\r\n\r\n  const onSubmit = async (data: FieldValues) => {\r\n    const dataToSend = {\r\n      templateId,\r\n      name: data.projectName,\r\n      description: data.description,\r\n      sector: data.sector,\r\n      country: data.country,\r\n    };\r\n    projectMutation.mutate(dataToSend);\r\n  };\r\n\r\n  if (sessionLoading || templateLoading || templateError) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <Modal isOpen={showModal} onClose={closeModal} className=\"w-3/6\">\r\n      <form className=\"flex flex-col gap-8\" onSubmit={handleSubmit(onSubmit)}>\r\n        <h1 className=\"heading-text\">Create a new project</h1>\r\n        <div className=\"flex flex-col gap-4\">\r\n          {/* Project Name */}\r\n          <div className=\"label-input-group group\">\r\n            <label htmlFor=\"project-name\" className=\"label-text\">\r\n              <FileText size={16} /> Project Name\r\n            </label>\r\n            <input\r\n              {...register(\"projectName\", {\r\n                required: \"Project name is required.\",\r\n              })}\r\n              id=\"project-name\"\r\n              type=\"text\"\r\n              className=\"input-field\"\r\n              placeholder=\"Enter a project name\"\r\n            />\r\n            {errors.projectName && (\r\n              <p className=\"text-red-500 text-sm\">{`${errors.projectName.message}`}</p>\r\n            )}\r\n          </div>\r\n          {/* Project Description */}\r\n          <div className=\"label-input-group group\">\r\n            <label htmlFor=\"description\" className=\"label-text\">\r\n              Description\r\n            </label>\r\n            <textarea\r\n              id=\"description\"\r\n              {...register(\"description\", {\r\n                required: \"Please enter the project description\",\r\n              })}\r\n              className=\"input-field resize-none\"\r\n              cols={4}\r\n              placeholder=\"Enter the project description\"\r\n            />\r\n            {errors.description && (\r\n              <p className=\"text-red-500 text-sm\">{`${errors.description.message}`}</p>\r\n            )}\r\n          </div>\r\n          {/* Country and Sector */}\r\n          <div className=\"grid grid-cols-2 gap-4\">\r\n            <div className=\"label-input-group group\">\r\n              <label htmlFor=\"country\" className=\"label-text\">\r\n                <Globe size={16} />\r\n                Country\r\n              </label>\r\n              <Select\r\n                id={`country`}\r\n                options={countries}\r\n                value={selectedCountry}\r\n                onChange={setSelectedCountry}\r\n              />\r\n              {errors.country && (\r\n                <p className=\"text-red-500 text-sm\">{`${errors.country.message}`}</p>\r\n              )}\r\n            </div>\r\n            <div className=\"label-input-group group\">\r\n              <label htmlFor=\"sector\" className=\"label-text\">\r\n                <Briefcase size={16} /> Sector\r\n              </label>\r\n              <Select\r\n                id={`sector`}\r\n                options={Object.values(SectorLabelMap)} // Display labels\r\n                value={\r\n                  selectedSector && SectorLabelMap[selectedSector]\r\n                    ? SectorLabelMap[selectedSector]\r\n                    : \"Select an option\"\r\n                }\r\n                onChange={(label) => {\r\n                  const selectedKey = labelToKey(label, SectorLabelMap);\r\n                  setSelectedSector(selectedKey); // Set the enum key for storage\r\n                }}\r\n              />\r\n              {errors.sector && (\r\n                <p className=\"text-red-500 text-sm\">{`${errors.sector.message}`}</p>\r\n              )}\r\n            </div>\r\n          </div>\r\n          <div className=\"flex justify-end gap-3 mt-4\">\r\n            <button type=\"button\" onClick={back} className=\"btn-outline\">\r\n              Back\r\n            </button>\r\n            <button type=\"submit\" className=\"btn-primary\">\r\n              {isSubmitting ? (\r\n                <span className=\"flex items-center gap-2\">\r\n                  Creating{\" \"}\r\n                  <div className=\"size-4 animate-spin border-x border-neutral-100 rounded-full\"></div>\r\n                </span>\r\n              ) : (\r\n                \"Create Project\"\r\n              )}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </form>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport { CreateProjectFromTemplateModal };\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AAhBA;;;;;;;;;;;;;;;;;AAkBA,MAAM,iCAAiC,CAAC,EACtC,SAAS,EACT,UAAU,EACV,IAAI,EACJ,UAAU,EAMX;IACC,MAAM,EACJ,QAAQ,EACR,WAAW,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,EAChD,YAAY,EACZ,QAAQ,EACR,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD;IAEV,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,WAAW;YAAE,UAAU;QAA0B;QAC1D,SAAS,UAAU;YAAE,UAAU;QAAyB;IAC1D,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,WAAW,iBAAiB;YAAE,gBAAgB;QAAY;QACnE,SAAS,UAAU,gBAAgB;YAAE,gBAAgB;QAAY;IACnE,GAAG;QAAC;QAAU;QAAiB;KAAe;IAE9C,MAAM,EAAE,IAAI,EAAE,WAAW,cAAc,EAAE,GAAG,CAAA,GAAA,iHAAA,CAAA,UAAO,AAAD;IAElD,MAAM,EACJ,MAAM,YAAY,EAClB,WAAW,eAAe,EAC1B,SAAS,aAAa,EACvB,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAY;QACrB,UAAU;YAAC;YAAa,MAAM;YAAI;SAAW;QAC7C,SAAS,IAAM,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE;gBAAE;YAAW;QAC9C,SAAS,CAAC,CAAC,MAAM;IACnB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc;YAChB,MAAM;gBACJ,aAAa,aAAa,IAAI;gBAC9B,aAAa,aAAa,WAAW;gBACrC,QAAQ,aAAa,MAAM;gBAC3B,SAAS,aAAa,OAAO;YAC/B;YACA,mBAAmB,aAAa,OAAO;YACvC,kBAAkB,aAAa,MAAM;QACvC;IACF,GAAG;QAAC;QAAc;KAAM;IAExB,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,kBAAkB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QAClC,YAAY,sHAAA,CAAA,4BAAyB;QACrC,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;gBAAE,OAAO;YAAM;YACrE;YACA,OAAO,IAAI,CAAC;YACZ,SACE,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS;gBACT,MAAM;YACR;QAEJ;QACA,SAAS,CAAC;YACR,SACE,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS,6BAA6B,MAAM,OAAO;gBACnD,MAAM;YACR;QAEJ;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,MAAM,aAAa;YACjB;YACA,MAAM,KAAK,WAAW;YACtB,aAAa,KAAK,WAAW;YAC7B,QAAQ,KAAK,MAAM;YACnB,SAAS,KAAK,OAAO;QACvB;QACA,gBAAgB,MAAM,CAAC;IACzB;IAEA,IAAI,kBAAkB,mBAAmB,eAAe;QACtD,OAAO;IACT;IAEA,qBACE,8OAAC,8HAAA,CAAA,UAAK;QAAC,QAAQ;QAAW,SAAS;QAAY,WAAU;kBACvD,cAAA,8OAAC;YAAK,WAAU;YAAsB,UAAU,aAAa;;8BAC3D,8OAAC;oBAAG,WAAU;8BAAe;;;;;;8BAC7B,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,SAAQ;oCAAe,WAAU;;sDACtC,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;wCAAM;;;;;;;8CAExB,8OAAC;oCACE,GAAG,SAAS,eAAe;wCAC1B,UAAU;oCACZ,EAAE;oCACF,IAAG;oCACH,MAAK;oCACL,WAAU;oCACV,aAAY;;;;;;gCAEb,OAAO,WAAW,kBACjB,8OAAC;oCAAE,WAAU;8CAAwB,GAAG,OAAO,WAAW,CAAC,OAAO,EAAE;;;;;;;;;;;;sCAIxE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,SAAQ;oCAAc,WAAU;8CAAa;;;;;;8CAGpD,8OAAC;oCACC,IAAG;oCACF,GAAG,SAAS,eAAe;wCAC1B,UAAU;oCACZ,EAAE;oCACF,WAAU;oCACV,MAAM;oCACN,aAAY;;;;;;gCAEb,OAAO,WAAW,kBACjB,8OAAC;oCAAE,WAAU;8CAAwB,GAAG,OAAO,WAAW,CAAC,OAAO,EAAE;;;;;;;;;;;;sCAIxE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,SAAQ;4CAAU,WAAU;;8DACjC,8OAAC,oMAAA,CAAA,QAAK;oDAAC,MAAM;;;;;;gDAAM;;;;;;;sDAGrB,8OAAC,gIAAA,CAAA,SAAM;4CACL,IAAI,CAAC,OAAO,CAAC;4CACb,SAAS,iGAAA,CAAA,UAAS;4CAClB,OAAO;4CACP,UAAU;;;;;;wCAEX,OAAO,OAAO,kBACb,8OAAC;4CAAE,WAAU;sDAAwB,GAAG,OAAO,OAAO,CAAC,OAAO,EAAE;;;;;;;;;;;;8CAGpE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,SAAQ;4CAAS,WAAU;;8DAChC,8OAAC,4MAAA,CAAA,YAAS;oDAAC,MAAM;;;;;;gDAAM;;;;;;;sDAEzB,8OAAC,gIAAA,CAAA,SAAM;4CACL,IAAI,CAAC,MAAM,CAAC;4CACZ,SAAS,OAAO,MAAM,CAAC,oHAAA,CAAA,iBAAc;4CACrC,OACE,kBAAkB,oHAAA,CAAA,iBAAc,CAAC,eAAe,GAC5C,oHAAA,CAAA,iBAAc,CAAC,eAAe,GAC9B;4CAEN,UAAU,CAAC;gDACT,MAAM,cAAc,CAAA,GAAA,iHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,oHAAA,CAAA,iBAAc;gDACpD,kBAAkB,cAAc,+BAA+B;4CACjE;;;;;;wCAED,OAAO,MAAM,kBACZ,8OAAC;4CAAE,WAAU;sDAAwB,GAAG,OAAO,MAAM,CAAC,OAAO,EAAE;;;;;;;;;;;;;;;;;;sCAIrE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,MAAK;oCAAS,SAAS;oCAAM,WAAU;8CAAc;;;;;;8CAG7D,8OAAC;oCAAO,MAAK;oCAAS,WAAU;8CAC7B,6BACC,8OAAC;wCAAK,WAAU;;4CAA0B;4CAC/B;0DACT,8OAAC;gDAAI,WAAU;;;;;;;;;;;+CAGjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}, {"offset": {"line": 4014, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/ChooseTemplateModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState } from \"react\";\r\nimport Modal from \"./Modal\";\r\nimport { IoChevronForward } from \"react-icons/io5\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport { fetchTemplates } from \"@/lib/api/templates\";\r\nimport { useAuth } from \"@/hooks/useAuth\";\r\nimport Spinner from \"../general/Spinner\";\r\nimport { GeneralTable } from \"../tables/GeneralTable\";\r\nimport { Template } from \"@/types\";\r\nimport { getTemplateListColumnsForProjectCreation } from \"../tables/columns/TemplateListColumnsForProjectCreation\";\r\nimport { CreateProjectFromTemplateModal } from \"./CreateProjectFromTemplateModal\";\r\n\r\ntype ProjectTemplateModalProps = {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  back: () => void;\r\n};\r\n\r\nconst ChooseTemplateModal: React.FC<ProjectTemplateModalProps> = ({\r\n  isOpen,\r\n  back,\r\n  onClose,\r\n}) => {\r\n  const { user } = useAuth();\r\n\r\n  const {\r\n    data: templatesData,\r\n    isLoading: templatesLoading,\r\n    isError: templatesError,\r\n  } = useQuery<Template[]>({\r\n    queryFn: fetchTemplates,\r\n    queryKey: [\"templates\", user?.id],\r\n    enabled: !!user?.id,\r\n  });\r\n\r\n  const [selectedRowId, setSelectedRowId] = useState<number | null>(null);\r\n  const [showCreateProjectModal, setShowCreateProjectModal] =\r\n    useState<boolean>(false);\r\n  const [isExiting, setIsExiting] = useState<boolean>(false);\r\n\r\n  const columns = getTemplateListColumnsForProjectCreation({\r\n    selectedRowId,\r\n    setSelectedRowId,\r\n  });\r\n\r\n  const handleNext = () => {\r\n    if (!selectedRowId) return;\r\n    setShowCreateProjectModal(true);\r\n  };\r\n\r\n  const handleCloseCreateProjectModal = (action: \"back\" | \"close\") => {\r\n    setIsExiting(true);\r\n    // Allow time for exit animation before actually changing state\r\n    setTimeout(() => {\r\n      setShowCreateProjectModal(false);\r\n      setSelectedRowId(null);\r\n      if (action === \"close\") {\r\n        onClose();\r\n      }\r\n\r\n      setIsExiting(false);\r\n    }, 300); // Match this to the exit animation duration in Modal component\r\n  };\r\n\r\n  if (!templatesData) return null;\r\n\r\n  if (templatesLoading) {\r\n    return <Spinner />;\r\n  }\r\n  if (templatesError) {\r\n    return <div>Error loading data, please try again</div>;\r\n  }\r\n  return (\r\n    <>\r\n      <Modal\r\n        isOpen={isOpen && !showCreateProjectModal}\r\n        onClose={() => {\r\n          setSelectedRowId(null);\r\n          onClose();\r\n        }}\r\n        className=\"bg-neutral-100 p-4 mobile:p-6 rounded-lg w-[95%] mobile:w-[85%] tablet:w-[75%] laptop:w-[60%] desktop:w-[50%] max-w-4xl mx-auto\"\r\n      >\r\n        <div className=\"flex flex-col gap-6\">\r\n          <div className=\"flex justify-between items-center\">\r\n            <h2 className=\"text-xl font-semibold text-neutral-700\">\r\n              Select a Template\r\n            </h2>\r\n          </div>\r\n          <GeneralTable data={templatesData} columns={columns} />\r\n\r\n          <div className=\"flex justify-end gap-3 mt-4\">\r\n            <button\r\n              onClick={() => {\r\n                setSelectedRowId(null);\r\n                back();\r\n              }}\r\n              className=\"btn-outline\"\r\n            >\r\n              Back\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              disabled={!selectedRowId}\r\n              className=\"btn-primary\"\r\n              onClick={handleNext}\r\n            >\r\n              Next\r\n              <IoChevronForward />\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </Modal>\r\n      {(showCreateProjectModal || isExiting) && selectedRowId !== null && (\r\n        <CreateProjectFromTemplateModal\r\n          showModal={showCreateProjectModal && !isExiting}\r\n          closeModal={() => handleCloseCreateProjectModal(\"close\")}\r\n          back={() => handleCloseCreateProjectModal(\"back\")}\r\n          templateId={selectedRowId}\r\n        />\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport { ChooseTemplateModal };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAZA;;;;;;;;;;;;AAoBA,MAAM,sBAA2D,CAAC,EAChE,MAAM,EACN,IAAI,EACJ,OAAO,EACR;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,iHAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,EACJ,MAAM,aAAa,EACnB,WAAW,gBAAgB,EAC3B,SAAS,cAAc,EACxB,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAc;QACvB,SAAS,uHAAA,CAAA,iBAAc;QACvB,UAAU;YAAC;YAAa,MAAM;SAAG;QACjC,SAAS,CAAC,CAAC,MAAM;IACnB;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,wBAAwB,0BAA0B,GACvD,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAEpD,MAAM,UAAU,CAAA,GAAA,yKAAA,CAAA,2CAAwC,AAAD,EAAE;QACvD;QACA;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,eAAe;QACpB,0BAA0B;IAC5B;IAEA,MAAM,gCAAgC,CAAC;QACrC,aAAa;QACb,+DAA+D;QAC/D,WAAW;YACT,0BAA0B;YAC1B,iBAAiB;YACjB,IAAI,WAAW,SAAS;gBACtB;YACF;YAEA,aAAa;QACf,GAAG,MAAM,+DAA+D;IAC1E;IAEA,IAAI,CAAC,eAAe,OAAO;IAE3B,IAAI,kBAAkB;QACpB,qBAAO,8OAAC,iIAAA,CAAA,UAAO;;;;;IACjB;IACA,IAAI,gBAAgB;QAClB,qBAAO,8OAAC;sBAAI;;;;;;IACd;IACA,qBACE;;0BACE,8OAAC,8HAAA,CAAA,UAAK;gBACJ,QAAQ,UAAU,CAAC;gBACnB,SAAS;oBACP,iBAAiB;oBACjB;gBACF;gBACA,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;;;;;;sCAIzD,8OAAC,qIAAA,CAAA,eAAY;4BAAC,MAAM;4BAAe,SAAS;;;;;;sCAE5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;wCACP,iBAAiB;wCACjB;oCACF;oCACA,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,UAAU,CAAC;oCACX,WAAU;oCACV,SAAS;;wCACV;sDAEC,8OAAC,+IAAA,CAAA,mBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAKxB,CAAC,0BAA0B,SAAS,KAAK,kBAAkB,sBAC1D,8OAAC,uJAAA,CAAA,iCAA8B;gBAC7B,WAAW,0BAA0B,CAAC;gBACtC,YAAY,IAAM,8BAA8B;gBAChD,MAAM,IAAM,8BAA8B;gBAC1C,YAAY;;;;;;;;AAKtB", "debugId": null}}, {"offset": {"line": 4195, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/NewProjectModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\nimport Modal from \"./Modal\";\r\nimport { LuLayoutTemplate } from \"react-icons/lu\";\r\nimport { MdEdit } from \"react-icons/md\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { showCreateProjectModal } from \"@/redux/slices/createProjectSlice\";\r\nimport { ChooseTemplateModal } from \"./ChooseTemplateModal\";\r\n\r\ninterface NewProjectModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nconst NewProjectModal: React.FC<NewProjectModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n}) => {\r\n  const dispatch = useDispatch();\r\n  const [showTemplateModal, setShowTemplateModal] = useState(false);\r\n\r\n  const handleCreateProject = () => {\r\n    // Close current modal first\r\n    onClose();\r\n    // Then show the CreateProjectModal via Redux\r\n    dispatch(showCreateProjectModal());\r\n  };\r\n\r\n  const handleUseTemplate = () => {\r\n    setShowTemplateModal(true);\r\n  };\r\n\r\n  const handleChooseTemplateModalClose = (action: \"back\" | \"close\") => {\r\n    setShowTemplateModal(false);\r\n    if (action === \"close\") {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Modal\r\n        isOpen={isOpen && !showTemplateModal}\r\n        onClose={onClose}\r\n        className=\"bg-neutral-100 p-4 mobile:p-6 rounded-lg w-[95%] mobile:w-[85%] tablet:w-[75%] laptop:w-[60%] desktop:w-[50%] max-w-3xl mx-auto\"\r\n      >\r\n        <div className=\"flex flex-col gap-4 mobile:gap-6\">\r\n          <div className=\"flex justify-between items-center\">\r\n            <h2 className=\"text-lg mobile:text-xl font-semibold text-neutral-700\">\r\n              Create project: Choose a source\r\n            </h2>\r\n          </div>\r\n\r\n          <p className=\"text-sm mobile:text-base text-neutral-600\">\r\n            Choose one of the options below to continue. You will be prompted to\r\n            enter name and other details in further steps.\r\n          </p>\r\n\r\n          <div className=\"grid grid-cols-1 mobile:grid-cols-2 gap-3 mobile:gap-4\">\r\n            {/* Build from scratch */}\r\n            <div\r\n              onClick={handleCreateProject}\r\n              className=\"bg-neutral-200 rounded-lg p-4 mobile:p-6 tablet:p-8 flex flex-col items-center justify-center gap-2 mobile:gap-3 cursor-pointer hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300\"\r\n            >\r\n              <MdEdit className=\"w-5 h-5 mobile:w-6 mobile:h-6\" />\r\n              <span className=\"text-sm mobile:text-base text-center\">\r\n                Build from scratch\r\n              </span>\r\n            </div>\r\n\r\n            {/* Use a template */}\r\n            <div\r\n              onClick={handleUseTemplate}\r\n              className=\"bg-neutral-200 rounded-lg p-4 mobile:p-6 tablet:p-8 flex flex-col items-center justify-center gap-2 mobile:gap-3 cursor-pointer hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300\"\r\n            >\r\n              <LuLayoutTemplate className=\"w-5 h-5 mobile:w-6 mobile:h-6\" />\r\n              <span className=\"text-sm mobile:text-base text-center\">\r\n                Use a template\r\n              </span>\r\n            </div>\r\n\r\n            {/* Upload an XLSForm */}\r\n            {/* <div className=\"bg-neutral-100 rounded-lg p-4 sm:p-6 md:p-8 flex flex-col items-center justify-center gap-2 sm:gap-3 cursor-pointer hover:bg-neutral-200 transition-all duration-300\">\r\n              <LuUpload className=\"w-5 h-5 sm:w-6 sm:h-6 text-neutral-600\" />\r\n              <span className=\"text-sm sm:text-base text-neutral-700 text-center\">Upload an XLSForm</span>\r\n            </div> */}\r\n\r\n            {/* Import an XLSForm via URL */}\r\n            {/* <div className=\"bg-neutral-100 rounded-lg p-4 sm:p-6 md:p-8 flex flex-col items-center justify-center gap-2 sm:gap-3 cursor-pointer hover:bg-neutral-200 transition-all duration-300\">\r\n              <LuLink className=\"w-5 h-5 sm:w-6 sm:h-6 text-neutral-600\" />\r\n              <span className=\"text-sm sm:text-base text-neutral-700 text-center\">Import an XLSForm via URL</span>\r\n            </div> */}\r\n          </div>\r\n        </div>\r\n      </Modal>\r\n\r\n      {/* Template selection modal */}\r\n\r\n      <ChooseTemplateModal\r\n        isOpen={showTemplateModal}\r\n        onClose={() => handleChooseTemplateModalClose(\"close\")}\r\n        back={() => handleChooseTemplateModalClose(\"back\")}\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport { NewProjectModal };"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAeA,MAAM,kBAAkD,CAAC,EACvD,MAAM,EACN,OAAO,EACR;IACC,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,sBAAsB;QAC1B,4BAA4B;QAC5B;QACA,6CAA6C;QAC7C,SAAS,CAAA,GAAA,qIAAA,CAAA,yBAAsB,AAAD;IAChC;IAEA,MAAM,oBAAoB;QACxB,qBAAqB;IACvB;IAEA,MAAM,iCAAiC,CAAC;QACtC,qBAAqB;QACrB,IAAI,WAAW,SAAS;YACtB;QACF;IACF;IAEA,qBACE;;0BACE,8OAAC,8HAAA,CAAA,UAAK;gBACJ,QAAQ,UAAU,CAAC;gBACnB,SAAS;gBACT,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAwD;;;;;;;;;;;sCAKxE,8OAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,8IAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAK,WAAU;sDAAuC;;;;;;;;;;;;8CAMzD,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,8IAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;sDAC5B,8OAAC;4CAAK,WAAU;sDAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAsB/D,8OAAC,4IAAA,CAAA,sBAAmB;gBAClB,QAAQ;gBACR,SAAS,IAAM,+BAA+B;gBAC9C,MAAM,IAAM,+BAA+B;;;;;;;;AAInD", "debugId": null}}, {"offset": {"line": 4354, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/CreateLibraryModal.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { RiQuestionLine } from \"react-icons/ri\";\r\nimport { TbTemplate } from \"react-icons/tb\";\r\nimport { FiUpload } from \"react-icons/fi\";\r\nimport { FaFolder } from \"react-icons/fa\";\r\nimport { hideCreateLibraryModal } from \"@/redux/slices/createLibrarySlice\";\r\nimport { RootState } from \"@/redux/store\";\r\nimport Modal from \"./Modal\";\r\nimport { showCreateLibraryItemModal } from \"@/redux/slices/createLibraryItemSlice\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\ntype ItemOption = {\r\n  id: string;\r\n  title: string;\r\n  icon: React.ElementType;\r\n  onClick: () => void;\r\n};\r\n\r\nconst CreateLibraryModal = () => {\r\n  const dispatch = useDispatch();\r\n  const isOpen = useSelector((state: RootState) => state.createLibrary.visible);\r\n\r\n  // without this closing animation won't work\r\n  const [isClosing, setIsClosing] = useState(false);\r\n  const handleClose = () => {\r\n    setIsClosing(true);\r\n    setTimeout(() => {\r\n      dispatch(hideCreateLibraryModal());\r\n    }, 300);\r\n    // match the time with animation duration\r\n  };\r\n\r\n  const router = useRouter();\r\n\r\n  const itemOptions: ItemOption[] = [\r\n    {\r\n      id: \"question-block\",\r\n      title: \"Question Block\",\r\n      icon: RiQuestionLine,\r\n      onClick: () => {\r\n        handleClose();\r\n        router.push(\"/library/question-block/form-builder\");\r\n      },\r\n    },\r\n    {\r\n      id: \"template\",\r\n      title: \"Template\",\r\n      icon: TbTemplate,\r\n      onClick: () => {\r\n        handleClose();\r\n        dispatch(showCreateLibraryItemModal(\"template\"));\r\n      },\r\n    },\r\n    {\r\n      id: \"upload\",\r\n      title: \"Upload\",\r\n      icon: FiUpload,\r\n      onClick: () => {\r\n        // Handle Upload\r\n        handleClose();\r\n      },\r\n    },\r\n    {\r\n      id: \"collection\",\r\n      title: \"Collection\",\r\n      icon: FaFolder,\r\n      onClick: () => {\r\n        // Handle Collection creation\r\n        handleClose();\r\n      },\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <Modal\r\n      isOpen={isOpen && !isClosing}\r\n      onClose={handleClose}\r\n      className=\"p-6 rounded-md w-3/5\"\r\n    >\r\n      <h2 className=\"text-lg font-semibold text-neutral-700 mb-4\">\r\n        Create Library Item\r\n      </h2>\r\n      <div className=\"grid grid-cols-2 gap-4\">\r\n        {itemOptions.map((option) => (\r\n          <button\r\n            key={option.id}\r\n            onClick={option.onClick}\r\n            className=\"flex flex-col gap-2 items-center justify-center p-6 bg-neutral-200 rounded-md hover:bg-primary-500 hover:text-neutral-100 cursor-pointer transition-all duration-300\"\r\n          >\r\n            <option.icon size={24} className=\"\" />\r\n            <span className=\"\">{option.title}</span>\r\n          </button>\r\n        ))}\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport { CreateLibraryModal };\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;;;;;;;;;;;;AASA,MAAM,qBAAqB;IACzB,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAqB,MAAM,aAAa,CAAC,OAAO;IAE5E,4CAA4C;IAC5C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,cAAc;QAClB,aAAa;QACb,WAAW;YACT,SAAS,CAAA,GAAA,qIAAA,CAAA,yBAAsB,AAAD;QAChC,GAAG;IACH,yCAAyC;IAC3C;IAEA,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,cAA4B;QAChC;YACE,IAAI;YACJ,OAAO;YACP,MAAM,8IAAA,CAAA,iBAAc;YACpB,SAAS;gBACP;gBACA,OAAO,IAAI,CAAC;YACd;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,8IAAA,CAAA,aAAU;YAChB,SAAS;gBACP;gBACA,SAAS,CAAA,GAAA,yIAAA,CAAA,6BAA0B,AAAD,EAAE;YACtC;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,8IAAA,CAAA,WAAQ;YACd,SAAS;gBACP,gBAAgB;gBAChB;YACF;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,8IAAA,CAAA,WAAQ;YACd,SAAS;gBACP,6BAA6B;gBAC7B;YACF;QACF;KACD;IAED,qBACE,8OAAC,8HAAA,CAAA,UAAK;QACJ,QAAQ,UAAU,CAAC;QACnB,SAAS;QACT,WAAU;;0BAEV,8OAAC;gBAAG,WAAU;0BAA8C;;;;;;0BAG5D,8OAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC;wBAEC,SAAS,OAAO,OAAO;wBACvB,WAAU;;0CAEV,8OAAC,OAAO,IAAI;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CACjC,8OAAC;gCAAK,WAAU;0CAAI,OAAO,KAAK;;;;;;;uBAL3B,OAAO,EAAE;;;;;;;;;;;;;;;;AAW1B", "debugId": null}}, {"offset": {"line": 4490, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/app/%28main%29/layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useRef, useLayoutEffect } from \"react\";\r\nimport Navbar from \"@/components/root/Navbar\";\r\nimport Sidebar from \"@/components/root/Sidebar\";\r\nimport { CreateProjectModal } from \"@/components/modals/CreateProjectModal\";\r\nimport { CreateLibraryItemModal } from \"@/components/modals/CreateLibraryItemModal\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { RootState } from \"@/redux/store\";\r\nimport { NewProjectModal } from \"@/components/modals/NewProjectModal\";\r\nimport { CreateLibraryModal } from \"@/components/modals/CreateLibraryModal\";\r\n\r\nexport default function DashboardLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\r\n  const [navbarHeight, setNavbarHeight] = useState(0);\r\n  const [showNewProjectModal, setShowNewProjectModal] = useState(false);\r\n  const navbarRef = useRef<HTMLElement | null>(null);\r\n  const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);\r\n\r\n  useLayoutEffect(() => {\r\n    const handleResize = () => {\r\n      if (navbarRef.current) {\r\n        setNavbarHeight(navbarRef.current.offsetHeight);\r\n      }\r\n    };\r\n\r\n    const resizeObserver = new ResizeObserver(handleResize);\r\n    if (navbarRef.current) resizeObserver.observe(navbarRef.current);\r\n\r\n    return () => resizeObserver.disconnect();\r\n  }, []);\r\n\r\n  // modals visibility\r\n  const showCreateProjectModal = useSelector(\r\n    (state: RootState) => state.createProject.visible\r\n  );\r\n  const showCreateLibraryModal = useSelector(\r\n    (state: RootState) => state.createLibrary.visible\r\n  );\r\n  const showCreateLibraryItemModal = useSelector(\r\n    (state: RootState) => state.createLibraryItem.visible\r\n  );\r\n\r\n  const handleNewProject = () => {\r\n    setShowNewProjectModal(true);\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex flex-col\">\r\n      {showCreateProjectModal && <CreateProjectModal />}\r\n      {showCreateLibraryModal && <CreateLibraryModal />}\r\n      {showCreateLibraryItemModal && <CreateLibraryItemModal />}\r\n      <NewProjectModal\r\n        isOpen={showNewProjectModal}\r\n        onClose={() => setShowNewProjectModal(false)}\r\n      />\r\n      <Navbar\r\n        toggleSidebar={toggleSidebar}\r\n        isSidebarOpen={isSidebarOpen}\r\n        navbarRef={navbarRef}\r\n      />\r\n\r\n      {/* Main area: Sidebar + Content */}\r\n      <div className=\"flex flex-1 overflow-hidden\">\r\n        <Sidebar\r\n          isOpen={isSidebarOpen}\r\n          toggleSidebar={toggleSidebar}\r\n          topOffset={navbarHeight}\r\n          onNewProject={handleNewProject}\r\n        />\r\n\r\n        {/* Main Content */}\r\n        <main\r\n          className=\"flex-1 p-6 overflow-y-auto\"\r\n          style={{ height: `calc(100vh - ${navbarHeight}px)` }}\r\n        >\r\n          {children}\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAVA;;;;;;;;;;AAYe,SAAS,gBAAgB,EACtC,QAAQ,EAGT;IACC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAsB;IAC7C,MAAM,gBAAgB,IAAM,iBAAiB,CAAC;IAE9C,CAAA,GAAA,qMAAA,CAAA,kBAAe,AAAD,EAAE;QACd,MAAM,eAAe;YACnB,IAAI,UAAU,OAAO,EAAE;gBACrB,gBAAgB,UAAU,OAAO,CAAC,YAAY;YAChD;QACF;QAEA,MAAM,iBAAiB,IAAI,eAAe;QAC1C,IAAI,UAAU,OAAO,EAAE,eAAe,OAAO,CAAC,UAAU,OAAO;QAE/D,OAAO,IAAM,eAAe,UAAU;IACxC,GAAG,EAAE;IAEL,oBAAoB;IACpB,MAAM,yBAAyB,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EACvC,CAAC,QAAqB,MAAM,aAAa,CAAC,OAAO;IAEnD,MAAM,yBAAyB,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EACvC,CAAC,QAAqB,MAAM,aAAa,CAAC,OAAO;IAEnD,MAAM,6BAA6B,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAC3C,CAAC,QAAqB,MAAM,iBAAiB,CAAC,OAAO;IAGvD,MAAM,mBAAmB;QACvB,uBAAuB;IACzB;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,wCAA0B,8OAAC,2IAAA,CAAA,qBAAkB;;;;;YAC7C,wCAA0B,8OAAC,2IAAA,CAAA,qBAAkB;;;;;YAC7C,4CAA8B,8OAAC,+IAAA,CAAA,yBAAsB;;;;;0BACtD,8OAAC,wIAAA,CAAA,kBAAe;gBACd,QAAQ;gBACR,SAAS,IAAM,uBAAuB;;;;;;0BAExC,8OAAC,6HAAA,CAAA,UAAM;gBACL,eAAe;gBACf,eAAe;gBACf,WAAW;;;;;;0BAIb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,8HAAA,CAAA,UAAO;wBACN,QAAQ;wBACR,eAAe;wBACf,WAAW;wBACX,cAAc;;;;;;kCAIhB,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,QAAQ,CAAC,aAAa,EAAE,aAAa,GAAG,CAAC;wBAAC;kCAElD;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}