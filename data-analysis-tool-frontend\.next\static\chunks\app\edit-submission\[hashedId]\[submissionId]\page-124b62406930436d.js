(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7767],{5287:(e,r,t)=>{"use strict";t.d(r,{GN:()=>o,J6:()=>i,O8:()=>n,s4:()=>a});var s=t(25784);let n=async(e,r)=>{try{let{data:t}=await s.A.delete("/form-submissions/".concat(e,"?projectId=").concat(r));return t}catch(e){throw console.error("Error deleting form submission:",e),e}},i=async(e,r)=>{try{let t=e.map(e=>s.A.delete("/form-submissions/".concat(e,"?projectId=").concat(r)));return(await Promise.all(t)).map(e=>e.data)}catch(e){throw console.error("Error deleting multiple form submissions:",e),e}},a=async(e,r)=>{try{if(!e.submissionId||!e.questionId)throw Error("submissionId and questionId are required");let t={...e};null===t.questionOptionId?delete t.questionOptionId:Array.isArray(t.questionOptionId)&&(t.questionOptionId=t.questionOptionId.filter(e=>null!=e),0===t.questionOptionId.length&&delete t.questionOptionId);let{data:n}=await s.A.patch("/answers/".concat(e.questionId,"?projectId=").concat(r),t);return n}catch(e){throw console.error("Error updating answer:",e),e}},o=async(e,r)=>{try{let{data:t}=await s.A.patch("/answers/multiple?projectId=".concat(r),e);return t}catch(e){throw console.error("Error updating multiple answers with endpoint:",e),e}}},37818:(e,r,t)=>{Promise.resolve().then(t.bind(t,60289))},60289:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>k});var s=t(95155),n=t(35695),i=t(19373),a=t(25784),o=t(88570),l=t(34947),d=t(57799),u=t(12115),c=t(82714),p=t(99474),m=t(95139),h=t(55747),y=t(92138),g=t(66474),f=t(13052),b=t(5041),v=t(34540),w=t(71402),x=t(16112),j=t(5287),q=t(10150),I=t(77361),N=t(3587),O=t(13388);function S(e){let{questions:r,submission:t,projectId:n,submissionId:o,onClose:l,onSave:d}=e,S=(0,v.wA)(),[E,k]=(0,u.useState)({}),[A,T]=(0,u.useState)({}),[C,F]=(0,u.useState)({}),[J,R]=(0,u.useState)(!1),[D,G]=(0,u.useState)([]),[_,P]=(0,u.useState)([]),[M,K]=(0,u.useState)({}),[L,Y]=(0,u.useState)(new Set),z=(0,u.useRef)(new Set),Q=(0,u.useRef)(!1),U=(0,u.useRef)(""),{data:H=[]}=(0,i.I)({queryKey:["questionGroups",n],queryFn:()=>(0,q.pr)({projectId:n}),enabled:!!n}),{data:B}=(0,i.I)({queryKey:["project",n],queryFn:()=>(0,I.kf)({projectId:n}),enabled:!!n});(0,u.useEffect)(()=>{let e={};r.forEach(r=>{if("selectmany"===r.inputType){let s=t.answers.filter(e=>e.question.id===r.id);e[r.id]=s.map(e=>e.value).filter(e=>null!=e&&""!==String(e).trim())}else{var s;let n=t.answers.find(e=>e.question.id===r.id);e[r.id]=null!=(s=null==n?void 0:n.value)?s:""}}),k(e),T(JSON.parse(JSON.stringify(e)))},[r,t]),(0,u.useEffect)(()=>{if(!r||0===Object.keys(A).length)return;let e=JSON.stringify(E);if(e===U.current)return;let t=(0,N.UL)(r,E),s=new Set(t.map(e=>e.id));if(G(t),P((0,N.Tr)(r,E)),!Q.current){z.current=s,Q.current=!0,U.current=e;return}if(!(s.size!==z.current.size||[...s].some(e=>!z.current.has(e)))){U.current=e;return}let n=new Set([...s].filter(e=>!z.current.has(e))),i=!1,a={...E};n.size>0&&n.forEach(e=>{let r=e.toString();!L.has(e)&&(void 0===E[r]||""===E[r]||Array.isArray(E[r])&&0===E[r].length)&&void 0!==A[r]&&""!==A[r]&&!(Array.isArray(A[r])&&0===A[r].length)&&(a[r]=A[r],i=!0)});let o=(0,N.OD)(a,t),l=Object.keys(o).length!==Object.keys(a).length;z.current=s,i||l?(U.current=JSON.stringify(o),k(o)):U.current=e},[r,E,A]),(0,u.useEffect)(()=>{if(H.length>0){let e={};H.forEach(r=>{e[r.id]=!0}),K(e)}},[H.length]);let V=(0,u.useMemo)(()=>H.reduce((e,t)=>(e[t.id]=r.filter(e=>e.questionGroupId===t.id),e),{}),[H,r]),W=(0,u.useMemo)(()=>r.filter(e=>null===e.questionGroupId||void 0===e.questionGroupId),[r]),X=(0,u.useMemo)(()=>{let e=[];return H.forEach(t=>{let s=r.filter(e=>e.questionGroupId===t.id),n=s.length>0?Math.min(...s.map(e=>e.position)):t.order;e.push({type:"group",data:t,order:n,originalPosition:n})}),W.forEach(r=>{e.push({type:"question",data:r,order:r.position,originalPosition:r.position})}),e.sort((e,r)=>e.order===r.order?(e.originalPosition||e.order)-(r.originalPosition||r.order):e.order-r.order)},[H,W,r]),Z=(0,u.useCallback)(e=>{K(r=>({...r,[e]:!r[e]}))},[]),$=(0,u.useCallback)((e,r)=>{Y(r=>new Set(r).add(e)),k(t=>({...t,[e]:r})),F(r=>({...r,[e]:""}))},[]),ee=()=>{let e=(0,N.WK)(D,E);return F(e),0===Object.keys(e).length},er=(0,b.n)({mutationFn:async e=>{let s=r.map(r=>{let s=e[r.id],i="selectmany"===r.inputType,a=t.answers.find(e=>e.question.id===r.id),l=!(null==a?void 0:a.id);if(i&&Array.isArray(s)){if(s.length>0){let e=[];r.questionOptions&&(e=s.map(e=>{let t=r.questionOptions.find(r=>r.label===e);return null==t?void 0:t.id}).filter(e=>void 0!==e));let t={projectId:n,questionId:r.id,answerType:r.inputType,value:s.join(", "),questionOptionId:e,isOtherOption:!1,formSubmissionId:o};return l?t:{...t,id:a.id}}return null}{let e,t;if(void 0===(e="number"===r.inputType||"decimal"===r.inputType?s?Number(s):void 0:"date"===r.inputType||"dateandtime"===r.inputType?s||void 0:"table"===r.inputType?Array.isArray(s)&&s.length>0?JSON.stringify(s):void 0:s?String(s):void 0))return null;if("selectone"===r.inputType&&s&&r.questionOptions){let e=r.questionOptions.find(e=>e.label===s);t=null==e?void 0:e.id}let i={projectId:n,questionId:r.id,answerType:r.inputType,value:e,questionOptionId:t,isOtherOption:!1,formSubmissionId:o};return l?i:{...i,id:a.id}}}).filter(e=>null!==e);if(0===s.length)throw Error("No valid answers with IDs to submit");let i=s.map(e=>e.id?{id:e.id,questionId:e.questionId,projectId:n,value:e.value,answerType:e.answerType,questionOptionId:e.questionOptionId,isOtherOption:e.isOtherOption||!1,formSubmissionId:e.formSubmissionId}:e.questionId?{questionId:e.questionId,projectId:n,value:e.value,answerType:e.answerType,questionOptionId:e.questionOptionId,isOtherOption:e.isOtherOption||!1,formSubmissionId:e.formSubmissionId}:null).filter(e=>null!==e);try{return await (0,j.GN)(i,n)}catch(t){console.error("Error with /answers/multiple endpoint:",t),t.response&&(console.error("Error response data:",JSON.stringify(t.response.data,null,2)),console.error("Error response status:",t.response.status),console.error("Error response headers:",t.response.headers));let e=[],r=[];for(let t of s)try{if(t.id){let{data:r}=await a.A.patch("/answers/".concat(t.id,"?projectId=").concat(n),{id:t.id,questionId:t.questionId,projectId:n,value:t.value,answerType:t.answerType,questionOptionId:t.questionOptionId,isOtherOption:t.isOtherOption||!1,formSubmissionId:t.formSubmissionId});e.push(r)}else if(t.questionId){let{data:r}=await a.A.post("/answers?projectId=".concat(n),{submissionId:t.formSubmissionId,questionId:t.questionId,value:t.value,answerType:t.answerType,questionOptionId:t.questionOptionId,isOtherOption:t.isOtherOption||!1});e.push(r)}}catch(s){let e=t.id||t.questionId;console.error("Error handling answer ".concat(e,":"),s),s.response&&console.error("Individual error response data:",JSON.stringify(s.response.data,null,2)),r.push(e)}if(r.length>0)throw Error("Failed to update answers with IDs: ".concat(r.join(", ")));if(e.length>0)return S((0,w.Ds)({message:"Submission updated successfully using individual updates. Consider checking the bulk update endpoint.",type:"warning"})),e;throw t}},onSuccess:()=>{S((0,w.Ds)({message:"Submission updated successfully. You can continue editing if needed.",type:"success"})),Y(new Set),d()},onError:e=>{var r,t,s,n,i,a;let o=(null==(t=e.response)||null==(r=t.data)?void 0:r.message)||(null==(n=e.response)||null==(s=n.data)?void 0:s.error)||e.message||"Failed to update submission. Please check your input and try again.";S((0,w.Ds)({message:o,type:"error"})),console.error("Update Error:",{message:o,status:null==(i=e.response)?void 0:i.status,data:JSON.stringify(null==(a=e.response)?void 0:a.data,null,2)})},onSettled:()=>{R(!1)}}),et=async e=>{e.preventDefault(),ee()&&(R(!0),er.mutate(E))},es=e=>r.some(r=>{var t;return null==(t=r.questionOptions)?void 0:t.some(r=>r.nextQuestionId===e)}),en=e=>{var r;return(null==(r=e.questionOptions)?void 0:r.some(e=>e.nextQuestionId))||!1},ei=e=>{let r=es(e.id),t=en(e);return(0,s.jsxs)("div",{className:"border rounded-md p-4 ".concat(r?"border-primary-200 dark:border-primary-700 bg-primary-100 dark:bg-primary-900/20":"border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"),children:[(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(c.J,{className:"text-base font-medium",children:[e.label,e.isRequired&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),r&&(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200",children:[(0,s.jsx)(y.A,{className:"w-3 h-3 mr-1"}),"Follow-up"]}),t&&(0,s.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent-200 text-accent-700 dark:bg-accent-700/20 dark:text-accent-200",children:"Has conditions"})]}),e.hint&&(0,s.jsx)("p",{className:"text-sm mt-1 ".concat(r?"text-primary-700 dark:text-primary-300":"text-muted-foreground"),children:e.hint}),C[e.id]&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:C[e.id]})]}),(0,s.jsx)("div",{className:"mt-2",children:ea(e)})]},e.id)},ea=e=>{var r,t,n,i;let a=null!=(r=E[e.id])?r:"selectmany"===e.inputType?[]:"";switch(e.inputType){case"text":if(null==(t=e.hint)?void 0:t.includes("multiline"))return(0,s.jsx)(p.T,{value:a,onChange:r=>$(e.id,r.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});return(0,s.jsx)("input",{className:"input-field w-full",value:a,onChange:r=>$(e.id,r.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"number":return(0,s.jsx)("input",{className:"input-field w-full",type:"number",value:a,onChange:r=>$(e.id,r.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"decimal":return(0,s.jsx)("input",{className:"input-field w-full",type:"number",step:"any",value:a,onChange:r=>$(e.id,r.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"selectone":return(0,s.jsx)(h.z,{value:a,onValueChange:r=>$(e.id,r),required:e.isRequired,children:(0,s.jsx)("div",{className:"space-y-2",children:null==(n=e.questionOptions)?void 0:n.map((e,r)=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(h.C,{value:e.label,id:"option-".concat(e.id)}),(0,s.jsx)(c.J,{htmlFor:"option-".concat(e.id),className:"cursor-pointer",children:e.label})]},r))})});case"selectmany":return(0,s.jsx)("div",{className:"space-y-2",children:null==(i=e.questionOptions)?void 0:i.map(r=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(m.S,{id:"option-".concat(r.id),checked:(a||[]).includes(r.label),onCheckedChange:t=>{let s=a||[],n=t?[...s,r.label]:s.filter(e=>e!==r.label);$(e.id,n)}}),(0,s.jsx)(c.J,{htmlFor:"option-".concat(r.id),className:"cursor-pointer",children:r.label})]},r.id))});case"date":return(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("input",{className:"input-field w-full",type:"date",value:a,onChange:r=>$(e.id,r.target.value),placeholder:e.hint||"Select date",required:e.isRequired})});case"dateandtime":return(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("input",{className:"input-field w-full",type:"time",value:a,onChange:r=>$(e.id,r.target.value),placeholder:e.hint||"Select time",required:e.isRequired})});case"table":return(0,s.jsx)(x.N,{questionId:e.id,value:a,onChange:r=>$(e.id,r),required:e.isRequired,tableLabel:e.label});default:return null}};return(0,s.jsxs)("div",{className:"w-full max-w-screen-lg bg-neutral-100 dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("h2",{className:"text-2xl font-semibold p-6 border-b border-gray-200 dark:border-gray-700",children:["Edit Submission",(null==B?void 0:B.name)?" for ".concat(B.name):""]}),(0,s.jsx)("form",{onSubmit:et,className:"p-6",children:(0,s.jsxs)("div",{className:"space-y-6",children:[0===r.length?(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"This form has no questions yet."})}):X.map(e=>{if("group"===e.type){let r=e.data,t=V[r.id]||[],n=t.filter(e=>D.some(r=>r.id===e.id)),i=M[r.id];return 0===n.length?null:(0,s.jsxs)("div",{className:"border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800",children:[(0,s.jsx)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700",onClick:()=>Z(r.id),children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[i?(0,s.jsx)(g.A,{className:"h-5 w-5 text-gray-500"}):(0,s.jsx)(f.A,{className:"h-5 w-5 text-gray-500"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:r.title}),(0,s.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:["(",n.length," visible question",1!==n.length?"s":"",")"]})]})}),i&&(0,s.jsx)("div",{className:"p-4 space-y-4",children:_.filter(e=>t.some(r=>r.id===e.question.id)).map(e=>(0,s.jsx)(O.A,{questionGroup:e,renderQuestionInput:ea,errors:C,className:""},e.question.id))})]},"group-".concat(r.id))}{let r=e.data;if(!D.some(e=>e.id===r.id))return null;let t=_.find(e=>e.question.id===r.id);return t?(0,s.jsx)(O.A,{questionGroup:t,renderQuestionInput:ea,errors:C,className:""},r.id):ei(r)}}),r.length>0&&(0,s.jsxs)("div",{className:"mt-6 flex justify-end gap-4",children:[(0,s.jsx)("button",{className:"btn-primary bg-neutral-500 hover:bg-neutral-600",type:"button",onClick:l,disabled:J,children:"Cancel"}),(0,s.jsx)("button",{className:"btn-primary",type:"submit",disabled:J,children:J?"Saving...":"Save Changes"})]})]})})]})}let E=async(e,r)=>{let{data:t}=await a.A.get("/form-submissions/".concat(e)),s=t.data.formSubmissions.find(e=>e.id===r);if(!s)throw Error("Submission not found");return s};function k(){let{hashedId:e,submissionId:r}=(0,n.useParams)(),t=(0,o.D)(e),a=Number(r);if(null===t||isNaN(a))return(0,s.jsx)("div",{children:"Error: Invalid project or submission ID."});let{data:u=[],isLoading:c,isError:p}=(0,i.I)({queryKey:["questions",t],queryFn:()=>(0,l.K4)({projectId:t}),enabled:!!t}),{data:m,isLoading:h,isError:y,refetch:g}=(0,i.I)({queryKey:["submission",t,a],queryFn:()=>E(t,a),enabled:!!t&&!!a});return c||h?(0,s.jsx)(d.A,{}):p||y||!u||!m?(0,s.jsx)("p",{className:"text-sm text-red-500",children:"Error loading submission or form. Please try again."}):(0,s.jsx)("div",{className:"min-h-screen w-full bg-neutral-100 dark:bg-gray-900 flex flex-col items-center p-4 md:p-6",children:(0,s.jsx)(S,{questions:u,submission:m,projectId:t,submissionId:a,onSave:()=>{window.opener&&window.opener.postMessage({type:"REFETCH_SUBMISSIONS"},"*"),g()}})})}},77361:(e,r,t)=>{"use strict";t.d(r,{D_:()=>c,Im:()=>d,Oo:()=>p,c3:()=>i,kf:()=>n,lj:()=>h,or:()=>l,pf:()=>u,vj:()=>a,wI:()=>m,xx:()=>o});var s=t(25784);let n=async e=>{let{projectId:r}=e,{data:t}=await s.A.get("/projects/".concat(r));return t.project},i=async e=>{let{data:r}=await s.A.post("/projects/from-template",e);return r},a=async()=>{try{let{data:e}=await s.A.get("/projects");return e.projects}catch(e){throw console.error("Error fetching projects:",e),e}},o=async e=>{let{data:r}=await s.A.delete("/projects/delete/".concat(e));return r},l=async e=>{try{let{data:r}=await s.A.delete("/projects/delete-multiple",{data:{projectIds:e}});return r}catch(e){throw console.error("Error deleting multiple projects:",e),e}},d=async e=>{try{let{data:r}=await s.A.patch("/projects/change-status/".concat(e),{status:"archived"});return r}catch(e){throw console.error("Error archiving project:",e),e}},u=async function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{let{data:r}=await s.A.patch("/projects/change-status/".concat(e),{status:"deployed"});return r}catch(e){throw console.error("Error deploying project:",e),e}},c=async e=>{try{let{data:r}=await s.A.patch("/projects/update-many-status",{projectIds:e,status:"archived"});return r}catch(e){throw console.error("Error archiving multiple projects:",e),e}},p=async e=>{try{let{data:r}=await s.A.post("/users/check-email",{email:e});return r}catch(e){var r,t,n,i,a,o;throw Error("object"==typeof(null==(t=e.response)||null==(r=t.data)?void 0:r.message)?JSON.stringify(null==(i=e.response)||null==(n=i.data)?void 0:n.message):(null==(o=e.response)||null==(a=o.data)?void 0:a.message)||e.message||"Failed to check user")}},m=async e=>{let{projectId:r,email:t,permissions:n}=e;try{let e=await p(t);if(!e||!e.success)throw Error((null==e?void 0:e.message)||"User not found");let{data:i}=await s.A.post("/project-users",{userId:e.user.id,projectId:r,permission:n});return i}catch(e){var i,a,o,l,d,u;throw console.error("Error adding user to project:",e),Error("object"==typeof(null==(a=e.response)||null==(i=a.data)?void 0:i.message)?JSON.stringify(null==(l=e.response)||null==(o=l.data)?void 0:o.message):(null==(u=e.response)||null==(d=u.data)?void 0:d.message)||e.message||"Failed to add user")}},h=async e=>{try{let{data:r}=await s.A.post("/answers/multiple",e);return r}catch(e){throw console.error("Error creating answer submission:",e),e}}},88570:(e,r,t)=>{"use strict";t.d(r,{D:()=>o,l:()=>a});var s=t(41050);let n=t(49509).env.SALT||"rushan-salt",i=new s.A(n,12),a=e=>i.encode(e),o=e=>{let r=i.decode(e)[0];return"bigint"==typeof r?r<Number.MAX_SAFE_INTEGER?Number(r):null:"number"==typeof r?r:null}}},e=>{var r=r=>e(e.s=r);e.O(0,[635,1445,6967,6903,4277,556,3481,7823,7248,8441,1684,7358],()=>r(37818)),_N_E=e.O()}]);